<!-- eslint-disable vue/require-toggle-inside-transition -->
<template>
  <transition name="fade" @after-leave="destroy">
    <div
      class="kkc-loading__wrap"
      :class="{'mask-layer': isShowMask}"
      :style="{
        backgroundColor: bgColor
      }"
    >
      <div class="kkc-loading">
        <img src="https://oss.kuke99.com/kukecloud/static/loading.png" class="kkc-loading__img" height="44" width="44">
        <div class="kkc-loading__con">
          {{ content }}
        </div>
      </div>
    </div>
  </transition>
</template>

<script setup lang="ts">

withDefaults(defineProps<{
  destroy?: (el: Element) => void;
  content?: string;
  bgColor?: string;
  isShowMask?: boolean
}>(), {
  destroy: () => { },
  content: '加载中',
  bgColor: 'rgba(0, 0, 0, 0.7)',
  // 是否展示遮罩层
  isShowMask: false
})
</script>
<style lang="scss">
.mask-layer {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 9999;
}

.kkc-loading {
  position: fixed;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 10px;
  box-sizing: border-box;
  top: 50%;
  left: 50%;
  transform: translate(-50%);
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  // flex-wrap: wrap;
  font-size: 14PX;

  width: 100PX;
  height: 100PX;

  z-index: 9999;

  &__img {
    position: relative;
    // width: 44PX;
    // height: 44PX;
    // border: 6PX solid var(--kkc-brand, #eb2330);
    // border-top-color: rgba(0, 0, 0, 0.1);
    // border-right-color: rgba(0, 0, 0, 0.1);
    // border-bottom-color: rgba(0, 0, 0, 0.1);
    // border-radius: 100%;
    // animation: circle infinite 0.75s linear;
  }

  &__con {
    margin-top: 5PX;
    color: #ccc;
  }

  // @keyframes circle {
  //   0% {
  //     transform: rotate(0);
  //   }

  //   100% {
  //     transform: rotate(360deg);
  //   }
  // }
}
</style>
