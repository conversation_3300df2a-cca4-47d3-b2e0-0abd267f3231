<template>
  <div
    class="audio-buffer"
    :class="isPc&&'pc_audio'"
    @click.stop
    @touchstart.stop
    @touchmove.stop
    @touchend.stop
  >
    <!-- 音频  @timeupdate="updateProgress"-->
    <audio
      :id="audioId"
      controls
      :playsinline="true"
      :webkit-playsinline="true"
      class="hidden"
      @loadedmetadata="setDuration"
      @ended="endPlay"
    >
      <source :src="props.url">
      您的浏览器不支持音频播放
    </audio>
    <div v-if="isPc" class="audio-text mx-[12px]">
      {{ formatTime(currentTime) }}
    </div>
    <div class="relative flex items-center">
      <input
        :id="uuid"
        type="range"
        class="audio-buffer-process"
        :value="progress"
        min="0"
        :max="durationTime"
        step="1"
        @change="changeRadio"
        @mousedown="pauseChange"
        @touchstart="pauseChange"
      >
      <div class="inner-procecss" :style="{width:`${(progress/durationTime)*100}%`}" />
    </div>
    <div v-if="isPc" class="audio-text ml-[12px] mr-[12px]">
      {{ formatTime(durationTime) }}
    </div>
    <div v-if="!isPc" class="flex items-center justify-between h-[44px]">
      <div class="audio-text">
        {{ formatTime(currentTime) }}
      </div>
      <div class="audio-text ml-[12px]">
        {{ formatTime(durationTime) }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// hooks
import md5 from 'md5'
import { buildUUID } from '../../utils/uuid'
import { addAudioUniquely, pauseAudio, removeAudio } from '../../utils/audio'
import { uploadProgress as uploadProgressData, } from '../../apis/video'
import type { UploadProgressParams } from '../../apis/video/types'
import { useAudioDetail } from './useAudioDetail'
import { useUserStore } from '~/stores/user.store'
const { isPc } = useAppConfig()
const { formatTime } = useAudioDetail()

const uuid = computed(() => 'process_' + buildUUID())
const audioId = computed(() => 'audio_' + buildUUID())
// 音频播放器元素
const audioPlayer = ref<HTMLAudioElement | null>(null)

// 当前播放时间（秒）
const currentTime = ref<number>(0)

// 是否暂停
const isPause = ref<boolean>(true)

// 播放进度
const progress = ref(0)
// 是否播放完毕
const isAudioEnd = ref(false)

// 音频信息
const props = withDefaults(
  defineProps<{
    // 音频资源
    url: string;
    nodeId: string,
    goodsMasterId: string,
    tryListenId?: string,
    learnProgress?: number,
    lastTimePoint?: number,
    audioDuration?: number,
    loadAudio?: boolean,
    isNoLoginSeeCourseNode?: boolean,
  }>(),
  {
    learnProgress: 0,
    lastTimePoint: 0,
    audioDuration: 0,
    loadAudio: false
  }
)
const emits = defineEmits<{
  (e: 'handleChanged'): void;
  (e: 'playEnd'): void;
  (e: 'pauseFun', v:boolean): void;
}>()
const lastTimePoint = ref(props.lastTimePoint || 0)
const courseNodeId = ref(props.nodeId)
// 是否是试听商品跳转到详情标识
const kkIsAudition = useCookie<string>(IS_AUDITION)
const kkPoolId = useCookie<string>(POOL_ID)
const isDistory = ref(false) // 组件销毁前kkIsAudition已经重置了，所以需要存储下
const { specificationItemId = '' } = useRoute()?.query
// 学习进度参数
const updateParams = reactive<UploadProgressParams>({
  nodeId: courseNodeId.value,
  learningToken: undefined,
  realLearningTime: '0',
  timePoint: undefined,
  playOver: undefined,
  goodsMasterId: props.goodsMasterId,
  tryListenId: props.tryListenId,
})
// 音频总时长（秒）
const durationTime = ref<number>(props.audioDuration || 0)

// 切换播放和暂停
const onClickBtn = () => {
  if (isPause.value) {
    pauseAudio()
    // 开始播放
    const playPromise = audioPlayer.value?.play()
    isPause.value = false

    // 处理播放承诺
    if (playPromise !== undefined) {
      playPromise.catch((error) => {
        console.error('播放失败:', error)
        // 自动播放策略可能阻止播放
        isPause.value = true
        // 在移动端，可能需要重新加载音频
        audioPlayer.value?.load()
      })
    }
  } else {
    // changeProgress(currentTime.value)
    audioPlayer.value?.pause()
  }
}
const time = new Date().getTime()
const md5Audio = md5(String(time) + (useUserStore().getUserInfo?.userId || ''))
// 上传播放进度
const changeProgress = async (timePoint:number) => {
  if (timePoint === 0) {
    return
  }
  if (props.isNoLoginSeeCourseNode) {
    return
  }
  try {
    const realLearningTime = (currentTime.value) - lastTimePoint.value
    if (realLearningTime > 0) {
      lastTimePoint.value = currentTime.value
    }
    updateParams.realLearningTime = Math.floor(realLearningTime > 0 ? realLearningTime : 0)
    updateParams.timePoint = Math.floor(currentTime.value)
    updateParams.learningToken = 'audio_' + md5Audio.substring(8, 24)
    if ((Number(kkIsAudition.value) === 1 || isDistory.value) && kkPoolId.value) {
      updateParams.poolId = kkPoolId.value
      updateParams.specificationItemId = specificationItemId ?? undefined
    }
    console.log('updateParams', updateParams)
    const { data, error } = await uploadProgressData({ ...updateParams })
    console.log(data, error.value)
  } catch (e) {
    console.log(e)
  }
}
// 更新当前播放进度
const updateProgress = () => {
  console.log('updateProgress', durationTime.value)
  if (durationTime.value) {
    currentTime.value = Math.floor(audioPlayer.value?.currentTime || 0)
    // 进度
    progress.value = currentTime.value
  }
}
// 拖拽前更新播放进度
const pauseChange = () => {
  audioPlayer.value?.pause()
  changeProgress(currentTime.value)
}

// 当音频元数据加载完成时设置总时长
const setDuration = () => {
  if (audioPlayer.value && props.url) {
    durationTime.value = Math.floor(audioPlayer.value?.duration) || durationTime.value
  }
}

// 设置流式加载的起始字节
// let byteStart = 0
// const chunkSize = 1024 * 1024 * 1 // 每次请求 1MB
// // 请求音频的一个块
// const fetchAudioChunk = (startByte:number) => {
//   const endByte = startByte + chunkSize - 1
//   const range = `bytes=${startByte}-${endByte}`
//   if (!props.url) {
//     return
//   }
//   // 使用 fetch API 获取音频数据的一个块
//   fetch(props.url, {
//     headers: {
//       Range: range
//     }
//   })
//     .then(response => response.arrayBuffer())
//     .then((data) => {
//       // 将音频数据转换为 Blob，并添加到音频元素
//       const audioBlob = new Blob([data], { type: 'audio/mpeg' })
//       const audioUrl = URL.createObjectURL(audioBlob)
//       // 更新音频元素的源 URL
//       playUrl.value = audioUrl
//       console.log('fetchAudioChunk-currentTime', audioPlayer.value?.currentTime)
//       // 如果音频没有播放完，继续请求下一块
//       if (data.byteLength === chunkSize) {
//         byteStart += chunkSize
//         fetchAudioChunk(byteStart) // 请求下一个音频块
//         isWaiting.value = true
//       } else {
//         isWaiting.value = false
//       }
//     })
//     .catch((error) => {
//       console.error('Error loading audio chunk:', error)
//       isWaiting.value = false
//     })
// }

// 修改进度条
const changeRadio = () => {
  audioPlayer.value?.pause()
  if (!props.url) {
    return
  }
  const changeVal = document.querySelector(`#${uuid.value}`)?.value
  console.log('audioPlayer.value', audioPlayer.value)
  if (audioPlayer.value) {
    audioPlayer.value.currentTime = changeVal
  }
  console.log('audioPlayer.value', audioPlayer.value?.currentTime)
  emits('handleChanged')
}

onBeforeUnmount(() => {
  // audioPlayer.value?.removeEventListener('beforeunload', beforeunloadHandler)
  // audioPlayer.value?.removeEventListener('unload', unloadHandler)
  audioPlayer.value?.removeEventListener('play', play)
  audioPlayer.value?.removeEventListener('pause', pause)
  audioPlayer.value?.removeEventListener('timeupdate', updateProgress)
  changeProgress(currentTime.value)
  // 移除缓存数据
  removeAudio(audioPlayer.value as HTMLAudioElement)
})
const play = () => {
  // 开始播放更新isPause状态
  isPause.value = false
  isAudioEnd.value = false
}

const pause = () => {
  // 暂停播放更新isPause状态
  isPause.value = true
  changeProgress(currentTime.value)
}
watch(() => isPause.value, (v) => {
  emits('pauseFun', v)
})
// 创建音频
const createAudio = () => {
  audioPlayer.value = document.querySelector(
      `#${audioId.value}`
  ) as HTMLAudioElement
  console.log('audioPlaer', audioPlayer.value)
  audioPlayer.value.currentTime = props.lastTimePoint
  progress.value = props.lastTimePoint
  currentTime.value = props.lastTimePoint
  console.log('currentTime', audioPlayer.value.currentTime)
  // updateProgress()
  addAudioUniquely(audioPlayer.value as HTMLAudioElement)
  // audioPlayer.value?.addEventListener('beforeunload', beforeunloadHandler)
  // audioPlayer.value?.addEventListener('unload', unloadHandler)
  audioPlayer.value?.addEventListener('play', play)
  audioPlayer.value?.addEventListener('pause', pause)
  audioPlayer.value?.addEventListener('timeupdate', updateProgress)
}
onMounted(() => {
  createAudio()
})
// const beforeunloadHandler = (event:any) => {
//   event.returnValue = '确定要刷新窗口吗？'
//   leave()
// }
// const unloadHandler = (event:any) => {
//   event.returnValue = '确定要关闭窗口吗？'
// }

// const leave = async () => {
//   await changeProgress(currentTime.value)
//   // 移除监听事件
//   audioPlayer.value?.removeEventListener('timeupdate', updateProgress)
//   audioPlayer.value?.removeEventListener('loadedmetadata', setDuration)
//   audioPlayer.value?.removeEventListener('play', play)
//   audioPlayer.value?.removeEventListener('pause', pause)
// }
// 播放完毕
const endPlay = () => {
  isPause.value = true
  changeProgress(currentTime.value)
  isAudioEnd.value = true
  emits('playEnd')
}

defineExpose({
  onClickBtn,
  // isPause,
  pause: () => {
    audioPlayer.value?.pause()
  },
  isAudioEnd
})
</script>

<style lang="scss" scoped>
.audio-buffer{
  @apply h-[34px] pr-[24px] mt-[6px] w-[100%] rounded-[6px];
  .audio-buffer-process {
    @apply w-full h-[4px] bg-[#dddddd] border-none rounded-[8px] cursor-pointer relative;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;

    &:focus {
      outline: none;
    }
    &::-webkit-slider-thumb {
      @apply w-[10px] h-[10px] bg-[#ffffff] rounded-[50%] cursor-pointer relative z-20 relative top-[-1px];
      box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.2);
      outline: none;
      -webkit-appearance: none;
      -moz-appearance: none;
      appearance: none;
    }
  }
  .inner-procecss{
    @apply h-[4px] bg-[#36D281] absolute pointer-events-none;
    border-radius: 8px 4px 4px 8px;
  }
}
.pc_audio{
  @apply flex items-center justify-between w-[100%];
  .audio-buffer-process{
    width:240px
  }
}
</style>
