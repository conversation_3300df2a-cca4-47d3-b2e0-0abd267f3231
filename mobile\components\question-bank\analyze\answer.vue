<!-- eslint-disable vue/no-v-html -->
<template>
  <div :class="type === 4 && qNum !== 1 ? 'pt-[50px]' : 'pt-[20px]'">
    <div class="flex flex-nowrap">
      <div v-if="type === 4 && isShowNum" class="qNum">
        {{ qNum }}
      </div>
      <div class="q-content" :class="type === 4 ? 'hasNumW' : 'w-[100%]'">
        <div class="text-[28px] text-[#111111] leading-[48px] w-[100%] text-wrap">
          <template v-if="showData.html === '' && showData.imgs.length === 0 && !showData.htmlSrc">
            未答
          </template>
          <template v-else>
            <div v-img-preview="showData.html" class="exam-html">
              <KKCHtmlMathJax :content="showData.html" />
            </div>
            <KKCAudioPlayer
              v-if="showData.htmlSrc"
              class="bg-[#f8f8f8] mt-[8px]"
              :icon-name="'icon-yinpinbofang'"
              :playing-icon="'icon-yinpinzanting'"
              :icon-size="48"
              :url="showData.htmlSrc"
            />
          </template>
        </div>
        <div
          v-if="showData.imgs && showData.imgs.length !== 0"
          class="flex h-[120px] overflow-auto flex-nowrap w-[100%]"
          :class="showData.html ? 'mt-[24px]' : ''"
        >
          <img v-for="(item, index) in showData.imgs" :key="index" class="w-[120px] mr-[24px] rounded-[10px]" :src="item">
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  answer: {
    type: Object,
    default: () => ({ }),
  },
  answerString: {
    type: String,
    default: '',
  },
  // 1：你的答案 传递answer 2：参考答案 传递answerString
  showType: {
    type: Number,
    default: 0,
  },
  qNum: {
    type: Number,
    default: 0,
  },
  type: {
    type: Number,
    default: 4,
  },
  isShowNum: {
    type: Boolean,
    default: true
  }
})
const showData = ref<any>({})
onMounted(() => {
  if (props.showType === 1) {
    showData.value = props.answer
  } else {
    showData.value.html = props.answerString
    showData.value.imgs = []
  }
})
</script>
<style scoped lang="scss">
.qNum {
  margin-top: 6px;
  margin-right: 16px;
  background-color: #e8eaf3;
  color: #5c658c;
  width: 36px;
  height: 36px;
  line-height: 36px;
  text-align: center;
  font-size: 26px;
  border-radius: 6px;
  font-family: PingFang SC-Medium, PingFang SC;
  font-weight: 400;
}

.hasNumW {
  max-width: calc(100% - 36px - 16px);
}

.text-wrap {
  text-wrap: wrap;
  word-wrap: break-word;
}
</style>
