import api from '@/apis/index'

// TODO  内存泄漏可疑点: 全局变量
export const useApi = () => api

interface IItem {
    cpId:string
    pageMasterId:string
}
/**
 * 网校定制化首页
 * 对接自定义页面
 * */
export const useKuke99DefaultHomeData = () => {
  const HOME_CUSTOM_ID_PAGE_MASTER_ID_MAP:Record<string, IItem> = {
    LOCAL: {
      cpId: '969190571348557825',
      pageMasterId: '969190571348557826',
    },
    DEV: {
      cpId: '969190571348557825',
      pageMasterId: '969190571348557826',
    },
    LOCAL_STAGING: {
      cpId: '966215870171422721',
      pageMasterId: '966215870171422722',
    },
    STAGING: {
      cpId: '966215870171422721',
      pageMasterId: '966215870171422722',
    },
    LOCAL_PRESSURE: {
      cpId: '985801725047345153',
      pageMasterId: '985801725047345154',
    },
    PRESSURE: {
      cpId: '985801725047345153',
      pageMasterId: '985801725047345154',
    },
    LOCAL_PRE: {
      cpId: '968065283323817985',
      pageMasterId: '968065283323817986',
    },
    PRE: {
      cpId: '968065283323817985',
      pageMasterId: '968065283323817986',
    },
    PROD: {
      cpId: '968065283323817985',
      pageMasterId: '968065283323817986', // TODO
    },
  }
  const {
    learnTargetInfo,
  } = useLearnTarget()
  const { appId, lesseeId, isXcx } = useAppConfig()
  const { public: { BUILD_ENV } } = useRuntimeConfig()
  const data = computed(() => {
    if (!learnTargetInfo.value.enable) {
      return {}
    } else if (appId === 1 && !lesseeId && !isXcx) {
      return HOME_CUSTOM_ID_PAGE_MASTER_ID_MAP[BUILD_ENV] || {}
    } else {
      return {}
    }
  })
  // console.log('[ datadata ] >', data)
  return {
    data
  }
}
export function isInteger (value) {
  // 如果是字符串，先转换成数字再检查
  if (typeof value === 'string') {
    if (!value) {
      return false
    }
    value = Number(value)
  }

  return Number.isInteger(value)
}
