
import type { CustomAppConfig } from 'nuxt/schema'
import type { Defu } from 'defu'
import cfg0 from "D:/workspace/0.nuxt3/migration/kuke-cloud---next/mobile/app.config"

declare const inlineConfig = {
  "requestAppid": "c4881275944",
  "requestAppkey": "awo6ureum8bn",
  "clientType": "7",
  "WX_APP_ID": "wx32638bab45b67226",
  "QQ_APP_ID": "102079805",
  "PRODUCT_TYPE_DOMAIN": "m-kukeyun-devc.tyyk99.com",
  "AUTH_CENTER": "https://auth-center-dev.kukewang.com",
  "USER_AGREEMENT_ID": "815463590374293504",
  "PRIVACY_POLICY_AGREEMENT_ID": "815463680002375680",
  "PRIVACY_POLICY_THIRD_ID": "897273773977350144",
  "FILE_VIEW_DOMAIN": "https://kkfileviewdev.kukewang.com",
  "KUKE_CLOUD_ORG_ID_LIST": "['1','2']",
  "requestEncryptKey": "vmHbx4bnI91ytL389b52nGBSScAQ50oIbYsNV4AfVgw=",
  "NUXT_KK_STUDENT_LOCALHOST": "*",
  "NUXT_KK_STUDENT_LOCALHOST_TY": "*",
  "nuxt": {}
}
type ResolvedAppConfig = Defu<typeof inlineConfig, [typeof cfg0]>
type IsAny<T> = 0 extends 1 & T ? true : false

type MergedAppConfig<Resolved extends Record<string, unknown>, Custom extends Record<string, unknown>> = {
  [K in keyof (Resolved & Custom)]: K extends keyof Custom
    ? unknown extends Custom[K]
      ? Resolved[K]
      : IsAny<Custom[K]> extends true
        ? Resolved[K]
        : Custom[K] extends Record<string, any>
            ? Resolved[K] extends Record<string, any>
              ? MergedAppConfig<Resolved[K], Custom[K]>
              : Exclude<Custom[K], undefined>
            : Exclude<Custom[K], undefined>
    : Resolved[K]
}

declare module 'nuxt/schema' {
  interface AppConfig extends MergedAppConfig<ResolvedAppConfig, CustomAppConfig> { }
}
declare module '@nuxt/schema' {
  interface AppConfig extends MergedAppConfig<ResolvedAppConfig, CustomAppConfig> { }
}
