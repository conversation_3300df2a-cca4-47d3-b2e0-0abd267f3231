/**
 * 做题模式
 * @alias 1 练习模式
 * @alias 2 考试模式（正计时）
 * @alias 3 快答模式
 * @alias 4 背题模式
 * @alias 5 考试模式（倒计时）
 * */
export type QuestionModel = 1 | 2 | 3 | 4 | 5

// 0未开始 1刷题中 2已做完
export enum QuestionDoStatusFromLearnStatusName {
  '未开始' = 0, // 0未开始
  '学习中', // 刷题中
  '已学完', // 已做完
}

export const QuestionDoStatusAlias = {
  刷题中: QuestionDoStatusFromLearnStatusName['学习中'],
  已做完: QuestionDoStatusFromLearnStatusName['已学完']
}
