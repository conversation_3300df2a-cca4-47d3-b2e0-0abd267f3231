import type { ButtonConfig, StatusType, ActivityInfoType } from '../types/type'
const { track } = useTrackEvent()
export const useAnswerLottery = (questionLotteryId:string) => {
  const { answerLotteryApi } = useApi()
  const { addBehaviorRecords } = useBehaviorRecord()
  const { isXcx } = useAppConfig()
  /**
   * 按钮配置
   */
  const buttonConfig = ref<{
    status:StatusType
    buttonArr:ButtonConfig[]
  }>({
    status: 0,
    buttonArr: []
  })
  /**
   * 跳转题库 查看成绩
   */
  const navigateToTikuScore = (data:ActivityInfoType) => {
    track({
      category: '营销',
      action: '抽奖-查看成绩'
    })
    navigateTo({
      path: '/question-bank/report',
      query: {
        testpaperId: data.testpaperId, // 898366565611585536
        moduleId: data.moduleManageId, // 978455410163945472
        activityId: questionLotteryId,
        activityName: data.activityName,
        examStatus: 'start',
        questionType: 5,
        moduleType: 0,
        targetResultId: data.targetResultId // 1066611710240210944
      }
    })
  }
  /**
   * 去抽奖
   */
  const goRaffle = (data:ActivityInfoType) => {
    track({
      category: '营销',
      action: '抽奖-查看成绩'
    })
    navigateTo({
      path: `/activity/raffle/${data.lotteryId}`,
      query: {
        questionLotteryId
      }
    })
  }
  /**
   * 各状态下 按钮配置
   */
  const buttonEnum = ref<{
    status:StatusType
    buttonArr:ButtonConfig[]
    }[]>([
      {
        status: 0,
        buttonArr: [
          {
            tips: '答题合格即可抽奖',
            click: async (info:ActivityInfoType) => {
              try {
                const { error } = await answerLotteryApi.questionLotteryVerify({ questionLotteryId })
                if (!error.value) {
                  track({
                    category: '营销',
                    action: '抽奖-开始答题'
                  })
                  navigateTo({
                    path: '/question-bank/paper',
                    query: {
                      testpaperId: info.testpaperId, // 898366565611585536
                      moduleId: info.moduleManageId, // 978455410163945472
                      activityId: questionLotteryId,
                      activityName: info.activityName,
                      examStatus: 'start',
                      questionType: 5,
                      moduleType: 0
                    }
                  })
                  addBehaviorRecords(ActionEnum.answerLottery, SourceEnum.Wap, {
                    goodsTitle: info.activityName, // 用户参与活动的名称
                    clientType: isXcx ? ClientType.WX_XCX : ClientType.WAP,
                    extension8: info.activityName,
                    cateIds: info.cateIds
                  })
                }
              } catch (e) {
                console.log(e)
              }
            }
          }
        ]
      },
      {
        status: 1,
        buttonArr: [
          {
            name: '查看成绩',
            tips: '答题成绩不满足活动要求，暂无法抽奖',
            click: (info:ActivityInfoType) => {
              navigateToTikuScore(info)
            }
          }
        ]
      },
      {
        status: 2,
        buttonArr: [
          {
            name: '查看成绩',
            bg: '#F2F3F5',
            color: '#111111',
            click: (info:ActivityInfoType) => {
              navigateToTikuScore(info)
            }
          },
          {
            name: '去抽奖',
            click: (info:ActivityInfoType) => {
              goRaffle(info)
            }
          }
        ]
      },
      {
        status: 3,
        buttonArr: [
          {
            name: '查看成绩',
            bg: '#F2F3F5',
            color: '#111111',
            click: (info:ActivityInfoType) => {
              navigateToTikuScore(info)
            }
          },
          {
            name: '抽奖结果',
            click: (info:ActivityInfoType) => {
              goRaffle(info)
            }
          }
        ]
      },
    ])
    /**
     * 获取当前状态下按钮配置
     * @param status
     */
  const getButtonConfig = (status:number) => {
    const findArr = buttonEnum.value.find(f => f.status === status)
    if (findArr) {
      buttonConfig.value = findArr
    }
  }
  return {
    buttonConfig,
    getButtonConfig
  }
}
