export interface CCVideoPlayerType {
  siteId: string // cc平台的userid
  ccFlag: number // cc录播标识，1是，0否
}

export interface PolyvVideoPlayerType {
  playSafe: string // PC端播放加密视频所需的授权凭证
  ts: string // 移动端H5播放加密视频需传入的13位毫秒级时间戳
  sign: string // 移动端H5播放加密视频所需的签名
}

export interface VideoDetailType extends CCVideoPlayerType, PolyvVideoPlayerType {
  videoId: string // 视频id
  videoType: 1 | 2 // 视频类型，1：保利威视频，2：CC视频
  questionId: string // 题目id
  orgId: string // 组织ID
}

export interface VideoDetailResult {
  data?: VideoDetailType
  [key: string]: any
}
