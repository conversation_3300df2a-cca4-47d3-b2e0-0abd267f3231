<template>
  <div class="pt-[44px] bg-[#FFFFFF] px-[28px] flex items-center mt-[24px] justify-between">
    <div class="flex items-center">
      <!-- <KKCIcon name="icon-xiangqing-jie" :size="21" /> -->
      <img :src="analysisIcon" class="analysis-icon" alt="">
      <div class="text-[32px] pl-[16px] font-medium">
        {{ title }}
      </div>
      <div v-if="desc" class="text-[24px] text-[#999]">
        （{{ desc }}）
      </div>
    </div>
    <slot>
      <span>
        <span v-if="label" class="text-[40px] text-[#333] font-gilroy-bold">{{ label }}</span>
        <span v-if="unit" class="text-[28px] text-[#333]">{{ unit }}</span>
      </span>
    </slot>
  </div>
</template>
<script setup lang="ts">
import analysisIcon from '@/assets/images/question-bank/analysis-icon.png'
defineProps({
  label: {
    type: String,
    default: '',
  },
  unit: {
    type: String,
    default: '',
  },
  desc: {
    type: String,
    default: '',
  },
  title: {
    type: String,
    default: '',
  }
})
</script>
<style scoped lang="scss">
.analysis-icon{
  width: 36px;
  height: 36px;
}
</style>
