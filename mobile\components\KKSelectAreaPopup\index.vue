<template>
  <div class="area-select-popup">
    <div v-if="provinceList?.length && provinceList?.length > 1" class="area-select-btn" @click="handleClick">
      <nuxt-icon name="area-icon" filled class="p-[6px]" />
      <div class="area-select-btn-text">
        <!-- 防止请求三次接口导致的文字抖动 -->
        <!-- <template v-if="getProvinceListLoading">
          地区
        </template>
        <template v-else>
          {{ areaTabs?.length ? areaTabs[areaTabs.length - 1].name : '地区' }}
        </template> -->
        {{ areaNames || '地区' }}
      </div>
    </div>
    <!-- 弹出层 -->
    <KKPopup
      ref="selectAreaPopup"
      position="bottom"
      title=""
      box-padding="0"
      :close-on-click-overlay="false"
      height="90%"
    >
      <div class="popup-box">
        <div class="flex items-center h-[124px]">
          <div class="ml-[95px] text-center flex-1 text-[#111] text-[36px] font-semibold leading-[50px]">
            选择地区
          </div>
          <div class="mx-[34px]" @click="handleClose">
            <KKCIcon name="icon-com_guangbi" :size="48" color="#666666" />
          </div>
        </div>
        <div class="popup-box-content">
          <div class="area-list bg-[#F7F7F7]">
            <div
              v-for="(item, index) in provinceList"
              :key="index"
              class="area-list-tabs"
              :class="{'area-list-tabs-active' : areaTabs?.[0] && String(areaTabs[0].id) === String(item.usedId)}"
              @click="handleAreaItem(item)"
            >
              {{ item.labelValue }}
            </div>
          </div>
          <div class="area-list">
            <div
              v-for="(item, index) in cityList"
              :key="index"
              class="area-list-tabs"
              :class="{'area-list-tabs-active' : areaTabs?.[1] && String(areaTabs[1]?.id) === String(item?.id)}"
              @click="handleAreaItem(item)"
            >
              {{ item.areaName }}
            </div>
          </div>
          <div class="area-list">
            <div
              v-for="(item, index) in areaList"
              :key="index"
              class="area-list-tabs"
              :class="{'area-list-tabs-active' : areaTabs?.[1] && String(areaTabs[2]?.id) === String(item?.id)}"
              @click="handleAreaItem(item)"
            >
              {{ item.areaName }}
            </div>
          </div>
        </div>
        <div class="popup-box-bottom safe-area">
          <button
            class="bottom-btn bg-[#F2F2F2]"
            @click="handleReset"
          >
            重置
          </button>
          <button
            class="bottom-btn btn-kkc"
            @click="handleOk"
          >
            确定
          </button>
        </div>
      </div>
    </KKPopup>
  </div>
</template>

<script lang="ts" setup>
import type { AreaLabel, RegionLabel } from '~/apis/news/types'

interface AreaCheckInfo {
  id?: string
  level: number
  name: string
}
const areaTabs = ref<AreaCheckInfo[]>([])
// 省市区对应数据
const provinceList = ref<RegionLabel[]>([])
const cityList = ref<AreaLabel[]>([])
const areaList = ref<AreaLabel[]>([])

const { newsApi } = useApi()

const props = defineProps({
  id: {
    type: Number,
    default: 0
  },
  data: {
    type: Object,
    default: () => {}
  },
  areaNames: {
    type: String,
    default: ''
  },
})

const emits = defineEmits<{
  (e: 'change', data: any): void;
}>()

// 控制弹框展示
const selectAreaPopup = ref()
const handleClick = () => {
  getProvinceList(props.id)
  selectAreaPopup.value?.showPopup()
}

// 关闭弹出层
const handleClose = () => {
  cityList.value = []
  areaList.value = []
  selectAreaPopup.value?.hide()
  // getProvinceList(props.id) // 关闭弹出层请求接口导致bug76624 改为唤起弹出请求
}

// 确定选择
const handleOk = () => {
  emits('change', areaTabs)
  selectAreaPopup.value?.hide()
}

const resetSelected = () => {
  cityList.value = []
  areaList.value = []
  areaTabs.value = [{ level: 1, name: '地区', id: '' }]
}

// 重置按钮
const handleReset = () => {
  resetSelected()
  handleOk()
}

watch(
  () => props.id,
  (val) => {
    nextTick(() => {
      val && getProvinceList(val)
    })
  },
  {
    immediate: true
  }
)

watch(
  () => props.data,
  (val) => {
    if (val && !val.province && !val.city) {
      resetSelected()
    }
  },
  { deep: true }
)

const getProvinceListLoading = ref(false)
const route = useRoute()
// 获取一级省市信息
const getProvinceList = async (val: number) => {
  getProvinceListLoading.value = true
  // const { data, error } = await newsApi.getAreaLabelList({ usedId: val })
  const { newsClassifyId } = route.query
  const params = {
    cateId: val,
    newsClassifyId
  }
  const { data, error } = await newsApi.getNewsCategoryAll(params)

  if (!error.value) {
    provinceList.value = [
      { usedId: '', labelValue: '全部', level: 1 },
      ...(data?.value?.provinceList || [])
    ]
    console.log('%c [ 一级二级下的省 ]-277', 'font-size:13px; background:pink; color:#bf2c9f;', params, provinceList.value)
    areaTabs.value = [{ level: 1, name: '地区', id: '' }]

    // 回显区域
    if (props.data?.province) {
      const filter = provinceList.value.filter(v => v.usedId === Number(props.data?.province))
      areaTabs.value = [
        { level: 1, name: filter[0]?.labelValue, id: filter[0]?.usedId.toString() },
        { level: 2, name: filter[0]?.labelValue, id: '' }
      ]
      // 请求市级数据
      await getAreaChildList(props.data.province, 2)
      // 回显市级选择
      if (props.data?.city) {
        const filter = cityList.value.filter(v => v.id === props.data?.city)
        areaTabs.value = [
          areaTabs.value[0],
          { level: 2, name: filter[0]?.areaName, id: filter[0]?.id },
          { level: 3, name: filter[0]?.areaName, id: '' }
        ]
        // 请求区县级数据
        await getAreaChildList(props.data.city, 3)
        // 回显区县级选择
        if (props.data?.area) {
          const filter = areaList.value.filter(v => v.id === props.data?.area)
          areaTabs.value = [areaTabs.value[0], areaTabs.value[1], { level: 3, name: filter[0]?.areaName, id: filter[0]?.id }]
        }
      }
    }
  }
  getProvinceListLoading.value = false
}
// 获取二级市级 level:地区级别,1省2市3县区4镇
const getAreaChildList = async (id: string, level: number) => {
  const { cateId } = route.params
  const { newsClassifyId } = route.query
  const body = {
    cateId,
    newsClassifyId
  }
  if (level === 2) {
    body.province = id
  } else if (level === 3) {
    // body.province = id // 是否需要传省
    body.city = id
  }
  const { data, error } = await newsApi.getNewsCategoryAll(body)

  if (!error.value) {
    // const cityOrAreaData = [
    //   { id: '', areaName: '全部', level },
    //   ...data.value.data.list
    // ]
    if (level === 2) {
      cityList.value = [
        { id: '', areaName: '全部', level },
        ...(data.value?.cityList || [])
      ]
      console.log('%c [ 省下的市 ]-277', 'font-size:13px; background:pink; color:#bf2c9f;', body, cityList.value)
    } else if (level === 3) {
      areaList.value = [
        { id: '', areaName: '全部', level },
        ...(data.value?.areaList || [])
      ]
      console.log('%c [ 市下的县 ]-277', 'font-size:13px; background:pink; color:#bf2c9f;', body, areaList.value)
    }
  }
}

// 选择省市区
const handleAreaItem = (item: any) => {
  // 选择分类中的全部
  if (item.labelValue === '全部' || item.areaName === '全部') {
    if (item.level === 1) {
      resetSelected()
    } else if (item.level === 2) {
      areaList.value = []
      areaTabs.value = [areaTabs.value[0], { level: 2, name: areaTabs.value[0]?.name, id: '' }]
    } else {
      areaTabs.value = [areaTabs.value[0], areaTabs.value[1], { level: 3, name: areaTabs.value[1]?.name, id: '' }]
    }
    return
  }

  if (item.level === 1) {
    cityList.value = []
    areaList.value = []
    getAreaChildList(item.usedId, 2)
    areaTabs.value = [{ level: item.level, name: item.labelValue, id: item.usedId }]
  } else if (item.level === 2) {
    areaList.value = []
    getAreaChildList(item.id, 3)
    areaTabs.value = [areaTabs.value[0], { level: item.level, name: item.areaName, id: item.id }]
  } else if (item.level === 3) {
    areaTabs.value = [areaTabs.value[0], areaTabs.value[1], { level: item.level, name: item.areaName, id: item.id }]
  }
}

</script>

<style lang="scss" scoped>
.area-select-popup {
  .area-select-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 200px;
    padding-right: 12px;
    &-text {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    :deep(.nuxt-icon svg) {
      fill: #111;
    }
  }
  .popup-box {
    height: 88vh;
    &-content {
      height: calc(100% - 224px);
      display: flex;
      .area-list {
        height: 100%;
        width: calc(100% / 3);
        overflow-y: auto;
        text-align: center;
        &-tabs {
          height: 72px;
          font-size: 26px;
          line-height: 72px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          &-active {
            background: #fff;
            color: var(--kkc-brand);
          }
        }
      }
    }
    &-bottom {
      position: fixed;
      bottom: 0;
      padding-left: 4px;
      padding-top: 12px;
      .bottom-btn {
        height: 88px;
        border-radius: 16px;
        font-size: 28px;
        margin-left: 20px;
        width: calc((100% - 68px) / 2);
      }
    }
  }
}
</style>
