export type NodeMode = 'chapter' | 'section' | 'class-hour'
// 1 直播 2 录播 3 讲义 4 试题
export enum CourseType {
  'Live' = 1,
  'Record',
  'Handout',
  'Question',
  'Audio'
}
// 0未开始 2直播中 3回放
export enum CourseLiveStatus {
  'NotLiveStreaming' = 0,
  'LivingStreaming' = 2,
  'LivedStreaming' = 3,
}
export interface IItem {
  children: IItem[],
  courseId: string,
  parentId: string,
  level: number,
  name: string,
  nodeType: NodeMode,
  path: string,
  desc: string
  type: CourseType,
  liveStatus: CourseLiveStatus,
  learnProgress: number,
  learnStatusName: string,
  polyvVideoId: string,
  numName?: string,
  isFree?: number,
  itemCount?: string,
  handoutFilename?: string,
  score?: string,
  [key: string]: any
}
