import { questionBankBack } from './useQuestionBankBack'
interface BackType {
  targetResultId?: string
  currentNum?: number
  nowStartTime?: string
  usedTime?: number
  moduleType?: number
  behaviorTime?: number; // 行为时长(秒数)
  extension6?: string; // 标签
  extension7?: string; // 做题内容
  doAllCount?: number;
  doObjectCount?: number;
  rightCount?: number;
  questionId?: string;
}
// 兼容app返回上一页
export const questionBankBackForApp = (props?: any) => {
  const { behaviorTime } = useUpdateQuestionBankBehaviorRecords()
  const paramsData: BackType = reactive({
  })

  const back = () => {
    console.log('questionBankBackForApp', props)
    const shouldShowDialog = props && !props.isShowPauseBtn && !props.isAnalysis
    console.log('shouldShowDialog', shouldShowDialog)
    if (shouldShowDialog) {
      Dialog({
        title: '确定离开吗？',
        message: '离开后时间不可暂停，作答时长结束后自动交卷，在结束前可以继续答题',
        confirmText: '确定',
        cancelText: '取消',
        onConfirm: handleConfirm
      })
    } else {
      handleConfirm()
    }
  }

  const handleConfirm = () => {
    console.log('handleConfirm', props)
    if (props) {
      const { examTitle, examTagsName, targetResultId, currentNum, time, nowStartTime, moduleType, current } = props
      const {
        doObjectCount,
        rightCount,
        doAllCount
      } = getChapterSubmitLeaveData()
      Object.assign(paramsData, {
        targetResultId,
        currentNum,
        nowStartTime,
        usedTime: time + behaviorTime.value,
        moduleType,
        extension6: examTagsName,
        extension7: examTitle,
        behaviorTime: behaviorTime.value,
        doObjectCount,
        rightCount,
        doAllCount,
        questionId: current.ptypeValueId !== 7 ? current.id : current.childQuestions[0].id
      })
    }

    pauseAudio()
    questionBankBack(paramsData)
  }

  // 获取章节已做未做等信息
  const getChapterSubmitLeaveData = () => {
    const { moduleType, quesList } = props
    let doObjectCount = 0
    let rightCount = 0
    let noDocount = 0
    let doAllCount = 0
    if (moduleType === 3) {
      const list = flat(quesList.map(item => item.questionList))
      // 章节练习暂存部分参数
      list.forEach((question: any) => {
      // 3 正确 4 错误 6 半对
        if ([3, 4, 6].includes(question.logStatus)) {
          doObjectCount++
          if (question.logStatus === 3) {
            rightCount++
          }
        }

        if (
          question.logStatus !== 2 &&
          question.logStatus !== 3 &&
          question.logStatus !== 4
        ) {
          noDocount++
        }
      })

      doAllCount = list.length - noDocount
    }

    return {
      doObjectCount,
      rightCount,
      doAllCount
    }
  }

  onMounted(() => {
    if (!window.questionBack) {
      window.questionBack = back
    }
  })

  return {
    paramsData,
    getChapterSubmitLeaveData,
  }
}
