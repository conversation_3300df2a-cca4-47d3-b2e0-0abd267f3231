<!-- eslint-disable vue/no-v-html -->

<template>
  <div v-if="info.id" class="bg-[#F7F8FC]">
    <template v-if="ptype">
      <div class="text-[14px] bg-[#FFFFFF] p-[24px]">
        <div class="inline-block">
          <div
            v-if="!hideAnswer && info.logAnswer"
            :class="logStatusBool ? 'bg-[#E3F9F6]' : 'bg-[#FFF4F4]'"
            class="flex px-[24px] items-center rounded-[12px] h-[64px]"
          >
            <KKCIcon
              :name="logStatusBool
                ? 'icon-huidazhengque'
                : 'icon-huidacuowu'
              "
              :color="logStatusBool ? '#15CCC0' : '#EB2330'"
              :size="30"
            />
            <div :class="logStatusBool ? 'text-[#15CCC0]' : 'text-[#EB2330]'" class="pl-[8px] text-[28px] font-[500]">
              {{ logStatusBool ? '回答正确' : '回答错误' }}
            </div>
          </div>
        </div>
        <div
          :class="['flex', !hideAnswer && info.logAnswer ? 'mt-[24px]' : '', 'bg-[#F7F8FC]', 'text-[28px]', 'rounded-[16px]', 'w-[100%]', 'px-[26px]', 'h-auto', 'leading-[76px]']"
        >
          <div class="w-[351px] text-left flex justify-center items-center">
            <div class="w-[141px]">
              参考答案
            </div>
            <div v-if="info.ptypeValueId !== ExamQuestionType.JUDGE" class="text-[#15CCC0] text-[32px] max-w-[170px]">
              {{ answer.length > 1 ? answer.join('、') : answer[0] }}
            </div>
            <div v-else class="text-[#15CCC0] text-[32px]">
              {{ answer[0] === '正确' ? 'A' : 'B' }}
            </div>
          </div>
          <div v-if="!hideAnswer" class="w-[351px] text-left flex justify-center items-center">
            <div class="w-[141px]">
              你的答案
            </div>
            <div :class="logStatusBool ? 'text-[#15CCC0]' : 'text-[#EB2330]'" class="text-[32px] max-w-[170px]">
              {{ logAnswer }}
            </div>
          </div>
        </div>
      </div>
    </template>

    <template v-else>
      <div class="bg-[#FFFFFF] p-[24px]">
        <div>
          <!-- <div v-if="logAnswer && logAnswer.length !== 0" class="text-[32px] text-[#333333] font-medium pb-[32px]">
            你的答案
            <QuestionBankAnalyzeAnswer v-for="(item, index) in logAnswer" :key="index" :answer="item" :q-num="index + 1"
              :type="info.ptypeValueId" :show-type="1" />
          </div> -->
          <div :class="['px-[24px]', 'pt-[32px]', 'bg-[#F7F8FC]', answerSrc && answerSrc.length ? 'rounded-t-[12px]' : 'rounded-[12px]']">
            <div class="text-[32px] text-[#333333] font-medium">
              参考答案
            </div>
            <div class="pb-[32px]">
              <QuestionBankAnalyzeAnswer
                v-for="(item, index) in answer"
                :key="index"
                :show-type="2"
                :answer-string="item"
                :q-num="index + 1"
                :type="info.ptypeValueId"
                :is-show-num="answer.length > 1"
              />
            </div>
          </div>
          <KKCAudioPlayer
            v-if="answerSrc && answerSrc.length"
            class="bg-[#e5e7eb] !rounded-t-[0px] !rounded-b-[12px]"
            :icon-name="'icon-yinpinbofang'"
            :playing-icon="'icon-yinpinzanting'"
            :icon-size="48"
            :url="answerSrc[0]"
          />
        </div>
      </div>
    </template>
    <div class="flex justify-around p-[24px] bg-[#FFFFFF]">
      <div v-if="info.ptypeValueId !== ExamQuestionType.SHORT_ANSWER" class=" text-center">
        <div class="text-[24px] font-bold">
          <KKCIcon name="icon-jieshishuoming1" color="#ccc" :size="28" overflow="visible" @click="watchPop(1)" />
          正确率
        </div>
        <div class="text-[#15CCC0] text-[36px] font-gilroy-bold">
          {{ info.ptypeValueId !== ExamQuestionType.SHORT_ANSWER ? info.rightRate.split('%')[0] : '-' }}<span v-if="info.ptypeValueId !== ExamQuestionType.SHORT_ANSWER" class="text-[24px] font-normal">%</span>
        </div>
      </div>
      <div v-if="info.wrongHigh && !hideWrongHigh" class=" text-center">
        <div class="text-[24px] font-bold">
          易错项
        </div>
        <div class="text-[#4C7AF2] text-[36px] font-bold">
          {{ info.wrongHigh }}
        </div>
      </div>
      <div class=" text-center">
        <div class="text-[24px] font-bold">
          <KKCIcon name="icon-jieshishuoming1" color="#ccc" overflow="visible" :size="28" @click="watchPop(2)" />
          作答次数
        </div>
        <div class="text-[#4C7AF2] text-[36px] font-gilroy-bold">
          {{ info.doCount }}<span class="text-[24px] font-normal">次</span>
        </div>
      </div>
    </div>
    <div v-if="isShowTeacherCorrect" :class="['bg-[#fff]', info.comment ? 'pb-[8px]' : 'pb-[32px]']">
      <QuestionBankAnalyzeTitle title="老师批改" :label="info.logScore" unit="分" />
      <div v-if="info.comment" class="m-[24px] p-[24px] bg-[#F7F8FC] rounded-[24px]">
        <KKCHtmlMathJax :content="info.comment" />
      </div>
    </div>
    <div v-if="isShowAICorrect" :class="['bg-[#fff]', info.comment ? 'pb-[8px]' : 'pb-[32px]']">
      <QuestionBankAnalyzeTitle title="AI批改" :desc="'内容由AI生成，分数仅供参考'" :label="info.logScore" unit="分" />
      <div v-if="info.comment" class="m-[24px] p-[24px] bg-[#F7F8FC] rounded-[24px]">
        <KKCHtmlMathJax :content="info.comment" />
      </div>
    </div>
    <div v-if="info.videoId" class="bg-[#fff] pb-[24px]">
      <QuestionBankAnalyzeTitle title="视频解析" :desc="info.validCc === 4 ? '内容由AI生成' : ''" />
      <QuestionBankVideo :question-id="info.parentId !== '0' ? info.parentId : info.id" />
    </div>
    <div v-if="info.analysis">
      <QuestionBankAnalyzeTitle :class="[info.videoId ? '!mt-0' : '']" title="文字解析" />
      <div v-img-preview="info.analysis" class="text-[32px] text-[#111111] p-[24px] bg-[#ffffff]">
        <KKCHtmlMathJax :content="info.analysis" />
      </div>
    </div>
    <div v-if="info.analysisSrc" class="pb-[12px] bg-[#ffffff]">
      <QuestionBankAnalyzeTitle title="音频解析" />
      <KKCAudioPlayer
        class="mt-[24px]"
        :icon-name="'icon-yinpinbofang'"
        :playing-icon="'icon-yinpinzanting'"
        :icon-size="48"
        :url="info.analysisSrc"
      />
    </div>
    <div v-if="info.knowledgePointList.length !== 0">
      <QuestionBankAnalyzeTitle title="知识点" />
      <div class="text-[32px] text-[#111111] pt-[24px] px-[24px] bg-[#ffffff] font-medium">
        <span
          v-for="(item, index) in info.knowledgePointList"
          :key="index"
          class="mb-[24px] rounded-[16px] inline-block text-[#333333] bg-[#F7F8FC] px-[24px] mr-[24px] leading-[72px] text-[28px]"
        >{{
          item }}</span>
      </div>
    </div>
    <div v-if="info.source">
      <QuestionBankAnalyzeTitle title="试题来源" />
      <div v-img-preview="info.source" class="text-[32px] text-[#111111] p-[24px] bg-[#ffffff] break-all">
        <KKCHtmlMathJax :content="info.source" />
      </div>
    </div>
    <div v-if="info.difficultValueTitle">
      <QuestionBankAnalyzeTitle title="试题难度" />
      <div class="text-[28px] text-[#EB2330] p-[24px] bg-[#ffffff] font-medium">
        <span class="rounded-[16px] inline-block bg-[#FFF1F0] px-[24px] leading-[72px] text-[28px]">{{
          info.difficultValueTitle }}</span>
      </div>
    </div>
    <!-- 查看正确率和作答次数弹框 -->
    <Teleport to="body">
      <KKPopup ref="kkPopupRef" :is-close="false" confirm-text="好的，知道啦" position="center" :hide-close-button="true">
        <template #header>
          <div class="p-[17px] !pb-[48px] text-center text-[32px] text-[#212121]">
            <KKCHtmlMathJax :content="submitTitle" />
          </div>
        </template>
      </KKPopup>
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import type { questType } from './type'
const props = defineProps({
  showAiCorrect: {
    type: Boolean,
    default: false
  },
  showTeacherCorrect: {
    type: Boolean,
    default: false
  },
  info: {
    type: Object as PropType<questType>,
    default: () => ({}),
  },
  hideAnswer: {
    type: Boolean,
    default: false
  },
  hideWrongHigh: {
    type: Boolean,
    default: false
  }
})

const isShowComment = computed(() => {
  const { ptypeValueId, awarding, isSelect, selectMark } = props.info
  /**
   * 题型定义:
   * 1 - 单选题
   * 2 - 多选题
   * 3 - 不定项选择题
   * 4 - 填空题
   * 5 - 判断题
   * 6 - 问答题
   * 7 - 材料题
   */

  // 常规题型(单选、多选、不定项、判断)不显示批改状态
  const normalQuestionTypes = [1, 2, 3, 5]
  if (normalQuestionTypes.includes(ptypeValueId)) {
    return false
  }

  // 需要AI批改的填空题判断
  const isAICorrectedFillBlank = ptypeValueId === 4 && awarding === 2

  // 已选中的情况
  if (selectMark === 1) {
    // 填空题且需要AI批改
    if (ptypeValueId === 4 && isAICorrectedFillBlank && isSelect === 1) {
      return true
    }

    // 问答题且已选中
    if (ptypeValueId === 6 && isSelect === 1) {
      return true
    }
  }

  // 未选中且为问答题或需AI批改的填空题
  if ((ptypeValueId === 6 || isAICorrectedFillBlank) && selectMark === 0) {
    return true
  }

  return false
})

/**
 * 是否显示Ai批改
 */
const isShowAICorrect = computed(() => {
  return props.showAiCorrect && isShowComment.value
})

/**
 * 是否显示老师批改
 */
const isShowTeacherCorrect = computed(() => {
  return props.showTeacherCorrect && isShowComment.value
})

// 查看正确率和作答次数
const submitTitle = ref<string>('')
const kkPopupRef = ref()
const watchPop = (status: number) => {
  if (status === 1) {
    submitTitle.value = '当前试题所有考生答对的次数/<br/>所有考生作答的总次数 *100%'
  } else {
    submitTitle.value = '当前试题所有考生作答的总次数'
  }
  kkPopupRef.value.showPopup()
}

// 初始状态不同类名处理
// 作答状态：1主观未做 2做了没评分 3对 4错 5客观未做
const handelStatus = (logStatus: number) => {
  let logBool = false
  let logValue = ''
  // 0 1 5
  if (logStatus === ExamAnswerType.DEFAULT || logStatus === ExamAnswerType.SUBJECTIVE_UNFINISHED || logStatus === ExamAnswerType.OBJECTIVE_UNFINISHED) {
    logValue = '未答'
  } else if (logStatus === ExamAnswerType.CORRECT) {
    logBool = true
  }
  return { logBool, logValue }
}
// 初始状态不同题型处理
const handleType = (ptypeValueId: number) => {
  const validTypeValues = [ExamQuestionType.RADIO, ExamQuestionType.MULTIPLE, ExamQuestionType.INDEFINITE, ExamQuestionType.JUDGE]
  return validTypeValues.includes(ptypeValueId)
}
const showAnswer = () => {
  if (!logAnswer.value || typeof logAnswer.value === 'string') {
    return logStatusValue.value || '未答'
  }
  if (logAnswer.value.length === 0) {
    return '未答'
  }
  return logAnswer.value.length === 1
    ? logAnswer.value[0]
    : sortLetters(logAnswer.value).join('、')
}
const sortLetters = (arr: string[]): string[] => {
  return arr.sort((a, b) => a.localeCompare(b))
}

const logAnswer = ref()
const answer = ref()
const answerSrc = ref()
const logStatusBool = ref<boolean>(true)
const logStatusValue = ref<string>('')
const ptype = ref<boolean>(true)
const updatePtype = (info: questType) => {
  // 根据info中的ptypeValueId更新ptype的值
  if (info.ptypeValueId) {
    ptype.value = handleType(info.ptypeValueId)
  }
}

const updateLogStatus = (info: questType) => {
  // 根据info中的logStatus，更新logStatusBool和logStatusValue的值
  const { logBool, logValue } = handelStatus(info.logStatus)
  logStatusBool.value = logBool
  logStatusValue.value = logValue
}

const updateLogAnswer = (info: questType) => {
  // 更新logAnswer的值
  logAnswer.value = ''
  if (info.logAnswer) {
    logAnswer.value = parseJSONOrAssign(info.logAnswer)
  }
  // 如果ptype的值为空，则显示answer，否则显示showAnswer()的结果
  if (ptype.value) {
    logAnswer.value = showAnswer()
  } else if (!logAnswer.value) {
    logAnswer.value = [{ html: '', imgs: [] }]
  }
}

const updateAnswer = (info: questType) => {
  // 根据info中的answer，更新answer的值
  if (info.answer) {
    answer.value = parseJSONOrAssign(info.answer)
  }
}

const updateAnswerSrc = (info: questType) => {
  // 根据info中的answerSrc，更新answerSrc的值
  if (info.answerSrc) {
    answerSrc.value = parseJSONOrAssign(info.answerSrc)
  }
}

const parseJSONOrAssign = (val) => {
  // 返回JSON.parse(val)或者val中的值
  return typeof val === 'string' ? JSON.parse(val) : val
}

watch(
  () => props.info,
  (val) => {
    if (val) {
      updatePtype(val)
      updateLogStatus(val)
      updateLogAnswer(val)
      updateAnswer(val)
      updateAnswerSrc(val)
    }
  },
  {
    immediate: true,
    deep: true,
  }
)
</script>

<style scoped lang="scss">
.info-inner {
  margin-top: 24px;

  >div {
    font-size: 14px;
    display: flex;
    margin-top: 16px;
    margin-right: 8px;
  }

  .info_analysis {
    width: calc(100% - 70px);
  }
}
</style>
