import type {
  QueryModuleListParams,
  ModuleList,
  QueryModuleCategoryListParams,
  DoQuestionListBodyType,
  RankProtBodyType,
} from '../types'
import filterQuestionBankBody from '../../../../base/utils/filterBody'
import type { SearchListParamsType } from './common.type'

enum Api {
  // 试卷搜索
  searchPaper = '/kukecorequestion/wap/moduleManageTags/searchTestPaperList',
  // 试题搜索
  searchQuestion = '/kukecorequestion/wap/moduleManageTags/searchQuestionList',
  // 获取金刚区数据
  getFixedModuleManage = '/kukecorequestion/wap/moduleManageTags/getFixedModuleInfoList',
  // 获取模块列表数据
  queryModuleList = '/kukecorequestion/wap/moduleManageTags/queryModule',
  // 模块分类
  queryModuleCategoryList = '/kukecorequestion/wap/moduleManageTags/moduleCategoryList',
  // 获取所有模块专业分类
  queryModuleAllStudyList = '/kukecorequestion/wap/moduleManageTags/moduleStudyLevelList',
  // 用户分类添加或修改
  updateMajorInfoData = '/kukecorequestion/wap/moduleManageTags/aoeUserTagsProt',
  // 通过模块id请求分类列表
  fetchCategoryListByModuleId = '/kukecorequestion/wap/moduleManageTagsV1/getStudyLevelList',
  // 模块分类标签查询
  getModuleCategoryTagsList = '/kukecorequestion/wap/moduleManageTags/moduleTagsList',
  // 获取刷题模式
  getReviewQuestionsModel = '/kukecorequestion/wap/daily/getReviewQuestionsModel',
  // 设置刷题模式
  doQuestionTypeProt = '/kukecorequestion/wap/daily/doQuestionTypeProt',
  // 每日练习列表
  getDailyPracticeList = '/kukecorequestion/wap/daily/getDailyPracticeList',
  // 试卷列表
  getTestpaperList = '/kukecorequestion/wap/testpaper/list',
  // 教材列表
  getTextbookList = '/kukecorequestion/wap/chapter/textbookList',
  // 章节列表
  getChapterList = '/kukecorequestion/wap/chapter/list',
  // 查询模块是否存在
  checkModuleManageProt = '/kukecorequestion/wap/daily/checkModuleManageProt',
  // 学习报告
  getReportInfo = '/kukecorequestion/wap/questionRank/studyReportProt',
  // 排行榜
  getRankList = '/kukecorequestion/wap/questionRank/getRankListProt',
}

/**
 * 试卷搜索
 *
 * @param {SearchListParamsType} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function searchPaper (body: SearchListParamsType): Promise<any> {
  return useHttp<any>(Api.searchPaper, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 试题搜索
 *
 * @param {SearchListParamsType} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function searchQuestion (body: SearchListParamsType): Promise<any> {
  return useHttp<any>(Api.searchQuestion, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 获取金刚区数据
 *
 * @param {any} body - 请求参数
 * @return {Promise<any>} - 返回数据
 */
export async function getFixedModuleManage (body: any): Promise<any> {
  return useHttp<any>(Api.getFixedModuleManage, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 获取模块列表数据
 *
 * @param {QueryModuleListParams} body - 请求参数
 * @returns {Promise<ModuleList>} - 返回数据
 */
export async function getModuleListData (body: QueryModuleListParams): Promise<any> {
  return useHttp<ModuleList>(Api.queryModuleList, {
    method: 'post',
    body,
    transform: res => res.data,
    isShowLoading: true
  })
}

/**
 * 模块分类查询
 *
 * @param {QueryModuleCategoryListParams} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function getModuleCategoryList (body: QueryModuleCategoryListParams): Promise<any> {
  return useHttp<any>(Api.queryModuleCategoryList, {
    method: 'post',
    body,
    transform: res => res.data,
    isShowLoading: true
  })
}

/**
 * 获取所有模块专业分类
 *
 * @param {any} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function getModuleAllStudyList (body: any): Promise<any> {
  return useHttp<any>(Api.queryModuleAllStudyList, {
    method: 'post',
    body,
    transform: res => res.data,
    isShowLoading: true
  })
}

/**
 * 用户分类添加或修改
 *
 * @param {any} body - 请求参数
 * @return {Promise<any>} - 返回数据
 */
export async function updateMajorInfoData (body: any): Promise<any> {
  return useHttp<any>(Api.updateMajorInfoData, {
    method: 'post',
    body,
    transform: res => res.data,
    isShowLoading: true
  })
}

/**
 * 通过模块id请求分类列表
 *
 * @param {{moduleManageId: string}} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function fetchCategoryListByModuleId (body: {moduleManageId: string}): Promise<any> {
  return useHttp<any>(Api.fetchCategoryListByModuleId, {
    method: 'post',
    body,
    transform: res => res.data,
    isShowLoading: true
  })
}

/**
 * 模块分类标签查询
 *
 * @param {QueryModuleCategoryListParams} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function getModuleCategoryTagsList (body: QueryModuleCategoryListParams): Promise<any> {
  return useHttp<any>(Api.getModuleCategoryTagsList, {
    method: 'post',
    body,
    transform: res => res.data,
    isShowLoading: true
  })
}

/**
 * 获取刷题模式
 *
 * @param {any} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function getReviewQuestionsModel (body: any): Promise<any> {
  return useHttp<any>(Api.getReviewQuestionsModel, {
    method: 'post',
    body,
    transform: res => res.data,
    isShowLoading: true
  })
}

/**
 * 设置刷题模式
 *
 * @param {any} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function doQuestionTypeProt (body: any): Promise<any> {
  return useHttp<any>(Api.doQuestionTypeProt, {
    method: 'post',
    body,
    transform: res => res.data,
    isShowLoading: true
  })
}

/**
 * 每日练习列表
 *
 * @param {DoQuestionListBodyType} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function getDailyPracticeList (body: DoQuestionListBodyType): Promise<any> {
  return useHttp<any>(Api.getDailyPracticeList, {
    method: 'post',
    body: filterQuestionBankBody(body),
    transform: res => res.data,
    isShowLoading: false,
    watch: false
  })
}

/**
 * 试卷列表
 *
 * @param {DoQuestionListBodyType} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function getTestpaperList (body: DoQuestionListBodyType): Promise<any> {
  return useHttp<any>(Api.getTestpaperList, {
    method: 'post',
    body: filterQuestionBankBody(body),
    transform: res => res.data,
    isShowLoading: false,
    watch: false
  })
}

/**
 * 教材列表
 *
 * @param {any} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function getTextbookList (body: any): Promise<any> {
  return useHttp<any>(Api.getTextbookList, {
    method: 'post',
    body: filterQuestionBankBody(body),
    transform: res => res.data,
    isShowLoading: true,
    watch: false
  })
}

/**
 * 章节列表
 *
 * @param {any} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function getChapterList (body: any): Promise<any> {
  return useHttp<any>(Api.getChapterList, {
    method: 'post',
    body: filterQuestionBankBody(body),
    transform: res => res.data,
    isShowLoading: false,
    watch: false
  })
}

/**
 * 查询模块是否存在
 *
 * @param {any} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function checkModuleManageProt (body: any): Promise<any> {
  return useHttp<any>(Api.checkModuleManageProt, {
    method: 'post',
    body,
    transform: res => res.data,
    isShowLoading: true
  })
}

/**
 * 获取题库个人学习报告
 *
 * @param {any} body - 请求参数
 * @return {Promise<any>} - 返回数据
 */
export async function getReportPort (body: any): Promise<any> {
  return useHttp<any>(Api.getReportInfo, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 获取排行榜列表
 *
 * @param {RankProtBodyType} body - 请求参数
 * @return {Promise<any>} - 返回数据
 */
export async function getRankingList (body: RankProtBodyType): Promise<any> {
  return useHttp<any>(Api.getRankList, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}
