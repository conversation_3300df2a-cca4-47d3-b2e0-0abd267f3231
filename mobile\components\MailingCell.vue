<!-- 资料邮寄和历史邮寄订单公用页面 -->
<template>
  <div>
    <div class="mailing-cell bg-[#fff] mb-[24px]">
      <div class="flex justify-between items-center text-[24px] text-[#111]">
        <div v-if="data.orderSn">
          订单编号 {{ data.orderSn }}
        </div>
        <div v-else>
          活动邮寄包裹
        </div>
        <div v-if="data.resourceTypeNames" class="tag">
          {{ data.resourceTypeNames }}
        </div>
      </div>
      <div class="py-[24px]" :class="{ 'border_bottom': pageType && pageType === 'history'}">
        <div class="mailing-cell-info-title font-medium text-[#111] line-clamp-2">
          <span>{{ data.goodsTitle || data.goodsAliasTitle }}</span>
        </div>
      </div>

      <!-- 历史邮寄订单页面展示 -->
      <div v-if="data.deliveryInfos.length && pageType !== 'history'">
        <div
          v-for="(item,ind) in data.deliveryInfos"
          :key="item.id"
          class="package_item"
          :class="[data.deliveryInfos.length === 1 ? '!rounded-[12px]' : '', data.deliveryInfos.length > 1 && ind ? '!border-t-0' : '']"
        >
          <div class="flex justify-between items-center">
            <div class="flex flex-col mr-[16px]">
              <div class="flex items-center">
                <KKCIcon name="icon-a-mingchengkuaidi" :size="32" class="mr-[16px]" />
                <span class="text-[#111]">
                  {{ isWaitSendPackage(item.deliveryStatus) ? '待发货包裹' : '待收货包裹' }}
                </span>
              </div>
              <!-- 邮寄资料名称 -->
              <div v-if="item.goodsAliasTitle" class="text-[#999] mt-[12px] line-clamp-2">
                {{ getMailingName(item.goodsAliasTitle) }}
              </div>
            </div>

            <KKCButton
              class="!w-[136px] !h-[48px] !text-[24px] !leading-[48px]"
              :class="[isWaitSendPackage(item.deliveryStatus) ? '!text-[#fff] !bg-[#EB2330]' : '!text-[#111] !bg-[#f2f2f2]']"
              size="small"
              @click.stop="handleClick(item)"
            >
              {{ isWaitSendPackage(item.deliveryStatus) ? '修改地址' : '查看物流' }}
            </KKCButton>
          </div>
        </div>
      </div>

      <div
        v-if="isMailing && data.completedInfos === '1'"
        class="flex items-center justify-end h-[48px] mt-[24px]"
        @click="skipHistoryOrderPackage"
      >
        <div class="text-[24px] text-[#999] mr-[2px]">
          查看此订单历史包裹
        </div>
        <nuxt-icon class="text-[#999] text-[24px] w-[24px] h-[24px]" name="right-arrow" filled />
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
const { query: { pageType = '' } } = useRoute()

const props = defineProps({
  data: {
    type: Object,
    default: () => ({ }),
  },
  // 是否是邮寄资料
  isMailing: {
    type: Boolean,
    default: false,
  },
})

const { data, isMailing } = toRefs(props)

const emits = defineEmits<{
    (e: 'showExpress', data: any): void;
    (e: 'updateAddress', data:any): void;
  }>()

// 是否是待发货包过 1 待发货，2 已发货，3 已完成
const isWaitSendPackage = computed(() => (deliveryStatus: number) => {
  return deliveryStatus === 1
})

// 查看此订单历史包裹
const skipHistoryOrderPackage = () => {
  const { orderSn, completedDeliveryIds = [] } = data.value
  navigateTo({
    path: '/user/order/logistics',
    query: {
      orderSn,
      pageType: 'history',
      completedDeliveryIds: completedDeliveryIds || [],
      title: '物流信息'
    }
  })
}

// 处理点击按钮事件
// deliveryStatus 1：待发货【修改地址】 2：已发货【查看物流】
const handleClick = (item: any) => {
  const { deliveryStatus } = item
  if (deliveryStatus === 1) {
    emits('updateAddress', item)
  } else {
    emits('showExpress', item)
  }
}

/**
 * @name 邮寄资料名称处理最多展示38字符
 * @param goodsAliasTitle - 商品别名标题字符串
 * @returns 截断后的邮寄名称或原始字符串
 */
const getMailingName = (goodsAliasTitle: string): string => {
  const MAX_LENGTH = 38
  if (!goodsAliasTitle) { return '' }
  return goodsAliasTitle.length > MAX_LENGTH
    ? `${goodsAliasTitle.slice(0, MAX_LENGTH)}...`
    : goodsAliasTitle
}

</script>
  <style lang="scss" scoped>
  :deep {
    .kkc-btn.small {
      padding: 0 20px !important;
    }
  }
  .mailing-cell {
    padding: 24px;
    border-radius: 15px;
    position: relative;

    .border_bottom {
      @apply border-b-[1px] border-[#eee] mb-[24px]
    }
    .tips-bg {
      background: rgba(235, 35, 48, 0.05);
    }

    &-info {
      &-title {
        font-size: 32px;
        line-height: 40px;
      }
    }

    &-btn {
      border-top: 1px solid #eee;
      padding-top: 24px;
    }

    .package_item {
      @apply border-[1px] border-[#eee] p-[16px] text-[24px];
      &:first-of-type {
        border-radius: 16px 16px 0px 0px;
      }
      &:last-of-type {
        border-radius: 0 0  16px 16px;
      }
    }

    .tag {
      @apply font-medium text-brand bg-brand/10 text-[22px] px-[12px] py-[4px] rounded-[8px];
    }
  }
</style>
