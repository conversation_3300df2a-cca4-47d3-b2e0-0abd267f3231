import { useFrom } from '~/pages/question-bank/hooks/useFrom'

interface BackType {
  targetResultId?: string
  currentNum?: number
  nowStartTime?: string
  usedTime?: number
  moduleType?: number
  behaviorTime?: number; // 行为时长(秒数)
  extension6?: string; // 标签
  extension7?: string; // 做题内容
  doAllCount?: number;
  doObjectCount?: number;
  rightCount?: number;
  questionId?: string;
  moduleTitle?: string;
}
// TODO 内存泄漏可疑点: 全局变量
const { questionBankApi } = useApi()

export const questionBankBack = async (paramsData?: BackType) => {
  try {
    const route = useRoute()
    if (paramsData) {
      // 更新做题行为记录
      updateQuestionBehavior(paramsData)
    }
    await submitLeaveProt(paramsData)
    if ((window.history.length > 1 && window.history.state.back) || route.query.fromPage === 'points') {
      window.history.back()
    } else if (os?.isApp) {
      if (os?.isAndroid) {
        window.android.goBack()
      } else if (os?.isPhone || os?.isTablet) {
        window.webkit.messageHandlers.goBack.postMessage(null)
      } else if (os?.isHarmonyOS) {
        window.harmony?.goBack()
      }
    } else {
      navigateTo('/')
    }
  } catch (error) {
    console.log(error)
  }
}

function submitLeaveProt (submitData?: BackType) {
  const route = useRoute()
  const { isStudyPlan } = useFrom()
  const {
    moduleType = 0,
    targetResultId,
    currentNum,
    usedTime,
    doAllCount,
    doObjectCount,
    rightCount,
    questionId
  } = submitData || {}
  // 需要忽略的页面name
  const ignoreList = ['question-bank-analysis']
  // 提交做题信息离开
  if ((moduleType || moduleType === 0 || isStudyPlan.value) && targetResultId && currentNum && !ignoreList.includes(route.name as string)) {
    switch (moduleType) {
      // case 0 || 2 || 4:
      //   console.log('paperSubmitLeave targetResultId', targetResultId)
      //   return questionBankApi.paperSubmitLeave({
      //     targetResultId,
      //     doCount: currentNum,
      //     startTime: nowStartTime,
      //   })
      case 1:
        return questionBankApi.dailySubmitLeave({
          targetResultId,
          doCount: currentNum,
          usedTime,
        })
      case 3:
        return questionBankApi.chapterSubmitLeave({
          targetResultId,
          questionId: questionId || '',
          usedTime: usedTime || 0,
          doAllCount: doAllCount || 0,
          doObjectCount: doObjectCount || 0,
          rightCount: rightCount || 0
        })
      case 5:
        return questionBankApi.submitAiLeaveExercise({
          targetResultId,
          doCount: currentNum,
          usedTime: usedTime || 0,
        })
      default:
        if (isStudyPlan.value) {
          return questionBankApi.studentLeaveProt({
            targetResultId,
            doCount: currentNum,
            usedTime: usedTime || 0,
          })
        } else {
          return questionBankApi.paperSubmitLeave({
            targetResultId,
            doCount: currentNum,
            usedTime: usedTime || 0,
          })
        }
    }
  } else {
    return Promise.resolve()
  }
}

function updateQuestionBehavior (submitData: BackType) {
  const route = useRoute()
  const { updateBehaviorRecordsFn } = useUpdateQuestionBankBehaviorRecords()
  const { extension6, extension7, behaviorTime, moduleTitle, moduleType } = submitData
  // 需要忽略的页面name
  const ignoreList = ['question-bank-analysis']
  if (behaviorTime && (extension6 || extension7) && !ignoreList.includes(route.name as string)) {
    const options = {
      extension6,
      extension7,
      behaviorTime
    }
    if (moduleType === 5) {
      Object.assign(options, {
        goodsTitle: moduleTitle
      })
    }
    // 提交做题行为记录
    updateBehaviorRecordsFn(options)
  }
}
