import type { VideoDetailResult } from '../../components/QuestionVideoPlayer/types'
import type { ClassifiedInformationParams, ClassifiedInformationResult, UploadProgressParams, UploadProgressParamsV2, LivePortResult } from './types'
export enum ApisLc {
  // getVideoSdkSign = '/kukeopen/polyvLive/getVideoSdkSign',
  // getWebLiveSdkSign='/kukeopen/polyvLive/getWebLiveSdkSign',
  // getWebLiveChatToken ='/kukeopen/polyvLive/getWebLiveChatToken',
  getClassifiedInformation ='/kukestudentservice/userBroadcast/getPolyvNodeInfoProt', // 获取录播加密信息
  getLiveUrl = '/kukestudentservice/userBroadcast/getPolyvLiveUrlProt', // 获取直播url
  uploadProgress = '/kukestudentservice/userBroadcast/uploadProgressProt',
  getLivePort = '/kukegoods/goodsCourse/getLivePlatform', // 获取直播平台类型
  getLivePortByWap = '/kukecoregoods/wap/field/getVideoAccount', // 获取直播平台类型
  getLivePortByPc = '/kukecoregoods/pc/field/getVideoAccount', // 获取直播平台类型
  getCCNodeInfoProt = '/kukestudentservice/userBroadcast/getCCNodeInfoProt', // cc录播加密信息
  getCCLiveUrlProt = '/kukestudentservice/userBroadcast/getCCLiveUrlProt', // cc直播url
  getPolyvInfo ='/kukestudentservice/wap/userBroadcast/getPolyvNodeInfo', // 不用鉴权获取录播加密信息
  getCCNodeInfo = '/kukestudentservice/userBroadcast/getCCNodeInfo', // 不用鉴权cc录播加密信息
  getQuestionVideoInfo = '/kukecorequestion/pc/question/getQuestionVideoInfoProt', // 获取试题视频信息
}

// // 获取保利威录播、直播回放时WEB SDK需要的加密参数
// export async function getVideoSdkSign (body = {}) {
//   return useHttp<VideoSdkSign>(ApisLc.getVideoSdkSign, {
//     method: 'post',
//     body,
//   })
// }
// // 获取保利威直播WEB SDK需要的直播加密参数
// export async function getWebLiveSdkSign (body = {}) {
//   return useHttp<channelId>(ApisLc.getWebLiveSdkSign, {
//     method: 'post',
//     body,
//   })
// }
// // 获取保利威直播需要的聊天室Token（授权和连麦的token）
// export async function getWebLiveChatToken (body = {}) {
//   return useHttp<LiveChatToken>(ApisLc.getWebLiveChatToken, {
//     method: 'post',
//     body,
//   })
// }

// 获取录播加密信息
export async function getClassifiedInformation (body:ClassifiedInformationParams) {
  return useHttp<ClassifiedInformationResult>(ApisLc.getClassifiedInformation, {
    method: 'post',
    body,
  })
}
/**
 * 获取保利威录播课时相关信息接口(不验证登录)
 * @param body
 * @returns
 */
export async function getClassifiedInformationOfNoLogin (body:ClassifiedInformationParams) {
  return useHttp<ClassifiedInformationResult>('/kukestudentservice/xxx/userBroadcast/getPolyvNodeInfo', {
    method: 'post',
    body,
  })
}
// 获取直播间地址信息
export async function getLiveUrl (body:ClassifiedInformationParams) {
  return useHttp<ClassifiedInformationResult>(ApisLc.getLiveUrl, {
    method: 'post',
    body,
    isShowLoading: true
  })
}
/**
 * 获取保利威直播间地址(不验证登录)
 *
*/
export async function getLiveUrlOfNoLogin (body:ClassifiedInformationParams) {
  return useHttp<ClassifiedInformationResult>('/kukestudentservice/xxx/userBroadcast/getPolyvLiveUrl', {
    method: 'post',
    body,
    isShowLoading: true
  })
}
// 更新学进度
export async function uploadProgress (body:UploadProgressParams) {
  return useHttp<void>(ApisLc.uploadProgress, {
    method: 'post',
    body
  })
}
/**
 * 上传视频学习进度(非商品课程)
 * 经沟通，可以使用/xxx/作为路径
 * http://yapi.kukewang.com:9005/project/444/interface/api/69683
*/
export async function uploadProgressByVideoId (body:UploadProgressParamsV2) {
  return useHttp<void>('/kukestudentservice/xxx/kssStudyProgress/uploadStudyProgressProt', {
    method: 'post',
    body
  })
}
// 获取直播平台类型
export async function getLivePort (body:any) {
  return useHttp<LivePortResult>(ApisLc.getLivePort, {
    method: 'post',
    body,
    isShowLoading: true
  })
}
// 获取直播平台类型
export async function getLivePortByWap (body:any) {
  return useHttp<any>(ApisLc.getLivePortByWap, {
    method: 'post',
    body,
    isShowLoading: true
  })
}
// 获取直播平台类型
export async function getLivePortByPc (body:any) {
  return useHttp<any>(ApisLc.getLivePortByPc, {
    method: 'post',
    body,
    isShowLoading: true
  })
}
// 获取cc录播加密信息
export async function getCCNodeInfoProt (body:ClassifiedInformationParams) {
  return useHttp<ClassifiedInformationResult>(ApisLc.getCCNodeInfoProt, {
    method: 'post',
    body,
  })
}
/**
 * 获取CC录播课时相关信息接口(不验证登录)
 * @param body
 * @returns
 */
export async function getCCNodeInfoProtOfNoLogin (body:ClassifiedInformationParams) {
  return useHttp<ClassifiedInformationResult>('/kukestudentservice/xxx/userBroadcast/getCCNodeInfo', {
    method: 'post',
    body,
  })
}
// 获取cc直播间地址信息
export async function getCCLiveUrlProt (body:ClassifiedInformationParams) {
  return useHttp<ClassifiedInformationResult>(ApisLc.getCCLiveUrlProt, {
    method: 'post',
    body,
    isShowLoading: true
  })
}
/**
 * 获取CC直播间地址(不验证登录)
 * @param body
 * @returns
 */
export async function getCCLiveUrlProtOfNoLogin (body:ClassifiedInformationParams) {
  return useHttp<ClassifiedInformationResult>('/kukestudentservice/xxx/userBroadcast/getCCLiveUrl', {
    method: 'post',
    body,
    isShowLoading: true
  })
}
// 不鉴权获取cc录播加密信息
export async function getCCNodeInfo (body:ClassifiedInformationParams) {
  return useHttp<ClassifiedInformationResult>(ApisLc.getCCNodeInfo, {
    method: 'post',
    body,
  })
}
// 不鉴权获取保利威录播加密信息
export async function getPolyvInfo (body:ClassifiedInformationParams) {
  return useHttp<ClassifiedInformationResult>(ApisLc.getPolyvInfo, {
    method: 'post',
    body,
  })
}
type ClassifiedInformationParamsV2 = {
  videoId: string
  orgId: string
}
/**
 * videoId获取保利威、cc录播加密信息
 * @param body
 * @returns
 */
export async function getPolyvOrCCInfoByVideoId (body:ClassifiedInformationParamsV2) {
  return useHttp<ClassifiedInformationResult>('/kukestudentservice/wap/userBroadcast/videoPlayInfoProt', {
    method: 'post',
    body,
  })
}

// 获取试题视频信息
export async function getQuestionVideoInfo (body: {questionId: string}) {
  return useHttp<VideoDetailResult>(ApisLc.getQuestionVideoInfo, {
    method: 'post',
    body,
    isShowLoading: true
  })
}
