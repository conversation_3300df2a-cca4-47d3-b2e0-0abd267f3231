import { useQuestionBankStore } from '~/stores/question.bank.store'
import type { ClassBasicType } from '~/pages/question-bank/types/basic'

interface OptionsType {
  classList: ClassBasicType[]
}

export const useQuestionBankInitQuery = (Options: OptionsType) => {
  const { classList } = Options

  const questionBankStore = useQuestionBankStore()
  const { transformObject } = useHistoryState()
  const route = useRoute()

  /**
   * 页面初始化分类标签等数据
   */
  const initQueryData = computed(() => {
    if (route.query.studyLevel1) {
      const customTagSelect = !!questionBankStore.getLastCustomTagHistory(Number(route.query.studyLevel1))
      const customListPageTagSelect = !!questionBankStore.getLastListPageCustomTagHistory(Number(route.query.studyLevel1))
      questionBankStore.setQuestionQuery({
        ...transformObject(route.query),
        customTagSelect,
        customListPageTagSelect
      })
      return transformObject(route.query)
    } else {
      const historyList = questionBankStore.getHistory()
      const classIds = classList.map((item: any) => item.studyLevel1)
      // 获取当前上次选中分类的第一条记录
      const preSelectClass = historyList[0]
      // 获取所有历史记录并筛选包含当前模块记录
      const hasModuleHistory = historyList.filter((item: any) => item.moduleManageId === route.query.moduleManageId)
      if (!hasModuleHistory || hasModuleHistory.length === 0) {
        const queryData = questionBankStore.getQueryData()
        // 没有记录时，根据当前可选分类第一条记录作为默认
        return classIds.includes(queryData.studyLevel1)
          ? queryData
          : {
              subjectType: -1,
              region: -1,
              academicSection: -1,
              directionType: -1,
              examFormat: -1,
              studyLevel1: classIds[0],
              moduleManageId: route.query.moduleManageId
            }
      }

      // 根据customClassSelect筛选出标识上次选中分类记录
      const customClassSelect = hasModuleHistory.find((item: any) => item.customClassSelect)
      if (customClassSelect) {
        // 标识本次存储的记录
        // tabChangeFlag.value = true TODO
        return customClassSelect
      }

      // 筛选出拥有当前可选分类的第一条记录用以默认
      if (classIds.includes(preSelectClass.studyLevel1)) {
        const hasPreSelect = hasModuleHistory.find((item: any) => item.studyLevel1 === preSelectClass.studyLevel1)
        return hasPreSelect || {
          ...hasModuleHistory[0],
          studyLevel1: preSelectClass.studyLevel1
        }
      }

      const history = hasModuleHistory.find((item: any) => classIds.includes(item.studyLevel1))
      return history || hasModuleHistory[0]
    }
  })

  return {
    initQueryData
  }
}
