import { hash } from 'ohash'
import type {
  listMaterialParams,
  newsListMaterialParams
} from './types'

export enum MaterialApi {
  downloadInc = '/kukecorenews/wap/resource/downloadInc',
  listMaterial = '/kukecorenews/wap/resource/moreList',
  cateRelateLabel = '/kukecorenews/wap/resource/cateRelateLabel',
  homeList = '/kukecorenews/wap/resource/homeList',
  newsDownloadList = '/kukecorenews/wap/resource/downloadList'
}
// 记录下载数
export async function downloadInc (body: any) {
  return useHttp<any>(MaterialApi.downloadInc, {
    method: 'post',
    body,
    transform: res => res.data,
    watch: false
  })
}

// 资料列表
export async function getMaterialList (body: listMaterialParams) {
  const _api = MaterialApi.listMaterial
  const key = hash([_api, ...Object.values(body)])
  return useHttp<any>(_api, {
    method: 'post',
    body,
    key,
    transform: res => res.data,
    watch: false
    // isShowLoading: false,
  })
}
// 获取资料专业分类和标签
export async function getCateRelateLabel (body: listMaterialParams) {
  const _api = MaterialApi.cateRelateLabel
  const key = hash([_api, ...Object.values(body)])
  return useHttp<any>(_api, {
    method: 'post',
    body,
    key,
    transform: res => res.data,
    watch: false
    // isShowLoading: false,
  })
}
// 首页-专升本/公考资料列表
export async function getHomeMaterialList (body: any) {
  return useHttp<any>(MaterialApi.homeList, {
    method: 'post',
    body,
    key: MaterialApi.homeList + body?.type,
    transform: res => res.data,
    watch: false
    // isShowLoading: false,
  })
}
// 资讯详情-资料下载
export async function getNewsDownloadList (body: newsListMaterialParams) {
  return useHttp<any>(MaterialApi.newsDownloadList, {
    method: 'post',
    body,
    transform: res => res.data,
    watch: false
    // isShowLoading: false,
  })
}
/**
 * 点击广告 下载/预览资料
 *
*/
export async function downloadOrPreview (body = {}/* , headers = {} */) {
  return useHttp<any>('/kukecorenews/wap/resource/downloadOrPreview', {
    method: 'post',
    body,
    // headers,
    transform: res => res.data,
    // isShowLoading: true,
  })
}
