import type {
  QuestionAnswerCardParamsType,
  QuestionDetailParamsType,
  QuestionDetailListParamsType,
  FixedCatalogueParams,
  TextBookParams,
} from '../types'

enum Api {
  // 获取答题卡
  getQuestionAnswerCard = '/kukecorequestion/wap/chapter/answerCardProt',
  // 获取试题详情
  getQuestionDetail = '/kukecorequestion/wap/chapter/getQuestionDetailProt',
  // 批量获取试题详情
  getQuestionDetailList = '/kukecorequestion/wap/chapter/getQuestionBatchProt',
  // 【新】刷题版本详情
  getQuestionBrushInfo = '/kukecorequestion/wap/goods/getBrushInfo',
  // 教材单品详情
  getTextBookDetail = '/kukecorequestion/wap/goods/getTextbookInfo',
}

/**
 * 获取答题卡
 *
 * @param {QuestionAnswerCardParamsType} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function getQuestionAnswerCard (body: QuestionAnswerCardParamsType) {
  return useHttp<any>(Api.getQuestionAnswerCard, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 获取试题详情
 *
 * @param {QuestionDetailParamsType} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function getQuestionDetail (body: QuestionDetailParamsType) {
  return useHttp<any>(Api.getQuestionDetail, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 批量获取试题详情
 *
 * @param {QuestionDetailListParamsType} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function getQuestionDetailList (body: QuestionDetailListParamsType) {
  return useHttp<any>(Api.getQuestionDetailList, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 获取刷题版本下目录
 *
 * @param {FixedCatalogueParams} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function getQuestionBrushInfo (body: FixedCatalogueParams) {
  return useHttp<any>(Api.getQuestionBrushInfo, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 获取单品教材下章节
 *
 * @param {TextBookParams} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function getTextBookDetail (body: TextBookParams) {
  return useHttp<any>(Api.getTextBookDetail, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}
