import type {
  FetchAddCartParams, FetchDeleteCartParams, FetchUpdateCartQuantityParams,
  FetchCollectCartParams, FetchUpdateCartSpecParams,
  ShoppingCartDTO, FetchCalcCartPriceInfoParams,
  CalcCartPriceInfoDTO, FetchCartListParams,
  FetchCheckCartGoodsListSettlementAbleParams,
  IsShowCart
} from '@/apis/cart/types'

enum CartApi {
  fetchCartCount = '/kukecoregoods/wap/userCart/cartCountProt',
  fetchAddCart = '/kukecoregoods/wap/userCart/addProt',
  fetchDeleteCart = '/kukecoregoods/wap/userCart/deleteProt',
  fetchCollectCart = '/kukecoregoods/wap/userCart/moveToCollectProt',
  fetchUpdateCartQuantity = '/kukecoregoods/wap/userCart/updateQuantityProt',
  fetchUpdateCartSpec = '/kukecoregoods/wap/userCart/switchSkuProt',
  fetchCartList = '/kukecoregoods/wap/userCart/goodsListProt',
  fetchCalcCartPriceInfo = '/kukecoregoods/wap/userCart/calcAmountProt',
  checkCartGoodsListSettlementAble = '/kukecoregoods/wap/userCart/checkCartGoodsProt',
  getIsShowCart = '/kukemarketing/wap/pageMaster/getIsShowCartNews',
  getAddOnGoods = '/kukecoupon/wap/marketingGoods/getAddOnGoods'
}

/**
 * 获取用户购物车内商品数量
 * @returns
 */
export async function fetchCartCount () {
  return useHttp<{count:number}>(CartApi.fetchCartCount, {
    method: 'post',
    transform: (res: any) => res.data,
  })
}

/**
 * 加入购物车
 * @returns
 */
export async function fetchAddCart (body : FetchAddCartParams) {
  return useHttp<IResponse<any>>(CartApi.fetchAddCart, {
    method: 'post',
    body,
    transform: (res: any) => res.data,
  })
}

/**
 * 删除购物车
 * @returns
 */
export async function fetchDeleteCart (body : FetchDeleteCartParams) {
  return useHttp<IResponse<any>>(CartApi.fetchDeleteCart, {
    method: 'post',
    body,
    transform: (res: any) => res.data,
  })
}

/**
 *收藏购物车
 * @param body
 * @returns
 */
export async function fetchCollectCart (body : FetchCollectCartParams) {
  return useHttp<IResponse<any>>(CartApi.fetchCollectCart, {
    method: 'post',
    body,
    transform: (res: any) => res.data,
  })
}

/**
 *修改购物车商品数量
 * @param body
 * @returns
 */
export async function fetchUpdateCartQuantity (body : FetchUpdateCartQuantityParams) {
  return useHttp<IResponse<any>>(CartApi.fetchUpdateCartQuantity, {
    method: 'post',
    body,
    transform: (res: any) => res.data,
  })
}

/**
 *修改购物车商品规格
 * @param body
 * @returns
 */
export async function fetchUpdateCartSpec (body : FetchUpdateCartSpecParams) {
  return useHttp<IResponse<any>>(CartApi.fetchUpdateCartSpec, {
    method: 'post',
    body,
    transform: (res: any) => res.data,
  })
}

/**
 * 获取购物车列表
 * @returns
 */
export async function fetchCartList (body : FetchCartListParams) {
  return useHttp<{list:ShoppingCartDTO[]}>(CartApi.fetchCartList, {
    method: 'post',
    body,
    transform: (res: any) => res.data,
  })
}

/**
 * 计算购物车价格信息
 * @param body
 * @returns
 */
export async function fetchCalcCartPriceInfo (body : FetchCalcCartPriceInfoParams) {
  return useHttp<CalcCartPriceInfoDTO>(CartApi.fetchCalcCartPriceInfo, {
    method: 'post',
    body,
    transform: (res: any) => res.data,
  })
}

/**
 * 校验购物车商品是否能够结算结算
 * @param body
 * @returns
 */
export async function checkCartGoodsListSettlementAble (body : FetchCheckCartGoodsListSettlementAbleParams) {
  return useHttp<IResponse<any>>(CartApi.checkCartGoodsListSettlementAble, {
    method: 'post',
    body,
    transform: (res: any) => res.data,
  })
}
/**
 * 是否开启购物车
 */
export async function getIsShowCart (body : {}) {
  return useHttp<IsShowCart>(CartApi.getIsShowCart, {
    method: 'post',
    body,
    transform: (res: any) => res.data,
  })
}

/**
 * 查询加价购凑单商品列表
 */
export async function getAddOnGoods (body:{buyAtMarkupId:string, page:number, pageSize:number}) {
  return useHttp<any>(CartApi.getAddOnGoods, {
    method: 'post',
    body,
    transform: (res: any) => res.data,
  })
}
export type KefuInfo = {
  // 客服类型 0合力 1启用企业微信
  service:0|1,
  popUpWindowsSet:{
    /**
     * 0 手动开启客服弹窗 1 自动弹出客服弹窗
     * */
    status:0|1
    /**
     * 自动弹出时间
     * */
    extendInformation:number
  },
  customerServiceList: {
    //  当为2时，此id为客服id，非分配id
    id:string
    //   客服名称
    name:string
    // 地址
    url:string
  }[]
}
/**
 * getKefuInfo
 */
export async function getKefuInfo (body = {}) {
  return useHttp<KefuInfo>(CartApi.getIsShowCart, {
    key: 'KefuInfo',
    method: 'post',
    body,
    transform: (res: any) => res.data,
  })
}
