export interface ClassifiedInformationParams {
  nodeId:string,
  goodsMasterId:string,
}
export interface ClassifiedInformationResult {
  kkAes:string,
  kkSdkString:string,
}

export interface UploadProgressParams {
  nodeId:string,
  learningToken?:string | undefined,
  realLearningTime: string | number,
  timePoint: string | number | undefined,
  playOver? :string |number| undefined,
  goodsMasterId:string,
  tryListenId?: string
}
/**
 * Video progress tracking data structure
 */
export interface UploadProgressParamsV2 {
  /**
   * 商品中心视频主键id
   * @mock @string("number", 18)
   */
  goodsFileId?: string;

  /**
   * 观看进度节点（类型为试题时传0），单位：秒
   * @required
   * @mock @integer(0)
   */
  timePoint: number;

  /**
   * 组织id
   */
  orgId?: string;
}
export interface LivePortResult {
  livePlatform: number
}
