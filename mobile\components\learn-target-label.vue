<template>
  <div class="home-navbar-learn-target__outer" @click="goLearnTarget($event)">
    <div :class="[bem()]">
      <div :class="[bem('icon')]">
        <KKCIcon name="icon-fangxiang1" :size="24" />
      </div>
      <client-only>
        <span :class="[bem('content'), 'truncate']">{{ getNames }}</span>
      </client-only>
      <div :class="[bem('arrow')]">
        <KKCIcon name="icon-wode-youjiantou" :size="32" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useLearnStore } from '~/stores/learn.store'
const bem = useBem('home-navbar-learn-target')

const props = defineProps({
  content: {
    type: String,
    default: '',
  },
  ltItems: {
    type: Array,
    default: undefined,
  },
})

const goLearnTarget = debounceFunc(() => {
  useLearnStore().state.learnTargetID = null
  useLearnStore().setLearnDialogState(true)
}, 800, true)
const getNames = computed(() => {
  const ltItems = props?.ltItems || []
  if (Array.isArray(ltItems)) {
    return ltItems.map(v => v.categoryName)?.join(' · ')
  } else {
    return ''
  }
})

</script>

<style lang="scss">
.home-navbar-learn-target__outer {
  height: 80px;
  display: flex;
  align-items: center;
  padding: 0 24px;
}

.home-navbar-learn-target {
  display: flex;
  align-items: center;
  height: 64px;
  border-radius: 16px;
  padding: 0 16px;
  width: 100%;
  background: rgba(255,255,255,0.4);
  &__icon {
    margin-right: 12px;
    background-color: var(--kkc-brand-text);
    color: #fff;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
  }

  &__content {
    flex: 1;
    height: 28px;
    font-size: 24px;
    font-weight: 500;
    color: #111;
    line-height: 28px;
  }

  &__arrow {
    margin-left: auto;
    color: #111;
  }
}
</style>
