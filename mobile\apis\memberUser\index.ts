// [AI-GEN]
export type Item = {
  /** 会员用户id */
  id: number;
  /** 会员等级名称 */
  name: string;
  // 专业分类
  cateId: number;
  // 一级标签项
  firstLabelType: string;
  // 一级标签值
  firstLabelValue: string;
    // 二级标签项
  secondLabelType: string;
  // 二级标签值
  secondLabelValue: string;
  /** 后端标识设置的样式 */
  logoSetting: 1 | 2 | 3 | 4 | 5 | 6;
}
export interface OpenMemberListProtResponse {
    list: Item[],
    isMember: 1 | 0
}

/**
 * 会员入口-用户已开通会员
 * */
export async function openMemberListProt (body = {}) {
  return useHttp<OpenMemberListProtResponse>('/kukemarketing/wap/memberUser/openMemberListProt', {
    method: 'post',
    body,
    transform: res => res.data,
    default () {
      return {
        list: [
          // {
          //   id: 1,
          //   name: '黄金会员',
          //   cateId: 1,
          //   firstLabelType: 'tag1',
          //   firstLabelValue: '1',
          //   secondLabelType: 'tag2',
          //   secondLabelValue: '2',
          //   logoSetting: 1
          // },
          // {
          //   id: 2,
          //   name: '白银会员',
          //   cateId: 3,
          //   firstLabelType: 'tag1',
          //   firstLabelValue: '1',
          //   secondLabelType: 'tag2',
          //   secondLabelValue: '2',
          //   logoSetting: 2
          // }
        ]
      }
    },
  })
}
/**
 * 会员中心详情
 * @see http://yapi.kukewang.com:9005/project/273/interface/api/71667
 * */
export async function memberDetails (body = {}) {
  return useHttp<{id:number, isMember: 0 | 1}>('/kukemarketing/wap/kmMember/memberDetails', {
    method: 'post',
    body,
    transform: res => res.data,
    default () {
      return {

        // id: 1,
        // // isMember: 0,
        // isMember: 1,

      }
    },
  })
}

export interface memberGoodsDetailsProtRequestData {
  /** 数据范围 1 全部数据 2正常数据 */
  type: number;
  /** 会员规格id */
  memberSpecId?: string;
  /** 会员商品id */
  memberGoodsId: string;
}
export interface memberGoodsDetailsProtResponseData {
  /** 会员商品 Id */
  id: string;
  /** 会员商品图片地址 */
  img: string;
  /** 会员名称 */
  name: string;
  /** 价格 */
  price: number;
  /** 专业分类 */
  cateId: number;
  /** 会员商品状态0 禁用 1 启用 */
  status: number;
  /** 副标题 */
  subtitle: string;
  /** 产品线 Id */
  productId: number;
  /** 删除时间 */
  deletedAt: number;
  /** 有效期（天数） */
  validityDay: number;
  /** 会员规格id */
  memberSpecId: string;
  /** 会员等级id */
  memberLevelId: string;
  /** 一级标签项（region地区 academic_section学段 subject_type科目 exam_format考试形式 direction_type方向） */
  firstLabelType: string;
  /** 会员等级名称 */
  memberLevelName: string;
  /** 一级标签 */
  firstLabelValue: string;
  /** 二级标签项（region地区 academic_section学段 subject_type科目 exam_format考试形式 direction_type方向） */
  secondLabelType: string;
  /** 二级标签 */
  secondLabelValue: string;
  /** 会员规格状态 0 禁用 1 启用 */
  memberSpecStatus: number;
}
/**
 * 会员商品详情
 * */
export async function memberGoodsDetailsProt (body = {}) {
  return useHttp<memberGoodsDetailsProtResponseData>('/kukemarketing/wap/kmMember/memberGoodsDetailsProt', {
    method: 'post',
    body,
    transform: res => res.data,
    default () {
      return {

      }
    },
  })
}

/**
 * 商品详情-会员价（暂时作废）
 * */
export async function goodsMemberInfoProt (body = {}) {
  // {goodsMasterId:string, goodsPresentPrice:string, }
  return useHttp<Record<string, any>>('/kukemarketing/wap/memberUser/goodsMemberInfoProt', {
    method: 'post',
    body,
    // transform: res => res.data,
    default () {
      return {
        // data: {}
      }
    },
  })
}

export interface memberPriceRequestData {
  /** 商品skuId */
  skuId?: string;
  /** 商品现价 */
  price: number;
  /** 商品类型 */
  goodsType: number;
  /** 商品 Id */
  goodsMasterId: string;
}
export interface memberPriceResponseData {
  /** 会员商品 Id */
  id: string;
  /** 会员等级名称 */
  name: string;
  /** 商品现价使用会员后的价格 */
  price: number;
  /** 专业分类 */
  cateId: number;
  /** 用户是否有该商品的会员 0没有 1有 */
  isMember: number;
  /** 会员规格id */
  memberSpecId: string;
  /** 会员等级id */
  memberLevelId: string;
  /** 一级标签项（region地区 academic_section学段 subject_type科目 exam_format考试形式 direction_type方向） */
  firstLabelType: string;
  /** 一级标签 */
  firstLabelValue: string;
  /** 二级标签项（region地区 academic_section学段 subject_type科目 exam_format考试形式 direction_type方向） */
  secondLabelType: string;
  /** 二级标签 */
  secondLabelValue: string;
  /** 用户使用会员的开通 Id */
  memberOpenRecordId: string;
}
/**
 * 获取正常商品详情会员价
 * */
export async function memberPrice (body = {}) {
  // {goodsMasterId:string, goodsPresentPrice:string, }
  return useHttp<{data:memberPriceResponseData}>('/kukemarketing/wap/kmMember/getGoodsDetails/memberPrice', {
    method: 'post',
    body,
    // transform: res => res.data,
    default () {
      return {
        // data: {}
      }
    },
  })
}
// #region 立即领取(商品列表或者商品详情、题库模块、优惠券)
export interface DeliveryInfos {
  /** 市id */
  cityId: string;
  /** 收件人名称 */
  receiver: string;
  /** 区/县id */
  countyId: string;
  /** 省份id */
  provinceId: string;
  /** 收件人手机号 */
  receiveMobile: string;
  /** 详细地址 */
  addressDetail: string;
}

export interface getItNowProtRequestData {
  /** 专业分类 Id */
  cateId: number | string;
  /** 商品id/优惠券id/题库模块Id */
  goodsId: string;
  /** 商品类型 1商品 2题库模块 3优惠券  */
  goodsType: number;
  /** 商品类型 1 网课，2 面授 3 图书 4 试卷 5 网课套餐，6 OMO套餐 7 图书套餐 8 面授套餐 9 试卷套餐 10 题库模块套餐(就是商品中心的类型) */
  resourceType?: number | string;
  receiveSchoolId?: number | string;
  /** 会员商品id */
  memberGoodsId?: string;
  /** 邮寄地址信息 ,DeliveryInfos */
  deliveryInfos?: DeliveryInfos;
}

/**
 * 立即领取(商品列表或者商品详情、题库模块、优惠券)
 * @see http://yapi.kukewang.com:9005/project/273/interface/api/71637
 * */
export async function getItNowProt (body = {}) {
  return useHttp<{data:unknown, code:string}>('/kukemarketing/wap/kmMember/getItNowProt', {
    method: 'post',
    body,
    // transform: res => res.data,
    // default () {
    //   return {
    //   }
    // },
  })
}
// #regionend

// TODO xbl:
/**
 * 会员日福利 - 领取积分
 * @description 在会员日（8月8日）可以领取积分福利
 * @see http://yapi.kukewang.com:9005/project/273/interface/api/xxxxx
 */
export const memberDayApi = {
  /**
   * 领取会员日积分
   * @returns Promise<{ code: string, message: string, data: { points: number } }>
   */
  async getPoints () {
    return useHttp('/kukemarketing/wap/kmMember/memberDay/getPoints', {
      method: 'post',
      transform: res => res.data,
      default () {
        return {
          points: 800 // 默认积分数量
        }
      }
    })
  }
}
