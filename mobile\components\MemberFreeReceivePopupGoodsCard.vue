<template>
  <div class="flex rounded-[24px] bg-white p-[24px]">
    <div class="flex-shrink-0">
      <img
        :src="goodsImg"
        class="w-[210px] h-[140px] rounded-[8px]"
        alt=""
      >
    </div>
    <div class="flex-1 flex flex-col ml-[16px]">
      <div class="line-clamp-2 break-all w-full text-[26px] font-[500] leading-[32px]">
        {{ _goodsTitle }}
      </div>
      <div class="flex mt-auto w-full items-end">
        <span
          class="text-[24px] text-[#666] leading-[40px] flex-1 line-clamp-1 break-all"
        >{{ _includeContent }}</span>
        <span class="ml-auto h-[40px] text-[#eb2330] font-gilroy-bold leading-[40px] flex-shrink-0">
          <span
            class="text-[24px] font-[500] h-[30px] leading-[30px]"
          >￥</span>
          <span
            class="text-[40px] h-[40px] leading-[40px]"
          >0</span>
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="MemberFreeReceivePopupGoodsCard">
import { useCourseStore } from '~/stores/course.store'
import { GoodsType } from '@/apis/course/types'

const courseStore = useCourseStore()
console.log('[ courseStore ] >', courseStore.courseInfo)

type Props = {
// info?:any
}
withDefaults(defineProps<Props>(), {
  // info: false
})
// const _emit = defineEmits<{
//   (e: 'fn', val?: string): void;
// }>()

// 是否是多规格商品
const isSpecGoods = computed(() => {
  return courseStore.courseInfo.goodsType === GoodsType.MultiSpecification
})

// 商品标题
const _goodsTitle = computed(() => {
  const { goodsTitle } = courseStore.courseInfo
  if (isSpecGoods.value && !courseStore.isSku) {
    return courseStore?.spuInfo?.goodsTitle
  }
  return goodsTitle
})
// 商品标题
const _includeContent = computed(() => {
  const { includeContent = [] } = courseStore.courseInfo

  return includeContent.join('')
})
// 商品图片
const goodsImg = computed(() => {
  if (isSpecGoods.value && !courseStore.isSku) {
    return courseStore.spuInfo?.goodsImg
  }
  return courseStore.courseInfo.goodsImg
})

</script>

<style lang="scss">
.MemberFreeReceivePopupGoodsCard{
  //
}
</style>
