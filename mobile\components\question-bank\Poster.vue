<template>
  <KKPopup
    class="question-poster-popup !h-auto"
    :show="show"
    :width="640"
    :is-show-btn="false"
    :box-padding="'0'"
    :close-icon-size="32"
    position="center"
  >
    <template #header>
      <div class="popup-header h-[136px] leading-[120px] text-[36px] font-[500] text-[#111] text-center">
        分享好友
      </div>
    </template>
    <div class="question-poster min-h-[400px] flex flex-col items-center rounded-[16px]">
      <div class="text-[28px] text-[#666] leading-[40px] pt-[16px] break-all title-area" v-html="shareTitle" />
      <img v-if="qrCodeUrl" class="w-[360px] h-[360px] mt-[15px]" :src="qrCodeUrl" alt="qr" @load="qrCodeUrlLoad">
      <div class="h-[37px] w-full" />
    </div>
    <KKCButton v-if="!os?.isHarmonyOS" class="w-[576px] text-[32px] m-[32px] mt-0" type="primary" :disabled="isDrawing" @click="shareClick">
      {{ os?.isApp ? '分享给好友' : '长按二维码转发给朋友/保存图片' }}
    </KKCButton>
  </KKPopup>
</template>

<script setup lang="ts">
import { toDataURL } from 'qrcode'
import type { QRCodeErrorCorrectionLevel } from 'qrcode'
import { toCanvas } from 'html-to-image'
import commonsVariable from '../../../base/utils/globalVariable'

const props = withDefaults(defineProps<{
  id: string,
  title: string,
  link: string,
  show: boolean,
  qrWidth?: number,
  qrMargin?: number
}>(), {
  title: '',
  link: '',
  show: false,
  qrWidth: 100,
  qrMargin: 2
})

// 二维码
const qrCodeUrl = ref('')
// 海报
const posterUrl = ref('')
const createQrCode = async () => {
  try {
    const { qrWidth, qrMargin, link } = props
    const options = {
      errorCorrectionLevel: 'H' as QRCodeErrorCorrectionLevel,
      width: qrWidth,
      margin: qrMargin,
      color: {
        dark: '#000',
        light: '#FAFAFA',
      },
    }
    const qrUrl = await toDataURL(link, options)
    qrCodeUrl.value = qrUrl
    // isDrawing.value = true
    // nextTick(() => {
    //   drawImg()
    // })
  } catch (err) {
    console.error(err)
  }
}

const qrCodeUrlLoad = () => {
  nextTick(() => {
    isDrawing.value = false
  })
}

/**
 * 海报是否绘制完成
 */
const isDrawing = ref(false)

const drawImg = () => {
  const element = document.querySelector('.question-poster') as HTMLElement

  toCanvas(element,
    {
      pixelRatio: 4,
      cacheBust: true,
      backgroundColor: '#fff',
    })
    .then((canvas: any) => {
      const base64 = canvas.toDataURL('image/jpg', 1)
      const file = base642File(base64, 'png')

      handleFileUpload(file)
    }).catch((error: any) => {
      Message('海报绘制失败，请重试~')
      console.log(error)
    })
}

// 生成海报文件名，相同试题试卷避免重复生成
const generateCustomFileName = (file: File, ossPath = '') => {
  if (typeof ossPath !== 'string') {
    throw new TypeError('类型错误, 请传入字符串')
  }
  let ext = ''
  if (file.name) {
    ext = file.name.split('.')
    ext = '.' + ext[ext.length - 1]
  }
  return (
    ossPath +
    props.id +
    ext
  )
}

// 上传海报图片
const { commonApi } = useApi()
const ossInstance = ref()
const handleFileUpload = async (file: File) => {
  try {
    const { data } = await commonApi.fetchOSSConfig()
    ossInstance.value = await initOSS(data || {})

    const result = await unref(ossInstance).put(
      generateCustomFileName(file, 'img/kukecloud/question/poster/'),
      file
    )

    posterUrl.value = result.url

    toShare()
  } catch (e) {
    Message('图片保存失败，请重试~')
  }
}

const shareClick = () => {
  drawImg()
}

// 分享
const toShare = async () => {
  try {
    if (os?.isApp) {
      // imageRes  分享图片 base64格式或者图片链接
      // imageType 分享图片类型 0：链接（网络图片） 1：base64
      const obj = {
        imageRes: posterUrl.value,
        imageType: 0
      }
      if (os.isAndroid) {
        (window as any).android.shareImage(JSON.stringify(obj))
      } else if (os.isHarmonyOS) {
        (window as any).harmony?.shareImage(JSON.stringify(obj))
      } else {
        (window as any).webkit.messageHandlers.shareImage.postMessage(JSON.stringify(obj))
      }
    } else {
      // 兼容处理夸克浏览器无法下载资源的问题
      /Quark/.test(navigator.userAgent)
        ? window.open(posterUrl.value, '_self')
        : downloadFile(posterUrl.value, '海报')
    }
  } catch (error) {
    console.log(error)
  }
}

const shareTitle = computed(() => {
  if (commonsVariable.containsLatex(props.title) || props.title.includes('img')) {
    return ''
  }
  return props.title
  // 去除html标签和特殊字符
  // const reg = /(<([^>]+)>)|&nbsp;|&ldquo;|&rdquo;|&lt;|&gt;|&#39;/ig
  // const title = props.title.replace(reg, (match) => {
  //   if (match === '&ldquo;' || match === '&rdquo;') {
  //     return '"'
  //   }
  //   if (match === '&nbsp;') {
  //     return ' '
  //   }
  //   if (match === '&lt;') {
  //     return '<'
  //   }
  //   if (match === '&gt;') {
  //     return '>'
  //   }
  //   if (match === '&#39;') {
  //     return "'"
  //   }
  //   return ''
  // })
  // return handleCutText(title)
})

// const handleCutText = (title: string, num = 80) => {
//   if (title.length > num) {
//     return title.slice(0, num) + '...'
//   }
//   return title
// }

// 上传图片初始化
useHead({
  script: [
    {
      src: 'https://gosspublic.alicdn.com/aliyun-oss-sdk-6.17.1.min.js',
    },
  ]
})

onMounted(() => {
  createQrCode()
})

defineExpose({
  toShare
})

</script>

<style lang="scss" scoped>
.popup-header {
  background: url('~/assets/images/question-bank/popup_title_bg.png') no-repeat;
  background-size: 100% 100%;
}
.title-area{
  width: 100%;
  padding: 0 20px;
  max-height: 122px;
  overflow: hidden;
}
</style>
