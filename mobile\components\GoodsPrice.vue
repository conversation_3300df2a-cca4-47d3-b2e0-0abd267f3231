<template>
  <div class="flex items-center text-[#f42b2a]">
    <div
      v-if="isShowActivityType"
      class="kb-goods-start__tag1 w-[64px] h-[36px] flex items-center justify-center"
    >
      <p
        class="text-[20px] font-medium text-white leading-[24px]"
      >
        {{ activityTypeName }}
      </p>
    </div>
    <p class="text-[24px] leading-[1]">
      <slot name="pre" />
      <span>￥</span>
      <strong class="font-gilroy-bold text-[40px] font-normal">{{ goodsPrice }}</strong>
      <small
        v-if="isShowMultipleKeyword"
        class="pl-[4px] text-[24px] font-medium"
      >
        起
      </small>
      <slot name="post" />
    </p>
  </div>
</template>

<script setup lang='ts'>
// types
import type { Props } from '~/pages/points/types'

const props = withDefaults(defineProps<{
  item: Props
  priceKey?: string
  multiple?: Props
}>(), {
  priceKey: 'goodsPresentPrice',
  multiple: () => ({
    key: 'priceType',
    value: 1
  })
})

// 获取当前商品的活动类型
const activityTypeList = computed(() => props.item?.activityTypes?.filter((type: number) => [1, 2, 4].includes(type)) || [])
// 是否显示活动类型
const isShowActivityType = computed(() => activityTypeList.value.length && props.item?.goodsType !== 6)
// 活动状态名称
const activityTypeName = computed(() => {
  const activityTypeMap = new Map([
    [1, '秒杀'],
    [2, '拼团'],
  ])

  const activitySet = new Set(activityTypeList.value)

  // 优先处理类型 '1' 和 '2'，返回对应名称
  for (const key of ['1', '2']) {
    if (activitySet.has(Number(key))) {
      return activityTypeMap.get(Number(key))
    }
  }

  return '分销'
})

/**
 * 格式化金额数据
 *   19.00 -> 19
 *   19.10 -> 19.1
 *   19.12 -> 19.12
 */
const goodsPrice = computed(() => {
  const money = props.item?.[props.priceKey]

  if (!money) { return '0' }
  if (typeof money !== 'string') { return money }
  // 判断是否是多规则的数据
  if (money.includes('-')) {
    const moneyList = money.split('-')
    return `${Number.parseFloat(moneyList[0])}-${Number.parseFloat(
      moneyList[1]
    )}`
  }
  return Number.parseFloat(money)
})
// 是否展示多规格关键字
const isShowMultipleKeyword = computed(() => {
  return (props.item?.goodsType === 6 || props.item?.resourceTypes === 12) &&
    props.item[props.multiple.key] === props.multiple.value
})
</script>
