<template>
  <div :key="signUrl" class="flex justify-center rounded-[16px] overflow-hidden">
    <img v-if="signUrl || isListAnswer" class="cursor-pointer rounded-[16px]" :style="styleImg" :src="signUrl" alt="">
    <div v-else class="bg-[#fff] w-[270px] h-[124px] flex flex-col justify-center items-center">
      <img class="w-[48px] loading-rotate" :src="LoadingIcon" alt="">
      <span class="text-[#999] text-[16px] pt-[20px]">加载中...</span>
    </div>
  </div>
</template>
<script lang="ts" setup>
import LoadingIcon from '@/assets/images/answer/loading-icon.png'
const props = withDefaults(
  defineProps<{
      url: string
      signFun?: Function
      isPrivate?: boolean // 是否私有
      ossInstance?: any
      styleImg?: any
      isListAnswer?:boolean
    }>(),
  {
    url: '',
    signFun: () => {
      return () => {}
    },
    isPrivate: true,
    styleImg: () => {
      return {
        width: '2.7rem',
        height: '100%'
      }
    },
    isListAnswer: false
  }
)

const signUrl = ref<string>('')
const asyncSignUrl = async () => {
  if (!props.isPrivate) {
    signUrl.value = props.url
    return
  }
  const { result } = await props.signFun(props.url, {}, props.ossInstance)
  // console.log(result, '================')
  if (result?.length) {
    signUrl.value = result[0]
  }
}
onMounted(() => {
  nextTick(() => {
    asyncSignUrl()
  })
})
onBeforeUpdate(() => {
  asyncSignUrl()
})
</script>
<style lang="scss" scoped>
.loading-rotate{
  animation: spin 1s linear infinite;
}
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
</style>
