<template>
  <div
    :class="[ { 'is-active': item.id === active }]"
    class="homework-item py-[16px] break-all"
    :data-level="item.level"
    @click="handleAction($event,item)"
  >
    <div class="homework-item-line">
      <!-- 作业没有音频类型的上传 -->
      <!-- <KKCIcon v-if="item.fileSource === 2" name="icon-yinpin" :size="36" /> -->
      <!-- v-else -->
      <KKCIcon
        name="icon-shijuan"
        :size="36"
      />
    </div>
    <div class="flex-1">
      <div class="flex items-center">
        <div class="flex-1">
          <div class="line-clamp-2 break-all text-[28px] leading-[34px]">
            <span
              v-if="item.hasUpdated"
              class="homework-item__hasUpdated flex-shrink-0 text-center"
            ><!-- 作业有更新 --></span>
            <span class="">
              {{ item.homeworkName }}
            <!-- -->
            </span>
          </div>
          <div class="flex items-center mt-[12px]">
            <template v-if="item.testpaperId">
              <span class="flex items-center text-[24px] text-[#666666] leading-[30px] mr-[12px]">
                <KKCIcon name="icon-deta_icon_shijian" :size="28" class="mr-[4px]" />
                <!-- 0开始考试 1刷题中 2已做完 -->
                <span class="pr-[4px]">
                  {{ item.doStatus === 0 ? '未开始' : item.doStatus === 1 ? '刷题中' : '已做完' }}
                </span>
                <span>
                  {{ item.doCount }}/{{ item.itemCount }}
                </span>
              </span>
              <!-- v-if="item.doStatus === 2" -->
              <span
                class="flex items-center text-[24px] text-[#666666] leading-[30px]"
              >
                <span>
                  总分：
                </span>
                <span>
                  {{ item.score }}分
                </span>
              </span>

              <span
                v-if="item.doStatus === 2 && !([3, 4].includes(item.scoringWay) && item.teacherState === 3)"
                class="ml-[24px] text-[#111111] text-[24px] h-[48px] leading-[48px] rounded-[12px] px-[20px] bg-[#F2F3F5]"
                title="再次做题"
                @click.stop="handleJiXuDati(item)"
              >
                再次做题
              </span>
            </template>
            <!-- v-if="item.fileNum" -->
            <template v-else>
              <span class="flex items-center text-[24px] text-[#666666] leading-[30px]">
                <KKCIcon name="icon-deta_icon_shijian" :size="28" class="mr-[4px]" />
                <span>
                  {{ item.fileNum }}
                </span>
                <span>
                  个作业
                </span>
              </span>
              <!-- v-if="item.score" -->
              <span
                v-if="item.status === 3 && item.scoringWay !== 1"
                class="flex items-center ml-[24px] text-[24px] text-[#666666] leading-[30px]"
              >
                <span>
                  得分
                </span>
                <span class="ml-[4px]">
                  {{ item.score }}分
                </span>
              </span>
            </template>
          </div>
        </div>
        <div class="flex flex-shrink-0 cursor-pointer">
          <template v-if="item.unlocked === false || liveDetailStore.freezeStatus">
            <KKCIcon
              name="icon-suo"
              color="#C5CAD5"
              :size="48"
            />
          </template>
          <template v-else>
            <!-- 如果是未批改展示再次答题按钮，可以再次答题
      如果是批改中展示再次答题按钮，点击此按钮不可以再次答题且toast提示“作业批改中无法做题，联系老师取消批改后再做”
      作业为已批改状态，不在显示再次答题按钮 -->
            <!-- <span v-if="item.doStatus === 1" class="ml-[24px]">
        再次做题
      </span> -->
            <!-- 老师批改状态 0无 1未阅卷 2阅卷中 3已阅卷 -->
            <template v-if="item.testpaperId">
              <span
                v-if="item.doStatus === 0"
                class="ml-[24px]"
                @click.stop="handleToQuestion(item)"
              >
                <KKCIcon
                  name="icon-deta_icon_zuoti"
                  color="#C5CAD5"
                  :size="48"
                />
              </span>
              <!-- <template v-else-if="!(item.doStatus === 1 && [3, 4].includes(item.scoringWay) && item.teacherState === 3)"> -->
              <!-- <template v-else-if="item.doStatus === 1"> -->
              <template v-if="item.doStatus === 1 && !([3, 4].includes(item.scoringWay) && item.teacherState === 3)">
                <!-- || [1, 2].includes(item.scoringWay) -->
                <!-- 试卷 -->
                <!--  doStatus 做题状态 0开始考试 1刷题中 2已做完 -->
                <!-- teacherState 老师批改状态 0无 1未阅卷 2阅卷中 3已阅卷 -->
                <!-- 作业 -->
                <!-- status 作业批改状态 1未批改 2批改中 3已批改 -->
                <!-- scoringWay 评分方式：1 不评分 2 AI阅卷 3 AI阅卷+人工阅卷 4 人工阅卷 -->
                <span
                  class="ml-[24px] text-[24px]"
                  title="继续答题"
                  @click.stop="handleJiXuDati(item)"
                >
                  <!-- 再次做题/继续刷题 -->
                  <KKCIcon
                    name="icon-deta_icon_zuoti"
                    color="#C5CAD5"
                    :size="48"
                  />
                </span>
              </template>
              <!-- v-else-if="!(item.doStatus === 2 && [3, 4].includes(item.scoringWay) && item.teacherState === 3)" -->
              <!-- <span
              v-if="item.doStatus === 2 && !([3, 4].includes(item.scoringWay) && item.teacherState === 3)"
              class="ml-[24px] text-[24px]"
              title="再次做题"
              @click.stop="handleJiXuDati(item)"
            >
              再次做题
            </span> -->

              <!-- {{ item.doStatus }} -->
              <!-- 是否有报告 hasReport 0无 1有 -->
              <!-- || item.doStatus === 2 -->
              <span
                v-if="item.hasReport === 1"
                class="ml-[24px] text-[24px]"
                title="查看报告"
                @click.stop="handleToQuestionReport(item)"
              >
                <KKCIcon
                  name="icon-chakanbaogao"
                  :size="48"
                  color="#C5CAD5"
                />
              </span>
            </template>
            <template v-if="item.fileNum">
              <KKCIcon
                name="icon-chakan"
                :size="48"
                class="ml-[24px]"
                color="#C5CAD5"
                @click.stop="showHomeworkModalList(item)"
              />
            </template>
            <!-- <template v-if="item.resourceType === 1">
          <KKCIcon
            name="icon-chakan"
            :size="26"
            @click.stop="handlePreview(item)"
          />

          <KKCIcon
            name="icon-xuexizhongxin-xiazai"
            color="#C5CAD5"
            :size="26"
            class="ml-[16px]"
            @click.stop="handleDownload(item)"
          />
        </template>
        <template v-else-if="item.resourceType === 2">
          <KKCIcon
            name="icon-xiangqing-bofang"
            color="#999"
            :size="24"
            @click.stop="handlePlay(item)"
          />
        </template> -->
          </template>
        </div>
      </div>
      <!-- 提交作业 -->
      <template v-if="item.homeworkModel === 2">
        <div
          v-if="!item.homeworkScoreId || [1, 2].includes(item.scoringWay)"
          class="flex items-center bg-[rgba(255,111,0,0.1)] justify-between rounded-[16px] pr-[8px]
        mt-[12px]"
        >
          <span
            class="line-clamp-1 flex-1 text-[24px] text-[#FF6F00] h-[64px] leading-[64px] pl-[16px]"
            @click.stop="handleVoid(item)"
          >
            <template v-if="item.scoringWay === 1">
              <!-- 此作业不评分，可正常提交作业 -->
              此作业不评分
            </template>
            <template v-else>
              学完本阶段课程后，请及时提交作业哦
            </template>
          </span>
          <span
            class="text-[24px] text-center ml-[20px] text-[#fff] w-[128px] bg-[#FF6F00] h-[48px] leading-[48px] rounded-[12px] cursor-pointer"
            @click.stop="handleHomeworkSubmit(item)"
          >
            提交作业
          </span>
        </div>
        <div
          v-else-if="[1,2].includes(item.status) "
          class="flex items-center bg-[rgba(255,111,0,0.1)] justify-between rounded-[16px] pr-[8px] mt-[12px]"
        >
          <span
            class="line-clamp-1 flex-1 text-[24px] text-[#FF6F00] mr-[12px] h-[64px] leading-[64px] pl-[16px]"
            @click.stop="handleVoid(item)"
          >
            作业已上传，请等待老师批改
          </span>
          <span
            class="text-[24px] text-center ml-auto text-[#fff] w-[128px] bg-[#FF6F00] h-[48px] leading-[48px] rounded-[12px] cursor-pointer"
            @click.stop="handleHomeworkSubmit(item)"
          >
            提交作业
          </span>
        </div>
        <div
          v-else-if="item.status===3 && item.content"
          class="flex items-center bg-[rgba(0,197,85,0.1)] justify-between rounded-[16px] pr-[8px] mt-[12px]"
        >
          <span
            class="line-clamp-1 flex-1 text-[24px] text-[#00c555] break-all h-[64px] leading-[64px] pl-[16px]"
            @click.stop="handleVoid(item)"
          >
            {{ item.content }}
          </span>
          <span
            class="text-[24px] text-center ml-[20px] text-[#fff] w-[128px] bg-[#00C555] h-[48px] leading-[48px] rounded-[12px] cursor-pointer"
            @click.stop="handleViewComment(item)"
          >
            查看评语
          </span>
        </div>
      </template>
    <!-- 提交作业 end  -->
    </div>

    <!-- 视频播放器 -->
    <!-- <KkVideoPlayer
      v-model:show="showVideo"
      :node-id="videoNodeId"
      :goods-master-id="(goodsMasterId as string)"
      :type="liveType"
      :live-org-id="liveOrgId"
      :try-listen-id="tryListenId"
      :play-by-video-id="videoId"
      @playResume="onPlayResume"
      @playPause="onPlayPause"
      @buy="onBuy"
    /> -->
    <StudyRequirementAndCommentModal
      v-model="showModal"
      title="作业评语"
      :content="content"
      @close="showModal = false"
    />
    <HomeworkModalList
      v-if="visibleHomeworkModalList"
      :outer-relation-id="homeworkId"
      :unlocked="unlocked"
      :jieduan-id="item._jieduanId"
      :class-start-time="item._classStartTime"
      @close="visibleHomeworkModalList = false"
    />
    <!-- 购买弹框 -->
    <Teleport to="body">
      <LearnBuyDialog
        ref="kkPopupRef"
        :select-goods="selectGoods"
        :goods-master-id="(goodsMasterId as string)"
        :specs-id="specsId"
      />
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import { events } from '@kukefe/kkutils'
// import { Base64 } from 'js-base64'
import type { NItem } from './type'
import { LearnBuyDialog } from '~/pages/learn-center/components/index'
import type { HomeworkInfo } from '~/types/homework'
import { useLiveDetailStore } from '~/stores/live-detail.store'
// const { getViewUrl } = useFileView()
//
const liveDetailStore = useLiveDetailStore()
const route = useRoute()
const {
  id: goodsMasterId,
  // tryListenId,
  // packageId = '',
  specsId = ''
} = route.query

const emits = defineEmits<{
  (e: 'status', event: NItem): void;
  (e: 'action', event: HomeworkInfo): void;
  (e: 'closeVideo'): void;
  (e: 'homeworkSubmit', event: HomeworkInfo): void;
}>()

// const select = ref(null)
// const videoId = ref('')
// const liveType: number = inject('liveType') as number
// const liveOrgId: string = inject('liveOrgId') as string

const props = defineProps<{
  item: HomeworkInfo,
  isFree?: boolean, // 课程是否免费
  isBuy?: boolean, // 课程是否购买
  isOpen?: boolean,
  isAllFree?: number,
  //
  // challengeModeNodeType?: number;
  active: string | undefined // 选中id
}>()
provide('isVideoBuy', props?.isAllFree)

//

const selectGoods = ref<any>({})
const kkPopupRef = ref(null)

// 判断是否过期
const checkGoodsExpired = async () => {
  const { data } = await useHttp<any>(ApisLc.checkGoodsExpire, {
    transform: input => input.data,
    body: {
      goodsMasterId,
      ...(specsId && { goodsSpecificationItemId: specsId })
    },
    default: () => [],
  })
  return data
}

//
// ------------------------------------------------------
const showModal = ref(false)
const content = ref('')

// 查看评语
const handleViewComment = async (item: HomeworkInfo) => {
  if (!item.content) {
    Message('评语不能为空')
    return
  }
  if (unref(liveDetailStore?.freezeStatus)) {
    Message('此订单退款权益冻结中，暂无法学习')
    return
  }
  const checkData = await checkGoodsExpired()
  const { status } = checkData.value
  if (status !== 1) {
    selectGoods.value = checkData.value
    kkPopupRef.value?.show()
    return
  }
  if (!await checkItemUnlockedAndClassStartTime(item)) {
    return
  }
  content.value = item.content
  showModal.value = true
}

const handleJiXuDati = async (item: HomeworkInfo) => {
  console.log('handleJiXuDati', item)
  if (unref(liveDetailStore?.freezeStatus)) {
    Message('此订单退款权益冻结中，暂无法学习')
    return
  }
  // teacherState 老师批改状态 0无 1未阅卷 2阅卷中 3已阅卷
  // if ([1, 3].includes(item.homeworkModel) && [3, 4].includes(item.scoringWay) && [1, 2].includes(item.teacherState)) {
  //   //
  //   if (item.teacherState === 2) {
  //     Message('作业批改中无法做题，联系老师取消批改后再做')
  //     return
  //   }
  // }
  // if ([0, 1].includes(item.teacherState)) {
  handleToQuestion(item)
  // }
}
/**
 * TODO 用户点击试卷一行或【开始做题】按钮，直接跳转至试卷做题页面
 * @param item
 */
const handleToQuestion = async (item: HomeworkInfo) => {
  console.log('handleToQuestion', item)
  const checkData = await checkGoodsExpired()
  const { status } = checkData.value
  if (status !== 1) {
    selectGoods.value = checkData.value
    kkPopupRef.value?.show()
    return
  }
  const { error } = await useHttp('/kukestudentservice/wap/kssHomeworkScore/checkSubmitProt', {
    body: {
      homeworkId: item.homeworkId,
      planStageId: item._jieduanId
    }
  })
  // TODO Message('作业批改中无法做题，联系老师取消批改后再做')
  console.log('error: ', unref(error)?.data)
  if (unref(error)) {
    return
  }
  navigateTo({
    path: '/question-bank/paper',
    query: {
      testpaperId: item.testpaperId,
      from: 2,
      goodsMasterId,
      // TODO
      studyPlanId: item._studyPlanId

    }
  }, {
    // open: { target: '_blank' }
  })
}
/**
 * 查看报告
 * @param item
 */
const handleToQuestionReport = async (item: HomeworkInfo) => {
  console.log('handleToQuestionReport', item)
  if (unref(liveDetailStore?.freezeStatus)) {
    Message('此订单退款权益冻结中，暂无法学习')
    return
  }
  const checkData = await checkGoodsExpired()
  const { status } = checkData.value
  if (status !== 1) {
    selectGoods.value = checkData.value
    kkPopupRef.value?.show()
    return
  }
  const { error } = await useHttp('/kukestudentservice/wap/kssHomeworkScore/checkViewProt', {
    body: {
      homeworkId: item.homeworkId,
      planStageId: item._jieduanId
    }
  })
  // 拦截错误码 22024，阻止跳转报告页
  console.log('error: ', unref(error)?.data)
  // 新接口不再处理 22024
  if (unref(error)) {
    return
  }
  navigateTo({
    path: '/question-bank/report',
    query: {
      // testpaperId: item.testpaperId,
      from: 2,
      //
      targetResultId: item.targetResultId
    }
  }, {
    // open: { target: '_blank' }
  })
}
const checkItemUnlockedAndClassStartTime = async (item:HomeworkInfo = {}) => {
  const checkData = await checkGoodsExpired()
  const { status } = checkData.value
  if (status !== 1) {
    selectGoods.value = checkData.value
    kkPopupRef.value?.show()
    return
  }
  if (item._classStartTime) {
    Message(`课程将于${item._classStartTime}开课，敬请期待`)

    return false
  }
  // TODO unlocked 学习计划阶段是否解锁
  if (item.unlocked === false) {
    const { error, data } = await useHttp('/kukestudentservice/wap/studyPlan/unlockNewStudyPlanStage', {
      body: {
        id: item._jieduanId
      }
    })
    console.log('error: ', unref(error)?.data)
    if (unref(error)?.data?.code === '22031') {
      // 再接再厉 请先学完上个阶段的课时
      if (process.client) {
        events.publish('studyPlan:checkJieDuan', item, 1)
      }
      // return
    }
    if (unref(error)?.data?.code === '10001' && unref(error)?.data?.msg === '用户已解锁该学习计划阶段') {
      if (process.client) {
        events.publish('studyPlan:checkJieDuan', item, 2)
      }
    }
    if (unref(data)?.code === '10000') {
      // 恭喜解锁本学习阶段
      if (process.client) {
        events.publish('studyPlan:checkJieDuan', item, 2)
      }
      // return
    }
    return false
  }
  return true
}
const handleAction = async (e: Event, item: HomeworkInfo) => {
  console.log('handleAction', item)
  if (unref(liveDetailStore?.freezeStatus)) {
    Message('此订单退款权益冻结中，暂无法学习')
    return
  }
  // emits('action', item)
  if (item.hasReport === 1) {
    handleToQuestionReport(item)
    return
  }
  if (item.testpaperId) {
    if (!await checkItemUnlockedAndClassStartTime(item)) {
      e?.stopPropagation()
      e?.preventDefault()
      return
    }
    // Message('点击整个区域去做题（待实现）')
    // if ([0, 1].includes(item.doStatus)) {
    handleJiXuDati(item)
    // }
  } else if (item.fileNum) {
    showHomeworkModalList(item)
    e?.stopPropagation()
    e?.preventDefault()
  }
  //
}

const handleHomeworkSubmit = async (item: HomeworkInfo) => {
  console.log('handleHomeworkSubmit', item)
  if (unref(liveDetailStore.freezeStatus)) {
    Message('此订单退款权益冻结中，暂无法学习')
    return
  }
  const checkData = await checkGoodsExpired()
  const { status } = checkData.value
  if (status !== 1) {
    selectGoods.value = checkData.value
    kkPopupRef.value?.show()
    return
  }
  //
  if (!await checkItemUnlockedAndClassStartTime(item)) {
    return
  }
  const { error } = await useHttp('/kukestudentservice/wap/kssHomeworkScore/checkSubmitProt', {
    body: {
      homeworkId: item.homeworkId,
      planStageId: item._jieduanId
    }
  })
  console.log('error: ', unref(error)?.data)
  if (unref(error)) {
    return
  }
  emits('homeworkSubmit', item)
}
// 查看作业
const visibleHomeworkModalList = ref(false)
const homeworkId = ref('')
const unlocked = ref(false)
const showHomeworkModalList = (item: any) => {
  console.log('showHomeworkModalList', item)
  if (unref(liveDetailStore.freezeStatus)) {
    Message('此订单退款权益冻结中，暂无法学习')
    return
  }
  homeworkId.value = item.homeworkId
  unlocked.value = item.unlocked
  visibleHomeworkModalList.value = true
}
const handleVoid = (_item: HomeworkInfo) => {
  console.log('handleVoid', _item)
}
</script>

<style lang="scss" scoped>
//
.homework-item {
  display: flex;
  width: 100%;
  //
}
.homework-item__hasUpdated{
  display: inline-block;
  // padding: 0 8px;
  margin-right: 8px;
  height: 34px;
  line-height: 34px;
  width: 149px;
  //
  vertical-align: bottom;
  // background: linear-gradient( 310deg, #FF6E48 1%, #FF003A 100%);
  background: url('@/assets/images/learn/study-plan-hasUpdated.png');
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
}
.homework-item-line {
    flex-shrink: 0;
    position: relative;
    //
    align-self: auto;
    .kkc-icon {
      color: #111;
      margin-right: 16px;
    }
     &::before {
      background-color: #ddd;
    content: "";
    // height: 90%;
    // height: 48px;
    height: calc(100% - 10px);
    left: 18px;
    position: absolute;
    top: 40px;
    width: 1px;
    }
  }
</style>
