import MarkdownIt from 'markdown-it'
import { tex } from '@mdit/plugin-tex'
// import mk from "markdown-it-katex"
import katex from 'katex'
import 'katex/contrib/mhchem'
import 'katex/dist/katex.css'
export const markDownItKateX = (data: string) => {
  const md = new MarkdownIt()
  const noFourSpacesData = data
    // 处理中文标点
    .replace(/[；，]/g, ',')
    // 处理特殊字符 \(\) \[\]
    .replace(/\\\(/g, '$')
    .replace(/\\\)/g, '$')
    // 处理块公式
    .replace(/\\\[/g, '$$$$')
    .replace(/\\\]/g, '$$$$')
    // 处理对齐和换行
    .replace(/\\\\/g, '\\\\\\') // 保留矩阵换行
    .replace(/ {4}/g, '\n') // 替换4空格为换行
    // 处理公式边界空格
    .replace(/(\${2})(.*?)\1|\$(.*?)\$/g, (_, doubleDollar, doubleContent, singleContent) => {
      if (doubleDollar) {
        // 双$$包裹的内容，不做处理
        return `${doubleDollar}${doubleContent}${doubleDollar}`
      } else {
        return `$${singleContent.trim()}$`
      }
    })
    // 最终清理
    .replace(/\$\$\$\$/g, '$$') // 统一公式符号

  const renderFun = md.use(tex, {
    render: (content) => {
      // 在此渲染 tex 并返回
      // console.log(content)
      // console.log(displayMode)
      return katex.renderToString(content, {
        throwOnError: false,
        error: true
      })
    }
  })
  return noFourSpacesData ? renderFun.render(noFourSpacesData) : ''

  // const katexRegex = /(\$[^\$]*\$)|(\\\([^\\)]*\\\))/g
  // if (katexRegex.test(data)) {
  //   const md = new MarkdownIt()
  //   const renderFun = md.use(tex, {
  //     render: (content) => {
  //       // 在此渲染 tex 并返回
  //       // console.log(content)
  //       // console.log(displayMode)
  //       return katex.renderToString(content, {
  //         throwOnError: false,
  //         error: true
  //       })
  //     }
  //   })
  //   return data ? renderFun.render(data) : ''
  // } else {
  //   return data
  // }
}
