<template>
  <div class="correction-form">
    <div class="h-[60vh] overflow-y-scroll">
      <div class="correction-form-item">
        <div class="correction-form-label">
          错误类型
        </div>
        <span
          v-for="item in correctionType"
          :key="item.valueId"
          :class="[
            'correction-form-tag',
            formData.feedbackType.includes(item.valueId)
              ? 'correction-form-tag-active'
              : '',
          ]"
          @click="onCorrectionClick(item.valueId)"
        >
          {{ item.title }}
          <KKCIcon
            v-if="formData.feedbackType.includes(item.valueId)"
            class="correction-form-tag-active-icon"
            name="icon-wapxuanzhong"
            color="var(--kkc-brand)"
            :size="32"
          />
        </span>
      </div>
      <div class="correction-form-item">
        <div class="correction-form-label">
          错误描述
        </div>
        <div class="correction-form-box">
          <van-field
            v-model="formData.feedbackMsg"
            placeholder="请填写相关错误类型描述，以便于快速解决您反馈的问题，感谢您的理解与支持！"
            class="correction-form-input"
            type="textarea"
            rows="5"
            maxlength="200"
            show-word-limit
            :autosize="{ maxHeight: 100 }"
            @focus.stop.prevent
          />
        </div>
      </div>
    </div>
    <slot name="footer" />
  </div>
</template>

<script setup lang="ts">
import type {
  QuestionErrorBaseType,
  QuestionErrorSubmitType,
} from '../../apis/question-bank/types'
import type { ExciseQuestionCorrectType } from '../../pages/question-bank/types/exercise'
const { questionBankApi } = useApi()
const props = defineProps<{
  id: string;
}>()
const emits = defineEmits(['success'])

const formData = reactive<QuestionErrorSubmitType>({
  questionId: '',
  feedbackType: [],
  feedbackMsg: '',
  sourceType: 1,
})

const onCorrectionClick = (type: number) => {
  formData.feedbackType.splice(0, formData.feedbackType.length, type)
}

// 获取纠错类型列表
const correctionType = reactive<ExciseQuestionCorrectType[]>([])
const getCorrectionTypeList = async () => {
  try {
    const body: QuestionErrorBaseType = {
      settingType: 2,
      state: 1,
    }
    const { data, error } = await questionBankApi.getCorrectTypeProt(body)
    if (!error.value) {
      correctionType.push(...data.value)
    }
  } catch (error) {
    console.warn(error)
  }
}
// 提交纠错内容
const submit = async () => {
  try {
    if (formData.feedbackType.length === 0) {
      Message('请选择错误类型')
      return
    }
    if (!formData.feedbackMsg?.trim()) {
      Message('请输入错误描述')
      return
    }
    formData.questionId = props.id
    const { error } = await questionBankApi.doCorrectQuestionProt(formData)
    if (!error.value) {
      Message('提交成功')
      // 提交成功回调
      emits('success')
    }
  } catch (error) {
    console.warn(error)
  }
}

const reset = () => {
  formData.feedbackType.splice(0, formData.feedbackType.length)
  formData.feedbackMsg = ''
}

onMounted(() => {
  getCorrectionTypeList()
})

defineExpose({
  submit,
  reset,
})
</script>

<style lang="scss" scoped>
.correction-form {
  height: 100%;
  overflow: auto;

  &-item {
    @apply mb-[30px] mr-[31px] flex flex-wrap justify-between;
  }

  &-label {
    @apply w-[750px] my-[18px] text-[#111111] text-[28px] leading-[34px] font-[500];
  }

  &-tag {
    @apply flex justify-center items-center w-[330px] h-[88px] bg-[#f2f2f2] rounded-[12px] mb-[16px] text-[28px] text-[#000000];
    border: 2px solid #f2f2f2;

    &-active {
      @apply relative bg-brand/5 text-brand font-[500];
      border: 2px solid var(--kkc-brand);

      &-icon {
        @apply absolute right-[-4px] top-[-3px];
      }
    }
  }

  &-box {
    @apply w-[750px] bg-[#f7f7f7] p-[16px] rounded-[12px] mb-[80px];
  }

  &-input {
    @apply rounded-[12px];
    padding: 0;
    background: transparent;

    &::after {
      border: none;
    }
    :deep() {
      input {
        font-size: 32px;
      }
      ::-webkit-input-placeholder {
        height: 200px;
        font-size: 24px;
      }
    }
  }
}
</style>
