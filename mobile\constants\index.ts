// export const REM_DESIGN_WIDTH = 750
export const REM_ROOT_VALUE = 100

// ----------------------------------------------------

export enum dicFunctionPageId {
  /** 首页 */
  HOME = 100,
  ME = 101, //
  LEARN_CENTER = 10, // 学习中心
  COURSE_LIST = 1003, // 商品列表
  COURSE_DETAIL = 103, // 商品详情页
  NEWS = 12, // 资讯
  QUESTION_BANK = 11, // 题库
  CUSTOM_PAGE = 105, // 自定义页面
  ORDER_LIST = 1001, // 我的订单列表页
  PAY_SUCCESS = 1002, // 支付成功页
  LIVE_CALENDAR = 14, // 直播日历

  NETWORK_COURSE = 1, // 网课（网课+网课套餐）
  FACE_TO_FACE_COURSE = 2, // 面授课（面授+面授套餐）
  OMO_COURSE = 3, // omo套餐
  BOOK_COURSE = 4, // 图书(图书+图书套餐）
  TRIAL_COURSE = 5, // 试听课
  PINTUAN_COURSE = 6, // 拼团商品
  ZERO_EXPERIENCE_COURSE = 7, // 0元体验课
  SECKILL_COURSE = 8, // 秒杀商品
  gift_packs = 9, // 大礼包
  PAPER_LIST = 13, // 试卷列表
  CART = 15, // 购物车
  MINI_LIVE = 18, // 小程序直播
  CLASSIFY_NAV = 16, // 分类导航
  ANSWER = 17, // 答疑
  //
  YUE_KE_LIST = 23,
  // 积分中心
  POINTS = 24,
  // 背单词
  WORD_BOOK = 30,
  // 会员中心
  MEMBER_CENTER = 36,
  ANSWER_QUESTION = 35
}

export const PAGE_IDS_COURSE_LIST = [
  dicFunctionPageId.COURSE_LIST, // 下标0
  //
  dicFunctionPageId.NETWORK_COURSE,
  dicFunctionPageId.FACE_TO_FACE_COURSE,
  dicFunctionPageId.OMO_COURSE,
  dicFunctionPageId.BOOK_COURSE,
  dicFunctionPageId.TRIAL_COURSE,
  dicFunctionPageId.PINTUAN_COURSE,
  dicFunctionPageId.ZERO_EXPERIENCE_COURSE,
  dicFunctionPageId.SECKILL_COURSE,
  dicFunctionPageId.gift_packs,
  dicFunctionPageId.PAPER_LIST,
  //
]

export const dicFunctionPageList = [
  {
    value: dicFunctionPageId.NETWORK_COURSE,
    label: '网课（网课+网课套餐）',
    title: '网课',
    // path: id => `/courseList/${id}`,
  },
  {
    value: dicFunctionPageId.FACE_TO_FACE_COURSE,
    label: '面授课（面授+面授套餐）',
  },
  {
    value: dicFunctionPageId.OMO_COURSE,
    label: 'omo套餐',
    title: 'OMO课堂',
  },
  {
    value: dicFunctionPageId.TRIAL_COURSE,
    label: '试听课',
    // TODO v1.4 前禁用
    disabled: true,
  },
  {
    value: dicFunctionPageId.ZERO_EXPERIENCE_COURSE,
    label: '0元体验课',
    title: '体验课',
    // disabled: true,
    // path: id => `/courseList/${id}`,
  },
  {
    value: dicFunctionPageId.BOOK_COURSE,
    label: '图书(图书+图书套餐）',
    title: '图书',
    // path: id => `/courseList/${id}`,
  },
  {
    value: dicFunctionPageId.PINTUAN_COURSE,
    label: '拼团商品',
    // disabled: true,
  },
  {
    value: dicFunctionPageId.SECKILL_COURSE,
    label: '秒杀商品',
    // disabled: true,
  },
  {
    value: dicFunctionPageId.gift_packs,
    label: '大礼包',
    path: id => `/activity/giftPacks/${id}`,
  },
  {
    value: dicFunctionPageId.LEARN_CENTER,
    label: '学习中心',
    path: '/learn-center',
    authLogin: false,
  },
  // {
  //   value: dicFunctionPageId.QUESTION_BANK,
  //   label: '题库',
  //   path: '/question-bank',
  // },
  {
    value: dicFunctionPageId.NEWS,
    label: '资讯',
    disabled: true,
    // path: '/news',
  },
  {
    value: dicFunctionPageId.PAPER_LIST,
    label: '试卷列表',
    // path: id => `/courseList/${id}`,
  },
  {
    value: dicFunctionPageId.ME,
    label: '我的',
    path: '/me',
    authLogin: false,
  },
  {
    value: dicFunctionPageId.LIVE_CALENDAR,
    label: '直播日历',
    path: '/live',
  },
  {
    value: dicFunctionPageId.HOME,
    label: '首页',
    path: '/',
  },
  {
    value: dicFunctionPageId.POINTS,
    label: '',
    path: '/points',
  },
  {
    value: dicFunctionPageId.MEMBER_CENTER,
    label: '会员中心',
    path: '/member-center',
  }
]

export const findDicFunctionId = (id: number) => {
  return dicFunctionPageList.find((v) => {
    return v.value === id
  })
}

export const userCenterDicPageList = [
  {
    value: '0',
    label: '全部订单',
    path: '/user/order?tab=0',
  },
  {
    value: '1',
    label: '待付款',
    path: '/user/order?tab=1',
  },
  {
    value: '2',
    label: '待发货',
    path: '/user/order?tab=2',
  },
  {
    value: '3',
    label: '待收货',
    path: '/user/order?tab=3',
  },
  {
    value: '5',
    label: '完成',
    path: '/user/order?tab=5',
  },
  {
    value: '6',
    label: '退款/售后',
    path: '/user/order?tab=6',
  },
  {
    value: '1001',
    label: '我的订单',
    path: '/user/order',
    authLogin: true,
  },
  {
    value: '10',
    label: '学习中心',
    path: '/learn-center',
  },
  // {
  //   value: '80',
  //   label: '题库',
  //   path: '/question-bank',
  // },
  {
    value: '99',
    label: '我的收藏',
    path: '/user/collect',
  },
  {
    value: '110',
    label: '我的足迹',
    path: '/user/footprint',
  },
  {
    value: '120',
    label: '我的优惠券',
    path: '/user/coupon',
  },
  {
    value: '130',
    label: '收货地址',
    path: '/user/address',
  },
  {
    value: '140',
    label: '消息通知',
    path: '/user/notification',
  },
  {
    value: '150',
    label: '意见反馈',
    path: '/user/feedback',
  },
  {
    value: '160',
    label: '帮助中心',
    path: null,
  },
  {
    value: '170',
    label: '账号管理',
    path: '/user/setting',
  },
  {
    value: '180',
    label: '服务热线',
    path: null,
  },
  {
    value: '190',
    label: '个人资料',
    path: null,
  },
  {
    value: '200',
    label: '退出登录', // 个人中心退出
    path: null,
    disabled: true,
  },
  {
    value: '220',
    label: 'App下载',
    path: null,
    disabled: true,
  },
  {
    value: '101',
    label: '个人中心',
    path: '/me',
  },
  {
    value: '280',
    label: '退出登录', // 页头退出
    path: null,
    disabled: true,
  },
  {
    value: '135',
    label: '资料邮寄',
    path: '/datamailing',
  },
  {
    value: '290',
    label: '错题本',
    path: '/question-bank/exercise-book/1',
    authLogin: true
  },
  {
    value: '300',
    label: '收藏本',
    path: '/question-bank/exercise-book/2',
    authLogin: true
  },
  {
    value: '310',
    label: '排行榜',
    path: '/question-bank/ranking',
    authLogin: true
  },
  {
    value: '320',
    label: '学习报告',
    path: '/question-bank/learning-report',
    authLogin: true
  },
  {
    value: '325',
    label: '练习历史',
    path: '/question-bank/exercise-book/3',
    authLogin: true
  },
  {
    value: '172',
    label: '我的课评',
    path: '/user/courseEvaluate',
  },
  {
    value: '24',
    label: '积分中心',
    path: '/points',
  },
]

/**
 * 协议场景枚举
 */
export enum agreementSceneTypeEnum {
  LOGIN = 'LOGIN', // 用户登录协议
  LOGOUT = 'LOGOUT', // 注销
  REFUND = 'REFUND' // 退款
}
// 组件id
export enum KkBusinessCompIdsEnum {
  //
  KKTabbarWap = 31, // 底部导航
  KKNavBarWap = 36, // 顶部导航
  // 基础组件
  KKClassifySwitchWap = 6, // 项目
  KKSearchBoxWap = 22, // 搜索
  /** 用户头像 */
  KKUserInfoWap = 26,
  /** 我的订单 */
  KKMyOrderWap = 27,
  /** 常用功能 */
  KKCommonFunctionWap = 28,
  /** 必备工具 */
  KKEssentialToolWap = 29,
  // 广告组件
  KKVajraDistrictWap = 7, // 金刚区
  KKCombinedAdvertWap = 8, // 组合广告
  KKFloatFrameWap = 24, // 悬浮窗

  // 资源组件
  KKHotGoodsWap = 9, // 热销商品
  KKOfficialRecommendWap = 10, // 官方力荐
  KKNewGoodsWap = 11, // 上新商品
  KKAuditionCourseWap = 12, // 试听课
  KKZeroExperienceWap = 13, // 0元体验课
  KKLiveGoodsWap = 23, // 直播课
  KKCarouselRecommendWap = 34, // 轮播推荐
  KKQuestionBankWap = 35, // 题库
  // assemblyType9 = 36, // 资讯专栏

  // 营销组件
  KKCollageGoodsWap = 14, // 拼团
  KKQuickBuyWap = 15, // 秒杀
  KKCourtesyCardWap = 16, // 优惠券
  KKGiftPacksWap = 25, // 大礼包

}

/**
 * 课程中心接口 listType 枚举值
*/
export const LIST_TYPE = {
  /**  */
  course: 'course',
  free: 'free',
  omo: 'omo',
  /** 录播 */
  RECORD: 'record',
  /** 直播 */
  LIVE: 'live',
  /** 题库 */
  TESTPAPER: 'testpaper',
  /** 图书 */
  BOOK: 'book',
  /** 面授 */
  OFFLINE_COURSE: 'offline_course',
  /** 题库套餐 */
  QUESTION: 'question',
  /** 资讯：课程 */
  INFO_COURSE: 'info_course',
  /** 题库资讯 */
  INFO_TESTPAPER: 'info_testpaper',
  /** 图书资讯 */
  INFO_BOOK: 'info_book',
  /** 分组组件 */
  GROUP_LIST: 'group_list',
  /** 0元组件 */
  ZERO_YUAN: '0_yuan',
  /** 音频 */
  AUDIO: 'audio',
  /** 合集 */
  COLLECTIONS: 'collections'
}
