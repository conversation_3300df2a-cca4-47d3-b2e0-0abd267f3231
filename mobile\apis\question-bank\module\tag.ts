import type { SaveTagQueryType } from '~/pages/question-bank/types/basic'

enum Api {
  // 获取标签树结构列表
  getTagTree = '/kukecorequestion/wap/moduleManageTags/getTagTreeList',
  // 根据专业分类和标签获取模块列表
  getModuleList = '/kukecorequestion/wap/moduleManageTags/getModuleInfoList',
  // 收藏标签
  collectTag = '/kukecorequestion/wap/moduleManageTags/collectTagsProt',
  // 删除收藏标签
  deleteTag = '/kukecorequestion/wap/moduleManageTags/deleteUserTagsByIdProt',
  // 获取收藏标签列表
  getCollectTagList = '/kukecorequestion/wap/moduleManageTags/getUserTagsProt',
  // 获取热门标签
  getHotTagList = '/kukecorequestion/wap/moduleManageTags/getPopularTags',
  // 点击标签组合，检查标签
  checkTag = '/kukecorequestion/wap/moduleManageTags/clickTags',
  // 保存用户所选标签组合
  saveTag = '/kukecorequestion/wap/kqUserTags/saveTags',
}

const FILTER_KEYS: string[] = ['subjectType', 'academicSection', 'directionType', 'examFormat', 'region']

// 过滤body属性tags中为空的字段
export function filterBody (body: { [key: string]: any }) {
  if (!body.tags) { return body }

  // 过滤tags对象中值为0或空的字段
  FILTER_KEYS.forEach((key) => {
    if (body.tags[key] === 0 || body.tags[key] === '' || body.tags[key] === undefined || body.tags[key] === null) {
      delete body.tags[key]
    }
  })

  return body
}

/**
 * 获取标签树结构列表
 *
 * @param body
 */
export async function getTagTree (body: any) {
  return useHttp<any>(Api.getTagTree, {
    method: 'post',
    body: filterBody(body),
    transform: res => res.data,
  })
}

/**
 * 根据专业分类和标签获取模块列表
 *
 * @param body
 */
export async function getModuleList (body: any) {
  return useHttp<any>(Api.getModuleList, {
    method: 'post',
    body: filterBody(body),
    transform: res => res.data,
  })
}

/**
 * 收藏标签
 *
 * @param body
 */
export async function collectTag (body: any) {
  return useHttp<any>(Api.collectTag, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 删除收藏标签
 *
 * @param body
 */
export async function deleteTag (body: any) {
  return useHttp<any>(Api.deleteTag, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 获取收藏标签列表
 *
 * @param body
 */
export async function getCollectTagList (body: any) {
  return useHttp<any>(Api.getCollectTagList, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 获取热门标签
 *
 * @param body
 */
export async function getHotTagList (body: any) {
  return useHttp<any>(Api.getHotTagList, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 点击标签组合
 *
 * @param body
 */
export async function checkTag (body: any) {
  return useHttp<any>(Api.checkTag, {
    method: 'post',
    body: filterBody(body),
    transform: res => res.data,
  })
}

/**
 * 保存用户所选标签组合
 *
 * @param body
 */
export async function saveTag (body: SaveTagQueryType) {
  return useHttp<any>(Api.saveTag, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}
