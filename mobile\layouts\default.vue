<template>
  <main
    ref="mainRef"
    :class="{
      'layout-default': true,
    }"
    @scroll="handleScroll"
  >
    <template v-if="isOnline">
      <template v-if="!isXcx && !isKukeCloudAppWebview">
        <NavBar v-if="$route.meta?.navTitle" :title="$route.query.title || $route.meta?.navTitle" />
      </template>
      <slot />
      <template v-if="type == 1">
        <!-- v-if="!$route.meta?.ignoreTabbar" -->
        <ClientOnly>
          <template v-if="!isKukeCloudAppWebview && showTabbar">
            <LazyTabbar v-if="!isXcxAudit" />
            <LazyTabbarTemp v-else />
          </template>
        </ClientOnly>
        <ClientOnly>
          <LazyPopupAd v-if="showAD && !isXcxAudit" :key="$route.path" />
        </ClientOnly>
      </template>
    </template>
    <KKCOffline v-else />
    <KKCPageLoading theme="dark" />
    <template v-if="!isXcxAudit">
      <PopupLearnTarget v-if="type == 1" :key="`${$route.fullPath}${refreshKey}`" ref="refPopupLearnTarget" />
    </template>
    <ClientOnly>
      <!-- {{ kefuStore.kefuEnabled && kefuStore.showKefuModal }} -->
      <LazyKefuSelectModal
        v-if="kefuStore.showKefuModal"
        @close="kefuStore.closeKefuModal"
      />
      <!-- <LazyKefuSelectModalIframe
        v-if="kefuStore.showKefuIframe"
        @close="kefuStore.closeKefuIframe"
      /> -->
    </ClientOnly>
  </main>
</template>
<script setup lang="ts">
import { watch } from 'vue'
import { useOnline } from '../../base/composables/useOnline'
import { PAGE_IDS_COURSE_LIST, dicFunctionPageId } from '../constants'
import { useUserStore } from '~/stores/user.store'
import { useLearnStore } from '~/stores/learn.store'
import { useKefuStore } from '~/stores/kefu.store'
const kefuStore = useKefuStore()

const route = useRoute()
const { isOnline } = useOnline()
const userStore = useUserStore()
const refPopupLearnTarget = ref()
watch(() => route.path, () => {
  refPopupLearnTarget?.value?.handleOnClose()
  kefuStore.closeKefuModal()
})

const { isXcx, productName, logoImg, isKukeCloudAppWebview, type, appId, isXcxAudit, defaultSeo } = useAppConfig()
const config = useRuntimeConfig()
/**
 * 和产品、后端约定，暂时写死
 */
const token = useCookie(config.public.TOKEN_KEY, {
  expires: new Date(Date.now() + 60 * 60 * 24 * 27 * 1000)
})
const wxOpenId = useCookie('WX_OPEN_ID')
const promotionDetails = ref()
const refreshKey = computed(() => {
  return useLearnStore().state.refreshKey
})
useTheme()
const seoTitle = ref()
const seoKeyword = ref()
const seoDescription = ref()
const whiteSeoList = ['index', 'courseList-id', 'search', 'category-categoryTag', 'news-id', 'course-id', 'news-category-list', 'news-tag-list', 'courseList', 'material-category-list']
// eslint-disable-next-line eqeqeq
if (appId && appId == 1) {
  whiteSeoList.push('activity-teamBuy-id', 'resource-comp-type')
}
// 设置全局SEO
const setGlobalSeo = async (_path: string) => {
  // const checkCp = /^\/cp\//
  // const customId = checkCp.test(path) ? path.split('/').pop() : ''
  const customId = route.name === 'cp-id' ? route.params.id : ''
  const { data } = await userStore.wxGetShareDataInfo(customId as string)
  // 题库路由校验
  const checkQuestionBankReg = /^\/question-bank\//

  // const { seoKeyword, seoDescription, seoTitle } = data?.value
  if (whiteSeoList.includes(route.name as string) || checkQuestionBankReg.test(route.path)) { return }
  promotionDetails.value = data?.value
  seoTitle.value = data?.value?.seoTitle || data?.value?.data?.seoTitle
  seoKeyword.value = data?.value?.seoKeyword || data?.value?.data?.seoKeyword
  seoDescription.value = data?.value?.seoDescription || data?.value?.data?.seoDescription
  // 分享
  // shareInfo()
}
const headers = useRequestHeaders()
const getTitle = (title: string) => {
  if (isKukeCloudAppWebview) { return title || productName || '' }
  const isWindow = (typeof window !== 'undefined')
  const userAgent = headers['user-agent'] || ['']
  const isBrowser = userAgent.includes('MicroMessenger') || userAgent.includes('MQQBrowser') || userAgent.includes('QBWebViewType')
  if (isXcx) {
    return title || productName || ''
  } else if ((isWindow && (isWxBrowser || isQQBrowserWebView)) || (!isWindow && isBrowser)) {
    return productName || ''
  } else {
    return seoTitle.value || title || productName
  }
}
if (!isKukeCloudAppWebview) {
  await setGlobalSeo(route.path)
}
// const reqUrl = useRequestURL()

useHead({
  titleTemplate: (title) => {
    // qq/微信内title展示产品线
    return getTitle(title || '')
  },
  meta: [
    { name: 'keywords', content: () => seoKeyword.value },
    { name: 'description', content: () => seoDescription.value },
    { name: 'applicable-device', content: () => 'mobile' }
  ],
  link: [
    // { rel: 'canonical', href: reqUrl.href },
    ...(logoImg
      ? [{
          rel: 'shortcut icon',
          href: logoImg
        }]
      : [])
  ]
})

const TABBAR_PAGE_ID = [
  dicFunctionPageId.HOME,
  dicFunctionPageId.ME,
  dicFunctionPageId.LEARN_CENTER,
  dicFunctionPageId.CUSTOM_PAGE,
  dicFunctionPageId.QUESTION_BANK,
  dicFunctionPageId.CART,
]
// showTabbar
const showTabbar = computed(() => {
  // TODO 确认APP是否隐藏
  return TABBAR_PAGE_ID.includes(Number(route.meta?.pageid))
})
const AD_PAGE_ID = [
  dicFunctionPageId.HOME,
  dicFunctionPageId.COURSE_LIST,
  dicFunctionPageId.COURSE_DETAIL,
  dicFunctionPageId.PAY_SUCCESS,
  dicFunctionPageId.ORDER_LIST,
  dicFunctionPageId.CUSTOM_PAGE,
]
// const { data: kuke99DefaultHomeData } = useKuke99DefaultHomeData()
// showAD
const showAD = computed(() => {
  // if (kuke99DefaultHomeData.value.cpId) { return false }
  // if (route.path === '/' && !route.query.lt && appId === 1 && !lesseeId) { return false }
  // 没有选择学习目标也需要谈弹窗广告
  const { pageid } = route.meta
  // if (!pageid) { return false }
  const id = Number(pageid)
  // 商品列表页面展示ad弹窗
  if (id === PAGE_IDS_COURSE_LIST[0] && PAGE_IDS_COURSE_LIST.includes(id)) {
    return true
  }
  return AD_PAGE_ID.includes(id)
})

/**
 * 处理小程序cookie丢失的问题
 */
const xcxHandleCookieClear = () => {
  // 小程序token和小程序openId cookie中苹果手机启动丢失逻辑处理
  if (!token.value) {
    token.value = localStorage.getItem('WX_TOKEN') || ''
  }
  if (!wxOpenId.value) {
    wxOpenId.value = localStorage.getItem('WX_OPEN_ID') || ''
  }
}

const { setShareInfo } = useWxShare()
const shareInfo = async () => {
  // 分享白名单 除商品详情页和资讯详情页分享单独设置
  const checkShareRoute = ['news-id', 'course-id', 'activity-giftPacks-id', 'shopping-pay-succ', 'user-order-id', 'user-order', 'activity-raffle-id', 'activity-invite-id', 'points', 'points-pages-mall',
    'member-center'
  ]
  //
  const routeName = route.name as string || ''
  if (!checkShareRoute.includes(routeName)) {
    console.log('shareInfo layout/default.vue', { promotionDetails, defaultSeo })
    //
    const title = promotionDetails.value?.shareTitle || defaultSeo?.shareTitle || productName
    const desc = promotionDetails.value?.shareDescription || defaultSeo?.shareDescription || productName
    const shareImg = promotionDetails.value?.shareImg || defaultSeo?.shareImg || logoImg
    // 小程序&微信内分享
    if (isXcx) {
      //
      userStore.wxMiniProgramPostShareData(route.path, title, shareImg)
    } else {
      //
      setShareInfo({
        title,
        desc,
        shareImg,
      }, false)
    }
  }
}

onMounted(() => {
  // TODO 内存泄漏可疑点: 确保客户端执行并及时清除
  setTimeout(() => {
    const isShowErrorPopup = isIOS && (isUCBrowser || isQQBrowser || isQQBrowserWebView || isSogouBrowser || isDingTalk6_5 || isQuarkBrowser)
    if (isShowErrorPopup) {
      // IOS设备下，uc qq 以及搜狗浏览器，ssr未生效，请求会存在发起两次（一次fetch,一次xhr），影响页面展示
      // TODO 暂未解决，toast提示
      Message('请使用Safari浏览器操作~', '', 3000)
    }
    if (isXcx) {
      xcxHandleCookieClear()
    }
    // 设置全局seo信息 在app内不展示tdk信息
    if (!os?.isApp) {
      shareInfo()
    }
  }, 200)
  document.addEventListener('scroll', handleScroll)
  window.Message_ = Message
})

useComponentEvents()
const state = reactive({
  isScroll: 0
})
const mainRef = ref(null)
const handleScroll = (event: Event) => {
  const el = event.target as HTMLElement
  state.isScroll = el.scrollTop || window.pageYOffset || document.documentElement.scrollTop
}
provide('scrollState', state)
</script>

<style lang="scss">
.layout-default {
  min-height: 100vh;
  // height: 100vh;
  // overflow-y: auto;
  // 在tabbar组件中动态设置高度，正常文档流到达padding-bottom效果；使用伪元素设置tabbar安全域
  // &.has-tabbar{
  //   padding-bottom: 108px;
  // }
}
</style>
