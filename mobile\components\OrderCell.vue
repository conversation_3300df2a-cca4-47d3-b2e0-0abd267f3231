<template>
  <!-- <NuxtLink
    :to="{
      path: `/user/order/${order.orderSn}`
    }"
  > -->
  <div>
    <div class="order-cell bg-[#fff] mb-[24px]" @click="orderDetail(order)">
      <div class="flex justify-between items-center text-[24px] text-[#111]">
        <div>
          订单编号 {{ order.orderSn }}
        </div>
        <!-- todo 拼团文案 -->
        <div
          class="font-medium"
          :class="[1, 6, 9, 12, 13].includes(order.visibleOrderStatus) ? 'text-[#E62129]' : order.visibleOrderStatus === 5 ? 'text-[#999999]' : 'text-[#111111]'"
        >
          {{ order.visibleOrderStatusName }}
        </div>
      </div>
      <!-- ~~~~~~~~~~~~分割~~~~~~~~~ -->
      <div v-for="(item, index) in order?.orderGoodsVOList" :key="index" class="flex mt-[24px]">
        <div class="img relative">
          <kkcImage class="order-cell-img" :src="item?.goodsImg" radius="4px" />
          <span v-if="MarketingOrderTagMap?.get(item?.changeType)" class="order-cell-tip">{{ MarketingOrderTagMap?.get(item?.changeType)
          }}</span>
        </div>
        <div class="order-cell-info flex flex-col w-[424px]">
          <div class="order-cell-info-title font-medium text-[#111] line-clamp-1">
            <span>{{ item?.goodsTitle }}</span>
          </div>
          <div v-if="order.orderType === 33" class="text-[#999] text-[24px] line-clamp-1">
            {{ item.includeItem }}
          </div>
          <div v-if="order.orderType === 17" class="text-[#999] text-[24px] line-clamp-1">
            {{ `${Math.ceil(Math.round(item.validityDay) / 31)}个月 (${item.validityDay}天)` }}
          </div>
          <div v-if="item?.specificationItemName || item?.includedGoodsName" class="order-cell-info-package line-clamp-2 text-[#999]">
            <span>{{
              item?.specificationItemName || item.includedGoodsName
            }}</span>
          </div>
          <div class="flex justify-between items-end text-[24px] flex-1">
            <span v-if="item?.changeType === 3">￥0</span>
            <span v-else>￥{{ Number(item?.activityPrice) ? item?.activityPrice : item.goodsPresentPrice }}</span>
            <span v-if="order.orderType !== 17" class="text-[#999]">X {{ item?.goodsNum }}</span>
            <!-- <span v-if="item?.changeType === 4">￥{{ item.goodsPayPrice }}</span>
            <span v-else-if="item?.changeType === 3">￥0</span>
            <span v-else>￥{{ item.goodsPresentPrice }}</span> -->
          </div>
        </div>
      </div>
      <div class="order-cell-price font-bold text-right flex justify-end items-center text-red">
        <span class="text-[#999] font-normal pr-[8px]">{{
          order.visibleOrderStatus === 1 || order.visibleOrderStatus === 5 || order.visibleOrderStatus === 9 || order.visibleOrderStatus ===
            13
            ? "应付"
            : "实付"
        }}</span>
        <span>￥</span>
        <span class="text-[28px] font-gilroy-bold">
          <span class="order-cell-price-text">{{ priceSplit(order)?.[0] }}.</span>
          <span>{{ priceSplit(order)?.[1] }}</span>
        </span>
      </div>

      <div v-if="finalPaymentTextVisible" class="flex justify-end text-red text-[24px]">
        尾款剩余 ￥{{ order.remainPay }}
      </div>

      <!-- ~~~~~~~~~~~~分割~~~~~~~~~~~ -->
      <div class="flex justify-between items-center mt-[24px]">
        <div class="!text-[24px] text-[#666]">
          <div
            v-if="[1, 13].includes(order.visibleOrderStatus) && order?.remainingExpireTime > 0
            "
          >
            剩余支付时间：<span class="inline-block">
              <van-count-down class="!text-red" :time="order?.remainingExpireTime * 1000" @finish="onFinish" />
            </span>
          </div>
          <div v-else-if="order?.groupTeamStatus && order?.groupTeamStatus?.countDown">
            距拼团结束：<span class="inline-block">
              <van-count-down class="!text-red" format="DD:HH:mm:ss" :time="order?.groupTeamStatus?.countDown" @finish="onFinish" />
            </span>
          </div>
        </div>
        <div class="flex justify-end items-center">
          <template v-if="statusList?.length">
            <KKCButton
              v-for=" item in statusList "
              :key="item.id"
              class="!w-[150px] !text-[24px] !leading-[64px] !ml-[16px]"
              :type="item.active ? 'primary' : 'info'"
              size="small"
              @click.stop="btnHandle(item.id)"
            >
              {{ item.label }}
            </KKCButton>
          </template>
          <template v-else-if="order?.groupTeamStatus && order?.groupTeamStatus?.countDown">
            <KKCButton class="!w-[150px] !text-[24px] !leading-[64px]" type="primary" size="small" @click.stop="toShare(order)">
              邀请好友
            </KKCButton>
          </template>
          <template v-else>
            <KKCButton class="!w-[150px] !text-[24px] !leading-[64px]" type="info" size="small" @click.stop="btnHandle(9)">
              查看详情
            </KKCButton>
          </template>
        </div>
      </div>
    </div>
    <!-- 弹框 -->
    <KKPopup ref="kkPopupOrderRef" :title="popupTitle" :confirm-text="confirmText" @ok="secondConfirm" />

    <ProtocolCheck
      v-if="protocolCheckVisible"
      ref="protocolCheckRef"
      :radio-visible="false"
      :key-index="order.orderSn"
      @ok="btnHandle(1)"
    />
    <SharePop ref="sharePopRef" :info="{...order?.orderGoodsVOList[0],groupPeopleCount:order?.groupTeamStatus?.groupPeopleCount}" :goods-master-id="order?.orderGoodsVOList[0]?.goodsMasterId" :specification-item-id="order?.orderGoodsVOList[0]?.specificationItemId" :team-id="order?.orderGroupVO?.teamId" />
  </div>
  <!-- </NuxtLink> -->
</template>
<script setup lang="ts">
const props = defineProps({
  order: {
    type: Object,
    default: () => ({ }),
  },
})

const emits = defineEmits<{
  (e: 'cancel', data: any): void;
  (e: 'refresh'): void;
}>()

const onFinish = () => {
  emits('refresh')
}

const list = ref(props?.order?.orderGoodsVOList)
let idx = null
const mainItem = list.value.find((item: any, index: number) => {
  if (item.changeType === 0 || item.changeType === 2) {
    idx = index
  }
  return item.changeType === 0 || item.changeType === 2
})

list.value.splice(idx, 1)
list.value.unshift(mainItem)
const {
  statusList,
  popupTitle,
  confirmText,
  btnStatusHandle,
  secondConfirmhandle,
} = orderEffect(props.order)

/**
 * 尾款剩余 是否展示
 */
const finalPaymentTextVisible = computed(() => {
  const { visibleOrderStatus, presentPay, shouldPay } = props.order
  return [OrderStatus.UNPAID, OrderStatus.PART_PAYMENT, OrderStatus.DISCOUNT_REVIEWING].includes(visibleOrderStatus) && +presentPay !== +shouldPay
})

const kkPopupOrderRef = ref(null)

/**
 * 协议勾选组件Ref
 */
const protocolCheckRef = ref()

/**
 * 协议弹窗打开方法
 */
const protocolCheckPopupOpen = () => {
  protocolCheckRef.value?.handleClickOpen()
}

const kuKeCloudHooks = useKuKeCloud()

/**
 * 协议勾选是否展示
 */
const protocolCheckVisible = computed(() => {
  const orgId = props.order.orgId
  return kuKeCloudHooks.isKukeCloudProductLineByOrgId(orgId)
})

const btnHandle = (id: number) => {
  if (id === 1) {
    // 立即付款
    const protocolIsCheck = protocolCheckRef.value?.radioStatus // 协议是否勾选
    // 如果协议勾选栏展示了 但是用户未勾选 则弹出协议弹窗
    if (protocolCheckVisible.value && !protocolIsCheck) { return protocolCheckPopupOpen() }
  }
  btnStatusHandle(id)
  if (id === 2) {
    // 取消订单
    emits('cancel', props.order)
  } else if ([4, 6, 7].includes(id)) {
    kkPopupOrderRef.value?.showPopup()
  }
}

const secondConfirm = async () => {
  secondConfirmhandle()
  kkPopupOrderRef.value?.hide()
  // TODO 内存泄漏可疑点: 确保客户端执行并及时清除
  setTimeout(() => {
    //  刷新列表
    emits('refresh')
  }, 800)
}

const orderDetail = debounceFunc(
  (item: any) => {
    navigateTo(`/user/order/${item.orderSn}`)
  },
  800,
  true
)
const sharePopRef = ref(null)
const toShare = (row: any) => {
  console.log('row', row, props.order)
  sharePopRef.value?.show()
  // handleClipboard(`${window.location.origin}/activity/teambuy/share?id=${row?.orderGoodsVOList[0]?.goodsMasterId}&specialId=${row?.orderGoodsVOList[0]?.specificationItemId}&teamId=${props?.order?.orderGroupVO?.teamId}`, event)
}
const priceSplit = (order: any) => {
  let showPrice = '0'
  if (order.visibleOrderStatus === 1 || order.visibleOrderStatus === 5 || order.visibleOrderStatus === 9 || order.visibleOrderStatus === 13) {
    if (finalPaymentTextVisible.value) {
      showPrice = order.partPay
    } else {
      showPrice = order.presentPay
    }
  } else {
    showPrice = order.actualPay
  }
  return showPrice.split('.')
}
</script>
<style lang="scss" scoped>
.order-cell {
  padding: 24px;
  border-radius: 15px;
  position: relative;

  .img {
    width: 210px;
    height: 140px;
    border-radius: 8px;
    margin-right: 20px;
  }

  &-tip {
    @apply text-[18px] text-[#fff] absolute top-[0px] left-[0px] px-[8px] py-[5px];
    border-radius: 8px 0 8px 0;
    line-height: 1;
    background-color: $primary;
  }

  &-info {
    &-title {
      font-size: 26px;
      line-height: 35px;

      span {
        vertical-align: text-bottom;
      }
    }

    &-package {
      min-height: 32px;
      font-size: 24px;
    }
  }

  &-price {
    font-family: DINPro-Bold;
    width: 100%;
    font-size: 24px;
    margin-bottom: 4px;

    &-text {
      font-size: 40px;
    }
  }
}
</style>
