<template>
  <van-popup
    v-model:show="innerShow"
    :round="round"
    :closeable="closeable"
    :position="position"
    teleport="body"
    destroy-on-close
    class="study-requirement-popup"
    @close="handleClose"
  >
    <div
      class="title"
    >
      {{ title }}
    </div>
    <div
      class="content"
    >
      <div class="modal-desc">
        <span>
          提交作业后，老师未批改时可再次提交作业
        </span>
        <!-- <br> -->
        <span>
          老师批改中可联系督学老师取消批改
        </span>
        <!-- <br> -->
        <span>
          取消批改后再次提交作业
        </span>
      </div>
      <div class="image-upload-grid">
        <div
          v-for="(item, idx) in images"
          :key="item.url || item.name"
          class="file-item image-item"
        >
          <template v-if="item.type && item.type.startsWith('image/')">
            <!-- 点击已选图片可放大预览，点击已选图片的删除按钮可以删除本图片 -->
            <img :src="item.url" class="preview-img" @click="handlePreview(item.url)">
            <!-- style="width: 100px; height: 100px" -->
            <!-- <el-image
              class="preview-img"
              :src="item.url"
              :zoom-rate="1.2"
              :max-scale="7"
              :min-scale="0.2"
              :preview-src-list="[item.url]"
              show-progress
              :initial-index="4"
              fit="cover"
            /> -->
          </template>
          <!-- <template v-else-if="item.type && item.type.startsWith('audio/')">
            <audio :src="item.url" class="preview-audio" />
          </template>
          <template v-else-if="item.type && item.type.startsWith('video/')">
            <video :src="item.url" class="preview-video" />
          </template> -->
          <!-- <template v-else-if="item.type === 'application/pdf'">
            <span class="file-icon pdf" />
            <span class="file-name">{{ item.name }}</span>
          </template>
          <template v-else-if="item.type === 'application/msword'">
            <span class="file-icon word" />
            <span class="file-name">{{ item.name }}</span>
          </template>
          <template v-else-if="item.type === 'application/vnd.ms-excel'">
            <span class="file-icon excel" />
            <span class="file-name">{{ item.name }}</span>
          </template> -->
          <template v-else>
            <span class="file-icon other" />
            <span class="file-name">{{ item.name }}</span>
          </template>
          <span class="delete-btn" @click="removeImage(idx)">×</span>
        </div>
        <div
          v-if="images.length < 9"
          class="image-item add-item"
          @click="handleAddImage"
        >
          <span class="add-icon">
            <!-- + -->
            <KKCIcon name="icon-tianjiatupian" :size="54" />
          </span>
          <span class="add-text">添加图片</span>
          <span class="add-text-num">(最多9张)</span>
        </div>
      </div>
      <!-- <div class="modal-footer">
        <el-button type="danger" @click="handleSubmit">
          确认提交
        </el-button>
      </div> -->
      <!-- 隐藏的文件选择器 -->
      <!-- ,.pdf,.doc,.docx,.xls,.xlsx,.mp3,.mp4,audio/*,video/*,application/pdf
       ,image/*,.wav,.aac -->
      <input
        ref="fileInput"
        type="file"
        accept=".png,.jpg,.jpeg,.gif"
        multiple
        hidden
        style="display:none"
        @change="onFileChange"
      >
    </div>
    <div class="modal-footer flex gap-[16px]">
      <div class="close-btn" @click="handleClose">
        关闭
      </div>
      <div
        class="submit-btn"
        :class="{ 'disabled': isSubmitting }"
        :disabled="isSubmitting"
        @click="handleSubmit"
      >
        确认提交
      </div>
      <!-- <el-button type="danger" @click="handleSubmit">
        确认提交
      </el-button> -->
    </div>
  </van-popup>
</template>

<script setup>

import { showImagePreview } from 'vant'
import { fetchOSSConfig } from '~/apis/common'

function handlePreview (url) {
  showImagePreview([url])
}
const props = defineProps({
  modelValue: Boolean,
  title: {
    type: String,
    // required: true,
    default: '提交作业',
  },
  round: {
    type: Boolean,
    default: true,
  },
  closeable: {
    type: Boolean,
    default: false,
  },
  position: {
    type: String,
    default: 'center',
  },
  // content: {
  //   type: String,
  //   default: '',
  // },
  homeworkId: {
    type: String,
    required: true
  },
})
const emit = defineEmits(['update:modelValue', 'close', 'uploaded'])

const innerShow = computed({
  get: () => props.modelValue,
  set: val => emit('update:modelValue', val),
})

function handleClose () {
  emit('update:modelValue', false)
}

const images = ref([])
const fileInput = ref(null)
const isSubmitting = ref(false)

// function handleClose () {
//   emit('close')
// }

function handleAddImage () {
  if (images.value.length >= 9) { return }
  fileInput.value && fileInput.value.click()
}
/**
 * 增加功能：只能上传图片类型的文件
 */
function onFileChange (e) {
  const files = Array.from(e.target.files)
  const remain = 9 - images.value.length
  const addFiles = files.slice(0, remain)
  // 过滤出图片类型的文件
  const imageFiles = addFiles.filter(file => file.type.startsWith('image/'))
  if (imageFiles.length !== addFiles.length) {
    Message('请上传图片类型的文件')
    return
  }
  imageFiles.forEach((file) => {
    let url = ''
    let type = ''
    if (file.type.startsWith('image/') || file.type.startsWith('audio/') || file.type.startsWith('video/') || file.type === 'application/pdf') {
      url = URL.createObjectURL(file)
      type = file.type
    } else {
      type = file.type || getFileTypeByExt(file.name)
    }
    images.value.push({
      file,
      url,
      type,
      name: file.name
    })
  })
  e.target.value = ''
}

function getFileTypeByExt (name) {
  const ext = name.split('.').pop().toLowerCase()
  if (['doc', 'docx'].includes(ext)) { return 'application/msword' }
  if (['xls', 'xlsx'].includes(ext)) { return 'application/vnd.ms-excel' }
  if (['pdf'].includes(ext)) { return 'application/pdf' }
  return ''
}

function removeImage (idx) {
  const img = images.value[idx]
  URL.revokeObjectURL(img.url)
  images.value.splice(idx, 1)
}
useHead({
  script: [
    {
      src: 'https://gosspublic.alicdn.com/aliyun-oss-sdk-6.17.1.min.js',
    },
  ],
})
const ossInstance = ref(null)
async function handleSubmit () {
  if (isSubmitting.value) { return }
  isSubmitting.value = true
  try {
    Loading(true)
    // 实例化oss
    const { data } = await fetchOSSConfig()
    ossInstance.value = initOSS(data || {})
    const uploadPromises = images.value.map((imgObj, _idx) => {
      const file = imgObj.file
      const objectKey = generateFilename(file, 'img/kukecloud/wap/study-plan/')
      return unref(ossInstance).put(objectKey, file).then(res => res.url)
    })
    const urls = await Promise.all(uploadPromises)
    console.log(urls, 'urls')
    await homeworkSubmitProt(urls)
    // [AI-GEN] 提交成功后清空已上传的文件
    images.value.forEach((img) => {
      URL.revokeObjectURL(img.url)
    })
    images.value = []
  } catch (err) {
    console.error(err)
  } finally {
    Loading(false)
    isSubmitting.value = false
  }
}

// 文件类作业提交
async function homeworkSubmitProt (submitHomeworkList = []) {
  const { error } = await useHttp('/kukestudentservice/wap/kssHomeworkScore/homeworkSubmitProt', {
    body: {

      homeworkId: props.homeworkId,
      submitHomeworkList
    }
  })
  if (!unref(error)) {
    emit('uploaded', submitHomeworkList)
  } else if (unref(error).data?.code === '10071') {
    Message(unref(error).data.msg)
  }
}
onBeforeUnmount(() => {
  console.log('onBeforeUnmount ')
})
onUnmounted(() => {
  console.log('onUnmounted ')
  //
  images.value.forEach((img) => {
    URL.revokeObjectURL(img.url)
  })
})

</script>
<style lang="scss" scoped>
.study-requirement-popup{
border-radius: 24px;
width: 600px;
padding: 32px;
.close-btn{
    // width: 536px;
    flex: 1;
    height: 80px;
background: #F2F3F5;
border-radius: 16px 16px 16px 16px;
font-size: 28px;
color: #111111;
// line-height: 34px;
text-align: center;
line-height: 80px;
margin-top: 16px;

}
.submit-btn{
    // width: 50%;
    flex: 1;
height: 80px;
background: #FF6F00;
border-radius: 16px 16px 16px 16px;
font-size: 28px;
color: #fff;
// line-height: 34px;
text-align: center;
line-height: 80px;
margin-top: 16px;

}
.submit-btn.disabled {
  // background: #ccc;
  // color: #fff;
  cursor: not-allowed;
  pointer-events: none;
}
.title{
    width: 536px;
height: 44px;
font-family: PingFang SC, PingFang SC;
font-weight: 500;
font-size: 34px;
color: #111111;
line-height: 42px;
text-align: center;
margin-bottom: 8px;
}
.content{
    width: 536px;
// height: 342px;
font-family: PingFang SC, PingFang SC;
font-weight: 400;
font-size: 24px;
color: #999;
line-height: 32px;
text-align: justify;
font-style: normal;
text-transform: none;
max-height: 724px;
overflow-y: auto;
}
}

.modal-desc {
    color: #999;
    font-size: 24px;
    // margin: 0 0 20px 0;
    // padding: 0 32px;
    text-align: left;
  }
  .image-upload-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    margin-bottom: 24px;
    margin-top: 24px;
  }
  .image-item {
    width: 168px;
    height: 168px;
    background: #f2f3f5;
    border-radius: 14px;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .preview-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  .delete-btn {
    position: absolute;
    top: 0;
    right: 0;
    // background: rgba(0,0,0,0.5);
    color: #fff;
    // border-radius: 50%;
    // border-bottom-left-radius: 24px;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    // cursor: pointer;
    font-size: 0;
    z-index: 2;
    background: url('@/assets/images/learn/icon_guanbi.png') no-repeat center center;
  }
  .add-item {
    flex-direction: column;
    cursor: pointer;
    background: #fafafa;
    // border: 1px dashed #ccc;
  }
  .add-icon {
    font-size: 54px;
    color: #999;
  }
  .add-text {
    font-size: 26px;
    line-height: 32px;
    color: #999;
    text-align: center;
    margin-top: 6px;
  }
  .add-text-num {
    font-size: 22px;
    line-height: 28px;
    color: #999;
    text-align: center;
  }
</style>
