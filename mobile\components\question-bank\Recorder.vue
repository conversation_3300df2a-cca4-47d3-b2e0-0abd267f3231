<template>
  <div
    class="recorder"
  >
    <KKCAudioPlayer
      v-if="url"
      :icon-name="'icon-yinpinbofang'"
      :playing-icon="'icon-yinpinzanting'"
      :icon-size="48"
      :url="url"
      :show-del="showDel"
      @del="handleDel"
    />

    <div v-if="!url && showDel" class="recorder-tag" @click="onActionClick">
      <KKCIcon
        class="mx-[8px]"
        name="icon-waphuatong"
        :size="28"
        color="#999999"
      />
      <span>点击开始说话录入语音答案</span>
    </div>
    <Teleport to="body">
      <KKPopup
        ref="recorderPopup"
        :title="title"
        :close-on-click-overlay="false"
        position="bottom"
        bg-color="#ffffff"
      >
        <KKCRecordAudio
          :is-open="isOpen"
          :is-in-pc="isInPc"
          @close="handleClose"
          @getUrl="handleGetUrl"
        />
      </KKPopup>
    </Teleport>
    <Teleport to="body">
      <KKPopup
        ref="delConfirm"
        title="确定删除本次录音吗？"
        confirm-text="确定"
        @ok="handleDelOk"
      />
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import { pauseAudio } from '../../../base/utils/audio'
const { getAuthOpen } = useRecorder()
const props = defineProps<{
  url?: string;
  showDel?: boolean;
  isDisabled?: boolean;
  questionTitle?: string;
}>()
const { track } = useTrackEvent()

const emits = defineEmits<{
  (e: 'success', val: string): void;
}>()
// 弹窗title
const title = ref<string>('')
const recorderPopup = ref<HTMLElement>()
const isOpen = ref<boolean>(false)
const isInPc = ref<boolean>(false)
const tips = '请先开启设备录音权限，再进行录音哦~'
const isXcxDesktopMessage = '因微信平台限制，暂不支持电脑端录音'
const microphonePermission = (permissionGranted: Boolean) => {
  console.log('permissionGranted: ', permissionGranted)
  // 将回调数据存入缓存
  localStorage.setItem('permissionGranted', JSON.stringify(permissionGranted))
  if (!permissionGranted) {
    if (os?.isWeChatMiniProgramDesktop) {
      Message(isXcxDesktopMessage)
    } else {
      Message(tips)
    }
  }
}

const onActionClick = () => {
  if (props.isDisabled) { return }
  const permissionGranted =
    localStorage.getItem('permissionGranted') || 'false'
  console.log('onActionClick 1', permissionGranted)
  // 做题->做题添加录音 埋点
  track({
    category: '题库',
    action: '做题添加录音',
    label: props.questionTitle
  })

  try {
    // 暂停音频播放
    pauseAudio()
    // 授权
    if (os?.isApp && os?.isAndroid) {
      console.log('android')
      handleAuth()
    } else if (
      os?.isApp &&
      (os?.isPhone || os?.isTablet) &&
      permissionGranted === 'false'
    ) {
      console.log('ios')
      // 请求麦克风权限
      window.webkit.messageHandlers.iosMicPermission.postMessage(null)
    } else {
      handleAuth()
    }
  } catch (error) {
    console.log('error: ', error)
    if (os?.isWeChatMiniProgramDesktop) {
      Message(isXcxDesktopMessage)
    } else {
      Message(tips)
    }
  }
}

const handleAuth = async () => {
  const hasAuth = await getAuthOpen()
  console.log('hasAuth: ', hasAuth)
  const { type } = hasAuth as { type: boolean }
  if (type) {
    recorderPopup.value?.showPopup()
    isOpen.value = true
  } else if (os?.isWeChatMiniProgramDesktop) {
    Message(isXcxDesktopMessage)
  } else {
    Message(tips)
  }
}

const popupHide = () => {
  recorderPopup.value?.hide()
  isOpen.value = false
}

const handleClose = () => {
  popupHide()
}

const handleGetUrl = (val: string) => {
  emits('success', val)
  popupHide()
}

// 删除确认
const delConfirm = ref(null)
const handleDel = () => {
  delConfirm.value?.showPopup()
}

const handleDelOk = () => {
  // 删除录音
  emits('success', '')
  delConfirm.value?.hide()
}

onMounted(() => {
  window.microphonePermission = microphonePermission
})
</script>

<style lang="scss" scoped>
.recorder {
  @apply bg-[#e5e7eb];

  &-tag {
    @apply flex justify-center items-center h-[68px] w-full text-[26px] font-[500] text-[#999999];
  }
}
</style>
