/**
 * 0初次答题 1成绩不符合要求 2未抽过奖 3已抽过奖
 */
export enum StatusType {
    defaultType = 0,
    one,
    two,
    three
}

export interface ActivityInfoType {
    // 活动名称
    activityName:string
    // 活动说明
    activityExplain:string
    // 背景图
    pcBackgroundPictureUrl:string
    wapBackgroundPictureUrl:string
    // 活动按钮名称
    activityButtonName:string
    // 按钮背景色
    buttonBackgroundColor:string
    // 按钮文字颜色
    buttonWordColor:string
    // 试卷ID
    testpaperId:string
    // 模块ID
    moduleManageId:string
    // 抽奖ID
    lotteryId:string
    // 活动状态
    status:StatusType
    // 作题记录id
    targetResultId:string
    cateIds: string
}

export interface ButtonConfig {
    name?:string
    tips?:string
    bg?:string
    color?:string
    click?:Function
}
