<template>
  <div
    v-if="show || os?.isBookInApp"
    class="close w-[86px] h-[64px] bg-white bg-opacity-60 fixed right-[9px] top-[9px] z-[99] flex items-center justify-center self-center border border-[#97979733] rounded-[32px]"
  >
    <img :src="close" class="w-[34px] h-[34px]" alt="close" @click="handleClose">
  </div>
</template>

<script setup lang="ts">
import close from '@/assets/images/nav/close.png'
// 业务用，默认不展示
withDefaults(defineProps<{
  show?: boolean
}>(), {
  show: false
})

const handleClose = () => {
  if (os?.isAndroid) {
    window.android?.goClose()
  } else if (os?.isPhone || os?.isTablet) {
    window.webkit?.messageHandlers?.goClose.postMessage(null)
  } else if (os?.isHarmonyOS) {
    window.harmony?.goClose()
  }
}

</script>

<style scoped>
/*  */
</style>
