/**
 * 将给定的数字分别返回整数部分和小数部分，控制小数部分的精度
 * @param num - 要处理的数字，可以是 number 类型或 string 类型的数字
 * @returns 包含整数部分和小数部分的对象
 */
export function splitNumber (num: number | string): { integerPart: number, decimalPart: string } {
  const numberValue: number = typeof num === 'string' ? parseFloat(num) : num

  const integerPart: number = Math.floor(numberValue)
  const rawDecimalPart: number = numberValue - integerPart
  const decimalPart: string = rawDecimalPart.toFixed(2).slice(2) // 将小数部分格式化为两位数的字符串

  return { integerPart, decimalPart }
}

/**
 * 把数字格式化为保留两位小数 不够自动补零
 * @param num
 * @returns
 */
export function formatDecimal (num: number): string {
  const numStr = num.toFixed(2)
  const [integerPart, decimalPart] = numStr.split('.')
  const paddedDecimalPart = decimalPart.padEnd(2, '0')
  return `${integerPart}.${paddedDecimalPart}`
}

/**
 * 把数字格式化为 去0的数字
 * @param numOrStr
 * @returns
 */
export function formatNumberWithoutTrailingZero (numOrStr: number | string): string {
  const number = typeof numOrStr === 'string' ? parseFloat(numOrStr) : numOrStr

  if (isNaN(number)) {
    return 'Invalid number'
  }

  const formattedNumber = number % 1 === 0 ? number.toFixed(0) : number.toFixed(2).replace(/\.?0*$/, '')
  return formattedNumber.toString()
}

/**
 * 数字转中文
 * @param num
 * @returns
 */
export function numberToChinese (num: number): string {
  const chineseNum = ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十'] // 定义汉字数字数组
  if (num >= 1 && num <= 10) {
    return chineseNum[num - 1] // 返回对应的汉字数字
  } else if (num > 10 && num < 20) {
    return '十' + chineseNum[num - 11] // 处理 11 到 19 的情况
  } else if (num >= 20 && num < 100) {
    const tens = chineseNum[Math.floor(num / 10) - 1] // 十位数
    const ones = num % 10 === 0 ? '' : chineseNum[num % 10 - 1] // 个位数
    return tens + '十' + ones // 返回十位数和个位数的汉字表示
  } else {
    return '未知' // 处理超出范围的情况
  }
}
