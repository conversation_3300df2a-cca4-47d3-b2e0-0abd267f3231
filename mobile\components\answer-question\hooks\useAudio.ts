export const useAudio = () => {
  /**
   * 音频播放时间换算
   * @param {number} value - 音频当前播放时间，单位秒
   */
  const transTime = (time) => {
    const duration = parseInt(time)
    const minute = parseInt(duration / 60)
    let minuteFormat = ''
    let sec = (duration % 60) + ''
    const isM0 = ':'
    if (minute === 0) {
      minuteFormat = '00'
    } else if (minute < 10) {
      minuteFormat = '0' + minute
    } else if (minute === 10) {
      minuteFormat = minute.toString()
    }
    if (sec.length === 1) {
      sec = '0' + sec
    }
    return minuteFormat + isM0 + sec
  }
  return {
    transTime
  }
}
