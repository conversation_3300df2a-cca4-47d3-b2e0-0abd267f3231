<template>
  <div
    :style="{ width: videoWidth, height: videoHeight }"
    class="video_player_wrap player-video is_picture_btn"
    :class="controlConfig"
  >
    <div
      v-if="!firstPic"
      class="w-[100%] h-[200px] bg-[#fff] flex justify-center items-center"
      :style="{height: videoHeight||'100%'}"
    >
      <img class="w-[50%]" :src="DefalutVideo" alt="">
    </div>
    <KKChatSignOssImg
      v-if="firstPic"
      :style-img="{ width: videoWidth, height: videoHeight||'100%', objectFit: 'cover' }"
      :url="firstPic"
      :sign-fun="signFun"
      :oss-instance="ossInstance"
      :is-list-answer="isListAnswer"
    />
    <div
      class="player-btn-wrap"
      @click="onPlayer"
    >
      <img
        v-if="!isPlay"
        class="player-btn"
        :src="IconPlay"
        alt=""
        fit="cover"
      >
    </div>
    <van-popup
      v-model:show="isLoad"
      z-index="2000"
      :style="{ width: '100%',margin:'0px',maxWidth:'100%' }"
      @opened="popOpened"
      @closed="popClosed"
    >
      <VideoPlayer
        v-if="isLoad"
        ref="kkvideoPlayer"
        :class="[refVideoPlayer,isFullStatus&&'full_rotate']"
        :events="events"
        v-bind="{ ...defaultConfig, ...videoConfig }"
        :sources="signSources"
        preload="auto"
        @fullscreenchange="fullscreenchange"
        @play="onPlayerPlay"
        @pause="onPlayerPause"
        @error="onError"
        @mounted="playerMounted"
      />
      <div
        class="player-btn-wrap !bg-[transparent]"
        @click="clickPlayer"
      >
        <img
          v-if="!isPlay"
          class="player-btn"
          :src="IconPlay"
          alt=""
          fit="cover"
        >
      </div>
    </van-popup>
  </div>
</template>
<script lang="ts" setup>
import { VideoPlayer } from '@videojs-player/vue'
import 'video.js/dist/video-js.css'
import KKChatSignOssImg from '../kkChatSignOssImg.vue'
import type { controlConfigType } from './type'
import { defaultConfig } from './defaultConfig'
import IconPlay from '@/assets/images/answer/video-play.png'
import DefalutVideo from '@/assets/images/answer/defalut_video.png'
// store
import { useChatStore } from '~/stores/chat.store'
const { updateCurPlayingMedia } = useChatStore()
console.log(VideoPlayer)
const props = withDefaults(
  defineProps<{
    sources: {
      src: string
      type: string
    }[]
    events?: string[]
    refVideoPlayer: string
    videoConfig?: any // videoPlayer其他配置 https://github.com/surmon-china/videojs-player?tab=readme-ov-file
    videoWidth?: string
    videoHeight?: string
    controlConfig?: controlConfigType[] // 不展示的项
    isPrivate?: boolean // 是否私有
    signFun?: Function // 私有调用方法
    ossInstance?: any
    firstPic?: string
    isListAnswer?:boolean
  }>(),
  {
    sources: () => {
      return [{ src: '', type: '' }]
    },
    events: () => {
      return ['fullscreenchange']
    },
    refVideoPlayer: 'videoPlayer',
    videoWidth: '2.7rem',
    isPrivate: true,
    signFun: () => {
      return () => {}
    },
    isListAnswer: false
  }
)
const kkvideoPlayer = ref()
/**
 * 是否已加载
 */
const isLoad = ref<boolean>(false)
// 是否播放
const isPlay = ref<boolean>(false)
const emits = defineEmits<{
  (e: 'isFull', value: boolean): void
  (
    e: 'playeringRef',
    value: {
      playingRef: string
      ref: any
      src: string
    }
  ): void
}>()
// 是否全屏
const isFullStatus = ref<boolean>(false)
/**
 * 全屏
 */
const fullscreenchange = (full: any) => {
  console.log('full', full)
  isFullStatus.value = !isFullStatus.value
  emits('isFull', isFullStatus.value)
}
/**
 * 播放状态
 */
const onPlayerPlay = (el: any) => {
  console.log(el, 'onPlayerPlay')
  isPlay.value = true
  updateCurPlayingMedia({
    playingRef: props.refVideoPlayer,
    option: {
      closePlayer
    }
  })
  emits('playeringRef', {
    playingRef: props.refVideoPlayer,
    ref: kkvideoPlayer,
    src: signSources.value[0].src
  })
}
/**
 * 暂停状态
 */
const onPlayerPause = (el: any) => {
  console.log(el, 'onPlayerPause')
  isPlay.value = false
}
/**
 * 视频播放
 */
const onPlayer = () => {
  if (!props.isPrivate || isLoad.value) {
    isPlay.value = true
    kkvideoPlayer.value.$el.player.play()
  } else {
    asyncSignUrl()
  }
}
/**
 * 暂停播放，并重置进度条
 */
const closePlayer = () => {
  // console.log(kkvideoPlayer.value.$el.player)
  if (!kkvideoPlayer.value?.$el?.player) { return }
  kkvideoPlayer.value.$el.player.pause() // 暂停
  kkvideoPlayer.value.$el.player.src([signSources.value]) // 重置
}
/**
 * 报错
 */
const onError = (e: any) => {
  console.log(e, '========')
  if (e.type === 'error') {
    const videoDom = document.getElementsByClassName(props.refVideoPlayer)[0]
    const errorTip = videoDom.getElementsByClassName(
      'vjs-modal-dialog-content'
    )[0]
    errorTip.textContent =
      '视频加载失败'
  }
}
const signSources = ref<{ src: string; type: string }[]>([
  { src: '', type: '' }
])
const loading = ref<boolean>(false)
const asyncSignUrl = async () => {
  if (!props.isPrivate) {
    signSources.value = props.sources
    return
  }
  if (isLoad.value) { return } // 如果已经加载，无需再次请求
  loading.value = true
  const { result } = await props.signFun(
    props.sources[0].src,
    {},
    props.ossInstance
  )
  if (result?.length) {
    signSources.value[0].src = result[0]
    isLoad.value = true
    loading.value = false
  }
}

/**
 * 弹窗打开
 */
const popOpened = () => {
  kkvideoPlayer.value.$el.player.play()
}
const curTime = ref<number>(0)
/**
 * 弹窗关闭
 */
const popClosed = () => {
  curTime.value = playerState.value.state.currentTime
  isPlay.value = false
}
/**
 * state
 */
const playerState = ref<any>()
const playerMounted = (v:any) => {
  playerState.value = v
  playerState.value.video.currentTime = curTime.value
  console.log(v, 'playerMounted')
}
/**
 * 点击打开弹窗的视频区域
 */
const clickPlayer = () => {
  if (isPlay.value) {
    kkvideoPlayer.value.$el.player.pause()
  } else {
    kkvideoPlayer.value.$el.player.play()
  }
}
onBeforeMount(() => {
  // asyncSignUrl()
})
defineExpose({
  onPlayer,
  closePlayer
})
</script>
<style lang="scss" scoped>
.video_player_wrap {
  position: relative;
  border-radius: 16px;
  overflow: hidden;
  :deep(.vjs-big-play-button) {
    display: none !important;
  }
  :deep(.vjs-control-bar){
    z-index: 10;
  }
  .player-btn-wrap{
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9;
    background: rgba(0, 0, 0, 0.50);
  }

  .player-btn {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 64px;
  }
}

.is_play_btn {
  :deep(.vjs-play-control) {
    display: none !important;
  }
}

.is_panel_btn {
  :deep(.vjs-volume-panel) {
    display: none !important;
  }
}

.is_progress_btn {
  :deep(.vjs-progress-control) {
    display: none !important;
  }
}

.is_remaining_time {
  :deep(.vjs-remaining-time) {
    display: none;
  }
}

.is_playback_btn {
  :deep(.vjs-playback-rate) {
    display: none;
  }
}

.is_picture_btn {
  :deep(.vjs-picture-in-picture-control) {
    display: none;
  }
}

.is_fullscreen_btn {
  :deep(.vjs-fullscreen-control) {
    display: none;
  }
}
.full_rotate{
  transform-origin:50% 50%;
  transform: rotate(90deg) !important;
  :deep(.vjs-control-bar){
    bottom: 20px !important;
  }
}
</style>
