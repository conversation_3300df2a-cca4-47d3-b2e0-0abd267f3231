// Generated by components discovery
declare module 'vue' {
  export interface GlobalComponents {
    'AgreementContent': typeof import("../components/AgreementContent.vue")['default']
    'AnswerBtn': typeof import("../components/AnswerBtn/index.vue")['default']
    'AnswerClassifyPop': typeof import("../components/AnswerClassifyPop/index.vue")['default']
    'AnswerClassifyPopTags': typeof import("../components/AnswerClassifyPop/tags.vue")['default']
    'BottomPop': typeof import("../components/BottomPop.vue")['default']
    'BottomPopAiEntry': typeof import("../components/BottomPopAiEntry.vue")['default']
    'BottomPopNoLogin': typeof import("../components/BottomPopNoLogin.vue")['default']
    'CartMultiCategory': typeof import("../components/CartMultiCategory/index.vue")['default']
    'CheckBoxIcon': typeof import("../components/CheckBoxIcon/index.vue")['default']
    'CodeInput': typeof import("../components/CodeInput/index.vue")['default']
    'CodeInputTypes': typeof import("../components/CodeInput/types")['default']
    'CommonTree': typeof import("../components/CommonTree/index.vue")['default']
    'CommonTreeNode': typeof import("../components/CommonTree/node.vue")['default']
    'CommonTreeUtils': typeof import("../components/CommonTree/utils")['default']
    'CouponCell': typeof import("../components/CouponCell.vue")['default']
    'CourseGoodsSchool': typeof import("../components/CourseGoodsSchool/index.vue")['default']
    'CourseGoodsSpecs': typeof import("../components/CourseGoodsSpecs/index.vue")['default']
    'CustomPageMobile': typeof import("../components/CustomPageMobile.vue")['default']
    'DealDialog': typeof import("../components/DealDialog.vue")['default']
    'GoodCell': typeof import("../components/GoodCell.vue")['default']
    'GoodCellCourseDetail': typeof import("../components/GoodCellCourseDetail.vue")['default']
    'GoodList': typeof import("../components/GoodList.vue")['default']
    'GoodListNoPage': typeof import("../components/GoodListNoPage.vue")['default']
    'GoodsClassTypeAndLabel': typeof import("../components/GoodsClassTypeAndLabel.vue")['default']
    'GoodsPrice': typeof import("../components/GoodsPrice.vue")['default']
    'HomePageTemplate': typeof import("../components/HomePageTemplate.vue")['default']
    'HomeworkModalList': typeof import("../components/HomeworkModal/list.vue")['default']
    'HomeworkModalPlayer': typeof import("../components/HomeworkModal/player.vue")['default']
    'HomeworkModalSubmit': typeof import("../components/HomeworkModal/submit.vue")['default']
    'KKCoupon': typeof import("../components/KKCoupon/index.vue")['default']
    'KKDialog': typeof import("../components/KKDialog/index.vue")['default']
    'KKPopup': typeof import("../components/KKPopup/index.vue")['default']
    'KKPopupTypes': typeof import("../components/KKPopup/types")['default']
    'KKQuestionBank': typeof import("../components/KKQuestionBank.vue")['default']
    'KKRichText': typeof import("../components/KKRichText/index.vue")['default']
    'KKSelectArea': typeof import("../components/KKSelectArea/index.vue")['default']
    'KKSelectAreaPopup': typeof import("../components/KKSelectAreaPopup/index.vue")['default']
    'KefuSelectModal': typeof import("../components/KefuSelectModal/index.vue")['default']
    'KkImageCropperIndexCopy': typeof import("../components/KkImageCropper/index-copy.vue")['default']
    'KkImageCropper': typeof import("../components/KkImageCropper/index.vue")['default']
    'KkImageCropperUseCropHooks': typeof import("../components/KkImageCropper/useCropHooks")['default']
    'KkImageCropperVueCropper': typeof import("../components/KkImageCropper/vueCropper")['default']
    'KkLoading': typeof import("../components/KkLoading.vue")['default']
    'KkVideoPlayer': typeof import("../components/KkVideoPlayer/index.vue")['default']
    'LaunchAd': typeof import("../components/LaunchAd.vue")['default']
    'Logo': typeof import("../components/Logo.vue")['default']
    'MailingCell': typeof import("../components/MailingCell.vue")['default']
    'MaskDialog': typeof import("../components/MaskDialog.vue")['default']
    'MaterialCell': typeof import("../components/MaterialCell.vue")['default']
    'MemberFreeReceivePopup': typeof import("../components/MemberFreeReceivePopup.vue")['default']
    'MemberFreeReceivePopupGoodsCard': typeof import("../components/MemberFreeReceivePopupGoodsCard.vue")['default']
    'NavbarHomeSearch': typeof import("../components/NavbarHome/Search.vue")['default']
    'NavbarHome': typeof import("../components/NavbarHome/index.vue")['default']
    'News99DownloadAppPopup': typeof import("../components/News99DownloadAppPopup.vue")['default']
    'NewsListCell': typeof import("../components/NewsListCell.vue")['default']
    'NewsNavTab': typeof import("../components/NewsNavTab.vue")['default']
    'NumberBox': typeof import("../components/NumberBox/index.vue")['default']
    'OrderCell': typeof import("../components/OrderCell.vue")['default']
    'PopupLearnTarget': typeof import("../components/PopupLearnTarget/index.vue")['default']
    'ProtocolCheck': typeof import("../components/ProtocolCheck/ProtocolCheck.vue")['default']
    'PublicAd': typeof import("../components/PublicAd.vue")['default']
    'SearchTag': typeof import("../components/SearchTag.vue")['default']
    'SharePop': typeof import("../components/SharePop.vue")['default']
    'SharePopup': typeof import("../components/SharePopup/index.vue")['default']
    'SharePoster': typeof import("../components/SharePoster/index.vue")['default']
    'SkeletonMaterial': typeof import("../components/SkeletonMaterial.vue")['default']
    'SlideOperate': typeof import("../components/SlideOperate/index.vue")['default']
    'StarRating': typeof import("../components/StarRating.vue")['default']
    'StudentOrDeainage': typeof import("../components/StudentOrDeainage.vue")['default']
    'StudentOrDrainagePop': typeof import("../components/StudentOrDrainagePop.vue")['default']
    'StudyRequirementAndCommentModal': typeof import("../components/StudyRequirementAndCommentModal.vue")['default']
    'TabbarTemp': typeof import("../components/TabbarTemp.vue")['default']
    'Tabs': typeof import("../components/Tabs.vue")['default']
    'TipTitle': typeof import("../components/TipTitle.vue")['default']
    'TipTitleSearch': typeof import("../components/TipTitleSearch.vue")['default']
    'TopNavbar': typeof import("../components/TopNavbar.vue")['default']
    'TopNavbarHome': typeof import("../components/TopNavbarHome.vue")['default']
    'YueKeItem': typeof import("../components/YueKeItem.vue")['default']
    'AddressItem': typeof import("../components/address/item.vue")['default']
    'AddressSelectPopup': typeof import("../components/address/selectPopup.vue")['default']
    'AnswerQuestionAnswerAi': typeof import("../components/answer-question/answer-ai.vue")['default']
    'AnswerQuestionAnswerCard': typeof import("../components/answer-question/answer-card.vue")['default']
    'AnswerQuestionComponents': typeof import("../components/answer-question/components/index")['default']
    'AnswerQuestionComponentsQuestionAnalysis': typeof import("../components/answer-question/components/questionAnalysis.vue")['default']
    'AnswerQuestionComponentsQuestionAnswer': typeof import("../components/answer-question/components/questionAnswer.vue")['default']
    'AnswerQuestionComponentsQuestionPoint': typeof import("../components/answer-question/components/questionPoint.vue")['default']
    'AnswerQuestionComponentsQuestionStem': typeof import("../components/answer-question/components/questionStem.vue")['default']
    'AnswerQuestionComponentsQuestionTabs': typeof import("../components/answer-question/components/questionTabs.vue")['default']
    'AnswerQuestionComponentsQuestionType': typeof import("../components/answer-question/components/questionType.vue")['default']
    'AnswerQuestionComponentsQuestionsOptions': typeof import("../components/answer-question/components/questionsOptions.vue")['default']
    'AnswerQuestionComponentsSearchAi': typeof import("../components/answer-question/components/search-ai.vue")['default']
    'AnswerQuestionHooksUseAudio': typeof import("../components/answer-question/hooks/useAudio")['default']
    'AnswerQuestionKkChatAudioPlayer': typeof import("../components/answer-question/kkChatAudioPlayer.vue")['default']
    'AnswerQuestionKkChatRichText': typeof import("../components/answer-question/kkChatRichText.vue")['default']
    'AnswerQuestionKkChatSignOssImg': typeof import("../components/answer-question/kkChatSignOssImg.vue")['default']
    'AnswerQuestionKkVideoPlayerDefaultConfig': typeof import("../components/answer-question/kkVideoPlayer/defaultConfig")['default']
    'AnswerQuestionKkVideoPlayer': typeof import("../components/answer-question/kkVideoPlayer/index.vue")['default']
    'AnswerQuestionKkVideoPlayerType': typeof import("../components/answer-question/kkVideoPlayer/type")['default']
    'AnswerQuestionPopTouchImg': typeof import("../components/answer-question/popTouchImg.vue")['default']
    'BuyDIalogSlot': typeof import("../components/buyDIalogSlot.vue")['default']
    'CatalogNodeDesc': typeof import("../components/catalog/NodeDesc.vue")['default']
    'CatalogQuestionTree': typeof import("../components/catalog/QuestionTree.vue")['default']
    'CatalogChapter': typeof import("../components/catalog/chapter.vue")['default']
    'CatalogNode': typeof import("../components/catalog/node.vue")['default']
    'CatalogTree': typeof import("../components/catalog/tree.vue")['default']
    'CatalogType': typeof import("../components/catalog/type")['default']
    'Close': typeof import("../components/close/index.vue")['default']
    'CollapseCheckJieDuanModal': typeof import("../components/collapse/CheckJieDuanModal.vue")['default']
    'CollapseStudyPlanFiles': typeof import("../components/collapse/StudyPlanFiles.vue")['default']
    'CollapseStudyPlanHomework': typeof import("../components/collapse/StudyPlanHomework.vue")['default']
    'CollapseStudyPlanJieDuanTitle': typeof import("../components/collapse/StudyPlanJieDuanTitle.vue")['default']
    'CollapseStudyPlanTitle': typeof import("../components/collapse/StudyPlanTitle.vue")['default']
    'CollapseChapter': typeof import("../components/collapse/chapter.vue")['default']
    'CollapseNode': typeof import("../components/collapse/node.vue")['default']
    'CollapseTree': typeof import("../components/collapse/tree.vue")['default']
    'CollapseType': typeof import("../components/collapse/type")['default']
    'CollectCell': typeof import("../components/collect-cell.vue")['default']
    'CommonHeader': typeof import("../components/commonHeader/index.vue")['default']
    'CourseJieduan': typeof import("../components/course/Jieduan.vue")['default']
    'Empty404': typeof import("../components/empty/404.vue")['default']
    'EmptyLogin': typeof import("../components/empty/login.vue")['default']
    'FloatingWindow': typeof import("../components/floating-window/index.vue")['default']
    'FooterPayBar': typeof import("../components/footerPayBar/index.vue")['default']
    'HomeBaoKao': typeof import("../components/home/<USER>")['default']
    'HomeBeiKao': typeof import("../components/home/<USER>")['default']
    'HomeBooks': typeof import("../components/home/<USER>")['default']
    'HomeBooksTitle': typeof import("../components/home/<USER>")['default']
    'HomeCateIdKuke': typeof import("../components/home/<USER>")['default']
    'HomeCourseCard': typeof import("../components/home/<USER>")['default']
    'HomeHeader': typeof import("../components/home/<USER>")['default']
    'HomePublicAdGongKao': typeof import("../components/home/<USER>/GongKao.vue")['default']
    'HomePublicAdBanner': typeof import("../components/home/<USER>/banner.vue")['default']
    'HomePublicAdZsb': typeof import("../components/home/<USER>/zsb.vue")['default']
    'HomeTeacher': typeof import("../components/home/<USER>")['default']
    'HomeTiKuMaterial': typeof import("../components/home/<USER>")['default']
    'HomeFloatingWidget': typeof import("../components/home/<USER>")['default']
    'HomeIconsZhuxue': typeof import("../components/home/<USER>/Zhuxue.vue")['default']
    'HomeIcons': typeof import("../components/home/<USER>/index.vue")['default']
    'Home': typeof import("../components/home/<USER>")['default']
    'HomeNewsBanner': typeof import("../components/home/<USER>/banner.vue")['default']
    'HomeNewsGongkao': typeof import("../components/home/<USER>/gongkao.vue")['default']
    'HomeNews': typeof import("../components/home/<USER>/index.vue")['default']
    'KkRecommendCourseDetail': typeof import("../components/kkRecommend/CourseDetail.vue")['default']
    'KkRecommendCourseDetail404': typeof import("../components/kkRecommend/CourseDetail404.vue")['default']
    'KkRecommend': typeof import("../components/kkRecommend/index.vue")['default']
    'KkRecommendSearch': typeof import("../components/kkRecommend/search.vue")['default']
    'KkcImage': typeof import("../components/kkcImage/index.vue")['default']
    'LearnCenterHistory': typeof import("../components/learn-center/history.vue")['default']
    'LearnTargetLabelName': typeof import("../components/learn-target-label-name.vue")['default']
    'LearnTargetLabel': typeof import("../components/learn-target-label.vue")['default']
    'LearnTargetButton': typeof import("../components/learn-target/Button.vue")['default']
    'LearnTargetNodeTitle': typeof import("../components/learn-target/NodeTitle.vue")['default']
    'LearnTargetTitle': typeof import("../components/learn-target/title.vue")['default']
    'LoginBtnButton': typeof import("../components/loginBtn/button.vue")['default']
    'MemberCourseDetailPrice': typeof import("../components/member/CourseDetailPrice.vue")['default']
    'MemberCoursePointsRedeemBarMember': typeof import("../components/member/CoursePointsRedeemBarMember.vue")['default']
    'MemberEntryBtn': typeof import("../components/member/EntryBtn.vue")['default']
    'MemberMeEntryBtn': typeof import("../components/member/MeEntryBtn.vue")['default']
    'MemberCenterNavBar': typeof import("../components/memberCenterNavBar/index.vue")['default']
    'MiniLive': typeof import("../components/miniLive/index.vue")['default']
    'NavBar': typeof import("../components/navBar/index.vue")['default']
    'NavBarTypes': typeof import("../components/navBar/types")['default']
    'PopupAd': typeof import("../components/popup-ad/index.vue")['default']
    'QuestionBankAnwserCard': typeof import("../components/question-bank/AnwserCard.vue")['default']
    'QuestionBankCatalogue': typeof import("../components/question-bank/Catalogue.vue")['default']
    'QuestionBankClassifySwitch': typeof import("../components/question-bank/ClassifySwitch.vue")['default']
    'QuestionBankCorrection': typeof import("../components/question-bank/Correction.vue")['default']
    'QuestionBankHeader': typeof import("../components/question-bank/Header.vue")['default']
    'QuestionBankMaterialSwiper': typeof import("../components/question-bank/MaterialSwiper.vue")['default']
    'QuestionBankMultipleChoice': typeof import("../components/question-bank/MultipleChoice.vue")['default']
    'QuestionBankPopupDescription': typeof import("../components/question-bank/PopupDescription.vue")['default']
    'QuestionBankPopupGuide': typeof import("../components/question-bank/PopupGuide.vue")['default']
    'QuestionBankPoster': typeof import("../components/question-bank/Poster.vue")['default']
    'QuestionBankRadioChoice': typeof import("../components/question-bank/RadioChoice.vue")['default']
    'QuestionBankRecorder': typeof import("../components/question-bank/Recorder.vue")['default']
    'QuestionBankShare': typeof import("../components/question-bank/Share.vue")['default']
    'QuestionBankShareGuide': typeof import("../components/question-bank/ShareGuide.vue")['default']
    'QuestionBankTopPart': typeof import("../components/question-bank/TopPart.vue")['default']
    'QuestionBankVideo': typeof import("../components/question-bank/Video.vue")['default']
    'QuestionBankAnalyze': typeof import("../components/question-bank/analyze.vue")['default']
    'QuestionBankAnalyzeAnswer': typeof import("../components/question-bank/analyze/answer.vue")['default']
    'QuestionBankAnalyzeTitle': typeof import("../components/question-bank/analyze/title.vue")['default']
    'QuestionBankFill': typeof import("../components/question-bank/fill.vue")['default']
    'QuestionBankMaterial': typeof import("../components/question-bank/material.vue")['default']
    'QuestionBankShowAnswer': typeof import("../components/question-bank/showAnswer.vue")['default']
    'QuestionBankType': typeof import("../components/question-bank/type")['default']
    'QuestionBankUpload': typeof import("../components/question-bank/upload.vue")['default']
    'RadioCell': typeof import("../components/radioCell/index.vue")['default']
    'Skeleton': typeof import("../components/skeleton.vue")['default']
    'SubmitBar': typeof import("../components/submitBar/index.vue")['default']
    'SubmitBarMaskDialog': typeof import("../components/submitBar/maskDialog.vue")['default']
    'Tabbar': typeof import("../components/tabbar.vue")['default']
    'UsedTime': typeof import("../components/usedTime/index.vue")['default']
    'KKCApplyProducts': typeof import("../../base/components/ApplyProducts/index.vue")['default']
    'KKCAudioPlayer': typeof import("../../base/components/AudioPlayer/index.vue")['default']
    'KKCCollapseTransition': typeof import("../../base/components/CollapseTransition.vue")['default']
    'KKCConfirmDialog': typeof import("../../base/components/ConfirmDialog.vue")['default']
    'KKCCountdown': typeof import("../../base/components/Countdown/index.vue")['default']
    'KKCCountdownUtils': typeof import("../../base/components/Countdown/utils/index")['default']
    'KKCEmptyConfig': typeof import("../../base/components/EmptyConfig.vue")['default']
    'KKCGetAward': typeof import("../../base/components/GetAward/index.vue")['default']
    'KKCGoodsTags': typeof import("../../base/components/GoodsTags/index.vue")['default']
    'KKCHtmlMathJax': typeof import("../../base/components/HtmlMathJax/index.vue")['default']
    'KKCIcon': typeof import("../../base/components/Icon.vue")['default']
    'KkcNuxtLink': typeof import("../../base/components/KkcNuxtLink")['default']
    'KKCLoading': typeof import("../../base/components/Loading/index.vue")['default']
    'KKCOffline': typeof import("../../base/components/Offline.vue")['default']
    'KKCQuestionVideoPlayerCcPlayer': typeof import("../../base/components/QuestionVideoPlayer/ccPlayer/index.vue")['default']
    'KKCQuestionVideoPlayer': typeof import("../../base/components/QuestionVideoPlayer/index.vue")['default']
    'KKCQuestionVideoPlayerPolyvPlayer': typeof import("../../base/components/QuestionVideoPlayer/polyvPlayer/index.vue")['default']
    'KKCQuestionVideoPlayerTypes': typeof import("../../base/components/QuestionVideoPlayer/types")['default']
    'KKCRichTextShadow': typeof import("../../base/components/RichTextShadow/index.vue")['default']
    'KKCTag': typeof import("../../base/components/Tag/index.vue")['default']
    'KKCButtonColours': typeof import("../../base/components/button-colours.vue")['default']
    'KKCButton': typeof import("../../base/components/button/index.vue")['default']
    'KKCCourseAudioPlayerAudioCom': typeof import("../../base/components/courseAudioPlayer/audioCom.vue")['default']
    'KKCCourseAudioPlayer': typeof import("../../base/components/courseAudioPlayer/index.vue")['default']
    'KKCCourseAudioPlayerUseAudioDetail': typeof import("../../base/components/courseAudioPlayer/useAudioDetail")['default']
    'KKCEmpty': typeof import("../../base/components/empty/index.vue")['default']
    'KKCFormHooks': typeof import("../../base/components/form/hooks")['default']
    'KKCForm': typeof import("../../base/components/form/index.vue")['default']
    'KKCInput': typeof import("../../base/components/input/input.vue")['default']
    'KKCLcSprite': typeof import("../../base/components/lc/sprite.vue")['default']
    'KKCMessage': typeof import("../../base/components/message/message.vue")['default']
    'KKCOverlay': typeof import("../../base/components/overlay.vue")['default']
    'KKCPageLoading': typeof import("../../base/components/page-loading.vue")['default']
    'KKCRecordAudio': typeof import("../../base/components/recordAudio/index.vue")['default']
    'KKCValidate': typeof import("../../base/components/validate/index.vue")['default']
    'KKCVideoPlayerCcplayer': typeof import("../../base/components/videoPlayer/ccplayer/index.vue")['default']
    'KKCVideoPlayer': typeof import("../../base/components/videoPlayer/index.vue")['default']
    'KKCVideoPlayerTypes': typeof import("../../base/components/videoPlayer/types")['default']
    'NuxtWelcome': typeof import("../../node_modules/@nuxt/ui-templates/dist/templates/welcome.vue")['default']
    'NuxtLayout': typeof import("../../node_modules/nuxt/dist/app/components/nuxt-layout")['default']
    'NuxtErrorBoundary': typeof import("../../node_modules/nuxt/dist/app/components/nuxt-error-boundary")['default']
    'ClientOnly': typeof import("../../node_modules/nuxt/dist/app/components/client-only")['default']
    'DevOnly': typeof import("../../node_modules/nuxt/dist/app/components/dev-only")['default']
    'ServerPlaceholder': typeof import("../../node_modules/nuxt/dist/app/components/server-placeholder")['default']
    'NuxtLink': typeof import("../../node_modules/nuxt/dist/app/components/nuxt-link")['default']
    'NuxtLoadingIndicator': typeof import("../../node_modules/nuxt/dist/app/components/nuxt-loading-indicator")['default']
    'NuxtIcon': typeof import("../../node_modules/nuxt-icons/dist/runtime/components/nuxt-icon.vue")['default']
    'VanActionBar': typeof import("../../node_modules/vant/es/action-bar/ActionBar")['default']
    'VanActionBarButton': typeof import("../../node_modules/vant/es/action-bar-button/ActionBarButton")['default']
    'VanActionBarIcon': typeof import("../../node_modules/vant/es/action-bar-icon/ActionBarIcon")['default']
    'VanActionSheet': typeof import("../../node_modules/vant/es/action-sheet/ActionSheet")['default']
    'VanAddressEdit': typeof import("../../node_modules/vant/es/address-edit/AddressEdit")['default']
    'VanAddressList': typeof import("../../node_modules/vant/es/address-list/AddressList")['default']
    'VanBackTop': typeof import("../../node_modules/vant/es/back-top/BackTop")['default']
    'VanArea': typeof import("../../node_modules/vant/es/area/Area")['default']
    'VanBadge': typeof import("../../node_modules/vant/es/badge/Badge")['default']
    'VanBarrage': typeof import("../../node_modules/vant/es/barrage/Barrage")['default']
    'VanButton': typeof import("../../node_modules/vant/es/button/Button")['default']
    'VanCalendar': typeof import("../../node_modules/vant/es/calendar/Calendar")['default']
    'VanCard': typeof import("../../node_modules/vant/es/card/Card")['default']
    'VanCascader': typeof import("../../node_modules/vant/es/cascader/Cascader")['default']
    'VanCell': typeof import("../../node_modules/vant/es/cell/Cell")['default']
    'VanCellGroup': typeof import("../../node_modules/vant/es/cell-group/CellGroup")['default']
    'VanCheckbox': typeof import("../../node_modules/vant/es/checkbox/Checkbox")['default']
    'VanCheckboxGroup': typeof import("../../node_modules/vant/es/checkbox-group/CheckboxGroup")['default']
    'VanCircle': typeof import("../../node_modules/vant/es/circle/Circle")['default']
    'VanCol': typeof import("../../node_modules/vant/es/col/Col")['default']
    'VanCollapse': typeof import("../../node_modules/vant/es/collapse/Collapse")['default']
    'VanCollapseItem': typeof import("../../node_modules/vant/es/collapse-item/CollapseItem")['default']
    'VanConfigProvider': typeof import("../../node_modules/vant/es/config-provider/ConfigProvider")['default']
    'VanContactCard': typeof import("../../node_modules/vant/es/contact-card/ContactCard")['default']
    'VanContactEdit': typeof import("../../node_modules/vant/es/contact-edit/ContactEdit")['default']
    'VanContactList': typeof import("../../node_modules/vant/es/contact-list/ContactList")['default']
    'VanCountDown': typeof import("../../node_modules/vant/es/count-down/CountDown")['default']
    'VanCoupon': typeof import("../../node_modules/vant/es/coupon/Coupon")['default']
    'VanCouponCell': typeof import("../../node_modules/vant/es/coupon-cell/CouponCell")['default']
    'VanDatePicker': typeof import("../../node_modules/vant/es/date-picker/DatePicker")['default']
    'VanCouponList': typeof import("../../node_modules/vant/es/coupon-list/CouponList")['default']
    'VanDialog': typeof import("../../node_modules/vant/es/dialog/Dialog")['default']
    'VanDivider': typeof import("../../node_modules/vant/es/divider/Divider")['default']
    'VanDropdownMenu': typeof import("../../node_modules/vant/es/dropdown-menu/DropdownMenu")['default']
    'VanDropdownItem': typeof import("../../node_modules/vant/es/dropdown-item/DropdownItem")['default']
    'VanEmpty': typeof import("../../node_modules/vant/es/empty/Empty")['default']
    'VanField': typeof import("../../node_modules/vant/es/field/Field")['default']
    'VanFloatingBubble': typeof import("../../node_modules/vant/es/floating-bubble/FloatingBubble")['default']
    'VanFloatingPanel': typeof import("../../node_modules/vant/es/floating-panel/FloatingPanel")['default']
    'VanForm': typeof import("../../node_modules/vant/es/form/Form")['default']
    'VanGrid': typeof import("../../node_modules/vant/es/grid/Grid")['default']
    'VanGridItem': typeof import("../../node_modules/vant/es/grid-item/GridItem")['default']
    'VanHighlight': typeof import("../../node_modules/vant/es/highlight/Highlight")['default']
    'VanIcon': typeof import("../../node_modules/vant/es/icon/Icon")['default']
    'VanImage': typeof import("../../node_modules/vant/es/image/Image")['default']
    'VanImagePreview': typeof import("../../node_modules/vant/es/image-preview/ImagePreview")['default']
    'VanIndexAnchor': typeof import("../../node_modules/vant/es/index-anchor/IndexAnchor")['default']
    'VanIndexBar': typeof import("../../node_modules/vant/es/index-bar/IndexBar")['default']
    'VanList': typeof import("../../node_modules/vant/es/list/List")['default']
    'VanLoading': typeof import("../../node_modules/vant/es/loading/Loading")['default']
    'VanNavBar': typeof import("../../node_modules/vant/es/nav-bar/NavBar")['default']
    'VanNoticeBar': typeof import("../../node_modules/vant/es/notice-bar/NoticeBar")['default']
    'VanNumberKeyboard': typeof import("../../node_modules/vant/es/number-keyboard/NumberKeyboard")['default']
    'VanNotify': typeof import("../../node_modules/vant/es/notify/Notify")['default']
    'VanOverlay': typeof import("../../node_modules/vant/es/overlay/Overlay")['default']
    'VanPagination': typeof import("../../node_modules/vant/es/pagination/Pagination")['default']
    'VanPasswordInput': typeof import("../../node_modules/vant/es/password-input/PasswordInput")['default']
    'VanPicker': typeof import("../../node_modules/vant/es/picker/Picker")['default']
    'VanPopover': typeof import("../../node_modules/vant/es/popover/Popover")['default']
    'VanPickerGroup': typeof import("../../node_modules/vant/es/picker-group/PickerGroup")['default']
    'VanPopup': typeof import("../../node_modules/vant/es/popup/Popup")['default']
    'VanProgress': typeof import("../../node_modules/vant/es/progress/Progress")['default']
    'VanPullRefresh': typeof import("../../node_modules/vant/es/pull-refresh/PullRefresh")['default']
    'VanRadio': typeof import("../../node_modules/vant/es/radio/Radio")['default']
    'VanRadioGroup': typeof import("../../node_modules/vant/es/radio-group/RadioGroup")['default']
    'VanRate': typeof import("../../node_modules/vant/es/rate/Rate")['default']
    'VanRollingText': typeof import("../../node_modules/vant/es/rolling-text/RollingText")['default']
    'VanSearch': typeof import("../../node_modules/vant/es/search/Search")['default']
    'VanRow': typeof import("../../node_modules/vant/es/row/Row")['default']
    'VanShareSheet': typeof import("../../node_modules/vant/es/share-sheet/ShareSheet")['default']
    'VanSidebar': typeof import("../../node_modules/vant/es/sidebar/Sidebar")['default']
    'VanSidebarItem': typeof import("../../node_modules/vant/es/sidebar-item/SidebarItem")['default']
    'VanSignature': typeof import("../../node_modules/vant/es/signature/Signature")['default']
    'VanSkeleton': typeof import("../../node_modules/vant/es/skeleton/Skeleton")['default']
    'VanSkeletonAvatar': typeof import("../../node_modules/vant/es/skeleton-avatar/SkeletonAvatar")['default']
    'VanSkeletonImage': typeof import("../../node_modules/vant/es/skeleton-image/SkeletonImage")['default']
    'VanSkeletonParagraph': typeof import("../../node_modules/vant/es/skeleton-paragraph/SkeletonParagraph")['default']
    'VanSlider': typeof import("../../node_modules/vant/es/slider/Slider")['default']
    'VanSkeletonTitle': typeof import("../../node_modules/vant/es/skeleton-title/SkeletonTitle")['default']
    'VanSpace': typeof import("../../node_modules/vant/es/space/Space")['default']
    'VanStep': typeof import("../../node_modules/vant/es/step/Step")['default']
    'VanStepper': typeof import("../../node_modules/vant/es/stepper/Stepper")['default']
    'VanSteps': typeof import("../../node_modules/vant/es/steps/Steps")['default']
    'VanSticky': typeof import("../../node_modules/vant/es/sticky/Sticky")['default']
    'VanSubmitBar': typeof import("../../node_modules/vant/es/submit-bar/SubmitBar")['default']
    'VanSwipe': typeof import("../../node_modules/vant/es/swipe/Swipe")['default']
    'VanSwipeCell': typeof import("../../node_modules/vant/es/swipe-cell/SwipeCell")['default']
    'VanSwipeItem': typeof import("../../node_modules/vant/es/swipe-item/SwipeItem")['default']
    'VanSwitch': typeof import("../../node_modules/vant/es/switch/Switch")['default']
    'VanTab': typeof import("../../node_modules/vant/es/tab/Tab")['default']
    'VanTabbar': typeof import("../../node_modules/vant/es/tabbar/Tabbar")['default']
    'VanTabbarItem': typeof import("../../node_modules/vant/es/tabbar-item/TabbarItem")['default']
    'VanTabs': typeof import("../../node_modules/vant/es/tabs/Tabs")['default']
    'VanTag': typeof import("../../node_modules/vant/es/tag/Tag")['default']
    'VanTextEllipsis': typeof import("../../node_modules/vant/es/text-ellipsis/TextEllipsis")['default']
    'VanToast': typeof import("../../node_modules/vant/es/toast/Toast")['default']
    'VanUploader': typeof import("../../node_modules/vant/es/uploader/Uploader")['default']
    'VanWatermark': typeof import("../../node_modules/vant/es/watermark/Watermark")['default']
    'VanTimePicker': typeof import("../../node_modules/vant/es/time-picker/TimePicker")['default']
    'VanTreeSelect': typeof import("../../node_modules/vant/es/tree-select/TreeSelect")['default']
    'NuxtPage': typeof import("../../node_modules/nuxt/dist/pages/runtime/page")['default']
    'NoScript': typeof import("../../node_modules/nuxt/dist/head/runtime/components")['NoScript']
    'Link': typeof import("../../node_modules/nuxt/dist/head/runtime/components")['Link']
    'Base': typeof import("../../node_modules/nuxt/dist/head/runtime/components")['Base']
    'Title': typeof import("../../node_modules/nuxt/dist/head/runtime/components")['Title']
    'Meta': typeof import("../../node_modules/nuxt/dist/head/runtime/components")['Meta']
    'Style': typeof import("../../node_modules/nuxt/dist/head/runtime/components")['Style']
    'Head': typeof import("../../node_modules/nuxt/dist/head/runtime/components")['Head']
    'Html': typeof import("../../node_modules/nuxt/dist/head/runtime/components")['Html']
    'Body': typeof import("../../node_modules/nuxt/dist/head/runtime/components")['Body']
    'LazyAgreementContent': typeof import("../components/AgreementContent.vue")['default']
    'LazyAnswerBtn': typeof import("../components/AnswerBtn/index.vue")['default']
    'LazyAnswerClassifyPop': typeof import("../components/AnswerClassifyPop/index.vue")['default']
    'LazyAnswerClassifyPopTags': typeof import("../components/AnswerClassifyPop/tags.vue")['default']
    'LazyBottomPop': typeof import("../components/BottomPop.vue")['default']
    'LazyBottomPopAiEntry': typeof import("../components/BottomPopAiEntry.vue")['default']
    'LazyBottomPopNoLogin': typeof import("../components/BottomPopNoLogin.vue")['default']
    'LazyCartMultiCategory': typeof import("../components/CartMultiCategory/index.vue")['default']
    'LazyCheckBoxIcon': typeof import("../components/CheckBoxIcon/index.vue")['default']
    'LazyCodeInput': typeof import("../components/CodeInput/index.vue")['default']
    'LazyCodeInputTypes': typeof import("../components/CodeInput/types")['default']
    'LazyCommonTree': typeof import("../components/CommonTree/index.vue")['default']
    'LazyCommonTreeNode': typeof import("../components/CommonTree/node.vue")['default']
    'LazyCommonTreeUtils': typeof import("../components/CommonTree/utils")['default']
    'LazyCouponCell': typeof import("../components/CouponCell.vue")['default']
    'LazyCourseGoodsSchool': typeof import("../components/CourseGoodsSchool/index.vue")['default']
    'LazyCourseGoodsSpecs': typeof import("../components/CourseGoodsSpecs/index.vue")['default']
    'LazyCustomPageMobile': typeof import("../components/CustomPageMobile.vue")['default']
    'LazyDealDialog': typeof import("../components/DealDialog.vue")['default']
    'LazyGoodCell': typeof import("../components/GoodCell.vue")['default']
    'LazyGoodCellCourseDetail': typeof import("../components/GoodCellCourseDetail.vue")['default']
    'LazyGoodList': typeof import("../components/GoodList.vue")['default']
    'LazyGoodListNoPage': typeof import("../components/GoodListNoPage.vue")['default']
    'LazyGoodsClassTypeAndLabel': typeof import("../components/GoodsClassTypeAndLabel.vue")['default']
    'LazyGoodsPrice': typeof import("../components/GoodsPrice.vue")['default']
    'LazyHomePageTemplate': typeof import("../components/HomePageTemplate.vue")['default']
    'LazyHomeworkModalList': typeof import("../components/HomeworkModal/list.vue")['default']
    'LazyHomeworkModalPlayer': typeof import("../components/HomeworkModal/player.vue")['default']
    'LazyHomeworkModalSubmit': typeof import("../components/HomeworkModal/submit.vue")['default']
    'LazyKKCoupon': typeof import("../components/KKCoupon/index.vue")['default']
    'LazyKKDialog': typeof import("../components/KKDialog/index.vue")['default']
    'LazyKKPopup': typeof import("../components/KKPopup/index.vue")['default']
    'LazyKKPopupTypes': typeof import("../components/KKPopup/types")['default']
    'LazyKKQuestionBank': typeof import("../components/KKQuestionBank.vue")['default']
    'LazyKKRichText': typeof import("../components/KKRichText/index.vue")['default']
    'LazyKKSelectArea': typeof import("../components/KKSelectArea/index.vue")['default']
    'LazyKKSelectAreaPopup': typeof import("../components/KKSelectAreaPopup/index.vue")['default']
    'LazyKefuSelectModal': typeof import("../components/KefuSelectModal/index.vue")['default']
    'LazyKkImageCropperIndexCopy': typeof import("../components/KkImageCropper/index-copy.vue")['default']
    'LazyKkImageCropper': typeof import("../components/KkImageCropper/index.vue")['default']
    'LazyKkImageCropperUseCropHooks': typeof import("../components/KkImageCropper/useCropHooks")['default']
    'LazyKkImageCropperVueCropper': typeof import("../components/KkImageCropper/vueCropper")['default']
    'LazyKkLoading': typeof import("../components/KkLoading.vue")['default']
    'LazyKkVideoPlayer': typeof import("../components/KkVideoPlayer/index.vue")['default']
    'LazyLaunchAd': typeof import("../components/LaunchAd.vue")['default']
    'LazyLogo': typeof import("../components/Logo.vue")['default']
    'LazyMailingCell': typeof import("../components/MailingCell.vue")['default']
    'LazyMaskDialog': typeof import("../components/MaskDialog.vue")['default']
    'LazyMaterialCell': typeof import("../components/MaterialCell.vue")['default']
    'LazyMemberFreeReceivePopup': typeof import("../components/MemberFreeReceivePopup.vue")['default']
    'LazyMemberFreeReceivePopupGoodsCard': typeof import("../components/MemberFreeReceivePopupGoodsCard.vue")['default']
    'LazyNavbarHomeSearch': typeof import("../components/NavbarHome/Search.vue")['default']
    'LazyNavbarHome': typeof import("../components/NavbarHome/index.vue")['default']
    'LazyNews99DownloadAppPopup': typeof import("../components/News99DownloadAppPopup.vue")['default']
    'LazyNewsListCell': typeof import("../components/NewsListCell.vue")['default']
    'LazyNewsNavTab': typeof import("../components/NewsNavTab.vue")['default']
    'LazyNumberBox': typeof import("../components/NumberBox/index.vue")['default']
    'LazyOrderCell': typeof import("../components/OrderCell.vue")['default']
    'LazyPopupLearnTarget': typeof import("../components/PopupLearnTarget/index.vue")['default']
    'LazyProtocolCheck': typeof import("../components/ProtocolCheck/ProtocolCheck.vue")['default']
    'LazyPublicAd': typeof import("../components/PublicAd.vue")['default']
    'LazySearchTag': typeof import("../components/SearchTag.vue")['default']
    'LazySharePop': typeof import("../components/SharePop.vue")['default']
    'LazySharePopup': typeof import("../components/SharePopup/index.vue")['default']
    'LazySharePoster': typeof import("../components/SharePoster/index.vue")['default']
    'LazySkeletonMaterial': typeof import("../components/SkeletonMaterial.vue")['default']
    'LazySlideOperate': typeof import("../components/SlideOperate/index.vue")['default']
    'LazyStarRating': typeof import("../components/StarRating.vue")['default']
    'LazyStudentOrDeainage': typeof import("../components/StudentOrDeainage.vue")['default']
    'LazyStudentOrDrainagePop': typeof import("../components/StudentOrDrainagePop.vue")['default']
    'LazyStudyRequirementAndCommentModal': typeof import("../components/StudyRequirementAndCommentModal.vue")['default']
    'LazyTabbarTemp': typeof import("../components/TabbarTemp.vue")['default']
    'LazyTabs': typeof import("../components/Tabs.vue")['default']
    'LazyTipTitle': typeof import("../components/TipTitle.vue")['default']
    'LazyTipTitleSearch': typeof import("../components/TipTitleSearch.vue")['default']
    'LazyTopNavbar': typeof import("../components/TopNavbar.vue")['default']
    'LazyTopNavbarHome': typeof import("../components/TopNavbarHome.vue")['default']
    'LazyYueKeItem': typeof import("../components/YueKeItem.vue")['default']
    'LazyAddressItem': typeof import("../components/address/item.vue")['default']
    'LazyAddressSelectPopup': typeof import("../components/address/selectPopup.vue")['default']
    'LazyAnswerQuestionAnswerAi': typeof import("../components/answer-question/answer-ai.vue")['default']
    'LazyAnswerQuestionAnswerCard': typeof import("../components/answer-question/answer-card.vue")['default']
    'LazyAnswerQuestionComponents': typeof import("../components/answer-question/components/index")['default']
    'LazyAnswerQuestionComponentsQuestionAnalysis': typeof import("../components/answer-question/components/questionAnalysis.vue")['default']
    'LazyAnswerQuestionComponentsQuestionAnswer': typeof import("../components/answer-question/components/questionAnswer.vue")['default']
    'LazyAnswerQuestionComponentsQuestionPoint': typeof import("../components/answer-question/components/questionPoint.vue")['default']
    'LazyAnswerQuestionComponentsQuestionStem': typeof import("../components/answer-question/components/questionStem.vue")['default']
    'LazyAnswerQuestionComponentsQuestionTabs': typeof import("../components/answer-question/components/questionTabs.vue")['default']
    'LazyAnswerQuestionComponentsQuestionType': typeof import("../components/answer-question/components/questionType.vue")['default']
    'LazyAnswerQuestionComponentsQuestionsOptions': typeof import("../components/answer-question/components/questionsOptions.vue")['default']
    'LazyAnswerQuestionComponentsSearchAi': typeof import("../components/answer-question/components/search-ai.vue")['default']
    'LazyAnswerQuestionHooksUseAudio': typeof import("../components/answer-question/hooks/useAudio")['default']
    'LazyAnswerQuestionKkChatAudioPlayer': typeof import("../components/answer-question/kkChatAudioPlayer.vue")['default']
    'LazyAnswerQuestionKkChatRichText': typeof import("../components/answer-question/kkChatRichText.vue")['default']
    'LazyAnswerQuestionKkChatSignOssImg': typeof import("../components/answer-question/kkChatSignOssImg.vue")['default']
    'LazyAnswerQuestionKkVideoPlayerDefaultConfig': typeof import("../components/answer-question/kkVideoPlayer/defaultConfig")['default']
    'LazyAnswerQuestionKkVideoPlayer': typeof import("../components/answer-question/kkVideoPlayer/index.vue")['default']
    'LazyAnswerQuestionKkVideoPlayerType': typeof import("../components/answer-question/kkVideoPlayer/type")['default']
    'LazyAnswerQuestionPopTouchImg': typeof import("../components/answer-question/popTouchImg.vue")['default']
    'LazyBuyDIalogSlot': typeof import("../components/buyDIalogSlot.vue")['default']
    'LazyCatalogNodeDesc': typeof import("../components/catalog/NodeDesc.vue")['default']
    'LazyCatalogQuestionTree': typeof import("../components/catalog/QuestionTree.vue")['default']
    'LazyCatalogChapter': typeof import("../components/catalog/chapter.vue")['default']
    'LazyCatalogNode': typeof import("../components/catalog/node.vue")['default']
    'LazyCatalogTree': typeof import("../components/catalog/tree.vue")['default']
    'LazyCatalogType': typeof import("../components/catalog/type")['default']
    'LazyClose': typeof import("../components/close/index.vue")['default']
    'LazyCollapseCheckJieDuanModal': typeof import("../components/collapse/CheckJieDuanModal.vue")['default']
    'LazyCollapseStudyPlanFiles': typeof import("../components/collapse/StudyPlanFiles.vue")['default']
    'LazyCollapseStudyPlanHomework': typeof import("../components/collapse/StudyPlanHomework.vue")['default']
    'LazyCollapseStudyPlanJieDuanTitle': typeof import("../components/collapse/StudyPlanJieDuanTitle.vue")['default']
    'LazyCollapseStudyPlanTitle': typeof import("../components/collapse/StudyPlanTitle.vue")['default']
    'LazyCollapseChapter': typeof import("../components/collapse/chapter.vue")['default']
    'LazyCollapseNode': typeof import("../components/collapse/node.vue")['default']
    'LazyCollapseTree': typeof import("../components/collapse/tree.vue")['default']
    'LazyCollapseType': typeof import("../components/collapse/type")['default']
    'LazyCollectCell': typeof import("../components/collect-cell.vue")['default']
    'LazyCommonHeader': typeof import("../components/commonHeader/index.vue")['default']
    'LazyCourseJieduan': typeof import("../components/course/Jieduan.vue")['default']
    'LazyEmpty404': typeof import("../components/empty/404.vue")['default']
    'LazyEmptyLogin': typeof import("../components/empty/login.vue")['default']
    'LazyFloatingWindow': typeof import("../components/floating-window/index.vue")['default']
    'LazyFooterPayBar': typeof import("../components/footerPayBar/index.vue")['default']
    'LazyHomeBaoKao': typeof import("../components/home/<USER>")['default']
    'LazyHomeBeiKao': typeof import("../components/home/<USER>")['default']
    'LazyHomeBooks': typeof import("../components/home/<USER>")['default']
    'LazyHomeBooksTitle': typeof import("../components/home/<USER>")['default']
    'LazyHomeCateIdKuke': typeof import("../components/home/<USER>")['default']
    'LazyHomeCourseCard': typeof import("../components/home/<USER>")['default']
    'LazyHomeHeader': typeof import("../components/home/<USER>")['default']
    'LazyHomePublicAdGongKao': typeof import("../components/home/<USER>/GongKao.vue")['default']
    'LazyHomePublicAdBanner': typeof import("../components/home/<USER>/banner.vue")['default']
    'LazyHomePublicAdZsb': typeof import("../components/home/<USER>/zsb.vue")['default']
    'LazyHomeTeacher': typeof import("../components/home/<USER>")['default']
    'LazyHomeTiKuMaterial': typeof import("../components/home/<USER>")['default']
    'LazyHomeFloatingWidget': typeof import("../components/home/<USER>")['default']
    'LazyHomeIconsZhuxue': typeof import("../components/home/<USER>/Zhuxue.vue")['default']
    'LazyHomeIcons': typeof import("../components/home/<USER>/index.vue")['default']
    'LazyHome': typeof import("../components/home/<USER>")['default']
    'LazyHomeNewsBanner': typeof import("../components/home/<USER>/banner.vue")['default']
    'LazyHomeNewsGongkao': typeof import("../components/home/<USER>/gongkao.vue")['default']
    'LazyHomeNews': typeof import("../components/home/<USER>/index.vue")['default']
    'LazyKkRecommendCourseDetail': typeof import("../components/kkRecommend/CourseDetail.vue")['default']
    'LazyKkRecommendCourseDetail404': typeof import("../components/kkRecommend/CourseDetail404.vue")['default']
    'LazyKkRecommend': typeof import("../components/kkRecommend/index.vue")['default']
    'LazyKkRecommendSearch': typeof import("../components/kkRecommend/search.vue")['default']
    'LazyKkcImage': typeof import("../components/kkcImage/index.vue")['default']
    'LazyLearnCenterHistory': typeof import("../components/learn-center/history.vue")['default']
    'LazyLearnTargetLabelName': typeof import("../components/learn-target-label-name.vue")['default']
    'LazyLearnTargetLabel': typeof import("../components/learn-target-label.vue")['default']
    'LazyLearnTargetButton': typeof import("../components/learn-target/Button.vue")['default']
    'LazyLearnTargetNodeTitle': typeof import("../components/learn-target/NodeTitle.vue")['default']
    'LazyLearnTargetTitle': typeof import("../components/learn-target/title.vue")['default']
    'LazyLoginBtnButton': typeof import("../components/loginBtn/button.vue")['default']
    'LazyMemberCourseDetailPrice': typeof import("../components/member/CourseDetailPrice.vue")['default']
    'LazyMemberCoursePointsRedeemBarMember': typeof import("../components/member/CoursePointsRedeemBarMember.vue")['default']
    'LazyMemberEntryBtn': typeof import("../components/member/EntryBtn.vue")['default']
    'LazyMemberMeEntryBtn': typeof import("../components/member/MeEntryBtn.vue")['default']
    'LazyMemberCenterNavBar': typeof import("../components/memberCenterNavBar/index.vue")['default']
    'LazyMiniLive': typeof import("../components/miniLive/index.vue")['default']
    'LazyNavBar': typeof import("../components/navBar/index.vue")['default']
    'LazyNavBarTypes': typeof import("../components/navBar/types")['default']
    'LazyPopupAd': typeof import("../components/popup-ad/index.vue")['default']
    'LazyQuestionBankAnwserCard': typeof import("../components/question-bank/AnwserCard.vue")['default']
    'LazyQuestionBankCatalogue': typeof import("../components/question-bank/Catalogue.vue")['default']
    'LazyQuestionBankClassifySwitch': typeof import("../components/question-bank/ClassifySwitch.vue")['default']
    'LazyQuestionBankCorrection': typeof import("../components/question-bank/Correction.vue")['default']
    'LazyQuestionBankHeader': typeof import("../components/question-bank/Header.vue")['default']
    'LazyQuestionBankMaterialSwiper': typeof import("../components/question-bank/MaterialSwiper.vue")['default']
    'LazyQuestionBankMultipleChoice': typeof import("../components/question-bank/MultipleChoice.vue")['default']
    'LazyQuestionBankPopupDescription': typeof import("../components/question-bank/PopupDescription.vue")['default']
    'LazyQuestionBankPopupGuide': typeof import("../components/question-bank/PopupGuide.vue")['default']
    'LazyQuestionBankPoster': typeof import("../components/question-bank/Poster.vue")['default']
    'LazyQuestionBankRadioChoice': typeof import("../components/question-bank/RadioChoice.vue")['default']
    'LazyQuestionBankRecorder': typeof import("../components/question-bank/Recorder.vue")['default']
    'LazyQuestionBankShare': typeof import("../components/question-bank/Share.vue")['default']
    'LazyQuestionBankShareGuide': typeof import("../components/question-bank/ShareGuide.vue")['default']
    'LazyQuestionBankTopPart': typeof import("../components/question-bank/TopPart.vue")['default']
    'LazyQuestionBankVideo': typeof import("../components/question-bank/Video.vue")['default']
    'LazyQuestionBankAnalyze': typeof import("../components/question-bank/analyze.vue")['default']
    'LazyQuestionBankAnalyzeAnswer': typeof import("../components/question-bank/analyze/answer.vue")['default']
    'LazyQuestionBankAnalyzeTitle': typeof import("../components/question-bank/analyze/title.vue")['default']
    'LazyQuestionBankFill': typeof import("../components/question-bank/fill.vue")['default']
    'LazyQuestionBankMaterial': typeof import("../components/question-bank/material.vue")['default']
    'LazyQuestionBankShowAnswer': typeof import("../components/question-bank/showAnswer.vue")['default']
    'LazyQuestionBankType': typeof import("../components/question-bank/type")['default']
    'LazyQuestionBankUpload': typeof import("../components/question-bank/upload.vue")['default']
    'LazyRadioCell': typeof import("../components/radioCell/index.vue")['default']
    'LazySkeleton': typeof import("../components/skeleton.vue")['default']
    'LazySubmitBar': typeof import("../components/submitBar/index.vue")['default']
    'LazySubmitBarMaskDialog': typeof import("../components/submitBar/maskDialog.vue")['default']
    'LazyTabbar': typeof import("../components/tabbar.vue")['default']
    'LazyUsedTime': typeof import("../components/usedTime/index.vue")['default']
    'LazyKKCApplyProducts': typeof import("../../base/components/ApplyProducts/index.vue")['default']
    'LazyKKCAudioPlayer': typeof import("../../base/components/AudioPlayer/index.vue")['default']
    'LazyKKCCollapseTransition': typeof import("../../base/components/CollapseTransition.vue")['default']
    'LazyKKCConfirmDialog': typeof import("../../base/components/ConfirmDialog.vue")['default']
    'LazyKKCCountdown': typeof import("../../base/components/Countdown/index.vue")['default']
    'LazyKKCCountdownUtils': typeof import("../../base/components/Countdown/utils/index")['default']
    'LazyKKCEmptyConfig': typeof import("../../base/components/EmptyConfig.vue")['default']
    'LazyKKCGetAward': typeof import("../../base/components/GetAward/index.vue")['default']
    'LazyKKCGoodsTags': typeof import("../../base/components/GoodsTags/index.vue")['default']
    'LazyKKCHtmlMathJax': typeof import("../../base/components/HtmlMathJax/index.vue")['default']
    'LazyKKCIcon': typeof import("../../base/components/Icon.vue")['default']
    'LazyKkcNuxtLink': typeof import("../../base/components/KkcNuxtLink")['default']
    'LazyKKCLoading': typeof import("../../base/components/Loading/index.vue")['default']
    'LazyKKCOffline': typeof import("../../base/components/Offline.vue")['default']
    'LazyKKCQuestionVideoPlayerCcPlayer': typeof import("../../base/components/QuestionVideoPlayer/ccPlayer/index.vue")['default']
    'LazyKKCQuestionVideoPlayer': typeof import("../../base/components/QuestionVideoPlayer/index.vue")['default']
    'LazyKKCQuestionVideoPlayerPolyvPlayer': typeof import("../../base/components/QuestionVideoPlayer/polyvPlayer/index.vue")['default']
    'LazyKKCQuestionVideoPlayerTypes': typeof import("../../base/components/QuestionVideoPlayer/types")['default']
    'LazyKKCRichTextShadow': typeof import("../../base/components/RichTextShadow/index.vue")['default']
    'LazyKKCTag': typeof import("../../base/components/Tag/index.vue")['default']
    'LazyKKCButtonColours': typeof import("../../base/components/button-colours.vue")['default']
    'LazyKKCButton': typeof import("../../base/components/button/index.vue")['default']
    'LazyKKCCourseAudioPlayerAudioCom': typeof import("../../base/components/courseAudioPlayer/audioCom.vue")['default']
    'LazyKKCCourseAudioPlayer': typeof import("../../base/components/courseAudioPlayer/index.vue")['default']
    'LazyKKCCourseAudioPlayerUseAudioDetail': typeof import("../../base/components/courseAudioPlayer/useAudioDetail")['default']
    'LazyKKCEmpty': typeof import("../../base/components/empty/index.vue")['default']
    'LazyKKCFormHooks': typeof import("../../base/components/form/hooks")['default']
    'LazyKKCForm': typeof import("../../base/components/form/index.vue")['default']
    'LazyKKCInput': typeof import("../../base/components/input/input.vue")['default']
    'LazyKKCLcSprite': typeof import("../../base/components/lc/sprite.vue")['default']
    'LazyKKCMessage': typeof import("../../base/components/message/message.vue")['default']
    'LazyKKCOverlay': typeof import("../../base/components/overlay.vue")['default']
    'LazyKKCPageLoading': typeof import("../../base/components/page-loading.vue")['default']
    'LazyKKCRecordAudio': typeof import("../../base/components/recordAudio/index.vue")['default']
    'LazyKKCValidate': typeof import("../../base/components/validate/index.vue")['default']
    'LazyKKCVideoPlayerCcplayer': typeof import("../../base/components/videoPlayer/ccplayer/index.vue")['default']
    'LazyKKCVideoPlayer': typeof import("../../base/components/videoPlayer/index.vue")['default']
    'LazyKKCVideoPlayerTypes': typeof import("../../base/components/videoPlayer/types")['default']
    'LazyNuxtWelcome': typeof import("../../node_modules/@nuxt/ui-templates/dist/templates/welcome.vue")['default']
    'LazyNuxtLayout': typeof import("../../node_modules/nuxt/dist/app/components/nuxt-layout")['default']
    'LazyNuxtErrorBoundary': typeof import("../../node_modules/nuxt/dist/app/components/nuxt-error-boundary")['default']
    'LazyClientOnly': typeof import("../../node_modules/nuxt/dist/app/components/client-only")['default']
    'LazyDevOnly': typeof import("../../node_modules/nuxt/dist/app/components/dev-only")['default']
    'LazyServerPlaceholder': typeof import("../../node_modules/nuxt/dist/app/components/server-placeholder")['default']
    'LazyNuxtLink': typeof import("../../node_modules/nuxt/dist/app/components/nuxt-link")['default']
    'LazyNuxtLoadingIndicator': typeof import("../../node_modules/nuxt/dist/app/components/nuxt-loading-indicator")['default']
    'LazyNuxtIcon': typeof import("../../node_modules/nuxt-icons/dist/runtime/components/nuxt-icon.vue")['default']
    'LazyVanActionBar': typeof import("../../node_modules/vant/es/action-bar/ActionBar")['default']
    'LazyVanActionBarButton': typeof import("../../node_modules/vant/es/action-bar-button/ActionBarButton")['default']
    'LazyVanActionBarIcon': typeof import("../../node_modules/vant/es/action-bar-icon/ActionBarIcon")['default']
    'LazyVanActionSheet': typeof import("../../node_modules/vant/es/action-sheet/ActionSheet")['default']
    'LazyVanAddressEdit': typeof import("../../node_modules/vant/es/address-edit/AddressEdit")['default']
    'LazyVanAddressList': typeof import("../../node_modules/vant/es/address-list/AddressList")['default']
    'LazyVanBackTop': typeof import("../../node_modules/vant/es/back-top/BackTop")['default']
    'LazyVanArea': typeof import("../../node_modules/vant/es/area/Area")['default']
    'LazyVanBadge': typeof import("../../node_modules/vant/es/badge/Badge")['default']
    'LazyVanBarrage': typeof import("../../node_modules/vant/es/barrage/Barrage")['default']
    'LazyVanButton': typeof import("../../node_modules/vant/es/button/Button")['default']
    'LazyVanCalendar': typeof import("../../node_modules/vant/es/calendar/Calendar")['default']
    'LazyVanCard': typeof import("../../node_modules/vant/es/card/Card")['default']
    'LazyVanCascader': typeof import("../../node_modules/vant/es/cascader/Cascader")['default']
    'LazyVanCell': typeof import("../../node_modules/vant/es/cell/Cell")['default']
    'LazyVanCellGroup': typeof import("../../node_modules/vant/es/cell-group/CellGroup")['default']
    'LazyVanCheckbox': typeof import("../../node_modules/vant/es/checkbox/Checkbox")['default']
    'LazyVanCheckboxGroup': typeof import("../../node_modules/vant/es/checkbox-group/CheckboxGroup")['default']
    'LazyVanCircle': typeof import("../../node_modules/vant/es/circle/Circle")['default']
    'LazyVanCol': typeof import("../../node_modules/vant/es/col/Col")['default']
    'LazyVanCollapse': typeof import("../../node_modules/vant/es/collapse/Collapse")['default']
    'LazyVanCollapseItem': typeof import("../../node_modules/vant/es/collapse-item/CollapseItem")['default']
    'LazyVanConfigProvider': typeof import("../../node_modules/vant/es/config-provider/ConfigProvider")['default']
    'LazyVanContactCard': typeof import("../../node_modules/vant/es/contact-card/ContactCard")['default']
    'LazyVanContactEdit': typeof import("../../node_modules/vant/es/contact-edit/ContactEdit")['default']
    'LazyVanContactList': typeof import("../../node_modules/vant/es/contact-list/ContactList")['default']
    'LazyVanCountDown': typeof import("../../node_modules/vant/es/count-down/CountDown")['default']
    'LazyVanCoupon': typeof import("../../node_modules/vant/es/coupon/Coupon")['default']
    'LazyVanCouponCell': typeof import("../../node_modules/vant/es/coupon-cell/CouponCell")['default']
    'LazyVanDatePicker': typeof import("../../node_modules/vant/es/date-picker/DatePicker")['default']
    'LazyVanCouponList': typeof import("../../node_modules/vant/es/coupon-list/CouponList")['default']
    'LazyVanDialog': typeof import("../../node_modules/vant/es/dialog/Dialog")['default']
    'LazyVanDivider': typeof import("../../node_modules/vant/es/divider/Divider")['default']
    'LazyVanDropdownMenu': typeof import("../../node_modules/vant/es/dropdown-menu/DropdownMenu")['default']
    'LazyVanDropdownItem': typeof import("../../node_modules/vant/es/dropdown-item/DropdownItem")['default']
    'LazyVanEmpty': typeof import("../../node_modules/vant/es/empty/Empty")['default']
    'LazyVanField': typeof import("../../node_modules/vant/es/field/Field")['default']
    'LazyVanFloatingBubble': typeof import("../../node_modules/vant/es/floating-bubble/FloatingBubble")['default']
    'LazyVanFloatingPanel': typeof import("../../node_modules/vant/es/floating-panel/FloatingPanel")['default']
    'LazyVanForm': typeof import("../../node_modules/vant/es/form/Form")['default']
    'LazyVanGrid': typeof import("../../node_modules/vant/es/grid/Grid")['default']
    'LazyVanGridItem': typeof import("../../node_modules/vant/es/grid-item/GridItem")['default']
    'LazyVanHighlight': typeof import("../../node_modules/vant/es/highlight/Highlight")['default']
    'LazyVanIcon': typeof import("../../node_modules/vant/es/icon/Icon")['default']
    'LazyVanImage': typeof import("../../node_modules/vant/es/image/Image")['default']
    'LazyVanImagePreview': typeof import("../../node_modules/vant/es/image-preview/ImagePreview")['default']
    'LazyVanIndexAnchor': typeof import("../../node_modules/vant/es/index-anchor/IndexAnchor")['default']
    'LazyVanIndexBar': typeof import("../../node_modules/vant/es/index-bar/IndexBar")['default']
    'LazyVanList': typeof import("../../node_modules/vant/es/list/List")['default']
    'LazyVanLoading': typeof import("../../node_modules/vant/es/loading/Loading")['default']
    'LazyVanNavBar': typeof import("../../node_modules/vant/es/nav-bar/NavBar")['default']
    'LazyVanNoticeBar': typeof import("../../node_modules/vant/es/notice-bar/NoticeBar")['default']
    'LazyVanNumberKeyboard': typeof import("../../node_modules/vant/es/number-keyboard/NumberKeyboard")['default']
    'LazyVanNotify': typeof import("../../node_modules/vant/es/notify/Notify")['default']
    'LazyVanOverlay': typeof import("../../node_modules/vant/es/overlay/Overlay")['default']
    'LazyVanPagination': typeof import("../../node_modules/vant/es/pagination/Pagination")['default']
    'LazyVanPasswordInput': typeof import("../../node_modules/vant/es/password-input/PasswordInput")['default']
    'LazyVanPicker': typeof import("../../node_modules/vant/es/picker/Picker")['default']
    'LazyVanPopover': typeof import("../../node_modules/vant/es/popover/Popover")['default']
    'LazyVanPickerGroup': typeof import("../../node_modules/vant/es/picker-group/PickerGroup")['default']
    'LazyVanPopup': typeof import("../../node_modules/vant/es/popup/Popup")['default']
    'LazyVanProgress': typeof import("../../node_modules/vant/es/progress/Progress")['default']
    'LazyVanPullRefresh': typeof import("../../node_modules/vant/es/pull-refresh/PullRefresh")['default']
    'LazyVanRadio': typeof import("../../node_modules/vant/es/radio/Radio")['default']
    'LazyVanRadioGroup': typeof import("../../node_modules/vant/es/radio-group/RadioGroup")['default']
    'LazyVanRate': typeof import("../../node_modules/vant/es/rate/Rate")['default']
    'LazyVanRollingText': typeof import("../../node_modules/vant/es/rolling-text/RollingText")['default']
    'LazyVanSearch': typeof import("../../node_modules/vant/es/search/Search")['default']
    'LazyVanRow': typeof import("../../node_modules/vant/es/row/Row")['default']
    'LazyVanShareSheet': typeof import("../../node_modules/vant/es/share-sheet/ShareSheet")['default']
    'LazyVanSidebar': typeof import("../../node_modules/vant/es/sidebar/Sidebar")['default']
    'LazyVanSidebarItem': typeof import("../../node_modules/vant/es/sidebar-item/SidebarItem")['default']
    'LazyVanSignature': typeof import("../../node_modules/vant/es/signature/Signature")['default']
    'LazyVanSkeleton': typeof import("../../node_modules/vant/es/skeleton/Skeleton")['default']
    'LazyVanSkeletonAvatar': typeof import("../../node_modules/vant/es/skeleton-avatar/SkeletonAvatar")['default']
    'LazyVanSkeletonImage': typeof import("../../node_modules/vant/es/skeleton-image/SkeletonImage")['default']
    'LazyVanSkeletonParagraph': typeof import("../../node_modules/vant/es/skeleton-paragraph/SkeletonParagraph")['default']
    'LazyVanSlider': typeof import("../../node_modules/vant/es/slider/Slider")['default']
    'LazyVanSkeletonTitle': typeof import("../../node_modules/vant/es/skeleton-title/SkeletonTitle")['default']
    'LazyVanSpace': typeof import("../../node_modules/vant/es/space/Space")['default']
    'LazyVanStep': typeof import("../../node_modules/vant/es/step/Step")['default']
    'LazyVanStepper': typeof import("../../node_modules/vant/es/stepper/Stepper")['default']
    'LazyVanSteps': typeof import("../../node_modules/vant/es/steps/Steps")['default']
    'LazyVanSticky': typeof import("../../node_modules/vant/es/sticky/Sticky")['default']
    'LazyVanSubmitBar': typeof import("../../node_modules/vant/es/submit-bar/SubmitBar")['default']
    'LazyVanSwipe': typeof import("../../node_modules/vant/es/swipe/Swipe")['default']
    'LazyVanSwipeCell': typeof import("../../node_modules/vant/es/swipe-cell/SwipeCell")['default']
    'LazyVanSwipeItem': typeof import("../../node_modules/vant/es/swipe-item/SwipeItem")['default']
    'LazyVanSwitch': typeof import("../../node_modules/vant/es/switch/Switch")['default']
    'LazyVanTab': typeof import("../../node_modules/vant/es/tab/Tab")['default']
    'LazyVanTabbar': typeof import("../../node_modules/vant/es/tabbar/Tabbar")['default']
    'LazyVanTabbarItem': typeof import("../../node_modules/vant/es/tabbar-item/TabbarItem")['default']
    'LazyVanTabs': typeof import("../../node_modules/vant/es/tabs/Tabs")['default']
    'LazyVanTag': typeof import("../../node_modules/vant/es/tag/Tag")['default']
    'LazyVanTextEllipsis': typeof import("../../node_modules/vant/es/text-ellipsis/TextEllipsis")['default']
    'LazyVanToast': typeof import("../../node_modules/vant/es/toast/Toast")['default']
    'LazyVanUploader': typeof import("../../node_modules/vant/es/uploader/Uploader")['default']
    'LazyVanWatermark': typeof import("../../node_modules/vant/es/watermark/Watermark")['default']
    'LazyVanTimePicker': typeof import("../../node_modules/vant/es/time-picker/TimePicker")['default']
    'LazyVanTreeSelect': typeof import("../../node_modules/vant/es/tree-select/TreeSelect")['default']
    'LazyNuxtPage': typeof import("../../node_modules/nuxt/dist/pages/runtime/page")['default']
    'LazyNoScript': typeof import("../../node_modules/nuxt/dist/head/runtime/components")['NoScript']
    'LazyLink': typeof import("../../node_modules/nuxt/dist/head/runtime/components")['Link']
    'LazyBase': typeof import("../../node_modules/nuxt/dist/head/runtime/components")['Base']
    'LazyTitle': typeof import("../../node_modules/nuxt/dist/head/runtime/components")['Title']
    'LazyMeta': typeof import("../../node_modules/nuxt/dist/head/runtime/components")['Meta']
    'LazyStyle': typeof import("../../node_modules/nuxt/dist/head/runtime/components")['Style']
    'LazyHead': typeof import("../../node_modules/nuxt/dist/head/runtime/components")['Head']
    'LazyHtml': typeof import("../../node_modules/nuxt/dist/head/runtime/components")['Html']
    'LazyBody': typeof import("../../node_modules/nuxt/dist/head/runtime/components")['Body']
  }
}

export const AgreementContent: typeof import("../components/AgreementContent.vue")['default']
export const AnswerBtn: typeof import("../components/AnswerBtn/index.vue")['default']
export const AnswerClassifyPop: typeof import("../components/AnswerClassifyPop/index.vue")['default']
export const AnswerClassifyPopTags: typeof import("../components/AnswerClassifyPop/tags.vue")['default']
export const BottomPop: typeof import("../components/BottomPop.vue")['default']
export const BottomPopAiEntry: typeof import("../components/BottomPopAiEntry.vue")['default']
export const BottomPopNoLogin: typeof import("../components/BottomPopNoLogin.vue")['default']
export const CartMultiCategory: typeof import("../components/CartMultiCategory/index.vue")['default']
export const CheckBoxIcon: typeof import("../components/CheckBoxIcon/index.vue")['default']
export const CodeInput: typeof import("../components/CodeInput/index.vue")['default']
export const CodeInputTypes: typeof import("../components/CodeInput/types")['default']
export const CommonTree: typeof import("../components/CommonTree/index.vue")['default']
export const CommonTreeNode: typeof import("../components/CommonTree/node.vue")['default']
export const CommonTreeUtils: typeof import("../components/CommonTree/utils")['default']
export const CouponCell: typeof import("../components/CouponCell.vue")['default']
export const CourseGoodsSchool: typeof import("../components/CourseGoodsSchool/index.vue")['default']
export const CourseGoodsSpecs: typeof import("../components/CourseGoodsSpecs/index.vue")['default']
export const CustomPageMobile: typeof import("../components/CustomPageMobile.vue")['default']
export const DealDialog: typeof import("../components/DealDialog.vue")['default']
export const GoodCell: typeof import("../components/GoodCell.vue")['default']
export const GoodCellCourseDetail: typeof import("../components/GoodCellCourseDetail.vue")['default']
export const GoodList: typeof import("../components/GoodList.vue")['default']
export const GoodListNoPage: typeof import("../components/GoodListNoPage.vue")['default']
export const GoodsClassTypeAndLabel: typeof import("../components/GoodsClassTypeAndLabel.vue")['default']
export const GoodsPrice: typeof import("../components/GoodsPrice.vue")['default']
export const HomePageTemplate: typeof import("../components/HomePageTemplate.vue")['default']
export const HomeworkModalList: typeof import("../components/HomeworkModal/list.vue")['default']
export const HomeworkModalPlayer: typeof import("../components/HomeworkModal/player.vue")['default']
export const HomeworkModalSubmit: typeof import("../components/HomeworkModal/submit.vue")['default']
export const KKCoupon: typeof import("../components/KKCoupon/index.vue")['default']
export const KKDialog: typeof import("../components/KKDialog/index.vue")['default']
export const KKPopup: typeof import("../components/KKPopup/index.vue")['default']
export const KKPopupTypes: typeof import("../components/KKPopup/types")['default']
export const KKQuestionBank: typeof import("../components/KKQuestionBank.vue")['default']
export const KKRichText: typeof import("../components/KKRichText/index.vue")['default']
export const KKSelectArea: typeof import("../components/KKSelectArea/index.vue")['default']
export const KKSelectAreaPopup: typeof import("../components/KKSelectAreaPopup/index.vue")['default']
export const KefuSelectModal: typeof import("../components/KefuSelectModal/index.vue")['default']
export const KkImageCropperIndexCopy: typeof import("../components/KkImageCropper/index-copy.vue")['default']
export const KkImageCropper: typeof import("../components/KkImageCropper/index.vue")['default']
export const KkImageCropperUseCropHooks: typeof import("../components/KkImageCropper/useCropHooks")['default']
export const KkImageCropperVueCropper: typeof import("../components/KkImageCropper/vueCropper")['default']
export const KkLoading: typeof import("../components/KkLoading.vue")['default']
export const KkVideoPlayer: typeof import("../components/KkVideoPlayer/index.vue")['default']
export const LaunchAd: typeof import("../components/LaunchAd.vue")['default']
export const Logo: typeof import("../components/Logo.vue")['default']
export const MailingCell: typeof import("../components/MailingCell.vue")['default']
export const MaskDialog: typeof import("../components/MaskDialog.vue")['default']
export const MaterialCell: typeof import("../components/MaterialCell.vue")['default']
export const MemberFreeReceivePopup: typeof import("../components/MemberFreeReceivePopup.vue")['default']
export const MemberFreeReceivePopupGoodsCard: typeof import("../components/MemberFreeReceivePopupGoodsCard.vue")['default']
export const NavbarHomeSearch: typeof import("../components/NavbarHome/Search.vue")['default']
export const NavbarHome: typeof import("../components/NavbarHome/index.vue")['default']
export const News99DownloadAppPopup: typeof import("../components/News99DownloadAppPopup.vue")['default']
export const NewsListCell: typeof import("../components/NewsListCell.vue")['default']
export const NewsNavTab: typeof import("../components/NewsNavTab.vue")['default']
export const NumberBox: typeof import("../components/NumberBox/index.vue")['default']
export const OrderCell: typeof import("../components/OrderCell.vue")['default']
export const PopupLearnTarget: typeof import("../components/PopupLearnTarget/index.vue")['default']
export const ProtocolCheck: typeof import("../components/ProtocolCheck/ProtocolCheck.vue")['default']
export const PublicAd: typeof import("../components/PublicAd.vue")['default']
export const SearchTag: typeof import("../components/SearchTag.vue")['default']
export const SharePop: typeof import("../components/SharePop.vue")['default']
export const SharePopup: typeof import("../components/SharePopup/index.vue")['default']
export const SharePoster: typeof import("../components/SharePoster/index.vue")['default']
export const SkeletonMaterial: typeof import("../components/SkeletonMaterial.vue")['default']
export const SlideOperate: typeof import("../components/SlideOperate/index.vue")['default']
export const StarRating: typeof import("../components/StarRating.vue")['default']
export const StudentOrDeainage: typeof import("../components/StudentOrDeainage.vue")['default']
export const StudentOrDrainagePop: typeof import("../components/StudentOrDrainagePop.vue")['default']
export const StudyRequirementAndCommentModal: typeof import("../components/StudyRequirementAndCommentModal.vue")['default']
export const TabbarTemp: typeof import("../components/TabbarTemp.vue")['default']
export const Tabs: typeof import("../components/Tabs.vue")['default']
export const TipTitle: typeof import("../components/TipTitle.vue")['default']
export const TipTitleSearch: typeof import("../components/TipTitleSearch.vue")['default']
export const TopNavbar: typeof import("../components/TopNavbar.vue")['default']
export const TopNavbarHome: typeof import("../components/TopNavbarHome.vue")['default']
export const YueKeItem: typeof import("../components/YueKeItem.vue")['default']
export const AddressItem: typeof import("../components/address/item.vue")['default']
export const AddressSelectPopup: typeof import("../components/address/selectPopup.vue")['default']
export const AnswerQuestionAnswerAi: typeof import("../components/answer-question/answer-ai.vue")['default']
export const AnswerQuestionAnswerCard: typeof import("../components/answer-question/answer-card.vue")['default']
export const AnswerQuestionComponents: typeof import("../components/answer-question/components/index")['default']
export const AnswerQuestionComponentsQuestionAnalysis: typeof import("../components/answer-question/components/questionAnalysis.vue")['default']
export const AnswerQuestionComponentsQuestionAnswer: typeof import("../components/answer-question/components/questionAnswer.vue")['default']
export const AnswerQuestionComponentsQuestionPoint: typeof import("../components/answer-question/components/questionPoint.vue")['default']
export const AnswerQuestionComponentsQuestionStem: typeof import("../components/answer-question/components/questionStem.vue")['default']
export const AnswerQuestionComponentsQuestionTabs: typeof import("../components/answer-question/components/questionTabs.vue")['default']
export const AnswerQuestionComponentsQuestionType: typeof import("../components/answer-question/components/questionType.vue")['default']
export const AnswerQuestionComponentsQuestionsOptions: typeof import("../components/answer-question/components/questionsOptions.vue")['default']
export const AnswerQuestionComponentsSearchAi: typeof import("../components/answer-question/components/search-ai.vue")['default']
export const AnswerQuestionHooksUseAudio: typeof import("../components/answer-question/hooks/useAudio")['default']
export const AnswerQuestionKkChatAudioPlayer: typeof import("../components/answer-question/kkChatAudioPlayer.vue")['default']
export const AnswerQuestionKkChatRichText: typeof import("../components/answer-question/kkChatRichText.vue")['default']
export const AnswerQuestionKkChatSignOssImg: typeof import("../components/answer-question/kkChatSignOssImg.vue")['default']
export const AnswerQuestionKkVideoPlayerDefaultConfig: typeof import("../components/answer-question/kkVideoPlayer/defaultConfig")['default']
export const AnswerQuestionKkVideoPlayer: typeof import("../components/answer-question/kkVideoPlayer/index.vue")['default']
export const AnswerQuestionKkVideoPlayerType: typeof import("../components/answer-question/kkVideoPlayer/type")['default']
export const AnswerQuestionPopTouchImg: typeof import("../components/answer-question/popTouchImg.vue")['default']
export const BuyDIalogSlot: typeof import("../components/buyDIalogSlot.vue")['default']
export const CatalogNodeDesc: typeof import("../components/catalog/NodeDesc.vue")['default']
export const CatalogQuestionTree: typeof import("../components/catalog/QuestionTree.vue")['default']
export const CatalogChapter: typeof import("../components/catalog/chapter.vue")['default']
export const CatalogNode: typeof import("../components/catalog/node.vue")['default']
export const CatalogTree: typeof import("../components/catalog/tree.vue")['default']
export const CatalogType: typeof import("../components/catalog/type")['default']
export const Close: typeof import("../components/close/index.vue")['default']
export const CollapseCheckJieDuanModal: typeof import("../components/collapse/CheckJieDuanModal.vue")['default']
export const CollapseStudyPlanFiles: typeof import("../components/collapse/StudyPlanFiles.vue")['default']
export const CollapseStudyPlanHomework: typeof import("../components/collapse/StudyPlanHomework.vue")['default']
export const CollapseStudyPlanJieDuanTitle: typeof import("../components/collapse/StudyPlanJieDuanTitle.vue")['default']
export const CollapseStudyPlanTitle: typeof import("../components/collapse/StudyPlanTitle.vue")['default']
export const CollapseChapter: typeof import("../components/collapse/chapter.vue")['default']
export const CollapseNode: typeof import("../components/collapse/node.vue")['default']
export const CollapseTree: typeof import("../components/collapse/tree.vue")['default']
export const CollapseType: typeof import("../components/collapse/type")['default']
export const CollectCell: typeof import("../components/collect-cell.vue")['default']
export const CommonHeader: typeof import("../components/commonHeader/index.vue")['default']
export const CourseJieduan: typeof import("../components/course/Jieduan.vue")['default']
export const Empty404: typeof import("../components/empty/404.vue")['default']
export const EmptyLogin: typeof import("../components/empty/login.vue")['default']
export const FloatingWindow: typeof import("../components/floating-window/index.vue")['default']
export const FooterPayBar: typeof import("../components/footerPayBar/index.vue")['default']
export const HomeBaoKao: typeof import("../components/home/<USER>")['default']
export const HomeBeiKao: typeof import("../components/home/<USER>")['default']
export const HomeBooks: typeof import("../components/home/<USER>")['default']
export const HomeBooksTitle: typeof import("../components/home/<USER>")['default']
export const HomeCateIdKuke: typeof import("../components/home/<USER>")['default']
export const HomeCourseCard: typeof import("../components/home/<USER>")['default']
export const HomeHeader: typeof import("../components/home/<USER>")['default']
export const HomePublicAdGongKao: typeof import("../components/home/<USER>/GongKao.vue")['default']
export const HomePublicAdBanner: typeof import("../components/home/<USER>/banner.vue")['default']
export const HomePublicAdZsb: typeof import("../components/home/<USER>/zsb.vue")['default']
export const HomeTeacher: typeof import("../components/home/<USER>")['default']
export const HomeTiKuMaterial: typeof import("../components/home/<USER>")['default']
export const HomeFloatingWidget: typeof import("../components/home/<USER>")['default']
export const HomeIconsZhuxue: typeof import("../components/home/<USER>/Zhuxue.vue")['default']
export const HomeIcons: typeof import("../components/home/<USER>/index.vue")['default']
export const Home: typeof import("../components/home/<USER>")['default']
export const HomeNewsBanner: typeof import("../components/home/<USER>/banner.vue")['default']
export const HomeNewsGongkao: typeof import("../components/home/<USER>/gongkao.vue")['default']
export const HomeNews: typeof import("../components/home/<USER>/index.vue")['default']
export const KkRecommendCourseDetail: typeof import("../components/kkRecommend/CourseDetail.vue")['default']
export const KkRecommendCourseDetail404: typeof import("../components/kkRecommend/CourseDetail404.vue")['default']
export const KkRecommend: typeof import("../components/kkRecommend/index.vue")['default']
export const KkRecommendSearch: typeof import("../components/kkRecommend/search.vue")['default']
export const KkcImage: typeof import("../components/kkcImage/index.vue")['default']
export const LearnCenterHistory: typeof import("../components/learn-center/history.vue")['default']
export const LearnTargetLabelName: typeof import("../components/learn-target-label-name.vue")['default']
export const LearnTargetLabel: typeof import("../components/learn-target-label.vue")['default']
export const LearnTargetButton: typeof import("../components/learn-target/Button.vue")['default']
export const LearnTargetNodeTitle: typeof import("../components/learn-target/NodeTitle.vue")['default']
export const LearnTargetTitle: typeof import("../components/learn-target/title.vue")['default']
export const LoginBtnButton: typeof import("../components/loginBtn/button.vue")['default']
export const MemberCourseDetailPrice: typeof import("../components/member/CourseDetailPrice.vue")['default']
export const MemberCoursePointsRedeemBarMember: typeof import("../components/member/CoursePointsRedeemBarMember.vue")['default']
export const MemberEntryBtn: typeof import("../components/member/EntryBtn.vue")['default']
export const MemberMeEntryBtn: typeof import("../components/member/MeEntryBtn.vue")['default']
export const MemberCenterNavBar: typeof import("../components/memberCenterNavBar/index.vue")['default']
export const MiniLive: typeof import("../components/miniLive/index.vue")['default']
export const NavBar: typeof import("../components/navBar/index.vue")['default']
export const NavBarTypes: typeof import("../components/navBar/types")['default']
export const PopupAd: typeof import("../components/popup-ad/index.vue")['default']
export const QuestionBankAnwserCard: typeof import("../components/question-bank/AnwserCard.vue")['default']
export const QuestionBankCatalogue: typeof import("../components/question-bank/Catalogue.vue")['default']
export const QuestionBankClassifySwitch: typeof import("../components/question-bank/ClassifySwitch.vue")['default']
export const QuestionBankCorrection: typeof import("../components/question-bank/Correction.vue")['default']
export const QuestionBankHeader: typeof import("../components/question-bank/Header.vue")['default']
export const QuestionBankMaterialSwiper: typeof import("../components/question-bank/MaterialSwiper.vue")['default']
export const QuestionBankMultipleChoice: typeof import("../components/question-bank/MultipleChoice.vue")['default']
export const QuestionBankPopupDescription: typeof import("../components/question-bank/PopupDescription.vue")['default']
export const QuestionBankPopupGuide: typeof import("../components/question-bank/PopupGuide.vue")['default']
export const QuestionBankPoster: typeof import("../components/question-bank/Poster.vue")['default']
export const QuestionBankRadioChoice: typeof import("../components/question-bank/RadioChoice.vue")['default']
export const QuestionBankRecorder: typeof import("../components/question-bank/Recorder.vue")['default']
export const QuestionBankShare: typeof import("../components/question-bank/Share.vue")['default']
export const QuestionBankShareGuide: typeof import("../components/question-bank/ShareGuide.vue")['default']
export const QuestionBankTopPart: typeof import("../components/question-bank/TopPart.vue")['default']
export const QuestionBankVideo: typeof import("../components/question-bank/Video.vue")['default']
export const QuestionBankAnalyze: typeof import("../components/question-bank/analyze.vue")['default']
export const QuestionBankAnalyzeAnswer: typeof import("../components/question-bank/analyze/answer.vue")['default']
export const QuestionBankAnalyzeTitle: typeof import("../components/question-bank/analyze/title.vue")['default']
export const QuestionBankFill: typeof import("../components/question-bank/fill.vue")['default']
export const QuestionBankMaterial: typeof import("../components/question-bank/material.vue")['default']
export const QuestionBankShowAnswer: typeof import("../components/question-bank/showAnswer.vue")['default']
export const QuestionBankType: typeof import("../components/question-bank/type")['default']
export const QuestionBankUpload: typeof import("../components/question-bank/upload.vue")['default']
export const RadioCell: typeof import("../components/radioCell/index.vue")['default']
export const Skeleton: typeof import("../components/skeleton.vue")['default']
export const SubmitBar: typeof import("../components/submitBar/index.vue")['default']
export const SubmitBarMaskDialog: typeof import("../components/submitBar/maskDialog.vue")['default']
export const Tabbar: typeof import("../components/tabbar.vue")['default']
export const UsedTime: typeof import("../components/usedTime/index.vue")['default']
export const KKCApplyProducts: typeof import("../../base/components/ApplyProducts/index.vue")['default']
export const KKCAudioPlayer: typeof import("../../base/components/AudioPlayer/index.vue")['default']
export const KKCCollapseTransition: typeof import("../../base/components/CollapseTransition.vue")['default']
export const KKCConfirmDialog: typeof import("../../base/components/ConfirmDialog.vue")['default']
export const KKCCountdown: typeof import("../../base/components/Countdown/index.vue")['default']
export const KKCCountdownUtils: typeof import("../../base/components/Countdown/utils/index")['default']
export const KKCEmptyConfig: typeof import("../../base/components/EmptyConfig.vue")['default']
export const KKCGetAward: typeof import("../../base/components/GetAward/index.vue")['default']
export const KKCGoodsTags: typeof import("../../base/components/GoodsTags/index.vue")['default']
export const KKCHtmlMathJax: typeof import("../../base/components/HtmlMathJax/index.vue")['default']
export const KKCIcon: typeof import("../../base/components/Icon.vue")['default']
export const KkcNuxtLink: typeof import("../../base/components/KkcNuxtLink")['default']
export const KKCLoading: typeof import("../../base/components/Loading/index.vue")['default']
export const KKCOffline: typeof import("../../base/components/Offline.vue")['default']
export const KKCQuestionVideoPlayerCcPlayer: typeof import("../../base/components/QuestionVideoPlayer/ccPlayer/index.vue")['default']
export const KKCQuestionVideoPlayer: typeof import("../../base/components/QuestionVideoPlayer/index.vue")['default']
export const KKCQuestionVideoPlayerPolyvPlayer: typeof import("../../base/components/QuestionVideoPlayer/polyvPlayer/index.vue")['default']
export const KKCQuestionVideoPlayerTypes: typeof import("../../base/components/QuestionVideoPlayer/types")['default']
export const KKCRichTextShadow: typeof import("../../base/components/RichTextShadow/index.vue")['default']
export const KKCTag: typeof import("../../base/components/Tag/index.vue")['default']
export const KKCButtonColours: typeof import("../../base/components/button-colours.vue")['default']
export const KKCButton: typeof import("../../base/components/button/index.vue")['default']
export const KKCCourseAudioPlayerAudioCom: typeof import("../../base/components/courseAudioPlayer/audioCom.vue")['default']
export const KKCCourseAudioPlayer: typeof import("../../base/components/courseAudioPlayer/index.vue")['default']
export const KKCCourseAudioPlayerUseAudioDetail: typeof import("../../base/components/courseAudioPlayer/useAudioDetail")['default']
export const KKCEmpty: typeof import("../../base/components/empty/index.vue")['default']
export const KKCFormHooks: typeof import("../../base/components/form/hooks")['default']
export const KKCForm: typeof import("../../base/components/form/index.vue")['default']
export const KKCInput: typeof import("../../base/components/input/input.vue")['default']
export const KKCLcSprite: typeof import("../../base/components/lc/sprite.vue")['default']
export const KKCMessage: typeof import("../../base/components/message/message.vue")['default']
export const KKCOverlay: typeof import("../../base/components/overlay.vue")['default']
export const KKCPageLoading: typeof import("../../base/components/page-loading.vue")['default']
export const KKCRecordAudio: typeof import("../../base/components/recordAudio/index.vue")['default']
export const KKCValidate: typeof import("../../base/components/validate/index.vue")['default']
export const KKCVideoPlayerCcplayer: typeof import("../../base/components/videoPlayer/ccplayer/index.vue")['default']
export const KKCVideoPlayer: typeof import("../../base/components/videoPlayer/index.vue")['default']
export const KKCVideoPlayerTypes: typeof import("../../base/components/videoPlayer/types")['default']
export const NuxtWelcome: typeof import("../../node_modules/@nuxt/ui-templates/dist/templates/welcome.vue")['default']
export const NuxtLayout: typeof import("../../node_modules/nuxt/dist/app/components/nuxt-layout")['default']
export const NuxtErrorBoundary: typeof import("../../node_modules/nuxt/dist/app/components/nuxt-error-boundary")['default']
export const ClientOnly: typeof import("../../node_modules/nuxt/dist/app/components/client-only")['default']
export const DevOnly: typeof import("../../node_modules/nuxt/dist/app/components/dev-only")['default']
export const ServerPlaceholder: typeof import("../../node_modules/nuxt/dist/app/components/server-placeholder")['default']
export const NuxtLink: typeof import("../../node_modules/nuxt/dist/app/components/nuxt-link")['default']
export const NuxtLoadingIndicator: typeof import("../../node_modules/nuxt/dist/app/components/nuxt-loading-indicator")['default']
export const NuxtIcon: typeof import("../../node_modules/nuxt-icons/dist/runtime/components/nuxt-icon.vue")['default']
export const VanActionBar: typeof import("../../node_modules/vant/es/action-bar/ActionBar")['default']
export const VanActionBarButton: typeof import("../../node_modules/vant/es/action-bar-button/ActionBarButton")['default']
export const VanActionBarIcon: typeof import("../../node_modules/vant/es/action-bar-icon/ActionBarIcon")['default']
export const VanActionSheet: typeof import("../../node_modules/vant/es/action-sheet/ActionSheet")['default']
export const VanAddressEdit: typeof import("../../node_modules/vant/es/address-edit/AddressEdit")['default']
export const VanAddressList: typeof import("../../node_modules/vant/es/address-list/AddressList")['default']
export const VanBackTop: typeof import("../../node_modules/vant/es/back-top/BackTop")['default']
export const VanArea: typeof import("../../node_modules/vant/es/area/Area")['default']
export const VanBadge: typeof import("../../node_modules/vant/es/badge/Badge")['default']
export const VanBarrage: typeof import("../../node_modules/vant/es/barrage/Barrage")['default']
export const VanButton: typeof import("../../node_modules/vant/es/button/Button")['default']
export const VanCalendar: typeof import("../../node_modules/vant/es/calendar/Calendar")['default']
export const VanCard: typeof import("../../node_modules/vant/es/card/Card")['default']
export const VanCascader: typeof import("../../node_modules/vant/es/cascader/Cascader")['default']
export const VanCell: typeof import("../../node_modules/vant/es/cell/Cell")['default']
export const VanCellGroup: typeof import("../../node_modules/vant/es/cell-group/CellGroup")['default']
export const VanCheckbox: typeof import("../../node_modules/vant/es/checkbox/Checkbox")['default']
export const VanCheckboxGroup: typeof import("../../node_modules/vant/es/checkbox-group/CheckboxGroup")['default']
export const VanCircle: typeof import("../../node_modules/vant/es/circle/Circle")['default']
export const VanCol: typeof import("../../node_modules/vant/es/col/Col")['default']
export const VanCollapse: typeof import("../../node_modules/vant/es/collapse/Collapse")['default']
export const VanCollapseItem: typeof import("../../node_modules/vant/es/collapse-item/CollapseItem")['default']
export const VanConfigProvider: typeof import("../../node_modules/vant/es/config-provider/ConfigProvider")['default']
export const VanContactCard: typeof import("../../node_modules/vant/es/contact-card/ContactCard")['default']
export const VanContactEdit: typeof import("../../node_modules/vant/es/contact-edit/ContactEdit")['default']
export const VanContactList: typeof import("../../node_modules/vant/es/contact-list/ContactList")['default']
export const VanCountDown: typeof import("../../node_modules/vant/es/count-down/CountDown")['default']
export const VanCoupon: typeof import("../../node_modules/vant/es/coupon/Coupon")['default']
export const VanCouponCell: typeof import("../../node_modules/vant/es/coupon-cell/CouponCell")['default']
export const VanDatePicker: typeof import("../../node_modules/vant/es/date-picker/DatePicker")['default']
export const VanCouponList: typeof import("../../node_modules/vant/es/coupon-list/CouponList")['default']
export const VanDialog: typeof import("../../node_modules/vant/es/dialog/Dialog")['default']
export const VanDivider: typeof import("../../node_modules/vant/es/divider/Divider")['default']
export const VanDropdownMenu: typeof import("../../node_modules/vant/es/dropdown-menu/DropdownMenu")['default']
export const VanDropdownItem: typeof import("../../node_modules/vant/es/dropdown-item/DropdownItem")['default']
export const VanEmpty: typeof import("../../node_modules/vant/es/empty/Empty")['default']
export const VanField: typeof import("../../node_modules/vant/es/field/Field")['default']
export const VanFloatingBubble: typeof import("../../node_modules/vant/es/floating-bubble/FloatingBubble")['default']
export const VanFloatingPanel: typeof import("../../node_modules/vant/es/floating-panel/FloatingPanel")['default']
export const VanForm: typeof import("../../node_modules/vant/es/form/Form")['default']
export const VanGrid: typeof import("../../node_modules/vant/es/grid/Grid")['default']
export const VanGridItem: typeof import("../../node_modules/vant/es/grid-item/GridItem")['default']
export const VanHighlight: typeof import("../../node_modules/vant/es/highlight/Highlight")['default']
export const VanIcon: typeof import("../../node_modules/vant/es/icon/Icon")['default']
export const VanImage: typeof import("../../node_modules/vant/es/image/Image")['default']
export const VanImagePreview: typeof import("../../node_modules/vant/es/image-preview/ImagePreview")['default']
export const VanIndexAnchor: typeof import("../../node_modules/vant/es/index-anchor/IndexAnchor")['default']
export const VanIndexBar: typeof import("../../node_modules/vant/es/index-bar/IndexBar")['default']
export const VanList: typeof import("../../node_modules/vant/es/list/List")['default']
export const VanLoading: typeof import("../../node_modules/vant/es/loading/Loading")['default']
export const VanNavBar: typeof import("../../node_modules/vant/es/nav-bar/NavBar")['default']
export const VanNoticeBar: typeof import("../../node_modules/vant/es/notice-bar/NoticeBar")['default']
export const VanNumberKeyboard: typeof import("../../node_modules/vant/es/number-keyboard/NumberKeyboard")['default']
export const VanNotify: typeof import("../../node_modules/vant/es/notify/Notify")['default']
export const VanOverlay: typeof import("../../node_modules/vant/es/overlay/Overlay")['default']
export const VanPagination: typeof import("../../node_modules/vant/es/pagination/Pagination")['default']
export const VanPasswordInput: typeof import("../../node_modules/vant/es/password-input/PasswordInput")['default']
export const VanPicker: typeof import("../../node_modules/vant/es/picker/Picker")['default']
export const VanPopover: typeof import("../../node_modules/vant/es/popover/Popover")['default']
export const VanPickerGroup: typeof import("../../node_modules/vant/es/picker-group/PickerGroup")['default']
export const VanPopup: typeof import("../../node_modules/vant/es/popup/Popup")['default']
export const VanProgress: typeof import("../../node_modules/vant/es/progress/Progress")['default']
export const VanPullRefresh: typeof import("../../node_modules/vant/es/pull-refresh/PullRefresh")['default']
export const VanRadio: typeof import("../../node_modules/vant/es/radio/Radio")['default']
export const VanRadioGroup: typeof import("../../node_modules/vant/es/radio-group/RadioGroup")['default']
export const VanRate: typeof import("../../node_modules/vant/es/rate/Rate")['default']
export const VanRollingText: typeof import("../../node_modules/vant/es/rolling-text/RollingText")['default']
export const VanSearch: typeof import("../../node_modules/vant/es/search/Search")['default']
export const VanRow: typeof import("../../node_modules/vant/es/row/Row")['default']
export const VanShareSheet: typeof import("../../node_modules/vant/es/share-sheet/ShareSheet")['default']
export const VanSidebar: typeof import("../../node_modules/vant/es/sidebar/Sidebar")['default']
export const VanSidebarItem: typeof import("../../node_modules/vant/es/sidebar-item/SidebarItem")['default']
export const VanSignature: typeof import("../../node_modules/vant/es/signature/Signature")['default']
export const VanSkeleton: typeof import("../../node_modules/vant/es/skeleton/Skeleton")['default']
export const VanSkeletonAvatar: typeof import("../../node_modules/vant/es/skeleton-avatar/SkeletonAvatar")['default']
export const VanSkeletonImage: typeof import("../../node_modules/vant/es/skeleton-image/SkeletonImage")['default']
export const VanSkeletonParagraph: typeof import("../../node_modules/vant/es/skeleton-paragraph/SkeletonParagraph")['default']
export const VanSlider: typeof import("../../node_modules/vant/es/slider/Slider")['default']
export const VanSkeletonTitle: typeof import("../../node_modules/vant/es/skeleton-title/SkeletonTitle")['default']
export const VanSpace: typeof import("../../node_modules/vant/es/space/Space")['default']
export const VanStep: typeof import("../../node_modules/vant/es/step/Step")['default']
export const VanStepper: typeof import("../../node_modules/vant/es/stepper/Stepper")['default']
export const VanSteps: typeof import("../../node_modules/vant/es/steps/Steps")['default']
export const VanSticky: typeof import("../../node_modules/vant/es/sticky/Sticky")['default']
export const VanSubmitBar: typeof import("../../node_modules/vant/es/submit-bar/SubmitBar")['default']
export const VanSwipe: typeof import("../../node_modules/vant/es/swipe/Swipe")['default']
export const VanSwipeCell: typeof import("../../node_modules/vant/es/swipe-cell/SwipeCell")['default']
export const VanSwipeItem: typeof import("../../node_modules/vant/es/swipe-item/SwipeItem")['default']
export const VanSwitch: typeof import("../../node_modules/vant/es/switch/Switch")['default']
export const VanTab: typeof import("../../node_modules/vant/es/tab/Tab")['default']
export const VanTabbar: typeof import("../../node_modules/vant/es/tabbar/Tabbar")['default']
export const VanTabbarItem: typeof import("../../node_modules/vant/es/tabbar-item/TabbarItem")['default']
export const VanTabs: typeof import("../../node_modules/vant/es/tabs/Tabs")['default']
export const VanTag: typeof import("../../node_modules/vant/es/tag/Tag")['default']
export const VanTextEllipsis: typeof import("../../node_modules/vant/es/text-ellipsis/TextEllipsis")['default']
export const VanToast: typeof import("../../node_modules/vant/es/toast/Toast")['default']
export const VanUploader: typeof import("../../node_modules/vant/es/uploader/Uploader")['default']
export const VanWatermark: typeof import("../../node_modules/vant/es/watermark/Watermark")['default']
export const VanTimePicker: typeof import("../../node_modules/vant/es/time-picker/TimePicker")['default']
export const VanTreeSelect: typeof import("../../node_modules/vant/es/tree-select/TreeSelect")['default']
export const NuxtPage: typeof import("../../node_modules/nuxt/dist/pages/runtime/page")['default']
export const NoScript: typeof import("../../node_modules/nuxt/dist/head/runtime/components")['NoScript']
export const Link: typeof import("../../node_modules/nuxt/dist/head/runtime/components")['Link']
export const Base: typeof import("../../node_modules/nuxt/dist/head/runtime/components")['Base']
export const Title: typeof import("../../node_modules/nuxt/dist/head/runtime/components")['Title']
export const Meta: typeof import("../../node_modules/nuxt/dist/head/runtime/components")['Meta']
export const Style: typeof import("../../node_modules/nuxt/dist/head/runtime/components")['Style']
export const Head: typeof import("../../node_modules/nuxt/dist/head/runtime/components")['Head']
export const Html: typeof import("../../node_modules/nuxt/dist/head/runtime/components")['Html']
export const Body: typeof import("../../node_modules/nuxt/dist/head/runtime/components")['Body']
export const LazyAgreementContent: typeof import("../components/AgreementContent.vue")['default']
export const LazyAnswerBtn: typeof import("../components/AnswerBtn/index.vue")['default']
export const LazyAnswerClassifyPop: typeof import("../components/AnswerClassifyPop/index.vue")['default']
export const LazyAnswerClassifyPopTags: typeof import("../components/AnswerClassifyPop/tags.vue")['default']
export const LazyBottomPop: typeof import("../components/BottomPop.vue")['default']
export const LazyBottomPopAiEntry: typeof import("../components/BottomPopAiEntry.vue")['default']
export const LazyBottomPopNoLogin: typeof import("../components/BottomPopNoLogin.vue")['default']
export const LazyCartMultiCategory: typeof import("../components/CartMultiCategory/index.vue")['default']
export const LazyCheckBoxIcon: typeof import("../components/CheckBoxIcon/index.vue")['default']
export const LazyCodeInput: typeof import("../components/CodeInput/index.vue")['default']
export const LazyCodeInputTypes: typeof import("../components/CodeInput/types")['default']
export const LazyCommonTree: typeof import("../components/CommonTree/index.vue")['default']
export const LazyCommonTreeNode: typeof import("../components/CommonTree/node.vue")['default']
export const LazyCommonTreeUtils: typeof import("../components/CommonTree/utils")['default']
export const LazyCouponCell: typeof import("../components/CouponCell.vue")['default']
export const LazyCourseGoodsSchool: typeof import("../components/CourseGoodsSchool/index.vue")['default']
export const LazyCourseGoodsSpecs: typeof import("../components/CourseGoodsSpecs/index.vue")['default']
export const LazyCustomPageMobile: typeof import("../components/CustomPageMobile.vue")['default']
export const LazyDealDialog: typeof import("../components/DealDialog.vue")['default']
export const LazyGoodCell: typeof import("../components/GoodCell.vue")['default']
export const LazyGoodCellCourseDetail: typeof import("../components/GoodCellCourseDetail.vue")['default']
export const LazyGoodList: typeof import("../components/GoodList.vue")['default']
export const LazyGoodListNoPage: typeof import("../components/GoodListNoPage.vue")['default']
export const LazyGoodsClassTypeAndLabel: typeof import("../components/GoodsClassTypeAndLabel.vue")['default']
export const LazyGoodsPrice: typeof import("../components/GoodsPrice.vue")['default']
export const LazyHomePageTemplate: typeof import("../components/HomePageTemplate.vue")['default']
export const LazyHomeworkModalList: typeof import("../components/HomeworkModal/list.vue")['default']
export const LazyHomeworkModalPlayer: typeof import("../components/HomeworkModal/player.vue")['default']
export const LazyHomeworkModalSubmit: typeof import("../components/HomeworkModal/submit.vue")['default']
export const LazyKKCoupon: typeof import("../components/KKCoupon/index.vue")['default']
export const LazyKKDialog: typeof import("../components/KKDialog/index.vue")['default']
export const LazyKKPopup: typeof import("../components/KKPopup/index.vue")['default']
export const LazyKKPopupTypes: typeof import("../components/KKPopup/types")['default']
export const LazyKKQuestionBank: typeof import("../components/KKQuestionBank.vue")['default']
export const LazyKKRichText: typeof import("../components/KKRichText/index.vue")['default']
export const LazyKKSelectArea: typeof import("../components/KKSelectArea/index.vue")['default']
export const LazyKKSelectAreaPopup: typeof import("../components/KKSelectAreaPopup/index.vue")['default']
export const LazyKefuSelectModal: typeof import("../components/KefuSelectModal/index.vue")['default']
export const LazyKkImageCropperIndexCopy: typeof import("../components/KkImageCropper/index-copy.vue")['default']
export const LazyKkImageCropper: typeof import("../components/KkImageCropper/index.vue")['default']
export const LazyKkImageCropperUseCropHooks: typeof import("../components/KkImageCropper/useCropHooks")['default']
export const LazyKkImageCropperVueCropper: typeof import("../components/KkImageCropper/vueCropper")['default']
export const LazyKkLoading: typeof import("../components/KkLoading.vue")['default']
export const LazyKkVideoPlayer: typeof import("../components/KkVideoPlayer/index.vue")['default']
export const LazyLaunchAd: typeof import("../components/LaunchAd.vue")['default']
export const LazyLogo: typeof import("../components/Logo.vue")['default']
export const LazyMailingCell: typeof import("../components/MailingCell.vue")['default']
export const LazyMaskDialog: typeof import("../components/MaskDialog.vue")['default']
export const LazyMaterialCell: typeof import("../components/MaterialCell.vue")['default']
export const LazyMemberFreeReceivePopup: typeof import("../components/MemberFreeReceivePopup.vue")['default']
export const LazyMemberFreeReceivePopupGoodsCard: typeof import("../components/MemberFreeReceivePopupGoodsCard.vue")['default']
export const LazyNavbarHomeSearch: typeof import("../components/NavbarHome/Search.vue")['default']
export const LazyNavbarHome: typeof import("../components/NavbarHome/index.vue")['default']
export const LazyNews99DownloadAppPopup: typeof import("../components/News99DownloadAppPopup.vue")['default']
export const LazyNewsListCell: typeof import("../components/NewsListCell.vue")['default']
export const LazyNewsNavTab: typeof import("../components/NewsNavTab.vue")['default']
export const LazyNumberBox: typeof import("../components/NumberBox/index.vue")['default']
export const LazyOrderCell: typeof import("../components/OrderCell.vue")['default']
export const LazyPopupLearnTarget: typeof import("../components/PopupLearnTarget/index.vue")['default']
export const LazyProtocolCheck: typeof import("../components/ProtocolCheck/ProtocolCheck.vue")['default']
export const LazyPublicAd: typeof import("../components/PublicAd.vue")['default']
export const LazySearchTag: typeof import("../components/SearchTag.vue")['default']
export const LazySharePop: typeof import("../components/SharePop.vue")['default']
export const LazySharePopup: typeof import("../components/SharePopup/index.vue")['default']
export const LazySharePoster: typeof import("../components/SharePoster/index.vue")['default']
export const LazySkeletonMaterial: typeof import("../components/SkeletonMaterial.vue")['default']
export const LazySlideOperate: typeof import("../components/SlideOperate/index.vue")['default']
export const LazyStarRating: typeof import("../components/StarRating.vue")['default']
export const LazyStudentOrDeainage: typeof import("../components/StudentOrDeainage.vue")['default']
export const LazyStudentOrDrainagePop: typeof import("../components/StudentOrDrainagePop.vue")['default']
export const LazyStudyRequirementAndCommentModal: typeof import("../components/StudyRequirementAndCommentModal.vue")['default']
export const LazyTabbarTemp: typeof import("../components/TabbarTemp.vue")['default']
export const LazyTabs: typeof import("../components/Tabs.vue")['default']
export const LazyTipTitle: typeof import("../components/TipTitle.vue")['default']
export const LazyTipTitleSearch: typeof import("../components/TipTitleSearch.vue")['default']
export const LazyTopNavbar: typeof import("../components/TopNavbar.vue")['default']
export const LazyTopNavbarHome: typeof import("../components/TopNavbarHome.vue")['default']
export const LazyYueKeItem: typeof import("../components/YueKeItem.vue")['default']
export const LazyAddressItem: typeof import("../components/address/item.vue")['default']
export const LazyAddressSelectPopup: typeof import("../components/address/selectPopup.vue")['default']
export const LazyAnswerQuestionAnswerAi: typeof import("../components/answer-question/answer-ai.vue")['default']
export const LazyAnswerQuestionAnswerCard: typeof import("../components/answer-question/answer-card.vue")['default']
export const LazyAnswerQuestionComponents: typeof import("../components/answer-question/components/index")['default']
export const LazyAnswerQuestionComponentsQuestionAnalysis: typeof import("../components/answer-question/components/questionAnalysis.vue")['default']
export const LazyAnswerQuestionComponentsQuestionAnswer: typeof import("../components/answer-question/components/questionAnswer.vue")['default']
export const LazyAnswerQuestionComponentsQuestionPoint: typeof import("../components/answer-question/components/questionPoint.vue")['default']
export const LazyAnswerQuestionComponentsQuestionStem: typeof import("../components/answer-question/components/questionStem.vue")['default']
export const LazyAnswerQuestionComponentsQuestionTabs: typeof import("../components/answer-question/components/questionTabs.vue")['default']
export const LazyAnswerQuestionComponentsQuestionType: typeof import("../components/answer-question/components/questionType.vue")['default']
export const LazyAnswerQuestionComponentsQuestionsOptions: typeof import("../components/answer-question/components/questionsOptions.vue")['default']
export const LazyAnswerQuestionComponentsSearchAi: typeof import("../components/answer-question/components/search-ai.vue")['default']
export const LazyAnswerQuestionHooksUseAudio: typeof import("../components/answer-question/hooks/useAudio")['default']
export const LazyAnswerQuestionKkChatAudioPlayer: typeof import("../components/answer-question/kkChatAudioPlayer.vue")['default']
export const LazyAnswerQuestionKkChatRichText: typeof import("../components/answer-question/kkChatRichText.vue")['default']
export const LazyAnswerQuestionKkChatSignOssImg: typeof import("../components/answer-question/kkChatSignOssImg.vue")['default']
export const LazyAnswerQuestionKkVideoPlayerDefaultConfig: typeof import("../components/answer-question/kkVideoPlayer/defaultConfig")['default']
export const LazyAnswerQuestionKkVideoPlayer: typeof import("../components/answer-question/kkVideoPlayer/index.vue")['default']
export const LazyAnswerQuestionKkVideoPlayerType: typeof import("../components/answer-question/kkVideoPlayer/type")['default']
export const LazyAnswerQuestionPopTouchImg: typeof import("../components/answer-question/popTouchImg.vue")['default']
export const LazyBuyDIalogSlot: typeof import("../components/buyDIalogSlot.vue")['default']
export const LazyCatalogNodeDesc: typeof import("../components/catalog/NodeDesc.vue")['default']
export const LazyCatalogQuestionTree: typeof import("../components/catalog/QuestionTree.vue")['default']
export const LazyCatalogChapter: typeof import("../components/catalog/chapter.vue")['default']
export const LazyCatalogNode: typeof import("../components/catalog/node.vue")['default']
export const LazyCatalogTree: typeof import("../components/catalog/tree.vue")['default']
export const LazyCatalogType: typeof import("../components/catalog/type")['default']
export const LazyClose: typeof import("../components/close/index.vue")['default']
export const LazyCollapseCheckJieDuanModal: typeof import("../components/collapse/CheckJieDuanModal.vue")['default']
export const LazyCollapseStudyPlanFiles: typeof import("../components/collapse/StudyPlanFiles.vue")['default']
export const LazyCollapseStudyPlanHomework: typeof import("../components/collapse/StudyPlanHomework.vue")['default']
export const LazyCollapseStudyPlanJieDuanTitle: typeof import("../components/collapse/StudyPlanJieDuanTitle.vue")['default']
export const LazyCollapseStudyPlanTitle: typeof import("../components/collapse/StudyPlanTitle.vue")['default']
export const LazyCollapseChapter: typeof import("../components/collapse/chapter.vue")['default']
export const LazyCollapseNode: typeof import("../components/collapse/node.vue")['default']
export const LazyCollapseTree: typeof import("../components/collapse/tree.vue")['default']
export const LazyCollapseType: typeof import("../components/collapse/type")['default']
export const LazyCollectCell: typeof import("../components/collect-cell.vue")['default']
export const LazyCommonHeader: typeof import("../components/commonHeader/index.vue")['default']
export const LazyCourseJieduan: typeof import("../components/course/Jieduan.vue")['default']
export const LazyEmpty404: typeof import("../components/empty/404.vue")['default']
export const LazyEmptyLogin: typeof import("../components/empty/login.vue")['default']
export const LazyFloatingWindow: typeof import("../components/floating-window/index.vue")['default']
export const LazyFooterPayBar: typeof import("../components/footerPayBar/index.vue")['default']
export const LazyHomeBaoKao: typeof import("../components/home/<USER>")['default']
export const LazyHomeBeiKao: typeof import("../components/home/<USER>")['default']
export const LazyHomeBooks: typeof import("../components/home/<USER>")['default']
export const LazyHomeBooksTitle: typeof import("../components/home/<USER>")['default']
export const LazyHomeCateIdKuke: typeof import("../components/home/<USER>")['default']
export const LazyHomeCourseCard: typeof import("../components/home/<USER>")['default']
export const LazyHomeHeader: typeof import("../components/home/<USER>")['default']
export const LazyHomePublicAdGongKao: typeof import("../components/home/<USER>/GongKao.vue")['default']
export const LazyHomePublicAdBanner: typeof import("../components/home/<USER>/banner.vue")['default']
export const LazyHomePublicAdZsb: typeof import("../components/home/<USER>/zsb.vue")['default']
export const LazyHomeTeacher: typeof import("../components/home/<USER>")['default']
export const LazyHomeTiKuMaterial: typeof import("../components/home/<USER>")['default']
export const LazyHomeFloatingWidget: typeof import("../components/home/<USER>")['default']
export const LazyHomeIconsZhuxue: typeof import("../components/home/<USER>/Zhuxue.vue")['default']
export const LazyHomeIcons: typeof import("../components/home/<USER>/index.vue")['default']
export const LazyHome: typeof import("../components/home/<USER>")['default']
export const LazyHomeNewsBanner: typeof import("../components/home/<USER>/banner.vue")['default']
export const LazyHomeNewsGongkao: typeof import("../components/home/<USER>/gongkao.vue")['default']
export const LazyHomeNews: typeof import("../components/home/<USER>/index.vue")['default']
export const LazyKkRecommendCourseDetail: typeof import("../components/kkRecommend/CourseDetail.vue")['default']
export const LazyKkRecommendCourseDetail404: typeof import("../components/kkRecommend/CourseDetail404.vue")['default']
export const LazyKkRecommend: typeof import("../components/kkRecommend/index.vue")['default']
export const LazyKkRecommendSearch: typeof import("../components/kkRecommend/search.vue")['default']
export const LazyKkcImage: typeof import("../components/kkcImage/index.vue")['default']
export const LazyLearnCenterHistory: typeof import("../components/learn-center/history.vue")['default']
export const LazyLearnTargetLabelName: typeof import("../components/learn-target-label-name.vue")['default']
export const LazyLearnTargetLabel: typeof import("../components/learn-target-label.vue")['default']
export const LazyLearnTargetButton: typeof import("../components/learn-target/Button.vue")['default']
export const LazyLearnTargetNodeTitle: typeof import("../components/learn-target/NodeTitle.vue")['default']
export const LazyLearnTargetTitle: typeof import("../components/learn-target/title.vue")['default']
export const LazyLoginBtnButton: typeof import("../components/loginBtn/button.vue")['default']
export const LazyMemberCourseDetailPrice: typeof import("../components/member/CourseDetailPrice.vue")['default']
export const LazyMemberCoursePointsRedeemBarMember: typeof import("../components/member/CoursePointsRedeemBarMember.vue")['default']
export const LazyMemberEntryBtn: typeof import("../components/member/EntryBtn.vue")['default']
export const LazyMemberMeEntryBtn: typeof import("../components/member/MeEntryBtn.vue")['default']
export const LazyMemberCenterNavBar: typeof import("../components/memberCenterNavBar/index.vue")['default']
export const LazyMiniLive: typeof import("../components/miniLive/index.vue")['default']
export const LazyNavBar: typeof import("../components/navBar/index.vue")['default']
export const LazyNavBarTypes: typeof import("../components/navBar/types")['default']
export const LazyPopupAd: typeof import("../components/popup-ad/index.vue")['default']
export const LazyQuestionBankAnwserCard: typeof import("../components/question-bank/AnwserCard.vue")['default']
export const LazyQuestionBankCatalogue: typeof import("../components/question-bank/Catalogue.vue")['default']
export const LazyQuestionBankClassifySwitch: typeof import("../components/question-bank/ClassifySwitch.vue")['default']
export const LazyQuestionBankCorrection: typeof import("../components/question-bank/Correction.vue")['default']
export const LazyQuestionBankHeader: typeof import("../components/question-bank/Header.vue")['default']
export const LazyQuestionBankMaterialSwiper: typeof import("../components/question-bank/MaterialSwiper.vue")['default']
export const LazyQuestionBankMultipleChoice: typeof import("../components/question-bank/MultipleChoice.vue")['default']
export const LazyQuestionBankPopupDescription: typeof import("../components/question-bank/PopupDescription.vue")['default']
export const LazyQuestionBankPopupGuide: typeof import("../components/question-bank/PopupGuide.vue")['default']
export const LazyQuestionBankPoster: typeof import("../components/question-bank/Poster.vue")['default']
export const LazyQuestionBankRadioChoice: typeof import("../components/question-bank/RadioChoice.vue")['default']
export const LazyQuestionBankRecorder: typeof import("../components/question-bank/Recorder.vue")['default']
export const LazyQuestionBankShare: typeof import("../components/question-bank/Share.vue")['default']
export const LazyQuestionBankShareGuide: typeof import("../components/question-bank/ShareGuide.vue")['default']
export const LazyQuestionBankTopPart: typeof import("../components/question-bank/TopPart.vue")['default']
export const LazyQuestionBankVideo: typeof import("../components/question-bank/Video.vue")['default']
export const LazyQuestionBankAnalyze: typeof import("../components/question-bank/analyze.vue")['default']
export const LazyQuestionBankAnalyzeAnswer: typeof import("../components/question-bank/analyze/answer.vue")['default']
export const LazyQuestionBankAnalyzeTitle: typeof import("../components/question-bank/analyze/title.vue")['default']
export const LazyQuestionBankFill: typeof import("../components/question-bank/fill.vue")['default']
export const LazyQuestionBankMaterial: typeof import("../components/question-bank/material.vue")['default']
export const LazyQuestionBankShowAnswer: typeof import("../components/question-bank/showAnswer.vue")['default']
export const LazyQuestionBankType: typeof import("../components/question-bank/type")['default']
export const LazyQuestionBankUpload: typeof import("../components/question-bank/upload.vue")['default']
export const LazyRadioCell: typeof import("../components/radioCell/index.vue")['default']
export const LazySkeleton: typeof import("../components/skeleton.vue")['default']
export const LazySubmitBar: typeof import("../components/submitBar/index.vue")['default']
export const LazySubmitBarMaskDialog: typeof import("../components/submitBar/maskDialog.vue")['default']
export const LazyTabbar: typeof import("../components/tabbar.vue")['default']
export const LazyUsedTime: typeof import("../components/usedTime/index.vue")['default']
export const LazyKKCApplyProducts: typeof import("../../base/components/ApplyProducts/index.vue")['default']
export const LazyKKCAudioPlayer: typeof import("../../base/components/AudioPlayer/index.vue")['default']
export const LazyKKCCollapseTransition: typeof import("../../base/components/CollapseTransition.vue")['default']
export const LazyKKCConfirmDialog: typeof import("../../base/components/ConfirmDialog.vue")['default']
export const LazyKKCCountdown: typeof import("../../base/components/Countdown/index.vue")['default']
export const LazyKKCCountdownUtils: typeof import("../../base/components/Countdown/utils/index")['default']
export const LazyKKCEmptyConfig: typeof import("../../base/components/EmptyConfig.vue")['default']
export const LazyKKCGetAward: typeof import("../../base/components/GetAward/index.vue")['default']
export const LazyKKCGoodsTags: typeof import("../../base/components/GoodsTags/index.vue")['default']
export const LazyKKCHtmlMathJax: typeof import("../../base/components/HtmlMathJax/index.vue")['default']
export const LazyKKCIcon: typeof import("../../base/components/Icon.vue")['default']
export const LazyKkcNuxtLink: typeof import("../../base/components/KkcNuxtLink")['default']
export const LazyKKCLoading: typeof import("../../base/components/Loading/index.vue")['default']
export const LazyKKCOffline: typeof import("../../base/components/Offline.vue")['default']
export const LazyKKCQuestionVideoPlayerCcPlayer: typeof import("../../base/components/QuestionVideoPlayer/ccPlayer/index.vue")['default']
export const LazyKKCQuestionVideoPlayer: typeof import("../../base/components/QuestionVideoPlayer/index.vue")['default']
export const LazyKKCQuestionVideoPlayerPolyvPlayer: typeof import("../../base/components/QuestionVideoPlayer/polyvPlayer/index.vue")['default']
export const LazyKKCQuestionVideoPlayerTypes: typeof import("../../base/components/QuestionVideoPlayer/types")['default']
export const LazyKKCRichTextShadow: typeof import("../../base/components/RichTextShadow/index.vue")['default']
export const LazyKKCTag: typeof import("../../base/components/Tag/index.vue")['default']
export const LazyKKCButtonColours: typeof import("../../base/components/button-colours.vue")['default']
export const LazyKKCButton: typeof import("../../base/components/button/index.vue")['default']
export const LazyKKCCourseAudioPlayerAudioCom: typeof import("../../base/components/courseAudioPlayer/audioCom.vue")['default']
export const LazyKKCCourseAudioPlayer: typeof import("../../base/components/courseAudioPlayer/index.vue")['default']
export const LazyKKCCourseAudioPlayerUseAudioDetail: typeof import("../../base/components/courseAudioPlayer/useAudioDetail")['default']
export const LazyKKCEmpty: typeof import("../../base/components/empty/index.vue")['default']
export const LazyKKCFormHooks: typeof import("../../base/components/form/hooks")['default']
export const LazyKKCForm: typeof import("../../base/components/form/index.vue")['default']
export const LazyKKCInput: typeof import("../../base/components/input/input.vue")['default']
export const LazyKKCLcSprite: typeof import("../../base/components/lc/sprite.vue")['default']
export const LazyKKCMessage: typeof import("../../base/components/message/message.vue")['default']
export const LazyKKCOverlay: typeof import("../../base/components/overlay.vue")['default']
export const LazyKKCPageLoading: typeof import("../../base/components/page-loading.vue")['default']
export const LazyKKCRecordAudio: typeof import("../../base/components/recordAudio/index.vue")['default']
export const LazyKKCValidate: typeof import("../../base/components/validate/index.vue")['default']
export const LazyKKCVideoPlayerCcplayer: typeof import("../../base/components/videoPlayer/ccplayer/index.vue")['default']
export const LazyKKCVideoPlayer: typeof import("../../base/components/videoPlayer/index.vue")['default']
export const LazyKKCVideoPlayerTypes: typeof import("../../base/components/videoPlayer/types")['default']
export const LazyNuxtWelcome: typeof import("../../node_modules/@nuxt/ui-templates/dist/templates/welcome.vue")['default']
export const LazyNuxtLayout: typeof import("../../node_modules/nuxt/dist/app/components/nuxt-layout")['default']
export const LazyNuxtErrorBoundary: typeof import("../../node_modules/nuxt/dist/app/components/nuxt-error-boundary")['default']
export const LazyClientOnly: typeof import("../../node_modules/nuxt/dist/app/components/client-only")['default']
export const LazyDevOnly: typeof import("../../node_modules/nuxt/dist/app/components/dev-only")['default']
export const LazyServerPlaceholder: typeof import("../../node_modules/nuxt/dist/app/components/server-placeholder")['default']
export const LazyNuxtLink: typeof import("../../node_modules/nuxt/dist/app/components/nuxt-link")['default']
export const LazyNuxtLoadingIndicator: typeof import("../../node_modules/nuxt/dist/app/components/nuxt-loading-indicator")['default']
export const LazyNuxtIcon: typeof import("../../node_modules/nuxt-icons/dist/runtime/components/nuxt-icon.vue")['default']
export const LazyVanActionBar: typeof import("../../node_modules/vant/es/action-bar/ActionBar")['default']
export const LazyVanActionBarButton: typeof import("../../node_modules/vant/es/action-bar-button/ActionBarButton")['default']
export const LazyVanActionBarIcon: typeof import("../../node_modules/vant/es/action-bar-icon/ActionBarIcon")['default']
export const LazyVanActionSheet: typeof import("../../node_modules/vant/es/action-sheet/ActionSheet")['default']
export const LazyVanAddressEdit: typeof import("../../node_modules/vant/es/address-edit/AddressEdit")['default']
export const LazyVanAddressList: typeof import("../../node_modules/vant/es/address-list/AddressList")['default']
export const LazyVanBackTop: typeof import("../../node_modules/vant/es/back-top/BackTop")['default']
export const LazyVanArea: typeof import("../../node_modules/vant/es/area/Area")['default']
export const LazyVanBadge: typeof import("../../node_modules/vant/es/badge/Badge")['default']
export const LazyVanBarrage: typeof import("../../node_modules/vant/es/barrage/Barrage")['default']
export const LazyVanButton: typeof import("../../node_modules/vant/es/button/Button")['default']
export const LazyVanCalendar: typeof import("../../node_modules/vant/es/calendar/Calendar")['default']
export const LazyVanCard: typeof import("../../node_modules/vant/es/card/Card")['default']
export const LazyVanCascader: typeof import("../../node_modules/vant/es/cascader/Cascader")['default']
export const LazyVanCell: typeof import("../../node_modules/vant/es/cell/Cell")['default']
export const LazyVanCellGroup: typeof import("../../node_modules/vant/es/cell-group/CellGroup")['default']
export const LazyVanCheckbox: typeof import("../../node_modules/vant/es/checkbox/Checkbox")['default']
export const LazyVanCheckboxGroup: typeof import("../../node_modules/vant/es/checkbox-group/CheckboxGroup")['default']
export const LazyVanCircle: typeof import("../../node_modules/vant/es/circle/Circle")['default']
export const LazyVanCol: typeof import("../../node_modules/vant/es/col/Col")['default']
export const LazyVanCollapse: typeof import("../../node_modules/vant/es/collapse/Collapse")['default']
export const LazyVanCollapseItem: typeof import("../../node_modules/vant/es/collapse-item/CollapseItem")['default']
export const LazyVanConfigProvider: typeof import("../../node_modules/vant/es/config-provider/ConfigProvider")['default']
export const LazyVanContactCard: typeof import("../../node_modules/vant/es/contact-card/ContactCard")['default']
export const LazyVanContactEdit: typeof import("../../node_modules/vant/es/contact-edit/ContactEdit")['default']
export const LazyVanContactList: typeof import("../../node_modules/vant/es/contact-list/ContactList")['default']
export const LazyVanCountDown: typeof import("../../node_modules/vant/es/count-down/CountDown")['default']
export const LazyVanCoupon: typeof import("../../node_modules/vant/es/coupon/Coupon")['default']
export const LazyVanCouponCell: typeof import("../../node_modules/vant/es/coupon-cell/CouponCell")['default']
export const LazyVanDatePicker: typeof import("../../node_modules/vant/es/date-picker/DatePicker")['default']
export const LazyVanCouponList: typeof import("../../node_modules/vant/es/coupon-list/CouponList")['default']
export const LazyVanDialog: typeof import("../../node_modules/vant/es/dialog/Dialog")['default']
export const LazyVanDivider: typeof import("../../node_modules/vant/es/divider/Divider")['default']
export const LazyVanDropdownMenu: typeof import("../../node_modules/vant/es/dropdown-menu/DropdownMenu")['default']
export const LazyVanDropdownItem: typeof import("../../node_modules/vant/es/dropdown-item/DropdownItem")['default']
export const LazyVanEmpty: typeof import("../../node_modules/vant/es/empty/Empty")['default']
export const LazyVanField: typeof import("../../node_modules/vant/es/field/Field")['default']
export const LazyVanFloatingBubble: typeof import("../../node_modules/vant/es/floating-bubble/FloatingBubble")['default']
export const LazyVanFloatingPanel: typeof import("../../node_modules/vant/es/floating-panel/FloatingPanel")['default']
export const LazyVanForm: typeof import("../../node_modules/vant/es/form/Form")['default']
export const LazyVanGrid: typeof import("../../node_modules/vant/es/grid/Grid")['default']
export const LazyVanGridItem: typeof import("../../node_modules/vant/es/grid-item/GridItem")['default']
export const LazyVanHighlight: typeof import("../../node_modules/vant/es/highlight/Highlight")['default']
export const LazyVanIcon: typeof import("../../node_modules/vant/es/icon/Icon")['default']
export const LazyVanImage: typeof import("../../node_modules/vant/es/image/Image")['default']
export const LazyVanImagePreview: typeof import("../../node_modules/vant/es/image-preview/ImagePreview")['default']
export const LazyVanIndexAnchor: typeof import("../../node_modules/vant/es/index-anchor/IndexAnchor")['default']
export const LazyVanIndexBar: typeof import("../../node_modules/vant/es/index-bar/IndexBar")['default']
export const LazyVanList: typeof import("../../node_modules/vant/es/list/List")['default']
export const LazyVanLoading: typeof import("../../node_modules/vant/es/loading/Loading")['default']
export const LazyVanNavBar: typeof import("../../node_modules/vant/es/nav-bar/NavBar")['default']
export const LazyVanNoticeBar: typeof import("../../node_modules/vant/es/notice-bar/NoticeBar")['default']
export const LazyVanNumberKeyboard: typeof import("../../node_modules/vant/es/number-keyboard/NumberKeyboard")['default']
export const LazyVanNotify: typeof import("../../node_modules/vant/es/notify/Notify")['default']
export const LazyVanOverlay: typeof import("../../node_modules/vant/es/overlay/Overlay")['default']
export const LazyVanPagination: typeof import("../../node_modules/vant/es/pagination/Pagination")['default']
export const LazyVanPasswordInput: typeof import("../../node_modules/vant/es/password-input/PasswordInput")['default']
export const LazyVanPicker: typeof import("../../node_modules/vant/es/picker/Picker")['default']
export const LazyVanPopover: typeof import("../../node_modules/vant/es/popover/Popover")['default']
export const LazyVanPickerGroup: typeof import("../../node_modules/vant/es/picker-group/PickerGroup")['default']
export const LazyVanPopup: typeof import("../../node_modules/vant/es/popup/Popup")['default']
export const LazyVanProgress: typeof import("../../node_modules/vant/es/progress/Progress")['default']
export const LazyVanPullRefresh: typeof import("../../node_modules/vant/es/pull-refresh/PullRefresh")['default']
export const LazyVanRadio: typeof import("../../node_modules/vant/es/radio/Radio")['default']
export const LazyVanRadioGroup: typeof import("../../node_modules/vant/es/radio-group/RadioGroup")['default']
export const LazyVanRate: typeof import("../../node_modules/vant/es/rate/Rate")['default']
export const LazyVanRollingText: typeof import("../../node_modules/vant/es/rolling-text/RollingText")['default']
export const LazyVanSearch: typeof import("../../node_modules/vant/es/search/Search")['default']
export const LazyVanRow: typeof import("../../node_modules/vant/es/row/Row")['default']
export const LazyVanShareSheet: typeof import("../../node_modules/vant/es/share-sheet/ShareSheet")['default']
export const LazyVanSidebar: typeof import("../../node_modules/vant/es/sidebar/Sidebar")['default']
export const LazyVanSidebarItem: typeof import("../../node_modules/vant/es/sidebar-item/SidebarItem")['default']
export const LazyVanSignature: typeof import("../../node_modules/vant/es/signature/Signature")['default']
export const LazyVanSkeleton: typeof import("../../node_modules/vant/es/skeleton/Skeleton")['default']
export const LazyVanSkeletonAvatar: typeof import("../../node_modules/vant/es/skeleton-avatar/SkeletonAvatar")['default']
export const LazyVanSkeletonImage: typeof import("../../node_modules/vant/es/skeleton-image/SkeletonImage")['default']
export const LazyVanSkeletonParagraph: typeof import("../../node_modules/vant/es/skeleton-paragraph/SkeletonParagraph")['default']
export const LazyVanSlider: typeof import("../../node_modules/vant/es/slider/Slider")['default']
export const LazyVanSkeletonTitle: typeof import("../../node_modules/vant/es/skeleton-title/SkeletonTitle")['default']
export const LazyVanSpace: typeof import("../../node_modules/vant/es/space/Space")['default']
export const LazyVanStep: typeof import("../../node_modules/vant/es/step/Step")['default']
export const LazyVanStepper: typeof import("../../node_modules/vant/es/stepper/Stepper")['default']
export const LazyVanSteps: typeof import("../../node_modules/vant/es/steps/Steps")['default']
export const LazyVanSticky: typeof import("../../node_modules/vant/es/sticky/Sticky")['default']
export const LazyVanSubmitBar: typeof import("../../node_modules/vant/es/submit-bar/SubmitBar")['default']
export const LazyVanSwipe: typeof import("../../node_modules/vant/es/swipe/Swipe")['default']
export const LazyVanSwipeCell: typeof import("../../node_modules/vant/es/swipe-cell/SwipeCell")['default']
export const LazyVanSwipeItem: typeof import("../../node_modules/vant/es/swipe-item/SwipeItem")['default']
export const LazyVanSwitch: typeof import("../../node_modules/vant/es/switch/Switch")['default']
export const LazyVanTab: typeof import("../../node_modules/vant/es/tab/Tab")['default']
export const LazyVanTabbar: typeof import("../../node_modules/vant/es/tabbar/Tabbar")['default']
export const LazyVanTabbarItem: typeof import("../../node_modules/vant/es/tabbar-item/TabbarItem")['default']
export const LazyVanTabs: typeof import("../../node_modules/vant/es/tabs/Tabs")['default']
export const LazyVanTag: typeof import("../../node_modules/vant/es/tag/Tag")['default']
export const LazyVanTextEllipsis: typeof import("../../node_modules/vant/es/text-ellipsis/TextEllipsis")['default']
export const LazyVanToast: typeof import("../../node_modules/vant/es/toast/Toast")['default']
export const LazyVanUploader: typeof import("../../node_modules/vant/es/uploader/Uploader")['default']
export const LazyVanWatermark: typeof import("../../node_modules/vant/es/watermark/Watermark")['default']
export const LazyVanTimePicker: typeof import("../../node_modules/vant/es/time-picker/TimePicker")['default']
export const LazyVanTreeSelect: typeof import("../../node_modules/vant/es/tree-select/TreeSelect")['default']
export const LazyNuxtPage: typeof import("../../node_modules/nuxt/dist/pages/runtime/page")['default']
export const LazyNoScript: typeof import("../../node_modules/nuxt/dist/head/runtime/components")['NoScript']
export const LazyLink: typeof import("../../node_modules/nuxt/dist/head/runtime/components")['Link']
export const LazyBase: typeof import("../../node_modules/nuxt/dist/head/runtime/components")['Base']
export const LazyTitle: typeof import("../../node_modules/nuxt/dist/head/runtime/components")['Title']
export const LazyMeta: typeof import("../../node_modules/nuxt/dist/head/runtime/components")['Meta']
export const LazyStyle: typeof import("../../node_modules/nuxt/dist/head/runtime/components")['Style']
export const LazyHead: typeof import("../../node_modules/nuxt/dist/head/runtime/components")['Head']
export const LazyHtml: typeof import("../../node_modules/nuxt/dist/head/runtime/components")['Html']
export const LazyBody: typeof import("../../node_modules/nuxt/dist/head/runtime/components")['Body']

export const componentNames: string[]
