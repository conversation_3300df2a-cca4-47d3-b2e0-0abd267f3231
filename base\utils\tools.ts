import { Base64 } from 'js-base64'
import { ExamQuestionType, ExciseModeEnum } from '../constants/index'
import { QuestionDoStatusFromLearnStatusName, QuestionDoStatusAlias } from '../constants/questionBankType'

export function flat (arr: any[], depth = 1) {
  return arr.reduce((acc, val) => {
    return Array.isArray(val) && depth > 0
      ? acc.concat(flat(val, depth - 1))
      : acc.concat(val)
  }, [])
}

/**
 * 是否材料题
 * @param type 题型
 * @returns boolean
 */
export function isMaterial (type: number) {
  return [ExamQuestionType.MATERIAL].includes(type)
}

/**
 * 是否填空题
 * @param type 题型
 * @returns boolean
 */
export function isFillQuestion (type: number) {
  return [ExamQuestionType.FILL, ExamQuestionType.SHORT_ANSWER].includes(type)
}

/**
 * 是否多选题
 * @param type 题型
 * @returns boolean
 */
export function isMultipleChoice (type: number) {
  return [ExamQuestionType.MULTIPLE, ExamQuestionType.INDEFINITE].includes(type)
}

/**
 * 是否单选题
 * @param type 题型
 * @returns boolean
 */
export function isRadioChoice (type: number) {
  return [ExamQuestionType.RADIO, ExamQuestionType.JUDGE].includes(type)
}

/**
 * 是否显示解析
 * @param model 做题模式
 * @returns boolean
 * */
export function isShowBankAnalyze (model: number) {
  return model === ExciseModeEnum.EXCISE || model === ExciseModeEnum.QUICK || model === ExciseModeEnum.RECITE
}

/**
 * 是否展开解析
 * @param model 做题模式
 * @param isExpand 是否展开
 * @returns boolean
 * */
export function isOpenBankAnalyze (model: number, isExpand?: boolean) {
  return model === ExciseModeEnum.RECITE || (isShowBankAnalyze(model) && isExpand)
}

/**
 * 题型标题展示
 * @description 试卷类型以及固定刷题、每日一练默认展示展示二级题型，否则展示一级题型；其他类型默认展示一级题型
 *
 * @param first 一级题型
 * @param second 二级题型
 * @param moduleType 类型
 * @returns string 要展示的文案
 */
export function handleQuestionTitle (first: string, second: string, moduleType: number) {
  return [0, 1, 2, 4, 5].includes(moduleType) ? second || first : first || second
}

/**
 * 将字符串转为base64
 * @param str 字符串
 * @returns base64
 */
export function toBase64 (str: string): string {
  return Base64.encode(str)
}

/**
 * 将answer转为base64
 * @param answer 答案
 * @returns base64
 */
export function toAnswerBase64 (answer: any) {
  return answer ? toBase64(typeof answer === 'string' ? answer : JSON.stringify(answer)) : ''
}

/**
 * 根据学习状态名称获取做题状态
 * @param statusName 学习状态名称
 * @returns number
 */
export function getQuestionDoStatusFromLearnStatusName (statusName: string): number {
  if (statusName in QuestionDoStatusAlias) {
    return QuestionDoStatusAlias[statusName as keyof typeof QuestionDoStatusAlias]
  }
  return QuestionDoStatusFromLearnStatusName[statusName as keyof typeof QuestionDoStatusFromLearnStatusName] || 0
}

/**
 * 阿拉伯数据子转换为中文数字
 *
 * @param num 阿拉伯数字
 * @returns 中文数字
 */
export function toChineseNum (num: number) {
  const chineseNums = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九']
  const chineseUnits = ['', '十', '百', '千', '万']

  if (num === 0) { return chineseNums[0] }

  const str = num.toString()
  let result = ''
  const length = str.length

  for (let i = 0; i < length; i++) {
    const digit = parseInt(str[i])
    const unit = length - i - 1

    if (digit === 0) {
      // 处理连续的零
      if (i < length - 1 && str[i + 1] !== '0') {
        result += chineseNums[0]
      }
    } else {
      result += chineseNums[digit] + chineseUnits[unit]
    }
  }

  // 处理十开头的特殊情况，如10-19
  if (num < 20 && num >= 10) {
    result = result.substring(1)
  }

  return result
}

/**
 * [AI-GEN] 根据指定格式格式化时间
 * @param time 时间（毫秒）
 * @param format 格式字符串，例如：'HH:mm:ss'，'DD天HH小时mm分ss秒'，'YYYY-MM-DD HH:mm:ss'
 * @returns 格式化后的时间字符串
 */
export const formatTimeByPattern = (time: number, format: string = 'HH:mm:ss'): string => {
  // 如果时间值过大，先转换为Date对象处理
  if (time > 31536000000) { // 大于1年的毫秒数
    return formatDateWithDateObject(new Date(time), format)
  }

  const SECOND = 1000
  const MINUTE = 60 * SECOND
  const HOUR = 60 * MINUTE
  const DAY = 24 * HOUR
  const YEAR = 365 * DAY
  const MONTH = 30 * DAY // 简化计算，每月按30天计算

  // 计算各时间单位
  const year = Math.floor(time / YEAR)
  const month = Math.floor((time % YEAR) / MONTH)
  const day = Math.floor((time % MONTH) / DAY)
  const hour = Math.floor((time % DAY) / HOUR)
  const minute = Math.floor((time % HOUR) / MINUTE)
  const second = Math.floor((time % MINUTE) / SECOND)
  const millisecond = time % SECOND

  // 格式化单个时间单位
  const padTwo = (t: number) => (t >= 10 ? t.toString() : '0' + t)
  const padThree = (t: number) => (t >= 100 ? t.toString() : t >= 10 ? '0' + t : '00' + t)
  const padFour = (t: number) => (t >= 1000 ? t.toString() : t >= 100 ? '0' + t : t >= 10 ? '00' + t : '000' + t)

  // 替换格式字符串中的占位符
  return format
    // 年份格式
    .replace(/YYYY/g, year.toString())
    .replace(/yyyy/g, padFour(year))
    .replace(/YY/g, year.toString().slice(-2))
    .replace(/yy/g, padTwo(year % 100))
    // 月份格式
    .replace(/MM/g, month.toString())
    .replace(/mm/g, padTwo(month))
    // 日格式
    .replace(/DD/g, day.toString())
    .replace(/dd/g, padTwo(day))
    // 小时格式
    .replace(/HH/g, hour.toString())
    .replace(/hh/g, padTwo(hour))
    // 分钟格式
    .replace(/NN/g, minute.toString())
    .replace(/nn/g, padTwo(minute))
    .replace(/MM/g, minute.toString()) // 保持向后兼容
    .replace(/mm/g, padTwo(minute)) // 保持向后兼容
    // 秒格式
    .replace(/SS/g, second.toString())
    .replace(/ss/g, padTwo(second))
    // 毫秒格式
    .replace(/S+/g, padThree(millisecond))
}

/**
 * [AI-GEN] 使用Date对象格式化时间，适用于大数值时间
 * @param time 时间（毫秒）
 * @param format 格式字符串
 * @returns 格式化后的时间字符串
 */
export const formatDateWithDateObject = (date: Date, format: string): string => {
  // 获取各时间单位
  const year = date.getFullYear()
  const month = date.getMonth() + 1 // 月份从0开始，需要+1
  const day = date.getDate()
  const hour = date.getHours()
  const minute = date.getMinutes()
  const second = date.getSeconds()
  const millisecond = date.getMilliseconds()

  // 格式化单个时间单位
  const padTwo = (t: number) => (t >= 10 ? t.toString() : '0' + t)
  const padThree = (t: number) => (t >= 100 ? t.toString() : t >= 10 ? '0' + t : '00' + t)
  const padFour = (t: number) => (t >= 1000 ? t.toString() : t >= 100 ? '0' + t : t >= 10 ? '00' + t : '000' + t)

  // 替换格式字符串中的占位符
  return format
    // 年份格式
    .replace(/YYYY/g, year.toString())
    .replace(/yyyy/g, padFour(year))
    .replace(/YY/g, year.toString().slice(-2))
    .replace(/yy/g, padTwo(year % 100))
    // 月份格式
    .replace(/MM/g, month.toString())
    .replace(/mm/g, padTwo(month))
    // 日格式
    .replace(/DD/g, day.toString())
    .replace(/dd/g, padTwo(day))
    // 小时格式
    .replace(/HH/g, hour.toString())
    .replace(/hh/g, padTwo(hour))
    // 分钟格式
    .replace(/NN/g, minute.toString())
    .replace(/nn/g, padTwo(minute))
    .replace(/MM/g, minute.toString()) // 保持向后兼容
    .replace(/mm/g, padTwo(minute)) // 保持向后兼容
    // 秒格式
    .replace(/SS/g, second.toString())
    .replace(/ss/g, padTwo(second))
    // 毫秒格式
    .replace(/S+/g, padThree(millisecond))
}
