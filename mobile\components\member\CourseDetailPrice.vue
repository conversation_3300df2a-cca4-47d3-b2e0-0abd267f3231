<template>
  <div class="box">
    <div class="box__main">
      <span>会员价低至<span class="font-gilroy-bold text-[28px]">￥{{ price }}</span>
        <template v-if="isShowRiseText">
          起
        </template>
      </span>
      <span
        class="box__arrow"
        @click.stop="membershipOpen"
      >
        <span>去开通</span>
        <KKCIcon name="icon-wode-youji<PERSON><PERSON>" />
      </span>
    </div>
    <slot name="right" />
  </div>
</template>
<script setup lang="ts">
import { useCourseStore } from '~/stores/course.store'
// import { useUserStore } from '~/stores/user.store'

type Props = {
  isShowRiseText?:boolean
  price?: string | number
}
withDefaults(defineProps<Props>(), {
  isShowRiseText: false,
  price: ''
})
// const userStore = useUserStore()
const courseStore = useCourseStore()
const {
  toMemberCenterPage,
} = useMemberUtils()
const memberInfo = computed(() => {
  return courseStore.memberInfo || {}
})

// const _emit = defineEmits<{
//   (e: 'membershipOpen', val?: string): void;
// }>()
/**
 * TODO
 * 点击“去开通”按钮，进入会员中心页面默认选中价格最低的会员专业分类和标签筛选项，若价格一样取排序最大的，若排序值一样取最新创建的
 */
const membershipOpen = () => {
  // _emit('membershipOpen', '')
  // if (userStore.isLoginFn()) {
  toMemberCenterPage(memberInfo.value)
  // }
}

</script>
<style scoped lang="scss">
.box{
    display: block;
    width: 686px;
    height: 56px;
    line-height: 56px;
    margin: 12px auto 0;
    &__main{
        display: flex;
        justify-content: space-between;
    background: linear-gradient( 134deg, #453D5C 0%, #1B142A 100%);
    border-radius: 12px 12px 12px 12px;
    //
    font-size: 24px;
    color: #FED8B8;
    padding: 0 16px;
    //

}
}
.box__arrow{
    display: flex;
    align-items: center;
    .kkc-icon{
        margin-left: 4px;
    }
}
</style>
