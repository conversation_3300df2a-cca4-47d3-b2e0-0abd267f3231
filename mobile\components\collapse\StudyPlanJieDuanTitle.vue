<template>
  <div>
    <div
      class="study-plan-jieduan-title break-all"
      :data-level="item.level"
      @click="handleAction(item)"
    >
      <!-- :class="[type]" -->
      <div class="flex">
        <div class="study-plan-jieduan-title__main break-all flex-1">
          <div class="study-plan-jieduan-title__icon mr-[12px]">
            <!-- -->
            <span v-if="item.unlocked === false" class="study-plan-jieduan-title__icon-index is-unlocked">
              <KKCIcon name="icon-suo" color="var(--kkc-brand)" :size="24" />
            </span>
            <span v-else class="study-plan-jieduan-title__icon-index font-gilroy-bold">
              {{ index+1 }}
            </span>
            <span class="study-plan-jieduan-title__icon-label">
              阶段
            </span>
          </div>
          <div class="">
            <div class="study-plan-jieduan-title__name">
              {{ item.name }}
            </div>
          </div>
        </div>
        <!-- v-if="item.children && item.children.length" -->
        <div
          v-if="item.nodeNum"
          class="study-plan-jieduan-title__right"
        >
          <KKCIcon
            class="study-plan-jieduan-title__icon-arrow "
            :class="{ 'rotate-180': isOpen }"
            name="icon-xiangqing-zhankai"
            :size="32"
          />
        </div>
      </div>
      <div v-if="item.description" class="study-plan-jieduan-title__desc">
        <div class="line-clamp-1 flex-1">
          {{ item.description.replace(/\n/g, ' ') }}
        <!-- -->
        </div>
        <div
          v-if="item.description.length > 24"
          class="study-plan-jieduan-title__all "
          @click.stop="handleStudyRequirement(item)"
        >
          更多
        </div>
      </div>
    </div>
    <div v-if="(item.fileNum || item.homeworkNum) && isOpen" class="title-tabs">
      <span
        class="title-tabs-item tabs-item__nodeNum is-active"
        @click.stop="fetchStudyPlanHomeworks(item, $event)"
      >
        课时 {{ item.nodeNum }}
      </span>
      <span
        v-if="item.fileNum"
        class="title-tabs-item tabs-item__fileNum"
        @click.stop="getStudyPlanFilesChildren(item, $event)"
      >
        资料 {{ item.fileNum }}
      </span>
      <span
        v-if="item.homeworkNum"
        class="title-tabs-item tabs-item__homeworkNum"
        @click.stop="getStudyPlanHomeworksChildren(item, $event)"
      >
        作业 {{ item.homeworkNum }}
      </span>
    </div>
    <StudyRequirementAndCommentModal
      v-model="showModal"
      title="学习要求"
      :content="description"
      @close="showModal = false"
    />
  </div>
</template>

<script setup lang="ts">
import { useCourseCatalogueLiveDetail } from '~/pages/learn-center/hooks/useCourseCatalogue'

interface IItem { }

const emits = defineEmits<{
  (e: 'action', item: IItem): void;
  //
  (e: 'updateOpenPaths', list: string[]): void;
}>()

const props = defineProps({
  item: {
    type: Object,
    required: true,
  },
  index: {
    type: Number,
    required: true,
  },
  isOpen: {
    type: Boolean,
    required: true,
  },
  // title: {
  //   type: String,
  //   required: true,
  // },
  // type: {
  //   type: String,
  //   required: true,
  // },
})

const route = useRoute()
const fetchStudyPlanHomeworks = async (item: IItem, event?: any) => {
  try {
    if (event?.target) {
      event.target.parentElement.querySelector('.is-active').classList.remove('is-active')
      event.target.classList.add('is-active')
    }
    Loading(true)
    const id = item.id
    if (!id) {
      throw new Error('缺少必要的 阶段ID')
    }
    const body = {
      goodsMasterId: route.query.id,
      userBuyUnitGoodsId: route.query.userBuyUnitGoodsId,
      tryListenId: route.query.tryListenId,
      planStageId: id,
    }
    const { data } = await getStudyPlanStageInfoProt(body)
    const { goodsCourseNodeList, unlocked, /* nodeNum, fileNum, homeworkNum  */ } = unref(data) || {}
    // const tree = ref([
    //   {
    //     id: id + '__' + 1,
    //     _jieduanId: id,
    //     title: '学习课时',
    //     nodeNum,
    //     unlocked,
    //     _nodeType: 'nodeNum',
    //     children: goodsCourseNodeList,
    //     fileNum,
    //     fileNumChildren: [],
    //     homeworkNum,
    //     homeworkNumChildren: [],
    //     // children
    //   },
    //   // {
    //   //   id: id + '__' + 2,
    //   //   _nodeType: 'fileNum',
    //   //   _jieduanId: id,
    //   //   fileNum,
    //   //   unlocked,
    //   //   title: '学习资料',
    //   //   children: []
    //   // },
    //   // {
    //   //   id: id + '__' + 3,
    //   //   _nodeType: 'homeworkNum',
    //   //   _jieduanId: id,
    //   //   unlocked,
    //   //   homeworkNum,
    //   //   title: '作业',
    //   //   children: []
    //   // }
    // ])
    const tree = goodsCourseNodeList.map((v3) => {
      return {
        ...v3,
        _jieduanId: id,
        unlocked
      }
    })
    // const { navList, } = useCourseCatalogueLiveDetail(tree,
    const { navList, openPaths } = useCourseCatalogueLiveDetail(ref(tree),
      {
        _classStartTime: item._classStartTime,
        _userSelectionMode: item._userSelectionMode,
        _studyPlanId: item._studyPlanId,
        path: item.path,
      }
    )
    console.log('navList', unref(navList), openPaths)
    updateOpenPaths(unref(openPaths))
    item.children = unref(navList)
  } catch (error) {
    console.error('获取学习计划阶段失败:', error)
    // TODO: 可以添加错误提示UI
  } finally {
    Loading(false)
  }
}
const updateOpenPaths = (paths: string[]) => {
  emits('updateOpenPaths', paths)
}
const handleAction = async (item: IItem) => {
  if (!props.isOpen) {
    await fetchStudyPlanHomeworks(item)
  }
  emits('action', item)
}
const showModal = ref(false)
const description = ref('')
const handleStudyRequirement = (item: IItem) => {
  console.log('item', item)
  if (!item.description) {
    Message('学习要求不能为空')
    return
  }
  description.value = item.description
  showModal.value = true
  // emits('studyRequirement', item)
}
// 常量定义
const NODE_TYPES = {
  FILE_NUM: 'fileNum',
  HOMEWORK_NUM: 'homeworkNum',
  FILE: 'file',
  HOMEWORK: 'homework'
} as const

// API端点定义
const API_ENDPOINTS = {
  GET_FILES: '/kukestudentservice/wap/studyPlan/getStudyPlanFiles',
  GET_HOMEWORKS: '/kukestudentservice/wap/studyPlan/getStudyPlanHomeworks'
} as const
/**
 * 获取学习计划 资料
*/
const getStudyPlanFilesChildren = async (item: IItem, event) => {
  try {
    if (event?.target) {
      event.target.parentElement.querySelector('.is-active').classList.remove('is-active')
      event.target.classList.add('is-active')
    }
    Loading(true)
    const id = item._jieduanId
    if (!id) {
      throw new Error('缺少必要的 阶段ID')
    }
    const { data: files } = await useHttp<{list:StudyPlanFile[]}>(API_ENDPOINTS.GET_FILES, {
      method: 'post',
      body: { id },
      transform: input => input.data
    })
    const list = (files.value.list || []).map((item2: StudyPlanFile) => ({
      ...item2,
      _nodeType: NODE_TYPES.FILE,
      unlocked: item.unlocked,
      _jieduanId: item._jieduanId,
      _userSelectionMode: item._userSelectionMode,
      _classStartTime: item._classStartTime,
      _studyPlanId: item._studyPlanId
    }))
    item.children = list
  } catch (error) {
    console.error('获取学习计划文件失败:', error)
    // TODO: 可以添加错误提示UI
  } finally {
    Loading(false)
  }
}
/**
 * 获取学习计划 作业
*/
const getStudyPlanHomeworksChildren = async (item: IItem, event) => {
  try {
    if (event?.target) {
      event.target.parentElement.querySelector('.is-active').classList.remove('is-active')
      event.target.classList.add('is-active')
    }

    Loading(true)
    const id = item._jieduanId
    if (!id) {
      throw new Error('缺少必要的 阶段ID')
    }
    const { data: homeworks } = await useHttp<{list:HomeworkInfo[]}>(API_ENDPOINTS.GET_HOMEWORKS, {
      method: 'post',
      body: { id },
      transform: input => input.data
    })
    const list = (homeworks.value.list || []).map((item2: HomeworkInfo) => ({
      ...item2,
      _nodeType: NODE_TYPES.HOMEWORK,
      unlocked: item.unlocked,
      _jieduanId: item._jieduanId,
      _userSelectionMode: item._userSelectionMode,
      _classStartTime: item._classStartTime,
      _studyPlanId: item._studyPlanId
    }))
    item.children = list
  } catch (error) {
    console.error('获取学习计划作业失败:', error)
    // TODO: 可以添加错误提示UI
  } finally {
    Loading(false)
  }
}

</script>

<style lang="scss" scoped>
.study-plan-jieduan-title {
  // display: flex;
  // justify-content: space-between;
  margin-bottom: 24px;

  &__main {
    display: flex;
    align-items: center;
  }

  &__icon {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    width: 80px;
    height: 80px;
    // padding: 8px 0 0 6px;
    border-radius: 16px;
    // background: rgba(235,35,48,0.1);
    @apply bg-brand/20;

    &-index {
      display: block;
      width: 68px;
      height: 32px;
      border-radius: 12px;
      background: var(--kkc-brand-text);
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      font-size: 24px;
      &.is-unlocked {
        background: transparent;
      }
    }
    &-label {
      display: block;
      font-size: 22px;
      line-height: 28px;
      color: var(--kkc-brand-text);
      margin-top: 4px;
    }
  }

  &__content {
    margin-left: 12px;
  }
  &__name {
    // overflow: hidden;
    // text-overflow: ellipsis;
    // white-space: nowrap;
    font-size: 32px;
    color: #111;
    line-height: 40px;
    font-weight: 500;
    @apply line-clamp-2;
  }
  &__desc {
    // overflow: hidden;
    // text-overflow: ellipsis;
    // white-space: nowrap;
    display: flex;
    font-size: 26px;
    color: #999;
    line-height: 32px;
    word-break: break-all;
    padding-left: 92px;
  }
  &__all {
    margin-left: 4px;
    flex-shrink: 0;
    cursor: pointer;
  }
  &__right {
    flex-shrink: 0;
    // display: flex;
    // align-items: center;
    // justify-content: center;
    // border-radius: 8px;
    // height: 30px;
    // width: 64px;
    // border: 1px solid #EBEDF0;
    cursor: pointer;
  }
  &__toggle {
    font-size: 14px;
    color: #111;
    line-height: 22px;
  }
  &__icon-arrow {
    margin-left: 4px;
    transition: transform 0.2s;
    &.rotate-180 {
      transform: rotate(180deg);
    }
  }
}
.title-tabs {
  display: flex;
  width: 750px;
  margin-left: -24px;
  background: linear-gradient( 180deg, #F2F3F5 0%, #FFFFFF 100%);
  border-radius: 16px 16px 0px 0px;
  padding-top: 24px;
  padding-left: 24px;
  padding-right: 24px;
.title-tabs-item{
    flex: 1;
    text-align: center;
    line-height: 40px;
    font-weight: 500;
    font-size: 32px;
    position: relative;
    padding: 20px 0;
    &::before{
      content: '';
      position: absolute;
      top: 27px;
      left: 0;
      width: 2px;
height: 26px;
background: #C9CDD4;
border-radius: 15px 15px 15px 15px;
    }
    &:first-child{
      &::before{
        display: none;
      }
    }
    &.is-active {
      color: var(--kkc-brand-text);
      background-color: white;
      border-radius: 32px 32px 0 0;
      &::before{
        display: none;
      }
    }
    &.tabs-item__nodeNum.is-active + .title-tabs-item {
      &::before{
        display: none;
      }
    }
    &.tabs-item__fileNum.is-active + .title-tabs-item {
      &::before{
        display: none;
      }
    }
}
}
</style>
