<template>
  <div class="tabber has-tabbar" :style="{ 'height': `${componentHeight}px` }">
    <component
      v-bind="{
        ...item.props,
        value: activeValue,
        cartCount,
        isDot:!!courseUpdateStatus
      }"
      :is="item.componentName"
      v-for="item of componentsData.components"
      v-show="showTabbar"
      :key="item.id"
      class="kkc-tabbar"
    />
  </div>
</template>
<script setup lang="ts">
import { useCartStore } from '~/stores/cart.store'
import { useUserStore } from '~/stores/user.store'
import { useSystemStore } from '~/stores/system.store'
const { isXcx } = useAppConfig()
const cartStore = useCartStore()
const userStore = useUserStore()
const systemStore = useSystemStore()
const route = useRoute()
const cartCount = ref(0)
const { courseUpdateStatus } = useCourseUpdateStatus()
const getCartCount = () => {
  cartCount.value = cartStore.getCartCount()
}

watch(() => cartStore.state.cartCount, (newV:any) => {
  cartCount.value = newV
}, {
  immediate: true,
  deep: true
})
// defineProps({
//   components: {
//     type: Array,
//     default: () => []
//   },
// })

const { data: componentsData } = await getReleasePersonCenterORNavigationDetails({
  pageId: '102',
  productSide: isXcx ? 5 : 7,
  pageTag: 1,
})
const isBottomNavigationWap = ref(0)
const isCustomePage = ref(false)
const getBottomNavigateStatus = async (id: string) => {
  // 获取自定义页面 导航配置
  const { data: customPageNavigateData } = await getCustomPageNavigationStatus({
    customPageId: id as string
  })
  isBottomNavigationWap.value = customPageNavigateData.value?.isBottomNavigationApp as number
}
watch(() => [route.name, route.params.id], ([newV, newId]) => {
  if (newId && newV === 'cp-id') {
    isCustomePage.value = true
    getBottomNavigateStatus(newId as string)
  } else {
    isCustomePage.value = false
  }
}, {
  immediate: true
})

const localId = useCookie<string>('LT')
const { data: kuke99DefaultHomeData } = useKuke99DefaultHomeData()
const showTabbar = computed(() => {
  if (route.meta?.pageid === '15' && route.query?.isShowBack) { return true }
  // TODO 运营中心修改后删除
  // if (route.meta?.pageid === '11') { return true }
  // if (!TABBAR_PAGE_ID.includes(route.meta.pageid as string)) { return false }
  if (!unref(componentsData)?.components?.length) { return false }
  // 自定义页面开启底部导航展示  不开启就不展示
  if (isBottomNavigationWap.value) { return true }
  if (isCustomePage.value && !isBottomNavigationWap.value) { return false }
  const { applicationPage = '' } = unref(componentsData).components[0]?.props?.bottomNavigationApp || {}
  return applicationPage.split(',').includes(route.meta.pageid)
})
const activeValue = computed(() => {
  if (!unref(componentsData)?.components[0]) { return }
  const { menuList = [] } = unref(componentsData)?.components[0].props.bottomNavigationApp || {}
  //
  if (route.path === '/' && kuke99DefaultHomeData.value.cpId && !localId.value) {
    return '-1'
  }
  const temp = menuList.find((v) => {
    const pageid = String(v.dicFunctionId)

    if (route.name === 'cp-id') {
      return route.params.id === v.customPageId
    }
    return pageid === route.meta.pageid
  }) || {}
  return temp.id
})
const componentHeight = ref(0)

const updateComponentHeight = () => {
  const component = document.querySelector('.kkc-tabbar')
  componentHeight.value = component?.clientHeight ?? 0
  if (componentHeight.value) { systemStore.setTabbarHeight(`${componentHeight.value}PX`) }
  console.log(componentHeight.value, '------------------')
}
// 页面切换
const handlePageChange = async () => {
  if (document.hidden) {
    console.log('页面切出')
  } else if (userStore.isLogin) {
    if (!isXcx) {
      await cartStore.fetchCartCount()
    }
  }
}
onMounted(async () => {
  nextTick(async () => {
    const component = document.querySelector('.kkc-tabbar')
    if (component) {
      updateComponentHeight()
    }
    // if (route.name === 'custom-page-id') {
    //   getBottomNavigateStatus(route.params.id as string)
    // }
    if (userStore.isLogin) {
      await cartStore.fetchCartCount()
      getCartCount()
    }
  })
  document.addEventListener('visibilitychange', handlePageChange)
})
onUnmounted(() => {
  document.removeEventListener('visibilitychange', handlePageChange)
})
onUpdated(() => {
  // mounted 中获取DOM时，component第一次加载组件并未渲染好为null
  // 所以放在updated中获取，在DOM更新时，进行高度获取与设置
  // 只要tabbar组件更新，即更新高度
  updateComponentHeight()
})
//
</script>

<style lang="scss">
.tabber {
  &::after {
    display: block;
    content: '';
    height: 60px;
    padding-bottom: constant(safe-area-inset-bottom);
    /*兼容 IOS<11.2*/
    padding-bottom: env(safe-area-inset-bottom);
    /*兼容 IOS>11.2*/
  }
}

.has-tabbar {
  .kkc-tabbar {
    width: 7.5rem;
    position: fixed;
    left: 50%;
    transform: translateX(-50%);
    bottom: 0;
    z-index: 19;
  }
}
</style>
