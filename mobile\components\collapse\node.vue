<template>
  <div :class="[bem(), mode,isActive?'is-active':'']" :data-level="item.level" @click="handleAction(item)">
    <div :class="[bem('status'), pageType ? '' : line]">
      <template v-if="isSection">
        <KKCIcon name="icon-xiangqing-jie" color="var(--kkc-brand)" :size="36" @click="handleStatus" />
      </template>
      <template v-else-if="isClassHour">
        <KKCIcon
          v-if="
            (item.type === 2 || item.type === 1) &&
              select === (liveType === 1 ? item.polyvVideoId : item.ccVideoId) &&
              (liveType === 1 ? item.polyvVideoId : item.ccVideoId)
          "
          name="icon-icon_zhibo"
          class="!mr-[10px]"
          :size="36"
          color="#F72331"
          @click="handleStatus"
        />
        <img v-else-if="item.type === CourseType.Audio && (!KKCCourseAudioPlayerRef?.expose.isPause || item.audioRef?.isPause===false)" class="w-[36px] mr-[16px]" src="@/assets/images/activationCode/play_status.png" alt="">
        <KKCIcon
          v-else
          :name="typeIconName"
          :class="{
            '!mr-[8px]': item.type === 1,
            '!mr-[9px]': item.type === 2,
          }"
          :size="[3, 4].includes(item.type) ? 36 : 36"
          @click="handleStatus"
        />
        <!-- 'ml-[8px]': [3, 4].includes(item.type), -->
        <!-- '!mr-[14px]': [3, 4].includes(item.type),
          'mt-[10px]': [3, 4].includes(item.type), -->
      </template>
    </div>
    <div :class="[bem('content')]">
      <div :class="[bem('main')]">
        <div
          :class="[
            bem('main', 'title'),
          ]"
        >
          <!-- isClassHour && [1, 2].includes(item.type) ? 'mt-[8px]' : '' -->
          <div
            v-if="item.level === 2 && item.liveStatus === CourseLiveStatus.LivingStreaming"
            class="tag_living_second_level"
          >
            直播中
          </div>
          <span v-if="item.level === 3 && getLastLearn(item)" class="last-tag">最近学习</span>
          <span v-if="item.level === 3" :class="(item.liveStatus === 2 || getLastLearn(item)) && 'red'">【{{ nameType
          }}】</span>
          <span :class="(item.liveStatus === 2 || getLastLearn(item)) && 'red'">
            {{ item.name }}</span>
        </div>
        <div v-if="isClassHour && item.desc" :class="[bem('main', 'desc')]">
          <!-- 音频课时 -->
          <div v-if="item.desc==='audio'" class="w-[100%]">
            <span v-if="!(!isAllFree && !item.isFree)" class="progress" :class="item.learnStatus === '已学完' && 'complete'">
              {{ item.learnStatus }}
              {{
                item.learnStatus === "已学完" ? 100 : item.learnProgress
              }}%</span>
            <KKCCourseAudioPlayer
              v-if="!(!isAllFree && !item.isFree)"
              ref="KKCCourseAudioPlayerRef"
              :url="item.audioUrl"
              :node-id="item.id"
              :learn-progress="item.learnProgress"
              :goods-master-id="goodsMasterId"
              :last-time-point="item.lastTimePoint"
              :audio-duration="item.videoDuration"
              :load-audio="item.loadAudio"
              @handleChanged="handleAction(item)"
              @playEnd="audioPlayEnd(item)"
            />
          </div>
          <span v-else class="text-flex">
            <KKCIcon
              :name="
                item.type === 4 && item.testpapers
                  ? 'icon-deta_icon_shijian'
                  : 'icon-xiangqing-shijian'
              "
              :size="28"
            />
            <!-- eslint-disable-next-line vue/no-v-html -->
            <span class="mr-[32px]" v-html="item.desc" />
            <!-----直播回放 | 录播 ---->
            <div v-if="(isLivedStream || isRecord) && item.videoDuration" class="mr-left">
              <!----学习进度已完成 ---->
              <KKCIcon
                v-if="item.learnStatus === '已学完'"
                class=""
                color="#00C555"
                name="icon-xiangqing-wancheng1"
                :size="28"
              />
              <span class="progress" :class="item.learnStatus === '已学完' && 'complete'">
                {{ item.learnStatus }}
                {{
                  item.learnStatus === "已学完" ? 100 : item.learnProgress
                }}%</span>
            </div>
            <!-----试卷---->
            <template v-if="item.type === 4 && item.testpapers">
              <span class="progress" :class="item.testpapers.doStatus === 2 && 'complete'">
                <KKCIcon v-if="item.testpapers.doStatus === 2" name="icon-xiangqing-wancheng1" :size="28" />
                {{ paperStatus[item.testpapers.doStatus] }}
                {{ item.testpapers.doCount }}/{{ item.testpapers.itemCount }}
              </span>
            </template>
          </span>
          <div v-if="item.unlockTime && item.unlockStatus === 0" class="w-full mt-[12px]">
            <span class="mr-[16px]">
              <KKCIcon name="icon-suo" :size="24" />
              解锁时间：{{ item.unlockTime }}
            </span>
          </div>
        </div>
      </div>

      <div :class="[bem('actions')]">
        <template v-if="isSection">
          <KKCIcon
            v-if="item?.children?.length"
            class="animate-180"
            :class="{ 'rotate-180': isOpen }"
            name="icon-xiangqing-zhankai"
            :size="32"
          />
        </template>

        <template v-else-if="isClassHour">
          <template v-if="!isAllFree && !item.isFree">
            <KKCIcon name="icon-suobeifen1" color="#C5CAD5" :size="48" @click="handleAction(item)" />
          </template>
          <!-- <KKCIcon v-if="isLock && !isAudition" name="icon-suobeifen1" color="#C5CAD5" :size="48"
          @click="handleAction(item)" /> -->
          <!-- <KKCTag v-else-if="isAudition" size="mini">
            试听
          </KKCTag> -->
          <template v-else-if="item.unlocked === false">
            <KKCIcon name="icon-suobeifen1" color="#C5CAD5" :size="48" />
          </template>
          <template v-else-if="liveDetailStore.freezeStatus && !item.isFree">
            <KKCIcon name="icon-suobeifen1" color="#C5CAD5" :size="48" />
          </template>
          <!-- 日期模式、闯关模式展示锁的状态  -->
          <template v-else-if="[2, 3].includes(item._userSelectionMode) && item.unlockStatus === 0 && item.type === 2">
            <template v-if="item._userSelectionMode === 3">
              <!-- 日期模式展示播放按钮 -->
              <KKCIcon name="icon-wapxuexibofang" color="#C5CAD5" :size="48" />
            </template>
            <template v-else>
              <nuxt-icon name="chuang-guan" class="text-[48px]" filled />
            </template>
          </template>
          <template v-else>
            <!----直播课操作按钮---->
            <div v-if="isLiveStreaming" class="k-tag" :class="[liveStatusName === '直播中' && 'active']">
              <!-- 查看回放 -->
              <template v-if="item.liveStatus === 3">
                {{
                  select ===
                    (liveType === 1 ? item.polyvVideoId : item.ccVideoId) &&
                    (liveType === 1 ? item.polyvVideoId : item.ccVideoId)
                    ? "暂停"
                    : isAudition?'试听':liveStatusName
                }}
              </template>
              <!-- 其他状态 -->
              <template v-else>
                {{ isAudition?'试听':liveStatusName }}
              </template>
            </div>
            <!----录播课操作按钮---->
            <template v-else-if="isRecord">
              <KKCIcon
                v-if="
                  select ===
                    (liveType === 1 ? item.polyvVideoId : item.ccVideoId) &&
                    (liveType === 1 ? item.polyvVideoId : item.ccVideoId)
                "
                name="icon-xiangqing-zanting"
                class="text-brand"
                :size="48"
              />
              <template v-else>
                <div v-if="nameType !== '试听'" class="icon-play">
                  <KKCIcon name="icon-wapxuexibofang" :size="48" />
                </div>
                <KKCTag v-else color="#666666" style="background-color: #f7f7f7;">
                  试听
                </KKCTag>
              </template>
            </template>
            <!----讲义操作按钮---->
            <template v-else-if="isHandout">
              <KKCIcon name="icon-chakan" :size="48" color="C5CAD5" />

              <KKCIcon
                name="icon-xuexizhongxin-xiazai"
                :size="48"
                color="C5CAD5"
                class="ml-[28px]"
                @click.stop="handleDownload(item)"
              />
            </template>
            <!----试卷操作按钮---->
            <template v-else-if="isQuestion">
              <KKCIcon
                v-if="item.learnStatus !== '2'"
                name="icon-deta_icon_zuoti"
                color="#C5CAD5"
                :size="48"
              />
              <KKCIcon v-else name="icon-chakanbaogao" :size="48" />
            </template>
            <!----音频操作按钮---->
            <template v-else-if="isAudio">
              <div v-if="KKCCourseAudioPlayerRef?.expose.isPause || item.audioRef?.isPause" class="text-[48px] text-[#C5CAD5]">
                <KKCIcon name="icon-wapxuexibofang" :size="48" />
              </div>
              <KKCIcon v-else class="text-brand" name="icon-xiangqing-zanting" :size="48" />
            </template>
          </template>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { events, isWeChat } from '@kukefe/kkutils'
import type { NodeMode, IItem } from './type'
import { CourseType, CourseLiveStatus } from './type'
import { useLiveDetailStore } from '~/stores/live-detail.store'

const { isXcx } = useAppConfig()
const bem = useBem('catalog-node')
const { query: { lastLearn: lastLearnQuery = '' } } = useRoute()
const { track } = useTrackEvent()
//
const liveDetailStore = useLiveDetailStore()

const emits = defineEmits<{
  (e: 'status', event: IItem): void;
  (e: 'action', event: IItem): void;
}>()
const route = useRoute()
const { id: goodsMasterId }:any = route.query
const props = defineProps<{
  item: IItem,
  mode: NodeMode,
  isFree?: boolean, // 课程是否免费
  // isBuy?: boolean, // 课程是否购买
  // 是否打开
  isOpen?: boolean
  tree?: any,
  select: any,
  liveType?:number
  pageType?:string
  isAllFree?:number
  selectNodeId?:string
}>()

console.log('props.item', props.item)
console.log('select', props.select)
console.log('props.liveType', props.liveType)

onMounted(async () => {
  nextTick(() => {
    // 设置默认第一条数据
    if (props.item.type) {
      // if (['1-1', '1-1-1', '1-1-1-1-1'].includes(String(props.item.path))) {
      if (['1-1', '1-1-1'].includes(String(props.item.path))) {
        sessionStorage.setItem('default', JSON.stringify(props.item))
      }
    }
  })
})

// 最近学习的课时
const getLastLearn = ({ id, lastLearn }: any) => {
  return lastLearnQuery ? lastLearnQuery === id : lastLearn
}

const isActive = computed(() => {
  if (props?.selectNodeId) {
    return props?.selectNodeId === props.item?.id
  } else {
    return false
  }
})

const isClassHour = computed(() => {
  return props.mode === 'class-hour'
})
const isSection = computed(() => {
  return props.mode === 'section'
})
const line = computed(() => {
  if (props.mode === 'section' && !props.isOpen) {
    return ''
  }
  return bem('line')
})
const nameType = computed(() => {
  let text = ''
  switch (props.item.type) {
    case 1:
      text = '直播'
      break
    case 2:
      if (props.isAllFree === 0 && props.item.isFree) {
        text = '试听'
      } else {
        text = '录播'
      }
      break
    case 3:
      text = '讲义'
      break
    case 4:
      text = '试题'
      break
    case 5:
      text = '音频'
      break
  }
  return text
})
// 根据课程类型展示图标
const typeIconName = computed(() => {
  const map = {
    [CourseType.Live]: 'icon-xiangqing-zhibo',
    [CourseType.Record]: 'icon-xiangqing-lubo',
    [CourseType.Handout]: 'icon-jiangyi1',
    [CourseType.Question]: 'icon-shijuan',
    [CourseType.Audio]: 'icon-yinpin',
  }
  return map[props.item.type] || 'icon-xiangqing-zhibo'
})
// 当课程为非免费且不是试听课
// const isLock = computed(() => {
//   return !props.isFree || !isAudition || !props.isBuy
// })
// 试听课
const isAudition = computed(() => {
  return props.isAllFree === 0 && props.item.isFree
})
// 直播
const isLiveStreaming = computed(() => {
  return props.item.type === CourseType.Live
})
// 0未开始 2直播中 3回放 4暂无回放
const liveStatusMap = {
  [CourseLiveStatus.NotLiveStreaming]: '未开始',
  [CourseLiveStatus.LivingStreaming]: '直播中',
  [CourseLiveStatus.LivedStreaming]: '查看回放',
  [CourseLiveStatus.LivedNone]: '暂无回放',
}
const liveStatusName = computed(() => {
  return liveStatusMap[props.item.liveStatus] || ''
})
// 是否是直播回放
const isLivedStream = computed(() => {
  return props.item.liveStatus === CourseLiveStatus.LivedStreaming
})
// 录播
const isRecord = computed(() => {
  return props.item.type === CourseType.Record
})
// 讲义
const isHandout = computed(() => {
  return props.item.type === CourseType.Handout
})
// 试卷
const isQuestion = computed(() => {
  return props.item.type === CourseType.Question
})
// 音频
const isAudio = computed(() => {
  return props.item.type === CourseType.Audio
})

// 试卷状态列表
const paperStatus = ref(['未开始', '刷题中', '已做完'])

const handleStatus = () => {
  emits('status', props.item)
}

const KKCCourseAudioPlayerRef = ref()
const curActionCourse = ref<string>('')
// 处理点击事件
const handleAction = (item: IItem) => {
  console.log(item)
  if (unref(liveDetailStore?.freezeStatus) && !item.isFree && !!item.type) {
    Message('此订单退款权益冻结中，暂无法学习')
    return
  }
  curActionCourse.value = item.id
  if (item.type === 5) {
    const audioPlayer = ref(KKCCourseAudioPlayerRef.value.expose)

    // 2. 使用reactive保持对象响应性
    item.audioRef = reactive({
      ...toRaw(audioPlayer.value),
    })
    // item.audioRef = KKCCourseAudioPlayerRef.value.expose
    item.loadAudio = true
  }
  console.log('🚀 ~ handleAction ~ item:', item)
  // [AI-GEN] 判断是否为试卷类型
  if (item.type === 4) {
    // 使用双重判断确保准确性
    const isCompleted = item.learnStatus === '2' ||
                       (item.testpapers && item.testpapers.doStatus === 2)
    if (isCompleted) {
      // 网课试卷-报告
      track({
        category: '学习中心',
        action: '网课试卷-报告'
      })
    } else {
      // 网课试卷-答题
      track({
        category: '学习中心',
        action: '网课试卷-答题'
      })
    }
  }
  emits('action', item)
}
const audioPlayEnd = (item:any) => {
  console.log('音频播放结束', item)
  if (item?.audioRef?.isAudioEnd) {
    item.learnStatus = '已学完'
  }
}
/**
 * 下载讲义
 * */
const handleDownload = debounceFunc(async (item: any) => {
  // if (unref(liveDetailStore?.freezeStatus)) {
  //   Message('此订单退款权益冻结中，暂无法学习')
  //   return
  // }
  if (item._classStartTime) {
    Message(`课程将于${item._classStartTime}开课，敬请期待`)
    return
  }
  if (item.unlocked === false) {
    const { error, data } = await useHttp('/kukestudentservice/wap/studyPlan/unlockNewStudyPlanStage', {
      body: {
        id: item._jieduanId
      }
    })
    console.log('error: ', unref(error)?.data)
    if (unref(error)?.data?.code === '22031') {
      //
      if (process.client) {
        events.publish('studyPlan:checkJieDuan', item, 1)
      }
      // return
    }
    if (unref(error)?.data?.code === '10001' && unref(error)?.data?.msg === '用户已解锁该学习计划阶段') {
      if (process.client) {
        events.publish('studyPlan:checkJieDuan', item, 2)
      }
    }
    if (unref(data)?.code === '10000') {
      //
      if (process.client) {
        events.publish('studyPlan:checkJieDuan', item, 2)
      }
      // return
    }
    // emits('action', item)
    return
  }
  //
  if (item.unlockStatus === 0 && [2, 3].includes(item._userSelectionMode) && item.type === 2) {
    if (item._userSelectionMode === 3) {
      //
      Message('暂未到解锁时间，敬请期待')
    } else {
      // 7、点击未闯关的录播课时，toast提示：请先完成前面关卡的闯关哦
      //
      Message('请先完成前面关卡的闯关哦')
    }
    return
  }
  if (isXcx && !os?.isPc) {
    handleWeChatPreview(item.handoutUrl, 1, item.name)
  } if (isWeChat() && !isXcx) {
    Message('请使用浏览器打开并进行下载～')
  } else {
    downloadFile(item.handoutUrl, item.name)
  }
}, 2000, true)

</script>

<style lang="scss" scoped>
.red {
  color: var(--kkc-brand-text);
}

.catalog-node {
  display: flex;
  align-items: center;
  position: relative;
  // padding: 24px 24px 24px 0;
  padding: 24px 0 24px 0;

  &__main--title {
    .last-tag {
      font-size: 22px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: var(--kkc-brand-text);
      padding: 3px 8px;
      background: rgba(var(--kkc-brand-text), 0.05);
      border-radius: 8px;
      // margin-right: 24px;
      margin-left: 8px;
    }
  }

  .text-flex {
    display: flex;
    align-items: center;
  }

  .mr-left {
    margin-left: 2px;
  }

  &.is-active {
    // height: 80px;
    background: rgba(var(--kkc-brand-text), 0.05);
    border-radius: 8px;

    .catalog-node__status {
      .kkc-icon {
        background-color: initial;
      }
    }

    .catalog-node__main--title {
      @apply text-brand;
    }

    .k-tag {
      @apply text-brand;
      background: rgba(var(--kkc-brand-text), 0.05);
      border-radius: 8px;
      display: flex;
      align-items: center;
    }
  }

  &__status {
    position: relative;
    align-self: flex-start;
    cursor: pointer;

    .kkc-icon {
      margin-right: 16px;
    }
  }

  .catalog-node__line  {
    // align-self: auto;
    // position: relative;
    position: initial;
    // flex-shrink: 0;
  }
  .catalog-node__line::before {
    content: "";
    position: absolute;
    left: calc(36px / 2 - 1px);
    // top: 36px;
    top: calc(36px + 24px + 9px);
    width: 1px;
    // height: 96px;
    height: calc(100% - 48px);
    background-color: #dddddd;
  }

  &.section {
    .catalog-node__status {
      &::before {
        height: 24px;
      }
    }
  }

  &__actions {
    cursor: pointer;

    .k-tag {
      font-size: 22px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #666666;
      padding: 6px 12px;
      background: #f7f7f7;
      border-radius: 8px;

      &.active {
        color: var(--kkc-brand-text);
        background: rgba(var(--kkc-brand-rgb) / 0.1);
      }
    }
  }

  &__content {
    flex: 1;
    display: flex;
    align-items: center;
    position: relative;
    word-break: break-all;
  }

  &.section &__content::after {
    content: "";
    position: absolute;
    left: 0;
    bottom: -16px;
    width: 100%;
    height: 1px;
    background-color: #eee;
  }

  &__main {
    flex: 1;

    &--title {
      font-size: 28px;
      font-weight: 400;
      line-height: 36px;
      // 直播中标签样式
    .tag_living_second_level {
      flex-shrink: 0;
      width: 82px;
      height: 34px;
      text-align: center;
      line-height: 34px;
      margin-right: 8px;
      border-radius: 8px;
      font-size: 22px;
      float: left;
      color: var(--kkc-brand-text);
      border: 1px solid var(--kkc-brand-text);
    }

      &.section {
        font-weight: 500;
      }
    }

    &--desc {
      display: flex;
      align-items: center;
      align-self: flex-start;
      flex-wrap: wrap;
      margin-top: 16px;
      padding-left: 10px;
      font-size: 24px;
      font-weight: 400;
      color: #999999;
      line-height: 28px;

      .kkc-icon {
        margin-right: 4px;
      }

      .progress {
        // padding-left: 10px;
      }

      .complete {
        // color: #01ae9d;
        color: #00C555;
      }
    }
  }
}

.icon-play {
  // width: 44px;
  // height: 44px;
  // background: #c5cad5;
  // border-radius: 41px;
  // backdrop-filter: blur(10px);
  font-size: 48px;
  // font-weight: 400;
  color: #c5cad5;
}
</style>
