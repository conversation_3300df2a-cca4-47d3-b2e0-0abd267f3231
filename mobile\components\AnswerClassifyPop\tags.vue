<template>
  <div v-for="(item,index) in classifyList" :key="index" class="classify-list">
    <div class="top-title">
      <div class="title-left">
        {{ item.title }}
      </div>
      <div v-if="item.showFoldButton" class="title-right" @click="handleAction(item)">
        <span> {{ item.fold ? '收起' : '展开' }} </span>
        <KKCIcon :name="item.fold ? 'icon-xiangqing-shouqi' : 'icon-xiangqing-zhankai'" color="#666" :size="24" />
      </div>
    </div>
    <div ref="listWrap" class="list-wrap" :class="(item.fold || !item.showFoldButton) ? 'h-auto' : 'height-fold'">
      <div
        v-for="(itemTag, indexTag) in item.value"
        :key="indexTag"
        class="classify-item"
        :class="itemTag.status === 1 ? 'item-active' : ''"
        @click="handleActive(itemTag, item)"
      >
        {{ itemTag.dataName }}
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import px2rem from '../../../base/utils/px2rem'
// types
import type { TagItem, ChoiceTag, Region } from '~/apis/answer/types'

const props = defineProps<{
  classifyList: TagItem[],
  cateId: string
}>()

const emits = defineEmits<{
  (e: 'change', val: ChoiceTag): void
}>()

const classifyList = computed(() => {
  return props.classifyList
})

/**
 * 展开收起
 */
const handleAction = (item: TagItem) => {
  item.fold = !item.fold
}

/**
 * 点击分类
 */

const handleActive = (itemTag: Region, item: TagItem) => {
  // 重置
  item.value.forEach((el) => {
    el.status = 0
  })

  // 点击选中判断
  itemTag.status = itemTag.status ? 0 : 1

  const { usedId, dataName } = itemTag

  emits('change', { usedId, dataName, key: item.key })
}
const listWrap = ref<HTMLElement[]>([])
const handleList = () => {
  listWrap.value.forEach((wrap, index) => {
    const value1 = parseFloat((px2rem(wrap.scrollHeight) as string)?.replace('rem', ''))
    const value2 = parseFloat((px2rem(160) as string)?.replace('rem', ''))
    console.log(value1, value2, 'value2')
    if (value1 > value2) {
      classifyList.value[index].showFoldButton = true
    }
  })
}
const handleBtn = () => {
  nextTick(() => {
    handleList()
  })
}
watch(() => props.cateId, (newV) => {
  if (newV) {
    handleBtn()
  }
})
onMounted(() => {
  handleBtn()
})
</script>
<style lang="scss" scoped>
.classify-list {
  margin-bottom: 28px;

  .top-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24px;

    .title-left {
      font-family: PingFang SC;
      font-weight: 500;
      font-size: 28px;
      color: #111111;
      line-height: 34px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }

    .title-right {
      display: flex;
      align-items: center;
      color: #666666;
      font-size: 24px;
    }
  }

  .list-wrap {
    display: flex;
    flex-wrap: wrap;

    .classify-item {
      text-align: center;
      margin-bottom: 20px;
      background: #F7F7F7;
      border-radius: 12px;
      margin-right: 20px;
      border: 2px solid #f7f7f7;
      color: #666666;
      font-size: 26px;
      font-weight: 400;
      padding: 10px 12px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    .item-active {
      font-weight: 500;
      @apply text-brand border-brand bg-brand/5;
    }
  }

  .height-fold {
    height: 160px;
    overflow-y: hidden;
  }

}
</style>
