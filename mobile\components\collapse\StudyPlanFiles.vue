<template>
  <div
    :class="[mode, 'plan-files-item', { 'is-active': isActive }]"
    :data-level="item.level"
    @click="handleAction($event, item)"
  >
    <div class="plan-files-item-line">
      <!--   -->
      <KKCIcon v-if="item.resourceType === 2" name="icon-xiangqing-lubo" :size="36" />
      <KKCIcon v-else name="icon-jiangyi1" :size="36" />
    </div>
    <div class="flex-1 flex">
      <div class="flex-1">
        <div v-if="item.resourceType === 2" class="line-clamp-3 leading-[34px] text-[28px]">
          <!-- @click.stop="handlePlay(item)" -->
          <!-- <span
            class="text-[12px] bg-[rgba(0,0,0,0.05)] text-center text-[#666666] h-[20px] w-[44px] leading-[20px] rounded-[4px] inline-block mr-[8px]"
          >录播课</span> -->
          <span class="">
            {{ item.videoTitle }}
          </span>
        </div>
        <div v-else-if="item.resourceType === 1" class="line-clamp-3 leading-[34px] text-[28px]">
          <!-- @click.stop="handlePreview(item)" -->
          <span class="">
            {{ item.fileName }}
          </span>
        </div>
        <div v-if="item.resourceType === 1" class="flex items-center mt-[12px]">
          <!-- @click.stop="handlePreview(item)" -->
          <span class="flex items-center text-[24px] text-[#666666] leading-[30px]">
            <KKCIcon name="icon-deta_icon_wenjian" :size="28" class="mr-[4px]" />
            <span>
              文件大小：
            </span>
            <span>
              {{ item.fileSizeFormat }}
            </span>
          </span>
          <span class="flex items-center ml-[24px] text-[24px] text-[#666666] leading-[30px]">
            <span>
              格式：
            </span>
            <span>
              {{ item.fileType || item.fileExt }}
            </span>
          </span>
        </div>
        <div v-else-if="item.resourceType === 2" class="flex items-center">
          <!-- @click.stop="handlePlay(item)" -->
          <span class="flex items-center text-[24px] text-[#666666] leading-[30px]">
            <KKCIcon name="icon-xiangqing-shijian" :size="28" class="mr-[4px]" />
            <span>
              时长：
            </span>
            <span>
              {{ item.durationFormat }}
            </span>
          </span>
          <span class="flex items-center ml-[24px] text-[24px] text-[#666666] leading-[30px]">
            <span>
              {{ item.learnStatusName }}
            </span>
            <span class="ml-[8px]">
              {{ item.studyProgressName }}
            </span>
          </span>
        </div>
      </div>
      <div class="flex flex-shrink-0 cursor-pointer">
        <template v-if="item.unlocked === false || liveDetailStore.freezeStatus">
          <KKCIcon name="icon-suo" color="#C5CAD5" :size="48" class="ml-[16px]" />
        </template>
        <!-- <template v-if="liveDetailStore.freezeStatus">
          <KKCIcon name="icon-suo" color="#C5CAD5" :size="48" class="ml-[16px]" />
        </template> -->
        <template v-else>
          <!-- 此阶段为未解锁状态：学习资料全部展示锁图标，点击文件名称展示继续学习弹窗 -->
          <template v-if="item.resourceType === 1">
            <KKCIcon
              name="icon-xuexizhongxin-xiazai"
              color="#C5CAD5"
              :size="48"
              class="ml-[16px]"
              @click.stop="handleDownload(item)"
            />
          </template>
          <template v-else-if="item.resourceType === 2">
            <KKCIcon name="icon-xiangqing-bofang" color="#C5CAD5" :size="48" @click.stop="handlePlay(item)" />
          </template>
        </template>
      </div>
    </div>
    <!-- 购买弹框 -->
    <Teleport to="body">
      <LearnBuyDialog
        ref="kkPopupRef"
        :select-goods="selectGoods"
        :goods-master-id="(goodsMasterId as string)"
        :specs-id="specsId"
      />
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import { isWeChat, events } from '@kukefe/kkutils'
import { Base64 } from 'js-base64'
import type { NodeMode } from './type'
import { LearnBuyDialog } from '~/pages/learn-center/components/index'
import type { StudyPlanFile } from '~/types/study-plan-files'
import { useLiveDetailStore } from '~/stores/live-detail.store'
const { isXcx, isPc } = useAppConfig()
const { getViewUrl } = useFileView()
const liveDetailStore = useLiveDetailStore()
//
const route = useRoute()
const { id: goodsMasterId, specsId = '' } = route.query
const emits = defineEmits<{
  (e: 'status', event: StudyPlanFile): void;
  (e: 'action', event: StudyPlanFile): void;
  // (e: 'closeVideo'): void;
}>()

// const select = ref(null)
const videoId = ref('')

const props = defineProps<{
  item: StudyPlanFile,
  mode: NodeMode,
  isFree?: boolean, // 课程是否免费
  isBuy?: boolean, // 课程是否购买
  isOpen?: boolean,
  isAllFree?: number,
  //
  selectNodeId?: string
}>()
// provide('isVideoBuy', props?.isAllFree)
const isActive = computed(() => {
  if (props?.selectNodeId) {
    return props?.selectNodeId === props.item?.id
  } else {
    return false
  }
})
//
const selectGoods = ref<any>({})
const kkPopupRef = ref(null)

// 判断是否过期
const checkGoodsExpired = async () => {
  const { data } = await useHttp<any>(ApisLc.checkGoodsExpire, {
    transform: input => input.data,
    body: {
      goodsMasterId,
      ...(specsId && { goodsSpecificationItemId: specsId })
    },
    default: () => [],
  })
  return data
}

// const handleStatus = () => {
//   emits('status', props.item)
// }

//

// const showVideo = ref(false)
// const currentPlayVideoId = ref('')
// watch(
//   () => showVideo.value,
//   (val) => {
//     console.log('val: ', val)
//     if (!val) {
//       currentPlayVideoId.value = ''
//       emits('closeVideo')
//     }
//   }
// )

const curActionCourse = ref<string>('')
// 处理点击事件
const handlePlay = async (item: StudyPlanFile) => {
  console.log('学习资料 - handleAction', item)
  if (unref(liveDetailStore?.freezeStatus)) {
    Message('此订单退款权益冻结中，暂无法学习')
    return
  }
  if (!item.videoId) {
    Message('videoId不存在')
    return
  }
  curActionCourse.value = item.id
  videoId.value = item.videoId
  emits('action', item)
}

const handleAction = async (e: Event, item: StudyPlanFile) => {
  console.log('handleAction', item)
  if (unref(liveDetailStore?.freezeStatus)) {
    Message('此订单退款权益冻结中，暂无法学习')
    return
  }
  const checkData = await checkGoodsExpired()
  const { status } = checkData.value
  if (status !== 1) {
    selectGoods.value = checkData.value
    kkPopupRef.value?.show()
    return
  }
  // Message('再接再厉\n请先学完上个阶段的课时\n再学习本阶段内容哦（弹窗待实现,先使用toast代替）')
  // events.publish('studyPlan:checkJieDuan', item, 1)
  // return
  // emits('action', item)
  if (item._classStartTime) {
    Message(`课程将于${item._classStartTime}开课，敬请期待`)
    e.stopPropagation()
    e.preventDefault()
    return
  }
  // TODO unlocked 学习计划阶段是否解锁
  if (item.unlocked === false) {
    const { error, data } = await useHttp('/kukestudentservice/wap/studyPlan/unlockNewStudyPlanStage', {
      body: {
        id: item._jieduanId
      }
    })
    console.log('error: ', unref(error)?.data)
    if (unref(error)?.data?.code === '22031') {
      // 再接再厉 请先学完上个阶段的课时
      if (process.client) {
        events.publish('studyPlan:checkJieDuan', item, 1)
      }
      // return
    }
    if (unref(error)?.data?.code === '10001' && unref(error)?.data?.msg === '用户已解锁该学习计划阶段') {
      if (process.client) {
        events.publish('studyPlan:checkJieDuan', item, 2)
      }
    }
    if (unref(data)?.code === '10000') {
      // 恭喜解锁本学习阶段
      if (process.client) {
        //   events.publish('studyPlan:refresh', item)
        events.publish('studyPlan:checkJieDuan', item, 2)
      }
      // return
    }

    e.stopPropagation()
    e.preventDefault()
    return
  }
  if (item.resourceType === 1) {
    handlePreview(item)
  } else if (item.resourceType === 2) {
    handlePlay(item)
  }
}
/**
 * 下载讲义
 * */
const handleDownload = async (item: StudyPlanFile) => {
  if (!item.fileSourceUrl) {
    Message('fileSourceUrl 不存在')
    return
  }
  const checkData = await checkGoodsExpired()
  const { status } = checkData.value
  if (status !== 1) {
    selectGoods.value = checkData.value
    kkPopupRef.value?.show()
    return
  }
  if (isXcx && !os?.isPc) {
    handleWeChatPreview(item.fileSourceUrl, 1, item.fileName)
  } if (isWeChat() && !isXcx) {
    Message('请使用浏览器打开并进行下载～')
  } else {
    downloadFile(item.fileSourceUrl, item.fileName)
  }
}
/**
 * 预览讲义
 * */
const handlePreview = async (item: StudyPlanFile) => {
  console.log('讲义', item)
  //
  if (!item.fileUrl) {
    Message('fileUrl 不存在')
    return
  }
  const checkData = await checkGoodsExpired()
  const { status } = checkData.value
  if (status !== 1) {
    selectGoods.value = checkData.value
    kkPopupRef.value?.show()
    return
  }
  if (isXcx && !isPc) {
    handleWeChatPreview(item.fileUrl, 0, item.fileName)
  } else {
    window.location.href = getViewUrl(
      `/onlinePreview?url=${encodeURIComponent(
        Base64.encode(item.fileUrl)
      )}`
    )
  }
}
</script>

<style lang="scss" scoped>
.plan-files-item {
  display: flex;
  width: 100%;
  @apply break-all;
  padding: 16px 0;
  // padding-left: 20px;
  // &:hover{
  //   background-color: #F5F6F9;
  //   border-radius: 12px 12px 12px 12px;
  //   .ziLiao-item__title{
  //     color: #eb2330;
  //   }
  //   .ziLiao-line .kkc-icon{
  //     color: #eb2330;
  //   }
  // }
}

.plan-files-item-line {
  flex-shrink: 0;
  position: relative;
  // width: 40px;
  // height: calc(100% + 16px);
  // align-self: auto;
  // align-self: flex-start;
  align-self: auto;

  .kkc-icon {
    color: #111;
    margin-right: 16px;
  }

  &::before {
    background-color: #ddd;
    content: "";
    // height: 90%;
    // height: 48px;
    height: calc(100% - 10px);
    left: 18px;
    position: absolute;
    top: 40px;
    width: 1px;
  }
}

.catalog-node {
  display: flex;
  // align-items: center;
  position: relative;
  padding: 16px 0 16px 16px;

  // .a-link {
  //   width: 100%;
  //   height: 100%;
  //   position: absolute;
  //   left: 0;
  //   top: 0;
  // }

  // .progress {
  //   display: flex;
  //   align-items: center;
  //   // margin-left: 24px;
  // }

  // .complete {
  //   color: #01ae9d;
  // }

  // .line-tag {
  //   padding: 3px 4px;
  //   background: #f2f2f2;
  //   border-radius: 6px;
  //   font-size: 12px;
  //   font-family: PingFangSC-Regular, PingFang SC;
  //   font-weight: 400;
  //   color: #666666;
  //   margin-right: 12px;

  //   &.active {
  //     color: var(--kkc-brand-text);
  //     background: rgba(var(--kkc-brand-text), 0.1);
  //   }
  // }

  // .tag_living_second_level {
  //   flex-shrink: 0;
  //   width: 44px;
  //   text-align: center;
  //   height: 20px;
  //   margin-right: 8px;
  //   border-radius: 4px;
  //   font-size: 12px;
  //   color: var(--kkc-brand-text);
  //   border: 1px solid var(--kkc-brand-text);
  //   line-height: 18px;
  //   &.is-level-3 {
  //     width: auto;
  //     border: none;
  //     height: auto;
  //     line-height: 20px;
  //     //
  //     padding: 3px 4px;
  //     @apply bg-brand/10;
  //     border-radius: 6px;
  //     font-size: 12px;
  //     font-family: PingFangSC-Regular, PingFang SC;
  //     font-weight: 400;
  //     color: var(--kkc-brand-text); margin-right: 12px;
  //   }
  // }

  .catalog-node__main--title {
    color: #111111;
    font-size: 16px;
    display: flex;
    align-items: flex-start;
  }

  &.is-active,
  &.class-hour:hover {
    background: rgba(204, 204, 204, 0.1);
    border-radius: 8px;
    cursor: pointer;

    .line-tag {
      @apply text-brand bg-brand/10;
    }

    .catalog-node__status {
      .kkc-icon {
        background-color: initial;
      }
    }

    .catalog-node__main--title {
      @apply text-brand;
    }
  }

  &.is-active {
    background: rgba(var(--kkc-brand-text), 0.05);
  }

  // .node-active {
  //   border-radius: 8px;
  //   cursor: pointer;
  //   // color: var(--kkc-brand-text);

  //   .line-tag {
  //     color: var(--kkc-brand-text);
  //     background: rgba(var(--kkc-brand-text), 0.1);
  //   }

  //   .catalog-node__status {
  //     .kkc-icon {
  //       background-color: initial;
  //     }
  //   }

  //   .catalog-node__main--title {
  //     @apply text-brand;
  //   }

  //   .last-tag {
  //     font-size: 12px;
  //     font-family: PingFangSC-Regular, PingFang SC;
  //     font-weight: 400;
  //     @apply text-brand bg-brand/10;
  //     padding: 3px 4px;
  //     border-radius: 6px;
  //     margin-left: 12px;
  //   }
  // }

  &__status {
    position: relative;
    align-self: flex-start;
    cursor: pointer;

    .kkc-icon {
      background-color: #fff;
      margin-right: 16px;
    }
  }

  .catalog-node__line::before {
    content: "";
    position: absolute;
    left: calc(24px / 2 - 1px);
    top: 24px + 2px;
    width: 1px;
    height: 48px;
    background-color: #dddddd;
  }

  // &.section {
  //   .catalog-node__status {
  //     &::before {
  //       height: 24px;
  //     }
  //   }
  // }

  &__actions {
    padding: 0 16px;
    cursor: pointer;
  }

  &__content {
    flex: 1;
    display: flex;
    align-items: center;
    //   border-bottom: 1px solid #eee;
    position: relative;
  }

  // &.section &__content::after {
  //   content: "";
  //   position: absolute;
  //   left: 0;
  //   bottom: -16px;
  //   width: 100%;
  //   height: 1px;
  //   background-color: #eee;
  // }

  &__main {
    flex: 1;

    &--title {
      min-height: 20px;
      font-size: 14px;
      font-weight: 400;
      // color: #111111;
      line-height: 20px;

      // &.section {
      //   font-weight: 500;
      // }
    }

    &--desc {
      display: flex;
      align-items: center;
      align-self: flex-start;
      margin-top: 12px;
      height: 16px;
      font-size: 14px;
      font-weight: 400;
      color: #999999;
      line-height: 16px;

      &-left {
        margin-right: 24px;
        display: flex;
        align-items: center;
      }

      &-right {
        color: #01ae83;
      }

      .kkc-icon {
        margin-right: 4px;
      }
    }
  }
}

// .goods-price {
//   text-align: center;
//   margin-bottom: 24px;
//   font-family: PingFang SC;
//   font-weight: 600;
//   font-size: 24px;
//   color: var(--kkc-brand-text);
//   line-height: 56px;
// }

</style>
