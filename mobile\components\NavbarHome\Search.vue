<template>
  <NuxtLink
    :class="[bem()]"
    :to="{
      path: `/search`,
      query: {
        keyword: realKeyword,
      },
    }"
  >
    <nuxt-icon name="search" filled :class="[bem('icon')]" />

    <!-- 换用普通div -->
    <input
      v-model.trim="keyword"
      type="text"
      maxlength="20"
      readonly
      :class="[bem('input')]"
      :placeholder="placeholder || defaultPlaceholder"
    >
    <!-- @keyup.enter="handleChange" -->
    <span
      :class="[bem('confirm')]"
      title="点击搜索"
    >
      <KKCIcon name="icon-sousuo1" :size="32" color="#ffffff" />
    </span>
  </NuxtLink>
</template>

<script setup>
const bem = useBem('home-navbar-search')
const defaultPlaceholder = '搜索更多课程'
const props = defineProps({
  placeholder: {
    type: String,
    default: undefined,
  },
})
const keyword = ref('')
const realKeyword = computed(() => {
  if (keyword.value) {
    return keyword.value.trim()
  } else if (props.placeholder) {
    return props.placeholder.trim()
  } else {
    return undefined
  }
})
//
onMounted(() => {
  const route = useRoute()
  if (route.query.keyword) {
    keyword.value = route.query.keyword
  }
})
</script>

<style lang="scss">
.home-navbar-search {
  display: inline-flex;
  align-items: center;
  width: 250px;
  height: 56px;
  line-height: 56px;
  background-color: #ffffff;
  border-radius: 28px;
  overflow: hidden;

  //
  border: 1px solid transparent;
  background-clip: padding-box, border-box;
  background-origin: padding-box, border-box;
  /*第一个linear-gradient表示内填充渐变色，第二个linear-gradient表示边框渐变色*/
  background-image: linear-gradient(180deg, #fff 0%, #fff 80%),
    linear-gradient(135deg, rgba(255, 153, 38, 1), rgba(245, 119, 179, 1));

  &__icon {
    width: 32px;
    height: 32px;
    margin-left: 12px;
    font-size: 32px;
  }
  &__input {
    flex: 1;
    height: 56px - 4px;
    font-size: 22px;
    min-width: 0;
    &:focus {
      outline: none;
    }
    &::placeholder {
      font-size: 22px;
      color: rgba(0, 0, 0, 0.3);
    }
  }
  &__confirm {
    width: 64px;
    height: 56px;
    background: linear-gradient(
      147deg,
      #ffa200 0%,
      #ff0505 27%,
      rgba(255, 51, 51, 0.8) 83%,
      rgba(222, 51, 255, 0.5) 100%
    );
    border-radius: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
