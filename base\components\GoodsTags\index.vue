<template>
  <div
    class="goods-tag"
    :style="[
      styles
    ]"
  >
    <span>{{ tag.wlName.length >= 6 ? tag.wlName.slice(0, 6) : tag.wlName }}</span>
  </div>
</template>

<script setup lang="ts">
import px2rem from '../../utils/px2rem'
import type { WordageLabels } from '@/apis/course/types'
//
const { isMobile } = useAppConfig()

const props = defineProps<{ tag: WordageLabels }>()

interface StyleType {
  backgroundColor: string
  borderColor: string
  color: string
  borderRadius: string
  height: string
  lineHeight: string
  borderWidth: string
  padding: string
  fontSize?: string,
  fontWeight?: number;
}
const styles = ref<StyleType>({
  backgroundColor: `${props.tag.backgroundColor}`,
  borderColor: `${props.tag.outlineColor}`,
  color: `${props.tag.wordageColor}`,
  borderRadius: `${props.tag.labelFillet}px`,
  height: '20px',
  lineHeight: '20px',
  borderWidth: `${props.tag.labelWidth}px` || '0px',
  padding: '0 4px',
  fontSize: '12px',
},)
if (isMobile) {
  styles.value = {
    backgroundColor: `${props.tag.backgroundColor}`,
    borderColor: `${props.tag.outlineColor}`,
    color: `${props.tag.wordageColor}`,
    borderRadius: `${px2rem(Number(props.tag.labelFillet) * 2)}`,
    height: `${px2rem('40')}`,
    lineHeight: `${px2rem('40')}`,
    borderWidth: `${px2rem(props.tag.labelWidth!)}`,
    padding: `0 ${px2rem('10')}`,
    fontSize: `${px2rem('22')}`,
    fontWeight: 400,
  }
}
console.log(styles.value, 'font-mediumfont-medium')

</script>

<style scoped lang="scss">
.goods-tag {
  display: inline-block;
}
.goods-tag + .goods-tag {
  margin-left: 5px;
}
</style>
