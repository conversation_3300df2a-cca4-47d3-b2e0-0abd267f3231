<template>
  <nuxt-link :to="goDetail(goods)" external @click="handleClick(goods)">
    <div
      class="goods-cell bg-[#fff] !mb-[16px]"
      :title="goods?.title || goods?.goodsTitle"
    >
      <!-- @click="goDetail(goods)" -->
      <div class="flex flex-[1] items-center">
        <slot />
        <div class="img">
          <kkcImage :src="goods?.goodsImg || CourseCover" :error-type="2" radius="8px" />
          <div
            v-if="(goods?.isSell === 0 || goods?.isShelf === 0) && isShowMask"
            class="off-shelf flex justify-center flex-col items-center bg-opacity-60 text-[#fff]"
          >
            <KKCIcon name="icon-suobeifen1" color="#ffffff" :size="48" class="" />
            <span v-if="goods?.isSell === 0 && goods?.isShelf !== 0">
              不可售
            </span>
            <span v-if="goods?.isSell === 0 && goods?.isShelf === 0 || goods?.isShelf === 0">
              已下架
            </span>
          </div>
        </div>
        <div class="goods-cell-info flex justify-between flex-col" :class="[isEdit ? '!w-[306px]' : 'w-[366px]']">
          <div
            class="goods-cell-info-title font-medium text-[#111] line-clamp-2 h-[80px] "
            :class="[isEdit ? '!w-[306px]' : 'w-[366px]']"
          >
            <!-- :class="!isSkip ? 'w-306' : 'w-430'" -->
            <span
              v-if="goods?.isBook || goods?.queryType === 3 || goods?.mark === '图书'"
              class="good-list-tag !bg-[#FF6F00] !text-[#fff] !rounded-[8px]"
            >图书</span>
            <template v-for="(item, i) in goods?.classTypeList" :key="i">
              <span
                v-if="item && item.classTypeName"
                class="good-list-tag"
                :style="{ background: item.bgColor, color: item.fontColor }"
              >{{ item.classTypeName }}</span>
            </template>
            <span>
              {{ goods?.title || goods?.goodsTitle }}</span>
          </div>
          <div
            v-if="goods?.content?.length"
            class="goods-cell-info-package line-clamp-1"
            :class="[isEdit ? '!w-[306px]' : 'w-[366px]']"
          >
            <span v-for="(item, index) in goods.content" :key="index">
              {{ item }}{{ goods.content?.length !== index + 1 ? '/' : '' }}
            </span>
          </div>
          <div class="goods-cell-info-price font-bold text-right flex justify-end items-center text-red">
            <nuxt-icon
              v-if="(goods.isTrialClass || goods?.mark === '体验') && !isSearch"
              name="free-class"
              filled
              class="goods-cell-info-price-free-class"
            />
            <del
              v-if="isGoodsPrice||((isSearch||isCourse)&&isShowActivityType(goods, true))"
              class="text-[#999] text-[20px] leading-[24px]"
            >￥{{ goods?.goodsPrice }}</del>

            <div class="flex flex-1 justify-end items-end kb-new-goods-item-price__current">
              <div
                v-if="(isSearch||isCourse)&&isShowActivityType(goods, false)"
                class="kb-new-goods-item-price__current-activity kb-goods-start__tag1"
              >
                <p class="kb-new-goods-item-price__current-activity-state activity-state-size">
                  {{ handleActivityTypeName(goods) }}
                </p>
              </div>
              <span class="goods-cell-info-price-texticon">￥</span>
              <span class="text-[24px] goods-cell-info-price-text">{{ goods?.goodsPresentPrice || goods?.goodsPrice
              }}</span>
              <span v-if="Number(goods.goodsType) == 6 || Number(goods.goodsType) == 8" class="text-[22px] pl-[2px] leading-[28px]">起</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 文字标签 -->
      <div v-if="goods?.wordageLabels?.length || isSaleCount" class="goods-cell-divide" />
      <div class="flex justify-between items-center">
        <div v-if="goods?.wordageLabels?.length" class="goods-cell-tag">
          <KKCGoodsTags v-for="tag in goods.wordageLabels" :key="tag.goodsMasterId" :tag="tag" />
        </div>
        <span v-else />
        <span v-if="isSaleCount" class="text-[#999] text-[20px]">已售 {{ goods?.saleCount }}</span>
        <span v-else />
      </div>
    </div>
  </nuxt-link>
</template>
<script setup lang="ts">
//
import { useUserStore } from '~/stores/user.store'
const CourseCover = 'https://oss.kuke99.com/kukecloud/static/common/course-cover.png'
const { isXcx } = useAppConfig()
const userStore = useUserStore()
const { isKukeCloudAppWebview } = useAppConfig()
const props = defineProps({
  goods: {
    type: Object,
    default: () => ({ }),
  },
  // 是否可跳转商品详情
  isSkip: {
    type: Boolean,
    default: true
  },
  // 跳转商品详情是否判断异常情况
  isConfirmAbnormal: {
    type: Boolean,
    default: true
  },
  // 是否是搜索页面
  isSearch: {
    type: Boolean,
    default: false
  },
  // 是否展示标签
  isCourse: {
    type: Boolean,
    default: false
  },
  // 是否显示不可售,已下架蒙层
  isShowMask: {
    type: Boolean,
    default: true
  },
  // 是否显示划线价
  isGoodsPrice: {
    type: Boolean,
    default: false
  },
  // 是否显示销量
  isSaleCount: {
    type: Boolean,
    default: false
  },
  // 是否触发了编辑（删除）
  isEdit: {
    type: Boolean,
    default: false
  }
})
const emit = defineEmits<{
  (e:'toGoodsDetail'): void,
}>()

const handleClick = (item: any) => {
  // 我的收藏编辑状态不进行页面跳转
  if (props.isEdit) { return false }

  emit('toGoodsDetail')
  if (isXcx && props.isSkip) {
    const paths = +item.goodsType === 8 ? '/courseList/collectGoods/' : '/course/'
    userStore.jumpAppletWebPage(`${paths}${item?.id || item?.goodsMasterId}`)
  }
}

// 跳转商品详情
const goDetail = (item: any) => {
  if (!props.isSkip || isXcx || props.isEdit) {
    return 'javascript: void(0)'
  }

  const courseId = item?.id || item?.goodsMasterId
  const collectGoodsPath = `/courseList/collectGoods/${courseId}`
  const coursePath = `/course/${courseId}`

  const isCollectGoodsPath = +item.goodsType === 8

  const path = isCollectGoodsPath ? collectGoodsPath : coursePath

  if (isKukeCloudAppWebview) {
    return isClient ? window.origin + path : path
  }

  if (props.isSkip) {
    return path
  }

  // if (isKukeCloudAppWebview) {
  //   if (isClient) {
  //     return window.origin + `/course/${item?.id || item?.goodsMasterId}`
  //   } else {
  //     return `/course/${item?.id || item?.goodsMasterId}`
  //   }
  // } else if (props.isSkip) {
  //   return (`/course/${item?.id || item?.goodsMasterId}`)
  // }
  // if (props.isConfirmAbnormal) {
  //   if (item.isSell === 0 && item.isShelf !== 0) {
  //     Message('该商品暂不可售~')
  //     return
  //   } else if ((item.isSell === 0 && item.isShelf === 0) || item.isShelf === 0) {
  //     Message('该商品已下架~')
  //     return
  //   }
  // }
  // if (isKukeCloudAppWebview) {
  //   window.location.href = window.origin + `/course/${item?.id || item?.goodsMasterId}`
  // } else if (props.isSkip) {
  //   navigateTo(`/course/${item?.id || item?.goodsMasterId}`)
  // }
}

// 是否显示活动状态
const isShowActivityType = (item, val: boolean) => {
  const filterArr = val ? [1, 2, 4] : [1, 2]
  const intersection = item.activityTypes?.filter(data => filterArr.includes(data)) || []
  return intersection.length && item.goodsType !== 6
}
// 获取活动状态名称
const handleActivityTypeName = (item) => {
  const intersection = item.activityTypes?.filter(data => [1, 2].includes(data)) || []
  return intersection?.join(',').includes('1') ? '秒杀' : '拼团'
}
</script>
<style lang="scss">
@mixin tagLighten($color, $num) {
  background-color: rgba($color, $num);
  color: $color;
}

.good-list-tag{
  @include tagLighten($primaryTagColor, 0.1);
  border-radius: 8px;
  box-sizing: border-box;
  white-space: nowrap;
  text-align: center;
  padding: 6px;
  font-size: 20px;
  border-radius: 4px;
  margin-right:5px;
  vertical-align:text-bottom!important;
}
.goods-cell {
  // padding: 20px;
  margin-bottom: 10px;
  border-radius: 15px;

  .img {
    width: 280px;
    height: 186px;
    border-radius: 8px;
    margin-right: 16px;
    overflow: hidden;
    position: relative;
  }

  .off-shelf {
    position: absolute;
    width: 280px;
    height: 186px;
    background: rgba(0, 0, 0, 0.6);
    border-radius: 8px;
    // opacity: 0.6;
    top: 0;
    left: 0;

    div {
      font-size: 24px;
      font-weight: 500;
      color: #FFFFFF;
    }

  }

  &-info {
    min-height: 186px;
    &-title {
      font-size: 28px;
      line-height: 40px;
      padding-top: 3px;
      // height: 0.8rem;
      word-break: break-all;

      span {
        vertical-align: text-bottom;
      }
    }

    .w-306 {
      width: 306px;
    }

    .w-430 {
      width: 430px;
    }

    .tip-img {
      width: 86px;
      height: 37px;
    }

    &-package {
      color: #999;
      width: 366px;
      margin: 14px 0 30px;
      font-size: 22px;
    }

    &-price {
      font-family: DINPro-Bold;
      width: 100%;
      height: 40px;
      justify-content: space-between;
      align-items: flex-end;

      &-free-class {
        display: flex;
        align-self: center;
        font-size: 56px;
        margin-top: 3px;
      }

      &-texticon {
        font-size: 24px;
        line-height: 28px;
      }

      &-text {
        font-size: 40px;
        line-height: 40px;
      }
    }
  }

  &-divide {
    height: 1px;
    border-bottom: 1px dashed #E5E6EC;
    margin: 12px 0;
  }
}
</style>
