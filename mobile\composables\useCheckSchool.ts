import { useUserStore } from '~/stores/user.store'
import { ResourceTypes } from '@/apis/course/types'
import { useCourseStore } from '~/stores/course.store'

export const useCheckSchool = (resourceTypes:ResourceTypes, schoolLength:number, schoolId:string, btnStatus: number) => {
  const userStore = useUserStore()
  const courseStore = useCourseStore()

  const { goodsAreas = [] } = courseStore.courseInfo
  // 简易面授课（3.3.2）没有分校数据不进行校验
  if (!goodsAreas.length) { return false }

  // 代理商项目分享打开的面授课商品不进行分校字段校验
  const { agentCode = '', internalSystemId = '' } = useRoute().query || {}
  if (agentCode && Number(internalSystemId) === 33) {
    return false
  }

  if (userStore.isLoginFn()) {
    return useCheckSchoolNotVerifyLogin(resourceTypes, schoolLength, schoolId, btnStatus)
  }
  return true
}

/**
 * @description 校验校区是否选择，不验证是否登录
 *
 * @param resourceTypes
 * @param schoolLength
 * @param schoolId
 * @param btnStatus
 * @returns
 */
export const useCheckSchoolNotVerifyLogin = (resourceTypes: ResourceTypes, schoolLength: number, schoolId: string, btnStatus: number) => {
  if ([1, 2, 4].includes(btnStatus)) {
    return useCheckSchoolNotButtonStatus(resourceTypes, schoolLength, schoolId)
  }
  return false
}

/**
 * @description 校验校区是否选择，不验证按钮状态
 *
 * @param resourceTypes
 * @param schoolLength
 * @param schoolId
 * @returns
 */
export const useCheckSchoolNotButtonStatus = (resourceTypes: ResourceTypes, schoolLength: number, schoolId: string) => {
  // 面授面授套餐 omo校验分校
  if ([ResourceTypes.OMOPackage, ResourceTypes.FaceToFaceCourse, ResourceTypes.FacePackage].includes(Number(resourceTypes))) {
    //
    if (!schoolLength) {
      Message('暂无分校选择，无法下单~')
      return true
    }
    if (!schoolId) {
      Message('请选择分校~')
      //
      return true
    }
  }
}
