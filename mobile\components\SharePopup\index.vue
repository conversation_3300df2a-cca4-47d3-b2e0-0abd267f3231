<template>
  <div class="share_img_link_com">
    <van-popup v-model:show="visible" round position="bottom">
      <!-- 标题 -->
      <div class="share_title">
        分享
      </div>
      <!-- 分享方式 -->
      <div class="share_pic_link_type">
        <div class="share_pic_wrap" @click="handleSharePic">
          <img class="share_pic_type" :src="sharePic" alt="分享图片">
          <div class="share_item_text">
            分享图片
          </div>
        </div>
        <div class="share_link_wrap" @click="handleShareLink">
          <img class="share_link_type" :src="shareLink" alt="分享链接">
          <div class="share_item_text">
            分享链接
          </div>
        </div>
      </div>
      <!-- 取消分享 -->
      <div class="cancel_share" @click="handleCancelShare">
        取消分享
      </div>
    </van-popup>

    <!-- 分享海报 -->
    <SharePoster
      ref="sharePosterRef"
      :sku-title-img="skuTitleImg"
    />
  </div>
</template>

<script setup lang='ts'>
import SharePoster from '../SharePoster/index.vue'
import sharePic from '~/assets/images/course/share_pic.png'
import shareLink from '~/assets/images/course/share_link.png'
import { useCourseStore } from '~/stores/course.store'
import { GoodsType } from '@/apis/course/types'

const courseStore = useCourseStore()

const visible = ref<boolean>(false)
interface SkuTitleImg {
  goodsTitle: string,
  itemImg: string
}
interface Props {
  skuTitleImg?: SkuTitleImg,
}
withDefaults(defineProps<Props>(), {
  skuTitleImg: () => { return {} },
})

// 分享链接
const handleShareLink = (e: Event) => {
  const { goodsType } = courseStore.courseInfo
  let href = window.location.href
  const origin = href.split('?')[0]
  // 分享的非多规格商品或者多规格 spu 商品链接
  const normalAndSpuGoods = goodsType !== GoodsType.MultiSpecification || (goodsType !== GoodsType.MultiSpecification && !courseStore.isSku)
  if (normalAndSpuGoods) {
    href = `${origin}`
  } else if (courseStore.courseSkuId) {
    // 分享的多规格 sku 商品
    href = `${origin}?courseSpecId=${courseStore.courseSkuId}`
  }
  console.log('href', href, { courseSkuId: courseStore.courseSkuId }, 1)
  handleClipboard(href, e, '链接已复制~', '链接复制失败，请重试~', handleCancelShare)
}

// 分享图片
const sharePosterRef = ref<InstanceType<typeof SharePoster>>()
const handleSharePic = () => {
  sharePosterRef.value.visible = true
  handleCancelShare()
}

// 取消分享
const handleCancelShare = () => {
  visible.value = false
}

defineExpose({
  visible
})
</script>

<style lang='scss' scoped>
.share_img_link_com {
  .share_title {
    background: linear-gradient(178deg, #E5FFED 0%, #FFFFFF 100%);
    @apply text-center font-bold h-[128px] leading-[128px] text-[36px]
  }
  .share_pic_link_type {
    @apply flex items-center justify-center my-[40px]
  }
  .share_pic_wrap {
    @apply mr-[272px]
  }
  .share_pic_wrap,.share_link_wrap {
    @apply flex flex-col justify-center items-center
  }
  .share_pic_type, .share_link_type {
    @apply w-[80px]
  }
  .share_item_text {
    @apply text-[24px] leading-[30px] text-[#666] mt-[24px]
  }
  .cancel_share {
    height: calc(88px + constant(safe-area-inset-bottom));
    height: calc(88px + env(safe-area-inset-bottom));
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
    @apply w-[702px] h-[88px] bg-[#f2f2f2] rounded-[16px] text-[28px]
    leading-[34px] font-bold leading-[88px] text-center mb-[12px] mx-auto;
  }
}
</style>
