// Generated by Nuxt'
import type { Plugin } from '#app'

type Decorate<T extends Record<string, any>> = { [K in keyof T as K extends string ? `$${K}` : never]: T[K] }

type InjectionType<A extends Plugin> = A extends Plugin<infer T> ? Decorate<T> : unknown

type NuxtAppInjections = 
  InjectionType<typeof import("../../../node_modules/@pinia/nuxt/dist/runtime/plugin.vue3").default> &
  InjectionType<typeof import("../../../node_modules/nuxt/dist/app/plugins/revive-payload.server").default> &
  InjectionType<typeof import("../../../node_modules/nuxt/dist/app/plugins/revive-payload.client").default> &
  InjectionType<typeof import("../../../node_modules/nuxt/dist/head/runtime/plugins/unhead").default> &
  InjectionType<typeof import("../../../node_modules/nuxt/dist/pages/runtime/plugins/router").default> &
  InjectionType<typeof import("../../../node_modules/nuxt/dist/pages/runtime/plugins/prefetch.client").default> &
  InjectionType<typeof import("../../../node_modules/@pinia-plugin-persistedstate/nuxt/dist/runtime/plugin").default> &
  InjectionType<typeof import("../../../base/plugins/00.device").default> &
  InjectionType<typeof import("../../../base/plugins/cls").default> &
  InjectionType<typeof import("../../../base/plugins/dayjs").default> &
  InjectionType<typeof import("../../../base/plugins/error.global").default> &
  InjectionType<typeof import("../../../base/plugins/finger.client").default> &
  InjectionType<typeof import("../../../base/plugins/imagePreview").default> &
  InjectionType<typeof import("../../../base/plugins/no-debugger").default> &
  InjectionType<typeof import("../../../base/plugins/noSwipe").default> &
  InjectionType<typeof import("../../../base/plugins/observeVisibility").default> &
  InjectionType<typeof import("../../../base/plugins/px2rem").default> &
  InjectionType<typeof import("../../../base/plugins/richText").default> &
  InjectionType<typeof import("../../plugins/01.server-init").default> &
  InjectionType<typeof import("../../plugins/business-wap").default> &
  InjectionType<typeof import("../../plugins/dragResize.client").default> &
  InjectionType<typeof import("../../plugins/imagePreview").default> &
  InjectionType<typeof import("../../plugins/pinia-plugin-persistedstate").default> &
  InjectionType<typeof import("../../plugins/vueqr.client").default>

declare module '#app' {
  interface NuxtApp extends NuxtAppInjections { }
}

declare module 'vue' {
  interface ComponentCustomProperties extends NuxtAppInjections { }
}

export { }
