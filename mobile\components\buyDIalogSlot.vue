<template>
  <div class="pay-money">
    <div>
      ￥<span class="font-gilroy-bold text-[48px]">
        {{ price }}
      </span>
    </div>
    <div v-if="isShowOtherCost" class="mt-[32px] text-[22px] leading-[28px] text-[#666] px-[8px] py-[12px] rounded-[12px] bg-[#fafafa] text-left">
      不包含
      <span v-if="Number(courseInfo.teachingMaterialPrice)">
        教材费 ￥{{ courseInfo.teachingMaterialPrice }}  </span>
      <span v-if="Number(courseInfo.roomPrice)"> 住宿费 ￥{{ courseInfo.roomPrice }}  </span>
      <span v-if="Number(courseInfo.otherPrice)">其他费用 ￥{{ courseInfo.otherPrice }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useCourseStore } from '~/stores/course.store'

const courseStore = useCourseStore()

const courseInfo = computed(() => {
  return courseStore.courseInfo
})

const isShowOtherCost = computed(() => {
  const { teachingMaterialPrice, otherPrice, roomPrice } = courseInfo.value
  return Number(teachingMaterialPrice) || Number(otherPrice) || Number(roomPrice)
})

const price = computed(() => {
  const { isMarketing, activityType, activityStatus, activityPrice } = courseStore.marketingInfo
  if (isMarketing === 1 && activityType === ActivityType.Seckill && activityStatus === 2) { return activityPrice }
  const { isMember, price: _price } = courseStore.memberInfo || {}
  if (isMember === 1) {
    return _price
  }
  return courseStore.courseInfo.goodsPresentPrice
})
</script>

<style lang="scss" scoped>
.pay-money {
  @apply py-[48px];
  text-align: center;
  font-size: 28px;
  font-weight: 500;
  color: #E62129;
}
</style>
