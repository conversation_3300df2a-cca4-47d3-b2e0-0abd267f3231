import type { ClassifyListType, ClassifyCheckListType, ClassifyBasicType } from '~/pages/question-bank/types/basic'
import type { ModuleManageTagsType } from '@/apis/question-bank/types'

interface OptionsType {
  studyLevel1: number
  moduleManageId: string
}

export const useQuestionBankTag = (Options: OptionsType) => {
  const { studyLevel1, moduleManageId } = Options

  const { questionBankApi } = useApi()

  const { handleCreateBody } = useQuestionBankDataChange()

  /**
   * 标签列表
   */
  const tagList = ref<ClassifyListType[]>([])

  /**
   * 选中的标签数据
   */
  const tagCheckList = ref<ClassifyCheckListType[]>([])

  const getTagCheckFieldObj = (arr: ClassifyCheckListType[]) => {
    return Array.from(arr, ({ field, id }: { field: string; id: any }) => [field, id]).reduce((pre: any, [field, id]) => {
      pre[field] = id
      return pre
    }, {})
  }

  /**
   * 选中的标签根据field和id生成的对象
   */
  const tagCheckObj = computed(() => {
    return getTagCheckFieldObj(tagCheckList.value)
  })

  const tagCheckTitle = ref('')

  const handleCreateTitle = () => {
    tagCheckTitle.value = tagCheckList.value.map((item: ClassifyCheckListType) => item.title).join(' | ')
  }

  const NULL_TAG_VALUE = null

  // 标签切换
  const handleTagSelect = async ({ field, item }: { field: string, item: ClassifyBasicType }) => {
    const select = tagCheckList.value.find((tag: ClassifyCheckListType) => tag.field === field)
    if (select) {
      select.id = item.id
    } else {
      tagCheckList.value.push({
        field,
        id: item.id,
        name: '',
        title: item.title
      })
    }
    const { subjectType, directionType, region, academicSection, examFormat } = handleCreateBody(tagCheckList.value)
    await getModuleTagsList(false, {
      subjectType: subjectType >= 0 ? subjectType : NULL_TAG_VALUE,
      directionType: directionType >= 0 ? directionType : NULL_TAG_VALUE,
      region: region >= 0 ? region : NULL_TAG_VALUE,
      academicSection: academicSection >= 0 ? academicSection : NULL_TAG_VALUE,
      examFormat: examFormat >= 0 ? examFormat : NULL_TAG_VALUE,
      [field]: item.id
    })
  }

  /**
   * 获取分类下标签
   * @param flag
   * @param params
   */
  const getModuleTagsList = async (flag = true, params?: any) => {
    try {
      const body = handleCreateBody(tagCheckList.value)
      if (params) {
        Object.assign(body, {
          subjectType: 0, region: 0, directionType: 0, academicSection: 0, examFormat: 0,
        }, params)
      }
      const postData: ModuleManageTagsType = {
        lastModule: null,
        nowModule: {
          ...body,
          studyLevel1,
          moduleManageId,
        }
      }

      if (!flag || !studyLevel1) {
      // 重新选择标签，不需要传最后一条记录
        postData.lastModule = null
      }
      const { data, error } = await questionBankApi.getModuleManageHomeTags(postData)
      if (error.value) { return false }
      const { checkList, list } = data.value
      tagList.value = list
      tagCheckList.value = checkList
      if (flag) {
        // const privilegeBody = chooseTag.value.handleCreatePrivilegeBody(tagCheckList.value)
        // await chooseTag.value.getModulePrivilege(privilegeBody)
        // getList()
      }
    } catch (error) {
      console.warn(error)
      Loading(false)
    }
  }

  // 重新选择
  const handleTagReset = () => {
    tagCheckList.value = tagCheckList.value.map((item: ClassifyCheckListType) => {
      item.id = null
      return item
    })
    getModuleTagsList(false)
  }

  return {
    tagList,
    tagCheckList,
    tagCheckObj,
    tagCheckTitle,
    handleCreateTitle,
    handleTagSelect,
    getModuleTagsList,
    handleTagReset
  }
}
