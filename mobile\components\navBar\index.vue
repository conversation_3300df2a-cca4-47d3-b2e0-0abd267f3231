<!--
 * @Date: 2025-07-18 09:05:44
-->
<template>
  <div class="h-[80px] nav-bar">
    <!-- 小程序不显示标题栏和顶部导航 -->
    <div
      class="center-con h-[80px] bg-white flex justify-center items-center w-[7.5rem] fixed z-[99] top-0"
      :class="[route.name === 'course-id' ? '!z-[101]' : '']"
    >
      <KKCIcon
        v-if="(showBack && !isXcx && !isCrm) || awayShowBack"
        name="icon-com_return"
        :color="backIconColor"
        :size="48"
        class="absolute left-[32px]"
        @click="handleBack"
      />
      <slot name="title">
        <h4 class="max-w-[600px] text-[36px] font-medium text-black leading-[52px] truncate my-title" :class="[customClass]">
          {{ title }}
        </h4>
      </slot>
      <div class="absolute right-[24px]">
        <slot name="config" />
      </div>
    </div>
    <slot />
    <Close />
  </div>
</template>

<script setup lang="ts">
const { isXcx } = useAppConfig()
const props = withDefaults(defineProps<{ title: string, customBack?: boolean, showBack?: boolean, awayShowBack?: boolean, backIconColor?: string, customClass?: string}>(), {
  customClass: '',
  customBack: false,
  showBack: true,
  awayShowBack: false,
  backIconColor: ''
})
const emit = defineEmits<{
  (e: 'back'): void
}>()
const route = useRoute()

// 判断详情链接是否是crm系统的试听商品(internalSystemId为26代表crm系统),如果是，则隐藏掉返回按钮
// bug71605,新增代理商商品(internalSystemId为33代表代理商系统),如果是则隐藏掉返回按钮
const internalSystemId = isClient && sessionStorage.getItem('internalSystemId')
const isCrm = computed(() => [26, 33].includes(Number(internalSystemId)) || [26, 33].includes(Number(route.query.internalSystemId)))
const handleBack = () => {
  console.log(window.history, 'window.history.length')
  console.log(route, 'route')
  const isLoginPage = route.name === 'login' // 登录页
  const isReceivePage = route.fullPath.includes('/receive-page-h5/') || route.query['kk-system'] === '1' // 推广来源页面
  const isBrowserHistoryValid = window.history?.length > 2 && window.history?.state?.position > 0 // 页面打开记录大于2，用于判断无法返回时返回至首页
  // 从砍价列表进入商品详情页，需返回商品详情页
  const isBrangin = route.fullPath.includes('/course/') && route.query.isBargain
  const isPhotoRecord = route.fullPath.includes('/photo-record')
  // emit('back')
  if (!props.customBack) {
    if (os?.isApp) {
      if (route.meta?.pageDeep) {
        if (os?.isAndroid) {
          console.log('🚀 ~ handleBack ~ window.android:', window.android)
          window.android.goBack()
        } else if (os?.isPhone || os?.isTablet) {
          window.webkit.messageHandlers.goBack.postMessage(null)
        } else if (os?.isHarmonyOS) {
          console.log('🚀 ~ handleBack ~ window.harmony:', window.harmony)
          window.harmony?.goBack()
        }
      } else {
        window.history.back()
      }
    } else if (sessionStorage.getItem('toLinkUrl')) { // 缓存支付成功页面链接
      navigateTo('/')
      sessionStorage.removeItem('toLinkUrl')
    } else if (isBrowserHistoryValid && isPhotoRecord) {
      // 拍照列表直接退出相机
      window.history.go(-2)
    } else if (isBrowserHistoryValid || isLoginPage || isReceivePage || isBrangin) {
      console.log('🚀 ~ handleBack ~ window.history:', window.history)
      window.history.back()
      // router.go(-1)
      // navigateTo('/')
    } else {
      navigateTo('/')
    }
    // if (os?.isApp && os?.isAndroid) {
    //   window.android.goBack()
    // } else if (os?.isApp && os.isPhone) {
    //   window.webkit.messageHandlers.goBack.postMessage(null)
    // } else {
    //   window.history.back()
    // }
  } else {
    emit('back')
  }
}

</script>
