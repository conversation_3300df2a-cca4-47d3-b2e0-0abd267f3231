<script setup lang="ts">
import type { ActivityRes } from '~/apis/course/activetyTypes'
import type { CourseDetailModel } from '@/apis/course/types'
interface ConfigItem {
  closeName: string,
  closeFun: Function | string,
  confirmName: string,
  confirmFun: Function | string,
  showCloseIcon: boolean,
}

withDefaults(
  defineProps<{
        modelValue: boolean,
        info:CourseDetailModel
    }>(),
  {
    modelValue: false
  }
)

interface InjectDataType {
  marketingInfo:Ref<ActivityRes>
}
const injectData: InjectDataType = inject('info')!
const { marketingInfo } = injectData
const activityType = ref(marketingInfo?.value?.activityType as number)
const tipInfo = ref()
const { isXcx } = useAppConfig()

const emits = defineEmits<{
    (e: 'update:modelValue', isShow: boolean): void
}>()

const close = () => {
  reloadNuxtApp()
  emits('update:modelValue', false)
}

const toIndex = () => {
  if (isXcx) {
    wx.miniProgram.navigateBack({
      delta: 1,
    })
  } else {
    navigateTo('/')
  }
  emits('update:modelValue', false)
}
const toSeckill = () => {
  if (marketingInfo?.value?.activityType === ActivityType.Seckill) {
    navigateTo('/activity/seckillList/0')
  }
}

// 打开弹窗
const showDialog = (val:any) => {
  emits('update:modelValue', true)
  tipInfo.value = val.replace(/\\n/g, '<br/>')
}

const config:Ref<Record<number, ConfigItem>> = ref({
  [ActivityType.Distribution]: {
    closeName: '我知道了',
    closeFun: close,
    showCloseIcon: false,
    confirmName: '',
    confirmFun: '',
  },
  [ActivityType.Seckill]: {
    closeName: '返回首页',
    closeFun: toIndex,
    showCloseIcon: true,
    confirmName: '查看其他活动',
    confirmFun: toSeckill,
  }
})

defineExpose({
  showDialog
})

</script>

<template>
  <teleport v-if="modelValue" to="body">
    <div class="mask-dialog">
      <div class="mask">
        <div class="mask-content">
          <div class="px-[24px]" v-html="tipInfo" />
          <div class="flex justify-around mt-[48px]">
            <div class="mask-content-btn text-[#111]" @click="config[activityType].closeFun">
              {{ config[activityType].closeName }}
            </div>
            <div v-if="config[activityType]?.confirmName" class="mask-content-btn !bg-[#EB2330] text-[#fff]" @click="config[activityType].confirmFun">
              {{ config[activityType]?.confirmName }}
            </div>
          </div>
        </div>
        <img v-if="config[activityType].showCloseIcon" class="mask-delete" src="@/assets/images/giftPacks/delete-icon.png" alt="" @click="close">
      </div>
    </div>
  </teleport>
</template>

<style scoped lang="scss">
.mask-dialog {
    width: 100vw;
    height: 100vh;
    position: fixed;
    top: 0;
    background: rgba($color: #000000, $alpha: 0.65);
    z-index: 9997;
    overflow: hidden;

    .mask {
        width: 560px;
        height:288px;
        background: #FFFFFF;
        border-radius: 12px 12px 12px 12px;
        opacity: 1;
        position: absolute;
        top:36%;
        left: 50%;
        transform: translateX(-50%);
        &-delete{
                bottom: -100px;
                width: 56px;
                height: 56px;
                left: 50%;
                position: absolute;
                transform: translateX(-50%);
            }
        &-content {
            margin-top: 48px;
            color: #111111;
            text-align: center;
            font-size: 32px;
            line-height: 40px;
            font-weight: 600;
            position: relative;
            &-btn {
                width: 238px;
                height: 80px;
                background: #F2F2F2;
                border-radius: 16px;
                text-align: center;
                font-weight: 400;
                font-size: 28px;
                line-height: 80px;
                cursor: pointer;
            }
        }
    }
}
</style>
