import { useQuestionBankStore } from '~/stores/question.bank.store'

interface OptionsType {
  currentModule: {
    moduleType: number,
    examMode: number,
    examinationPaperModuleName: string,
    examinationPaperModuleId: string
  },
  studyLevel1: number
}

export const useQuestionBankList = (Options: OptionsType) => {
  const { questionBankApi } = useApi()
  const questionBankStore = useQuestionBankStore()
  const list = ref<any[]>([])

  const { currentModule, studyLevel1 } = Options

  const { getExerciseModel, getTextBookList, textBookList, modeList, } = useQuestionBankHandleEvents()

  /**
   * 做题模式
   */
  const questionType = computed(() => questionBankStore.queryData.questionType)

  const textbookId = computed(() => questionBankStore.queryData.textbookId)

  // 要传递参数
  const fetchListParams = reactive({
    moduleManageId: currentModule.examinationPaperModuleId,
    studyLevel1,
    academicSection: 0,
    examFormat: 0,
    region: 0,
    subjectType: 0,
    directionType: 0,
    questionType: questionType.value,
    // textbookId: textbookId.value,
    moduleType: currentModule.moduleType
  })

  const handleQueryData = (baseBody: any) => {
    questionBankStore.initTagFilterQueryPrams()
    questionBankStore.initLearnTargetPrams()
    for (const key in baseBody) {
      questionBankStore.setQueryData(key, baseBody[key])
    }
  }

  const getPromise = async (tagCheckObj: any, needCheck = true) => {
    fetchListParams.academicSection = tagCheckObj.academicSection || 0
    fetchListParams.region = tagCheckObj.region || 0
    fetchListParams.subjectType = tagCheckObj.subjectType || 0
    fetchListParams.directionType = tagCheckObj.directionType || 0
    fetchListParams.examFormat = tagCheckObj.examFormat || 0
    fetchListParams.moduleManageId = currentModule.examinationPaperModuleId
    if (needCheck) {
      await handleGetOthersBodyParams()
    }
    // 兼容老版本queryData处理方式
    handleQueryData(fetchListParams)
    // 获取列表
    const options = {
      getDailyPracticeList: { key: 'getDailyPracticeList', body: { ...fetchListParams, questionType: questionType.value } },
      getTestpaperList: { key: 'getTestpaperList', body: fetchListParams },
      getChapterList: { key: 'getChapterList', body: { ...fetchListParams, textbookId: textbookId.value } },
      getFixedExamList: { key: 'getTestpaperList', body: fetchListParams, questionType: questionType.value },
    }
    let optionsKey = ''
    switch (currentModule.moduleType) {
      case 1:
        optionsKey = 'getDailyPracticeList'
        break
      case 2:
        optionsKey = 'getTestpaperList'
        break
      case 3:
        optionsKey = 'getChapterList'
        break
      case 4:
        optionsKey = 'getFixedExamList'
        break
    }

    if (optionsKey) {
      return options[optionsKey as keyof typeof options]
    }
  }

  const handleGetOthersBodyParams = async () => {
    const mId = currentModule.examinationPaperModuleId as any
    if ([1, 3, 4].includes(currentModule.moduleType)) {
      // 获取做题模式
      await getExerciseModel(mId)
    }
    // 获取教材
    if (currentModule.moduleType === 3) {
      await getTextBookList({
        academicSection: fetchListParams.academicSection,
        region: fetchListParams.region,
        subjectType: fetchListParams.subjectType,
        directionType: fetchListParams.directionType,
        examFormat: fetchListParams.examFormat,
        studyLevel1,
        moduleManageId: mId,
      })
    }
  }

  /**
   * 标识当前分类需要下次选中
   */
  // const tabChangeFlag = ref(false)

  const { setHistoryState } = useHistoryState()

  const { getIngList } = useQuestionBankHandleEvents()

  // 分页数据
  const page = ref<number>(1)
  const pageSize = 10
  const loading = ref(false)
  // 加载状态
  const finished = ref(true)
  const isEmpty = ref<boolean>(false)

  const hasTimerModel = [2, 3, 4]

  const getList = async (tagCheckObj = fetchListParams, needCheck = true) => {
    try {
      const { key, body } = await getPromise(tagCheckObj, needCheck) as { key: string, body: any }
      const { data, error } = await questionBankApi[key as keyof typeof questionBankApi]({
        ...body,
        page: page.value,
        pageSize,
      })
      // 页数为1，清空数据
      if (page.value === 1) {
        list.value = []
      }
      // const customTagSelect = !!questionBankStore.getLastCustomTagHistory(body.studyLevel1)
      // questionBankStore.setQuestionQuery({ ...body, customClassSelect: tabChangeFlag.value, customTagSelect, customListPageTagSelect: customTagFlag.value })
      // 当前分类标签标识重置，只在主动提交标签为true
      // customTagFlag.value = false
      // lastFetchCheckList.value = deepClone(tagCheckList.value)
      if (!error.value) {
        list.value.push(...data.value.list)
        if (currentModule.moduleType === 2 || hasTimerModel.includes(currentModule.examMode)) {
          // 模考以及估分需要计时更新试卷
          getIngList(list.value, updateTestpaperStatus)
        }
        if (currentModule.moduleType === 3) {
          // 章节练习，根据接口返回的上次选中数据，进行标红等处理
          questionBankStore.setDefaultChapterActiveId(data.value.lastTargetResultId, list.value)
        }
        isEmpty.value = list.value.length === 0
        if (list.value.length >= data.value.count) {
          finished.value = true
        } else {
          finished.value = false
        }
      } else {
        isEmpty.value = true
      }

      nextTick(() => {
        setHistoryState(body)
      })

      Loading(false)
    } catch (error) {
      console.log(error)
      Loading(false)
    }
  }

  // 加载分页
  const loadMore = () => {
    finished.value = true
    page.value += 1
    Loading(true)
    getList(fetchListParams)
  }

  /**
 * 更新试卷状态
 */
  const updateTestpaperStatus = async (paperArr: any[]) => {
    if (!paperArr || !paperArr.length) {
      return
    }

    try {
      const { data, error } = await questionBankApi.getPaperStatus({
        studyLevel1,
        moduleManageId: fetchListParams.moduleManageId,
        subjectType: fetchListParams.subjectType || 0,
        region: fetchListParams.region || 0,
        directionType: fetchListParams.directionType || 0,
        academicSection: fetchListParams.academicSection || 0,
        examFormat: fetchListParams.examFormat || 0,
        testpaperId: paperArr.map(item => item.id).join(''),
      })
      if (!error.value) {
        const { id, doStatus, btnStatus } = data.value
        // 根据id，在列表中找到对应的试卷并更新状态
        const paper = list.value.find((item: any) => item.id === id)
        if (paper) {
          paper.doStatus = doStatus
          paper.btnStatus = btnStatus
        }
      }
    } catch (error) {
      console.error('更新试卷状态失败:', error)
    }
  }

  // 重新加载
  const reload = () => {
    page.value = 1
    list.value = []
    finished.value = true
    Loading(true)
    getList(fetchListParams)
  }

  return {
    fetchListParams,
    list,
    page,
    loading,
    finished,
    isEmpty,
    textBookList,
    modeList,
    getPromise,
    getList,
    loadMore,
    reload
  }
}
