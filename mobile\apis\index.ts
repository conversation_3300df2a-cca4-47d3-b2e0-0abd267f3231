// 模块拆分
import * as commonApi from './common'
import * as customApi from './custom'
import * as userApi from './user'
import * as searchApi from './search'
import * as courseApi from './course'
import * as giftpacksApi from './giftpacks'
import * as learnCenter from './learn-center'
import * as orderApi from './order'
import * as questionBankApi from './question-bank'
import * as activityApi from './activity'
import * as agreementApi from './agreement'
import * as raffleApi from './raffle'
import * as incomeApi from './income'
import * as bargainApi from './bargainPrice'
import * as newsApi from './news'
import * as feedbackApi from './feedback'
import * as cartApi from './cart'
import * as inviteApi from './invite'
import * as liveApi from './live-play'
import * as bookApi from './book'
import * as promoterApi from './activity/promoter'
import * as answerApi from './answer'
import * as pointsApi from './points'
import * as answerLotteryApi from './answerLottery'
import * as activationCodeApi from './activationCode'
import * as redemptionCenterApi from './redemptionCenter'
import * as materialApi from './material'
import * as memberCenterApi from './member-center'

// 落地页接口
import * as landingPageApi from './topic/landing-page'
import * as tagApi from './tags'

export * from './learn-target'
export * from './learn-center'
export * from './home'
export * from './address'

// 统一导出
export default {
  tagApi,
  userApi,
  commonApi,
  courseApi,
  searchApi,
  giftpacksApi,
  learnCenter,
  orderApi,
  questionBankApi,
  activityApi,
  raffleApi,
  incomeApi,
  bargainApi,
  agreementApi,
  newsApi,
  feedbackApi,
  landingPageApi,
  cartApi,
  inviteApi,
  customApi,
  liveApi,
  bookApi,
  promoterApi,
  answerApi,
  pointsApi,
  answerLotteryApi,
  activationCodeApi,
  redemptionCenterApi,
  materialApi,
  memberCenterApi
}
export const userWxPhoto = userApi.getUserWxPhoto
