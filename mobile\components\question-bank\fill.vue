<template>
  <div :id="`ques-` + questionItem.id">
    <!-- 题目标题 -->
    <div class="exam-quest-title">
      <span class="quest-title">
        {{ question.chineseNum ? `${question.chineseNum}、` : '' }}
        <!-- 显示题目类型标题 -->
        {{ questionTitle }}
      </span>
      <!-- 若为考试模式且非材料题，则显示说明 -->
      <span v-if="(moduleType === 2 || moduleType === 0) && !isMaterial" class="explain" @click="onDescriptionClick">
        <!-- 显示题目说明 -->
        <span v-if="question.description">（{{ question.description }}）</span>&nbsp;
        <!-- 若说明内容较长，则显示箭头图标 -->
        <KKCIcon
          v-if="question.description?.length > 15"
          class="quest-title-right-icon"
          name="icon-wode-youjiantou"
          :size="24"
        />
      </span>
    </div>
    <!-- 题干内容 -->
    <div
      v-img-preview="question.stem"
      class="fill-stem exam-html"
    >
      <KKCHtmlMathJax
        :content="useQuestSortStem(
          question.stem,
          question.sort,
          moduleType,
          question.score
        )"
      />
    </div>
    <!-- 若题干有音频，则显示音频播放器 -->
    <KKCAudioPlayer
      v-if="question.stemSrc"
      class="bg-[#f8f8f8]"
      :icon-name="'icon-yinpinbofang'"
      :playing-icon="'icon-yinpinzanting'"
      :icon-size="48"
      :url="question.stemSrc"
    />
    <!-- 答案区域 -->
    <div v-if="examModeNumber !== 3 || moduleType !== 2" class="ans-box">
      <!-- 循环显示答案填写区域 -->
      <div
        v-for="(item, index) in questionItem.logAnswer"
        :key="index"
        v-img-preview="item"
        class="fill-answer"
        :class="{ '!mb-[50px]': isEditFill }"
        :style="{ background: isEditFill ? 'none' : '#f7f7f7', border: isEditFill ? 'none' : '1px solid #eeeeee' }"
      >
        <div v-if="!isEditFill" class="srda px-[24px] pt-[24px]">
          <!-- 若非编辑模式，则显示输入答案提示 -->
          <span v-if="!isEditFill">答案</span>
          <!-- 若图片数量未达到上限且非编辑模式，则显示上传按钮 -->
          <QuestionBankUpload
            v-if="item.imgs.length < maxNumber && !isEditFill"
            :question-title="questionTitle"
            @success="(val) => successImg(val, index)"
          />
        </div>
        <div v-if="isEditFill && index === 0" class="text-[#333333] text-[32px] mt-[30px] mb-[24px] font-medium">
          你的答案
        </div>
        <!-- 答案填写区域  如果是估分则隐藏下面组件-->
        <div
          class="flex  relative"
          :class="{ 'px-[24px]': !isEditFill, 'pb-[44px]': !isEditFill, 'pb-[24px]': isEditFill && item?.htmlSrc !== '' }"
        >
          <!-- 若题型为选择题，则显示题号 -->
          <span v-if="question.ptypeValueId == 4 && questionItem.logAnswer.length > 1" class="num">{{ index + 1 }}</span>
          <div :class="['answer-box', isEditFill ? '!w-full' : '']">
            <!-- 根据答题状态显示不同内容 -->
            <span
              v-if="question.logStatus === 1 && isEditFill || (isEditFill &&
                item.html === '' &&
                !item.imgs.length &&
                (item?.htmlSrc === '' || item?.htmlSrc === undefined))
              "
              class="text-[28px]"
            >未答</span>
            <span
              v-if="question.logStatus !== 1 && isEditFill && (item.html !== '' ||
                item.imgs.length ||
                item?.htmlSrc !== '')"
              class="text-[28px]"
            >{{ item.html }}</span>
            <!-- 若为编辑模式，则显示文本输入框 -->
            <van-field
              v-if="!isEditFill"
              v-model="item.html"
              class="exam-inpt"
              type="textarea"
              rows="2"
              :autosize="{ maxHeight: 150 }"
              maxlength="1000"
              :placeholder="placeholder"
              :disabled="isEditFill"
              @blur="blurInput"
              @focus="focusInput"
            />
            <!-- 循环显示已上传的图片 -->
            <div class="flex" :class="{ 'mb-[24px]': item.htmlSrc !== '' && item.imgs.length }">
              <div
                v-for="(v, i) in item.imgs"
                :key="i"
                class="img-box"
                :class="{ '!mt-[5px]': isEditFill && item.html === '' }"
              >
                <img :src="v" alt="" class="object-contain">
                <!-- 若非编辑模式，则显示删除按钮 -->
                <div v-if="!isEditFill" class="delete" @click="deleteImg(index, i)">
                  <KKCIcon name="icon-com_guangbi" :size="22" color="#ffffff" />
                </div>
              </div>
            </div>
            <!-- 若未上传图片且答案为空且为编辑模式，则显示录音组件 -->
            <QuestionBankRecorder
              v-if="isEditFill"
              :class="{
                '!bg-[#F7F8FC]': isEditFill,
                'rounded-[12px]': isEditFill,
                'rounded-b-[12px]': !isEditFill
              }"
              :url="item.htmlSrc"
              :show-del="!isEditFill"
              :question-title="questionTitle"
              @success="(val: string) => handleAudio(val, index)"
            />
          </div>
          <div v-if="!isEditFill" class="text-[22px] text-number">
            <span :class="{ 'text-[#cccccc]': item.html.length === 0 }">{{
              item.html.length
            }}</span><span class="text-[#cccccc]">/1000</span>
          </div>
        </div>
        <!-- 若有已上传图片或答案不为空且为编辑模式或非编辑模式，则显示录音组件 -->
        <QuestionBankRecorder
          v-if="!isEditFill"
          :class="{
            '!bg-[#F7F8FC]': isEditFill,
            'rounded-[12px]': isEditFill,
            'rounded-b-[12px]': !isEditFill
          }"
          :url="item.htmlSrc"
          :show-del="!isEditFill"
          @success="(val: string) => handleAudio(val, index)"
        />
      </div>
    </div>
    <!-- 描述弹窗 -->
    <Teleport to="body">
      <QuestionBankPopupDescription
        v-if="description"
        :show="isShowDescription"
        :description="description"
        @ok="onDescriptionClick"
      />
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import type { questType, fillType } from './type'
const route = useRoute()
// 1每日一练,3 章节练习,2 试卷，处理数据逻辑不同
const moduleType = Number(route.query.moduleType)
const { track } = useTrackEvent()
// 试题信息
interface Props {
  question: questType; // 试题信息
  isEdit?: boolean; // 1可以操作，2纯展示
  maxNumber?: number; // 图片最大可上传数量
  model?: number; // 1联系模式，2考试模式
  isMaterial?: boolean; // true材料题，false 非材料题
  examModeNumber?: number // 考试模块
  isExpand?: boolean; // true展开，false 不展开
  isDrag?: boolean; // 是否正在拖动
}
const props = withDefaults(defineProps<Props>(), {
  isEdit: true,
  maxNumber: 3,
  model: 2,
  isMaterial: false,
})
const emit = defineEmits<{
  (e: 'change', arr: fillType[], status: number): void;
  (e: 'getIsFocus', bool: boolean): void;
}>()
const questionItem = ref<questType>({
  id: '',
  analysis: '',
  answer: '',
  logAnswer: [],
  logStatus: 0,
  metas: '',
  stem: '',
  sort: 0,
  childQuestions: [],
  isAnalyze: 0,
})
const logStatus = ref(1) // true代表答案为空，该题目未做，false 代表该题已做
// 聚焦
const focusInput = () => {
  // 做题->做题输入框 埋点
  track({
    category: '题库',
    action: '做题输入框',
    label: questionTitle.value
  })
  // 选中时 允许全局滚动
  document.body.removeAttribute('style')

  emit('getIsFocus', true)
}
// 输入框失去焦点
const blurInput = () => {
  getlogStatus()

  // 重制滚动条的位置
  window.scrollTo(0, 0)
  document.body.setAttribute('style', 'overflow: hidden;')

  emit('getIsFocus', false)
  emit('change', questionItem.value.logAnswer, logStatus.value)
}
// 判断答案是否为空
const getlogStatus = () => {
  logStatus.value = questionItem.value.logAnswer.some(
    item => item.html || item.imgs.length > 0 || item.htmlSrc
  )
    ? 2
    : 1
}

// 图片
// 图片上传成功
const successImg = (val, index) => {
  questionItem.value.logAnswer[index].imgs.push(val)
  getlogStatus()
  if (questionItem.value.logAnswer[index].imgs.length === props.maxNumber) {
    Message('添加图片已达上限')
  }
  emit('change', questionItem.value.logAnswer, logStatus.value)
}
// 删除图片
const deleteImg = (index: number, i: number) => {
  questionItem.value.logAnswer[index].imgs.splice(i, 1)
  getlogStatus()
  emit('change', questionItem.value.logAnswer, logStatus.value)
}
// 音频上传成功
const handleAudio = (val: string, index: number) => {
  questionItem.value.logAnswer[index].htmlSrc = val
  getlogStatus()
  emit('change', questionItem.value.logAnswer, logStatus.value)
}
// 是否可编辑，true不可编辑，false可以编辑
const isEditFill = computed(() => {
  return (
    !props.isEdit || ((props.model === 1 || props.model === 3) && questionItem.value.isAnalyze === 1) || props.model === 4 || props.isExpand
  )
})
const placeholder = computed(() => {
  if (isEditFill.value && props.question.logStatus === 1) {
    return '未答'
  } else if (
    props.question.ptypeValueId !== 4 ||
    props.question.awarding === 2
  ) {
    return '当前题型系统暂不支持自动判分哦~'
  } else {
    return '请输入你的答案'
  }
})
// 监听试题的变化
watch(
  () => props.question,
  (newVal) => {
    questionItem.value = JSON.parse(JSON.stringify(newVal))
    const { answer, logAnswer } = newVal
    if (answer && typeof answer === 'string') {
      questionItem.value.answer = JSON.parse(answer)
    }
    if (logAnswer && typeof logAnswer === 'string') {
      questionItem.value.logAnswer = JSON.parse(logAnswer)
    }
    // 首次进入做题，给logAnswer 我的答案数据结构转换
    if (newVal.answer && !logAnswer) {
      questionItem.value.logAnswer = reactive([])
      if (props.question.ptypeValueId === 4) {
        // 填空题需要设置n个答案对应的数量
        // const ans = answer.split(',')
        const ans = questionItem.value.answer
        ans.forEach(() => {
          questionItem.value.logAnswer.push({ html: '', imgs: [], htmlSrc: '' })
        })
      } else {
        // 问答题只需要设置一个
        questionItem.value.logAnswer.push({ html: '', imgs: [], htmlSrc: '' })
      }
    }
  },
  {
    immediate: true,
    deep: true,
  }
)

// 题型说明
const isShowDescription = ref(false)
const description = computed(() => questionItem.value.description)
const onDescriptionClick = () => {
  if ((moduleType === 2 || moduleType === 0) && !props.isMaterial && description.value) {
    isShowDescription.value = !isShowDescription.value
  }
}

/**
 * 题型title
 */
const questionTitle = computed(() => {
  const { ptypeValueIdTitle, questionTypeValueIdTitle } = props.question
  return handleQuestionTitle(ptypeValueIdTitle || '', questionTypeValueIdTitle, moduleType)
})

</script>

<style scoped lang="scss">
.fill-stem {
  font-size: 32px;
  font-weight: 400;
  color: #111;
  margin-bottom: 25px;
}

.text-number {
  position: absolute;
  bottom: 6px;
  right: 24px;
}

.ans-box {
  margin-top: 58px;
  padding-bottom: 8px;

  .fill-answer {
    // padding: 24px;
    margin-bottom: 24px;
    border-radius: 12px;
    background: #f7f7f7;
    border: 1px solid #eeeeee;

    .srda {
      @apply flex items-center justify-between mb-[26px] text-[32px] font-[500];
    }

    .num {
      width: 36px;
      height: 36px;
      line-height: 36px;
      font-size: 26px;
      margin-right: 16px;
      margin-top: 4px;
      text-align: center;
      background: #e8eaf3;
      color: #5c658c;
      border-radius: 6px 6px 6px 6px;
      font-family: PingFang SC-Medium, PingFang SC;
    }

    .answer-box {
      width: 650px;
      line-height: 41px;
    }

    .exam-inpt {
      padding: 0;
      background: transparent;

      :deep(.van-field__body) .van-field__control {
        // padding-right: 16px;
      }

      &::after {
        border: none;
      }
    }
  }
}

.img-box {
  @apply relative mt-[32px];

  img {
    width: 120px;
    height: 120px;
    margin-right: 24px;
    border-radius: 8px;
  }

  .delete {
    position: absolute;
    top: 0;
    left: 86px;
    width: 34px;
    height: 34px;
    line-height: 34px;
    text-align: center;
    border-radius: 0 8px 0 8px;
    background: #000;
    opacity: 0.7;
    cursor: pointer;

    svg {
      vertical-align: inherit;
      fill: #ffffff;
    }
  }
}
</style>
