import { events } from '@kukefe/kkutils'
import { emitter } from '../../base/utils/mitt'
import { checkGroupId } from '~/apis/course'
import { getNewsCategoryAll } from '~/apis/news'
import { dicFunctionPageId, findDicFunctionId, userCenterDicPageList } from '~/constants'
import { usePointsWxShareInfo } from '~/pages/points/hooks/usePointsWxShareInfo'
import { useKefuStore } from '~/stores/kefu.store'
import { useLearnStore } from '~/stores/learn.store'
import { useUserStore } from '~/stores/user.store'
import { memberDetails } from '~/apis/memberUser'
import { statisticalPublicAdvertExposure } from '~/apis/learn-target'
// const { track } = useTrackEvent()
// import { downloadOrPreview } from '~/apis/material'
//
const { activityApi, commonApi, cartApi, liveApi } = useApi()
export interface IComponentData<T = any> {
  componentName: string
  item?: T
  index: number
  id: string
  pageMasterId: string,
  isShowBack?: boolean
}

// const { NUXT_HELP_URL } = process.env
export const useComponentEvents = (
  // components?: Ref<IComponentData[]>
) => {
  const { data: kuke99DefaultHomeData } = useKuke99DefaultHomeData()
  const localId = useCookie<string>('LT')
  const { setCp } = useCpClueSource()
  const { checkPointsClueSource } = usePointsClueSource()
  const learnStore = useLearnStore()
  const { isApplyProducts } = useCustomizedTenants({ tenants: ['kuke99_com'] })
  const openUrl = (url:string) => {
    if (isApplyProducts.value) {
      if (isXcx) {
        userStore.jumpAppletWebPage(`/${url}`)
      } else {
        navigateTo({
          path: `/${url}`,
        })
      }
    } else {
      Message('暂无数据~')
    }
  }
  // const { public: { BUILD_ENV } } = useRuntimeConfig()
  const xcxLaunchData = reactive({
    miniprogramId: '',
    pageUrl: '',
    envVersion: 'release',
  })
  // if (BUILD_ENV === 'PROD' || BUILD_ENV === 'STAGING') {
  //   xcxLaunchData.envVersion = 'release'
  // } else if (IS_DEV_ENV(BUILD_ENV)) {
  //   xcxLaunchData.envVersion = 'develop'
  // } else {
  //   xcxLaunchData.envVersion = 'trial'
  // }
  const route = useRoute()
  const router = useRouter()
  const kefuStore = useKefuStore()
  const { isXcx, logo2, theme, appId, lesseeId, isKukeCloudAppWebview } = useAppConfig()
  const {
    usableId,
  } = useLearnTarget()
  const themeColor = encodeURIComponent(theme?.brand?.color || '')
  const isLoginMiniProgram = ref(false)
  const userStore = useUserStore()
  let linkHandle: (ctx: IComponentData) => void = () => { }
  let pageHandle: (ctx: IComponentData) => void = () => { }
  let moreHandle: (ctx: IComponentData) => void = () => { }
  let appletHandle: (ctx: IComponentData) => void = () => { }
  let searchHandle: (ctx: IComponentData) => void = () => { }
  let loginHandle: (ctx: IComponentData) => void = () => { }
  let customPageHandle: (ctx: IComponentData) => void = () => { }
  let kefuHandle: (ctx: IComponentData) => void = () => { }
  let indexHandle: (ctx:IComponentData) => void = () => { }
  let goodsGroupHandle: (ctx: IComponentData) => void = () => { }
  // let materialDetailHandle: (ctx: IComponentData) => void = () => { }
  let traceAnalyticsHandle: (ctx: IComponentData) => void = () => { }
  /**
   * 在线客服
   * */
  let onlineKefuHandle: (ctx: IComponentData) => void = () => { }
  /**
   * AI练习开始答题
   */
  let onQuestionModuleOpenHandle: (ctx: IComponentData) => void = () => { }
  // 立即领取api
  const handleReceiveApi = async (params: { couponId: string }) => {
    const { data, error } = await activityApi.userCouponsGetProt(params)
    if (error.value) {
      console.log(error.value)
    } else {
      if (data.value.status === 2 || data.value.status === 1) {
        Message('领取成功，快去使用吧')
      } else if (data.value.title) {
        Message(data.value.title)
      }
      return data.value.status
    }
  }
  // 购物车跳转判断
  const handleCart = async () => {
    const { data, error } = await cartApi.getIsShowCart({})
    if (!error.value) {
      const { isShowCart, getIsShowAppletLive } = data.value
      return { isShowCart, getIsShowAppletLive }
    }
  }

  /**
  * 打开客服
  */
  const openKefu = (item = {}) => {
    // kefuStore.openKefuIframe(item)

    // 七鱼上报客户信息
    if ((window as any).ysf) {
      const userStore = useUserStore()
      if (userStore.isLogin) {
        (window as any).ysf('config', {
          // 赋值操作时，需要配置其参数 expires
          uid: useCookie('user').value.userId,
          name: useCookie('user').value.userId,
          data: JSON.stringify([
            { index: 1, key: 'avatar', label: '头像', value: useCookie('user').value.photo || 'https://oss.kuke99.com/kukecloud/static/common/default-avatar.png' }
          ]),
          success: function () { // 成功回调
            console.log('上报成功')
          },
          error: function () { // 错误回调
            console.log('上报失败')
          }
        })
      }
      // 库课云app内的资讯详情客服使用app原生方法
      if (isKukeCloudAppWebview && route.name === 'news-id' && item.url) {
      // if (isKukeCloudAppWebview && route.name === 'news-id') {
        // ysf('url')
        const dataStr = JSON.stringify({ urlString: item.url })
        console.log('[ dataStr ] >', dataStr)
        if (window?.android?.goCustomerService) {
          window.android.goCustomerService(dataStr)
        } else if (window?.webkit?.messageHandlers) {
          window.webkit.messageHandlers.goCustomerService.postMessage(dataStr)
        } else if (window?.harmony?.goCustomerService) {
          window?.harmony?.goCustomerService(dataStr)
        }
      } else {
        (window as any).ysf('open')
      }
    }
  }

  // 判断自定义页面底部导航有没有打开
  const getBottomNavigateStatus = async (id: string) => {
  // 获取自定义页面 导航配置
    const { data: customPageNavigateData } = await getCustomPageNavigationStatus({
      customPageId: id as string
    })
    return customPageNavigateData.value?.isBottomNavigationApp as number
  }

  onMounted(async () => {
    if (!isXcx && +appId === 1 && !lesseeId) {
      const a = document.createElement('script')
      a.async = true
      const url = '//qiyukf.kukecloud.cn/script/63bd8952f58bbc54f13b2142585c095b.js'
      a.src = `${url}?hidden=1`
      document.body.appendChild(a)
    }

    // await nextTick()
    // if (unref(components)?.length) {
    // 链接跳转处理
    linkHandle = ({ componentName, item = {}, index, id, resourceType }) => {
      console.log('~~link~', item)
      // return
      if (!isXcx && (+appId === 1 && !lesseeId)) {
        // TODO: 临时处理 如果是七鱼客服 不走链接跳转（原因：链接跳转不支持上报用户信息）
        if (item?.dicSkipId === 999) {
          openKefu(item)
          return
        }
      }

      // TODO 拦截a标签跳转事件
      console.log({ componentName, item, index, id, resourceType }, 'link11')
      if (componentName === 'KKCourtesyCardWap') {
        // receiveCoupon(item.id)
      } else if (isXcx) {
        if (componentName === 'KKNewsInformationWap') {
          userStore.jumpAppletWebPage(`/news/${item.id || ''}`)
        } else if ((item.dicSkipId === 18 || item.visitWayUrl === '1') && item.url) {
          // 题库url不使用jumpAppletWebFullPage跳转
          const questionStartUrl = '/question-bank/'
          if (item.url.includes(questionStartUrl)) {
            // 截取包含题库url
            const questionUrl = item.url.substring(item.url.indexOf(questionStartUrl))
            navigateTo(questionUrl)
          } else {
            userStore.jumpAppletWebFullPage(`https://${item.url || ''}`)
          }
          // 轮播推荐组件 resourceType 1: 资讯 2: 商品
        } else if (componentName === 'KKCarouselRecommendWap') {
          userStore.jumpAppletWebPage(`/${resourceType === 1 ? 'news' : 'course'}/${item.id || ''}`)
        } else if (item.goodsMasterId) {
          // 主页商品相关跳转
          let paths = ''
          if (+item.goodsType === 8) {
            // 合集商品跳转
            paths = '/courseList/collectGoods/' + (item?.id || item?.goodsMasterId)
          } else {
            // 主商品跳转
            paths = '/course/' + item?.goodsMasterId
          }
          // const paths = +item.goodsType === 8 ? '/courseList/collectGoods/' : '/course/'
          userStore.jumpAppletWebPage(`${paths}`)
          // userStore.jumpAppletWebPage(`/course/${item.goodsMasterId || ''}`)
        }
        console.log({ componentName, item, index, id, resourceType }, 'link')
      }
      const { sourceType, } = item || {}
      if (sourceType === 2 && componentName === 'KKCarouselWap') {
        _statisticalPublicAdvertExposure(item, 2)
      }
    }

    // 功能页面跳转
    pageHandle = async (ctx) => {
      console.log('~~page~', ctx)

      const { componentName, item = {}, pageMasterId, index, id } = ctx
      if (componentName === 'KKCategoryNavigateWap') {
        return
      }
      if (!isXcx && (+appId === 1 && !lesseeId)) {
        // TODO: 临时处理 如果是七鱼客服 不走链接跳转（原因：链接跳转不支持上报用户信息）
        if (item?.dicSkipId === 999) {
        // 七鱼上报客户信息
          openKefu(item)
          return
        }
      }

      const pageId = pageMasterId || ''
      // [AI GEN] 从href中获取currentQuery
      const currentQuery = Object.fromEntries(
        new URL(window.location.href).searchParams.entries()
      )
      // TODO 跳转功能不同的字典值
      // navigateTo({
      //   path: `/function-page/${dicFunctionId}`,
      // })
      const { dicFunctionId, title = '', dictCode, activityTitle, discountContent, skipId, questionModuleId, questionModuleName, sourceType } = item
      const isTabClick = componentName === 'KKTabbarWap'
      //
      if (sourceType === 2 && componentName === 'KKCarouselWap') {
        _statisticalPublicAdvertExposure(item, 2)
      }
      // 题库模块跳转
      if (skipId === 1) {
        const { studyLevel1, subjectType, directionType, region, academicSection, examFormat } = currentQuery
        if (item.moduleType === 5) {
          navigateTo({
            path: '/question-bank/ai-exercise',
            query: {
              studyLevel1,
              moduleManageId: questionModuleId,
              subjectType,
              directionType,
              region,
              academicSection,
              examFormat
            }
          })
        } else {
          navigateTo({
            path: '/question-bank/list',
            query: {
              studyLevel1,
              moduleManageId: questionModuleId,
              moduleName: questionModuleName,
              subjectType,
              directionType,
              region,
              academicSection,
              examFormat
            }
          })
        }

        return
      }
      // 优惠券
      if (componentName === 'KKCourtesyCardWap') {
        const userStore = useUserStore()
        const state = userStore.isLoginFn()
        if (state) {
          if (item.type === 'get') {
            console.log(item, '优惠券')
            if (item.isGet !== 1) {
              const status = await handleReceiveApi({ couponId: item.id })
              if (status === 1 || status === 2) {
                // TODO 内存泄漏可疑点: 及时去除事件绑定
                emitter.emit('couponProps', { index, isGet: status, componentName, id })
              }

              // 对接crm
              const { addBehaviorRecords } = useBehaviorRecord()
              addBehaviorRecords(ActionEnum.CouponManagement, SourceEnum.Wap, {
                goodsTitle: activityTitle,
                clientType: isXcx ? ClientType.WX_XCX : ClientType.WAP,
                extension8: discountContent
              })
            }
          }
          if (item.type === 'go') {
            if (item.goodsSetting === 1) {
              if (isXcx) {
                userStore.jumpAppletWebPage(`/courseList/1?title=网课&pageId=${pageMasterId}`)
              } else {
                navigateTo({
                  path: '/courseList/1',
                  query: {
                    title: '网课',
                    pageId: pageMasterId,
                    pageType: 100
                  }
                })
              }
            } else if (item.goodsSetting === 4) {
              navigateTo(
                {
                  path: `/activity/couponGoods/${item.id}`,
                  query: {
                    assignTime: new Date().getTime(),
                    pageId: pageMasterId,
                  }
                }
              )
            } else {
              navigateTo({
                path: `/user/coupon/${item.id}`,
              })
            }
          }
        }
        // 购物车功能页面跳转
      } else if (dicFunctionId === dicFunctionPageId.ANSWER) {
        if (userStore.isLoginFn()) {
          if (isXcx) {
            userStore.jumpAppletWebPage('/answerQuestion')
          } else {
            navigateTo({
              path: '/answerQuestion'
            })
          }
        }
      } else if (dicFunctionId === dicFunctionPageId.ANSWER_QUESTION) {
        if (userStore.isLoginFn()) {
          if (isXcx) {
            userStore.jumpAppletWebPage('/answerQuestion/picture')
          } else {
            navigateTo({
              path: '/answerQuestion/picture'
            })
          }
        }
      } else if (dicFunctionId === dicFunctionPageId.CART) {
        const { isShowCart } = await handleCart() || {}
        if (isShowCart === 1) {
          // TODO 基础组件待对接
          const { isShowBack } = ctx
          if (isXcx) {
            userStore.jumpAppletWebPage('/cart?isShowBack=true')
          } else {
            navigateTo({
              path: '/cart',
              query: {
                isShowBack
              }
            })
          }
        } else {
          Message('系统已关闭购物车功能的使用，请刷新页面～')
        }
      } else if (dicFunctionId === dicFunctionPageId.CLASSIFY_NAV) {
        // const ltTags = useCookie<object>('LT_TAGS')
        // const url = `/book-classify?cateId=${ltTags.value?.cateId || ''}`
        const _params = {
          ...(useCookie<object>('LT_TAGS').value || {}),
        }
        const { href: href2 } = router.resolve({
          path: '/book-classify',
          query: {
            ..._params,
          }
        })
        // 分类导航
        if (isXcx) {
          userStore.jumpAppletWebPage(href2)
        } else {
          navigateTo(href2)
        }
      } else if (dicFunctionId === dicFunctionPageId.YUE_KE_LIST) {
        // 我的约课
        if (userStore.isLoginFn()) {
          const { href: href2 } = router.resolve({
            path: '/learn-center/schedule-class',
            query: { }
          })
          //
          if (isXcx) {
            userStore.jumpAppletWebPage(href2)
          } else {
            navigateTo(href2)
          }
        }
      } else if (dicFunctionId === dicFunctionPageId.MINI_LIVE) {
        // const params = encodeURIComponent(JSON.stringify({ order: 1 }))
        const { getIsShowAppletLive } = await handleCart() || {}
        if (getIsShowAppletLive === 1) {
          const { error, data } = await liveApi.getLiveInfo()
          if (!error.value) {
            const { roomid } = data.value
            if (isXcx) {
              wx.miniProgram.navigateTo({
                url: `plugin-private://wx2b03c6e691cd7370/pages/live-player-plugin?room_id=${roomid}&custom_params=1`
              })
            } else {
              // 非小程序环境
              const path = `/pages/index/index?pluginPrivate=plugin-private://wx2b03c6e691cd7370/pages/live-player-plugin?room_id=${roomid}&custom_params=1`
              const { data, error } = await liveApi.getUrlScheme({ path })
              if (!error.value) {
                const { openlink } = data.value || {}
                console.log('~~~~~~~openlink~~~~', openlink)
                if (!openlink) {
                  Message('页面丢失了，刷新重试一下吧')
                } else {
                  location.href = openlink + '&custom_params=1'
                }
              }
            }
          }
        } else {
          Message('直播间已关闭，请刷新重新重试~')
        }
        // 大礼包功能页面跳转
      } else if (dicFunctionId === dicFunctionPageId.gift_packs) {
        const { giftPackageId } = item
        if (isXcx) {
          userStore.jumpAppletWebPage(`/activity/giftPacks/${giftPackageId}`)
        } else {
          navigateTo({
            path: `/activity/giftPacks/${giftPackageId}`,
          })
        }
        // 拼团商品功能页面跳转
      } else if (dicFunctionId === dicFunctionPageId.PINTUAN_COURSE) {
        const { id } = item
        if (isXcx) {
          userStore.jumpAppletWebPage(`/activity/teambuy/${id}?seeMoreType=${ctx.item.seeMoreType}`)
        } else {
          navigateTo({
            path: `/activity/teambuy/${id}`,
            query: {
              title,
              seeMoreType: ctx.item.seeMoreType
            }
          })
        }
        // 秒杀商品功能页面跳转
      } else if (dicFunctionId === dicFunctionPageId.SECKILL_COURSE) {
        const { id } = item
        if (isXcx) {
          userStore.jumpAppletWebPage(`/activity/seckillList/${id}?seeMoreType=${ctx.item.seeMoreType}`)
        } else {
          navigateTo({
            path: `/activity/seckillList/${id}?seeMoreType=${ctx.item.seeMoreType}`,
          })
        }
        // 资讯功能页面跳转
      } else if (dicFunctionId === dicFunctionPageId.NEWS) {
        const { data } = await userStore.getCustomPageHomeId(12)
        if (data.value.data.id) {
          if (isXcx) {
            userStore.jumpAppletWebPage(`/cp/${data.value?.data.id}`)
          } else {
            navigateTo(`/cp/${data.value.data.id}`)
          }
        } else {
          // TODO
          const { data: newsList } = await getNewsCategoryAll({})
          console.log('%c [ newsList ]-369', 'font-size:13px; background:pink; color:#bf2c9f;', unref(newsList))
          const categoryList = newsList.value.cateList || []
          if (categoryList && categoryList.length > 0) {
            if (isXcx) {
              userStore.jumpAppletWebPage(`/news/category-list/${categoryList[0].usedId}`)
            } else {
              navigateTo(`/news/category-list/${categoryList[0].usedId}`)
            }
          } else {
            // navigateTo('/cp/1')
            navigateTo('/news/category-list/1')
            // Message('功能暂未开放，敬请期待~')
          }
        }
      } else if (dicFunctionId === dicFunctionPageId.POINTS) {
        try {
          //
          const { getWxShareInfo } = usePointsWxShareInfo()
          await getWxShareInfo()

          const path = findDicFunctionId(dicFunctionId)!.path as string
          // 判断是否登录，未登录拉起登录
          if (userStore.isLoginFn()) {
            if (isXcx) {
              userStore.jumpAppletWebPage(path)
            } else {
              navigateTo(path)
            }
          }
        } catch (error) {
          console.log(error)
        }
      } else if (dicFunctionId === dicFunctionPageId.MEMBER_CENTER) {
        try {
          // const { getWxShareInfoMember } = useMemberWxShareInfo()
          // await getWxShareInfoMember()

          // 会员中心
          // const { getWxShareInfo } = usePointsWxShareInfo()
          // await getWxShareInfo()

          const { data } = await memberDetails()
          const isOpen = (data as any)?.value?.isMember === 1
          if (!isOpen) {
            Message('会员中心停用，暂无法使用')
            return
          }
          const path = findDicFunctionId(dicFunctionId)!.path as string
          if (isXcx) {
            userStore.jumpAppletWebPage(path)
          } else {
            navigateTo(path)
          }
        } catch (error) {
          console.log(error)
        }
      } else if (dicFunctionId === dicFunctionPageId.WORD_BOOK) {
        window.location.href = window.location.origin + '/word-book/'
      } else if (dicFunctionId) {
        console.log('[ dicFunctionId ] >', dicFunctionId)
        const { path, authLogin, disabled } = findDicFunctionId(dicFunctionId) || {}
        if (path) {
          const getPath = () => {
            return typeof path === 'function' ? path(dicFunctionId) : path
          }
          if (authLogin) {
            if (userStore.isLoginFn()) {
              if (isXcx && !isTabClick) {
                userStore.jumpAppletWebPage(getPath())
              } else {
                navigateTo(getPath())
              }
            }
          } else if (isXcx && !isTabClick) {
            userStore.jumpAppletWebPage(getPath())
          } else if (getPath() === '/' && route.fullPath === '/') {
            // WAP：默认页面-金刚区配置首页，点击后无反馈 #71411
            navigateTo(getPath(), { external: true })
          } else {
            navigateTo(getPath())
          }
        } else if (!disabled) {
          let _params = {
            ...(useCookie<object>('LT_TAGS').value || {}),
            ...(componentName === 'LaunchAd' && { isFromLaunchAd: 1 })
            // examFormatId: ,
          }
          // HomePublicAdBanner
          // HomePublicAdZsb
          // HomePublicAdGongKao
          if (['PublicAd', 'HomePublicAdBanner', 'HomePublicAdZsb', 'HomePublicAdGongKao', 'NewsDetailPublicAd'].includes(componentName)) {
            _params = {
              cateId: item.cateId
            }
          }
          if ([19, 25].includes(dicFunctionId)) { // 全部课程
            const query = {
              title,
              ..._params,
            }
            if (dicFunctionId === 25) {
              // query.listType = 'all'
              query.hasFree = 1
            }
            const { href } = router.resolve({
              path: '/courseList',
              query
            })
            if (isXcx) {
              userStore.jumpAppletWebPage(href)
            } else {
              navigateTo(href)
            }
            return
          }
          const path = `/courseList/${dicFunctionId}`
          // if (dicFunctionId === 20) { // 0元商品
          //   return
          // }
          const { href: href2 } = router.resolve({
            path,
            query: {
              title,
              pageId,
              // pageType: currentMeta?.pageid,
              ..._params,
            }
          })
          if (isXcx) {
            userStore.jumpAppletWebPage(href2)
          } else {
            navigateTo(href2)
          }
        } else {
          Message('功能暂未开放，敬请期待~')
        }
      } else if (componentName === 'KKGiftPacksWap') {
        if (isXcx) {
          userStore.jumpAppletWebPage(`/activity/giftPacks/${item.id}`)
        } else {
          navigateTo({
            path: `/activity/giftPacks/${item.id}`,
          })
        }
      } else if (dictCode === '160') {
        // 帮助中心
        openUrl('website/helpCenter')
      } else if (dictCode === '165') {
        // 关于我们
        // openUrl('website/aboutUs?type=intro')
        Message('暂无数据~')
      } else if (dictCode === '180') {
        window.location.href = `tel:${item?.content}`
        // 服务热线无提示
      } else {
        const { dictCode } = item
        const { path, disabled, authLogin } = userCenterDicPageList.find(v => v.value === dictCode) || {}
        console.log('tiku', path, authLogin)
        if (isXcx && authLogin && !userStore.isLogin) { // 未登录
          setCp()
          checkPointsClueSource()
          if (!isLoginMiniProgram.value) {
            isLoginMiniProgram.value = true
            wx.miniProgram.navigateTo({
              url: `/pages/login/index?redirect_uri=${encodeURIComponent(
                route.fullPath
              )}&logo2=${logo2 || ''}&themeColor=${themeColor}`,
              complete: function () {
                setTimeout(() => {
                  isLoginMiniProgram.value = false
                }, 1000)
              },
            })
          }
        } else if (path) {
          if (isXcx) {
            const checkUserReg = /^\/user\//
            // 判断是否为题库页面
            const isQuestionBankPage = path.includes('/question-bank/')
            const jumpMiniProgram = isQuestionBankPage ? userStore.jumpToQuestionBank : userStore.jumpAppletWebPage
            if (checkUserReg.test(path) || authLogin) {
              if (!userStore.isLogin) { // 未登录
                setCp()
                checkPointsClueSource()
                if (!isLoginMiniProgram.value) {
                  isLoginMiniProgram.value = true
                  wx.miniProgram.navigateTo({
                    url: `/pages/login/index?redirect_uri=${encodeURIComponent(
                      route.fullPath
                    )}&logo2=${logo2 || ''}&themeColor=${themeColor}`,
                    complete: function () {
                      setTimeout(() => {
                        isLoginMiniProgram.value = false
                      }, 1000)
                    },
                  })
                }
              } else {
                if (path.includes('/notification')) {
                  localStorage.setItem('notificationPath', route.fullPath)
                }
                jumpMiniProgram(path)
              }
            } else {
              jumpMiniProgram(path)
            }
          } else if (authLogin && !userStore.isLogin) {
            userStore.isLoginFn()
          } else {
            const { dictName: name } = item
            // path带query场景支持
            if (!path.includes('?')) {
              // 退出登录需要重定向来源页，兼容/me或cp/id
              const questionArr = ['/question-bank/learning-report', '/question-bank/ranking']
              const userInfo = useUserStore().getUserInfo
              // 增加题库刷题排行和刷题报告页面判断，如果是这两个页面 将用户id拼接在路径后面 分享时使用
              if (os?.isApp && os.isAndroid) {
                window.android.login()
              } else if (os?.isApp && (os?.isPhone || os?.isTablet)) {
                window.webkit.messageHandlers.login.postMessage(null)
              } else if (os?.isApp && os.isHarmonyOS) {
                window.harmony?.login()
              } else {
                setCp()
                checkPointsClueSource()
                dictCode === '170'
                  ? navigateTo({ path, query: { name, redirect_uri: encodeURIComponent(route.fullPath) } })
                  : questionArr.includes(path) ? navigateTo({ path, query: { name, usedId: userInfo?.userId } }) : navigateTo({ path, query: { name } })
              }
            } else {
              navigateTo(path)
            }
          }
        } else if (disabled) {
          // TODO
        } else {
          Message('功能暂未开放，敬请期待~')
        }
      }
    }

    // 页面搜索时跳转
    searchHandle = (ctx) => {
      console.log(ctx, 'search')
      const { pageMasterId } = ctx
      const pageId = pageMasterId || ''
      const { meta: currentMeta } = route
      const pageMasterIdByCookie = useCookie(LT_LOCAL_KEY)
      // 首页跳转搜索 更改pageId
      let pageIdBypage = currentMeta?.pageid === '100' ? (pageMasterIdByCookie?.value || usableId.value || '') : pageId
      if (kuke99DefaultHomeData.value.cpId && !pageMasterIdByCookie?.value) {
        // !route.query.lt
        pageIdBypage = pageId
      }
      if (isXcx) {
        userStore.jumpAppletWebPage(`/search?pageId=${pageIdBypage}`)
      } else {
        navigateTo({
          path: '/search',
          query: {
            pageId: pageIdBypage
          }
        })
      }
    }

    moreHandle = (ctx) => {
      console.log(ctx, 'more')
      const { id, componentName, item = {}, pageMasterId } = ctx
      const pageId = pageMasterId || ''
      const { meta: currentMeta } = route
      // const { title } = ctx.item
      // TODO 先判断拼团&秒杀逻辑
      // const result = componentDictMap[componentName]
      if (componentName === 'KKQuickBuyWap') {
        if (isXcx) {
          userStore.jumpAppletWebPage(`/activity/seckillList/${id}?seeMoreType=${ctx.item.seeMoreType}`)
          return
        }

        navigateTo({
          // path: `/resource-comp/${result.assemblyType}`,
          path: `/activity/seckillList/${id}`,
          query: {
            seeMoreType: ctx.item.seeMoreType,
          }
        })
        return
      }
      if (id) {
        if (componentName === 'KKCollageGoodsWap') {
          if (isXcx) {
            userStore.jumpAppletWebPage(`/activity/teamBuy/${id}?seeMoreType=${ctx.item.seeMoreType}`)
            return
          }
          navigateTo({
            path: `/activity/teamBuy/${id}`,
            query: {
              pageType: currentMeta?.pageid,
              seeMoreType: ctx.item.seeMoreType
            }
          })
          return
        }
        if (componentName === 'KKNewsInformationWap') {
          const commonId = item.currentActive ? item.currentActive.id : ''
          if (isXcx) {
            userStore.jumpAppletWebPage(`/news/list?id=${item.id}&commonId=${commonId}&pageId=${pageId}`)
            return
          }
          navigateTo({
            path: '/news/list',
            query: {
              id: item.id,
              commonId,
              pageId
            }
          })
          return
        }
        if (isXcx) {
          userStore.jumpAppletWebPage(`/resource-comp/${id}?pageId=${pageId}`)
          return
        }
        navigateTo({
          path: `/resource-comp/${id}`,
          query: {
            title: item.title,
            pageId,
            pageType: currentMeta?.pageid
          },
        })
      } else {
        Message('功能暂未开放，敬请期待~')
      }
    }

    // 小程序跳转
    appletHandle = async (ctx) => {
      console.log(ctx, 'appletHandle')
      const { appid, pageUrl, appletOriginalId } = ctx.item || {}
      console.log('[ { appid, pageUrl, appletOriginalId } ] >', { appid, pageUrl, appletOriginalId })
      xcxLaunchData.miniprogramId = appid
      xcxLaunchData.pageUrl = pageUrl
      if (isXcx) { // 微信小程序内
        const param = {
          appId: appid,
          path: pageUrl,
          envVersion: xcxLaunchData.envVersion,
        }
        wx.miniProgram.navigateTo({
          url: `/pages/jumpMiniProgram/index?jumpParam=${encodeURIComponent(
            JSON.stringify(param)
          )}`,
        })
      } else {
        const { data, error } = await commonApi.getURLSchemeNews({ appletOriginalId })
        if (!error.value) {
          const { openlink } = data.value || {}
          console.log('~~~~~~~openlink~~~~', openlink)
          if (!openlink) {
            Message('页面丢失了，刷新重试一下吧')
          } else {
            location.href = openlink
          }
        }
      }
    }

    // 登录跳转
    loginHandle = (ctx) => {
      console.log(ctx, 'login')
      setCp()
      checkPointsClueSource()
      if (userStore.isLogin) {
        if (isXcx) {
          userStore.jumpAppletWebPage('/user/info')
        } else {
          navigateTo('/user/info')
        }
      } else if (isXcx) {
        if (!isLoginMiniProgram.value) {
          isLoginMiniProgram.value = true
          wx.miniProgram.navigateTo({
            url: `/pages/login/index?redirect_uri=${encodeURIComponent(
              route.fullPath
            )}&logo2=${logo2 || ''}&themeColor=${themeColor}`,
            complete: function () {
              setTimeout(() => {
                isLoginMiniProgram.value = false
              }, 1000)
            },
          })
        }
      } else if (os?.isApp && os.isAndroid) {
        window.android.login()
      } else if (os?.isApp && (os?.isPhone || os?.isTablet)) {
        window.webkit.messageHandlers.login.postMessage(null)
      } else if (os?.isApp && os.isHarmonyOS) {
        window.harmony?.login()
      } else {
        navigateTo(`/login?redirect_uri=${encodeURIComponent(route.fullPath)}`)
      }
    }

    // 自定义页面跳转
    customPageHandle = async (ctx) => {
      console.log(ctx, 'customPage')
      const { item, componentName } = ctx || {}
      if (componentName === 'KKCategoryNavigateWap') {
        return
      }
      const bottomNavStatus = await getBottomNavigateStatus(item.customPageId)
      if (isXcx) {
        // 底部导航开启使用 nuxt 的跳转方式
        if (bottomNavStatus) {
          navigateTo({
            path: `/cp/${item.customPageId}`
          }, { external: true })
        } else {
          // 底部导航没有开启使用小程序的跳转方式（打开一个新的 webview）
          userStore.jumpAppletWebPage(`/cp/${item.customPageId}`)
        }
      } else {
        navigateTo({
          path: `/cp/${item.customPageId}`
        }, { external: true })
      }
      // Message('功能暂未开放，敬请期待~')
      const { sourceType, } = item || {}
      if (sourceType === 2 && componentName === 'KKCarouselWap') {
        _statisticalPublicAdvertExposure(item, 2)
      }
    }

    // 客服跳转
    kefuHandle = (ctx) => {
      // TODO: 临时处理 如果是七鱼客服 不走链接跳转（原因：链接跳转不支持上报用户信息）
      console.log(ctx, 'kefuHandle')
      if (isXcx) {
        wx.miniProgram.navigateTo({
          url: '/pages/wxService/index'
        })
      }
    }

    // 首页跳转
    indexHandle = () => {
      // if (!userStore.isLogin) { }
      // 登录：展示已经选过的【学习目标首页】（前提用户已经保存过）
      // 非登录：回到【默认首页】并展示弹窗
      // if (localId.value) {
      if (kuke99DefaultHomeData.value.cpId) {
        if (!localId.value && !learnStore.state.showLearnDialog) {
          //  && route.name === 'index'
          learnStore.setLearnDialogState(true)
        } else {
          navigateTo({
            path: '/',
            query: {
              lt: localId.value,
              // chooseLt: localId.value ? undefined : '1'
            }
          }, { external: true })
        }
      } else {
        navigateTo({
          path: '/',
          query: {
            lt: localId.value ? localId.value : undefined
          }
        }, { external: true })
      }
    }
    goodsGroupHandle = async (ctx) => {
      console.log(ctx, 'goodsGroup')
      const { item, componentName } = ctx
      const { dicSkipId, groupId, groupIdName, title, sourceType } = item
      console.log('[ dicSkipId ] >', dicSkipId)
      if (sourceType === 2 && componentName === 'KKCarouselWap') {
        _statisticalPublicAdvertExposure(item, 2)
      }

      const { error } = await checkGroupId({ groupId })
      if (error.value) {
        // Message('配置失效了哦~')
        return
      }
      let _params = {
        ...(useCookie<object>('LT_TAGS').value || {})
      }
      if (['PublicAd', 'HomePublicAdBanner', 'HomePublicAdZsb', 'HomePublicAdGongKao', 'NewsDetailPublicAd'].includes(componentName)) {
        // if (componentName === 'PublicAd') {
        _params = {
          cateId: item.cateId
        }
      }
      const { href } = router.resolve({
        path: `/courseList/group-${groupId}`,
        query: {
          title: groupIdName || title,
          ..._params,
        },
      })
      if (isXcx) {
        userStore.jumpAppletWebPage(href)
      } else {
        navigateTo(href)
      }
    }
    onlineKefuHandle = async (ctx) => {
      //
      console.log(ctx, 'onlineKefuHandle')
      // if (isXcx) {
      //   // userStore.jumpAppletWebFullPage('https://imxcx15.7x24cc.com/phone_webChat.html?accountId=N000000036901&chatId=7953b0b6-a9af-42fd-babc-429a3f0941ab&productId=8')
      //   userStore.jumpAppletWebFullPage('https://work.weixin.qq.com/kfid/kfca0728c2014d5af98')
      //   // location.href = 'https://work.weixin.qq.com/kfid/kfca0728c2014d5af98'
      // } else {
      //   location.href = 'https://work.weixin.qq.com/kfid/kfca0728c2014d5af98'
      // }
      // 从不同来源获取客服相关信息
      const {
        customerServiceType,
        customerServiceId,
        cateId,
      } = ctx.item.dictCode === '330'
        ? (ctx.item.kmCustomFunctionInfo || {})
        : (ctx.item || {})
      if (customerServiceId) {
        const ltTags = useCookie<object>('LT_TAGS').value || {}
        const { isXcx } = useAppConfig()
        const body = {
          id: customerServiceId,
          productId: appId,
          // TODO
          categoryId: cateId || ltTags?.cateId,
          // 1合力2微信
          // eslint-disable-next-line eqeqeq
          type: customerServiceType == 1 ? 2 : 1,
          customerServiceType,
          productSide: isXcx ? 5 : 7
        }
        if (isKukeCloudAppWebview) {
          body.productSide = 9
        }
        kefuStore.getKefuDataOfComponent(body)
        // await getRuleListByIdParams(body)
      }
    }
    // materialDetailHandle = async (ctx) => {
    //   console.log(ctx, 'materialDetailHandle')
    // }
    traceAnalyticsHandle = (ctx) => {
      console.log(ctx, 'traceAnalytics')
      const { item } = ctx || {}
      const { sourceType, } = item
      if (sourceType === 2 && process.client) {
        // setTimeout(() => {
        nextTick(() => {
          _statisticalPublicAdvertExposure(item, 1)
        })
        // }, 200)
      }
    }
    onQuestionModuleOpenHandle = (ctx) => {
      // 题库模块跳转
      const { item = {}, } = ctx
      const { cateId, moduleType, questionModuleId, questionModuleName } = item
      if (moduleType === 5) {
        // AI 练习模块跳转AI练习首页
        navigateTo({
          path: '/question-bank/ai-exercise',
          query: {
            studyLevel1: cateId === 0 ? '' : cateId,
            moduleManageId: questionModuleId,
          }
        })
      } else {
        // 跳转模块详情页
        navigateTo({
          path: '/question-bank/list',
          query: {
            studyLevel1: cateId === 0 ? '' : cateId,
            moduleManageId: questionModuleId,
            moduleName: questionModuleName,
          }
        })
      }
    }
    events.subscribe('link', linkHandle)
    events.subscribe('page', pageHandle)
    events.subscribe('more', moreHandle)
    events.subscribe('search', searchHandle)
    events.subscribe('applet', appletHandle)
    events.subscribe('login', loginHandle)
    events.subscribe('customPage', customPageHandle)
    events.subscribe('kefu', kefuHandle)
    events.subscribe('header:index', indexHandle)
    events.subscribe('goodsGroup', goodsGroupHandle)
    events.subscribe('onlineKefu', onlineKefuHandle)
    // events.subscribe('materialDetail', materialDetailHandle)
    events.subscribe('traceAnalytics', traceAnalyticsHandle)
    events.subscribe('questionModuleOpen', onQuestionModuleOpenHandle)
  })
  /**
   * 统计广告曝光
   * @param item
   * @param type 1 曝光  2  打开
  */
  async function _statisticalPublicAdvertExposure (item: Record<string, any>, type: number = 1) {
    const { id } = item || {}
    if (!id) {
      return
    }
    const body = {
      id,
      type,
    }
    await statisticalPublicAdvertExposure(body)
  }
  onUnmounted(() => {
    events.unsubscribe(linkHandle)
    events.unsubscribe(pageHandle)
    events.unsubscribe(moreHandle)
    events.unsubscribe(searchHandle)
    events.unsubscribe(appletHandle)
    events.unsubscribe(loginHandle)
    events.unsubscribe(customPageHandle)
    events.unsubscribe(kefuHandle)
    events.unsubscribe(indexHandle)
    events.unsubscribe(goodsGroupHandle)
    events.unsubscribe(onlineKefuHandle)
    // events.unsubscribe(materialDetailHandle)
    events.unsubscribe(traceAnalyticsHandle)
    events.unsubscribe(onQuestionModuleOpenHandle)
  })
  return {
    xcxLaunchData,
  }
}
