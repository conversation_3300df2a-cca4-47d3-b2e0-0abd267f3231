<template>
  <div v-if="categoryList && categoryList.length > 0" class="news-nav-wrap h-[82px] bg-[#fff]">
    <div ref="newsTabs" class="news-tabs">
      <nuxt-link
        v-for="item in categoryList"
        :key="item.id"
        :class="['news-tabs-item', item.title === activeName || (item.id === 0 && !activeName) ? 'news-tabs-item-active' : '']"
        :to="item.id === 0 ? `/cp/${homeId}` : `/news/category-list/${item.id}`"
        replace
      >
        {{ item.title }}
      </nuxt-link>
    </div>
    <div v-if="props.isShowSearch" class="news-search">
      <div class="w-[64px] h-[64px] flex justify-center items-center bg-[#F7F7F7] rounded-[50%] ml-[20px]">
        <KKCIcon
          name="icon-sousuo1"
          :size="26"
          color="#666"
          @click="handleSearch"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useUserStore } from '~/stores/user.store'
import { getNewsCategoryAll } from '~/apis/news'

const userStore = useUserStore()
// 专业分类
const categoryList = ref()
// const { newsApi } = useApi()
const { isXcx } = useAppConfig()
const route = useRoute()
const homeId = ref()
const activeName = ref()

const props = defineProps({
  pageType: {
    type: Number,
    default: 0
  },
  // cateList: {
  //   type: Array,
  //   default: () => ([])
  // },
  isShow: {
    type: Boolean,
    default: false
  },
  isShowSearch: {
    type: Boolean,
    default: true
  }
})
const [{ value: customPageInfo }, { value: categoryInfo }] = await Promise.allSettled([
  userStore.getCustomPageHomeId(12),
  getNewsCategoryAll({})
])

homeId.value = customPageInfo.data.value?.data.id
const list = categoryInfo.data.value?.cateList?.map(v => ({ title: v.categoryName, id: v.usedId })) || []
console.log('%c [ 可用的一级 ]-277', 'font-size:13px; background:pink; color:#bf2c9f;', list)
const filter = list?.filter(v => Number(v.id) === Number(route.params.cateId as string)) || []
activeName.value = filter.length > 0 ? filter[0].title : ''
console.log(homeId.value)
if ((route.params.id === homeId.value || route.name === 'news-category-list' || route.name === 'cp-id') && homeId.value) {
  categoryList.value = [{ title: '推荐', id: 0 }, ...list]
} else {
  categoryList.value = [...list]
  const firstId = categoryList.value[0]?.id
  if (route.name === 'cp-id' && firstId) {
    navigateTo({
      path: `/news/category-list/${firstId}`,
      // params: {
      //   cateId: firstId,
      // },
      query: { newsClassifyId: '' },
      replace: true
    })
  }
}
// 跳转至目标tab
const newsTabs = ref()
const index = categoryList.value.length > 0 && categoryList.value.findIndex((item: any) => item.id === Number(route.params.cateId))
if (process.client) {
  // TODO 内存泄漏可疑点: 确保客户端执行并及时清除
  setTimeout(() => {
    index > 2 && newsTabs.value.children[index].scrollIntoView({ behavior: 'instant', inline: 'center' })
    // const wrap: HTMLElement = document.querySelector('.layout-default') as HTMLElement
    // document.body.scrollTop = document.documentElement.scrollTop = 0
    // wrap.scrollTop = 0
  }, 300)
}

// 去搜索
const handleSearch = () => {
  if (isXcx) {
    userStore.jumpAppletWebPage('/search?id=5')
  } else {
    navigateTo({ path: '/search', query: { id: 5 } })
  }
}
</script>

<style lang="scss" scoped>
.news-nav-wrap {
  position: relative;
  top: -1PX;
  .news-search {
    position: absolute;
    right: 0;
    top: 10px;
    background-color: #fff;
    padding-left: 10px;
    padding-right: 24px;
    height: 72px;
  }
}
.news-tabs {
  width: 100%;
  white-space: nowrap;
  line-height: 80px;
  overflow-x: auto;
  padding-left: 24px;
  padding-right: 90px;
  &::-webkit-scrollbar {
    display: none;
  }
  &-item {
    margin-right: 28px;
    color: #333;
    font-size: 30px;
  }
  &-item-active {
    @apply text-brand text-[32px] font-[600] relative;
    &:after {
      content: '';
      display: block;
      position: absolute;
      width: 47px;
      height: 8px;
      border-radius: 8px;
      bottom: -20px;
      left: 50%;
      transform: translateX(-50%);
      @apply bg-brand;
    }
  }
}
</style>
