<template>
  <section
    class="fade-enter fade-enter-img "
    :class="isMoveClass ? 'show-popup-half background-up' : 'unshown-popup-half !bg-[#fff]'"
  >
    <div class="fade-enter-content">
      <div class="icon-arrows" @click="changeMove">
        <KKCIcon name="icon-wode-youjiantou" :size="32" :class="isMoveClass? 'icon-arrows-top' : 'icon-arrows-bottom'" />
      </div>
      <div class="placeholder flex items-center justify-between">
        <img :src="Logo" alt="">
        <div class="down-btn" @click="toJump">
          立即下载
        </div>
      </div>
      <div class="card-layout text-[#666]">
        <NuxtLink class="right-line" :to="`agreement/default/${linkIds[0]}`">
          权限列表
        </NuxtLink>
        <NuxtLink class="right-line" :to="`agreement/default/${linkIds[1]}`">
          隐私政策
        </NuxtLink>
        <span class="right-line">版本号V{{ appVersion }}</span>
        <span>更新时间: {{ appTime }}</span>
      </div>
      <div class="card-layout">
        <span class="right-line">开发者: 库课文化科技集团股份有限公司</span>
        <span>服务热线: 400-6529-888</span>
      </div>
    </div>
    <div
      class="kukeAi"
    >
      <BottomPopNoLogin
        v-if="!userStore.isLogin"
        class=""
        @click="userStore.isLoginFn()"
      />
      <!-- @close="userStore.isLoginFn()" -->
      <BottomPopAiEntry
        v-else-if="kukeAiUrl"
        class=""
        :kuke-ai-url="kukeAiUrl"
      >
        <!-- <span class="flex-shrink-0">
          <img
            class="w-[72px] h-[72px]"
            src="@/assets/images/home/<USER>"
            alt="库AI"
          >
        </span>
        <div
          class="text-[#6F44FF] px-[8px] text-[24px] leading-[32px] font-medium flex-1"
        >
          已接入DeepSeek，备<br>
          考、答疑都可以找我~
        </div>
        <span
          class="flex-shrink-0"
          @click.stop.prevent="closeKukeAi"
        >
          <KKCIcon
            name="icon-com_guangbi"
            :size="24"
            color="#111"
          />
        </span> -->
      </BottomPopAiEntry>
    </div>
  </section>
</template>
<script setup lang="ts">
import { useUserStore } from '~/stores/user.store'
import Logo from '@/assets/images/common/logo.png'
const userStore = useUserStore()
const androidUrl = ref('')
const appVersion = ref('')
const appTime = ref('')
interface LinkType {
  versionNumber: string,
  content: string,
  isForce: number
  upgradeDialog: number
  upgradeUrl: string
  createdAt: string
}
const { $dayjs: dayjs } = useNuxtApp()

// const runTimeconfig = useRuntimeConfig()
// const { PRIVACY_POLICY_THIRD_ID } =
//     runTimeconfig.public
const { PRIVACY_POLICY_AGREEMENT_ID, PRIVACY_POLICY_THIRD_ID } = useAppConfig()
const linkIds = ref([PRIVACY_POLICY_THIRD_ID, PRIVACY_POLICY_AGREEMENT_ID])
const getData = async () => {
  const { data, error } = await useHttp<LinkType>(ApisCommon.getAndroidLink, {
    method: 'post',
    body: {
      productType: 1
    },
    transform: res => res.data,
  })
  if (!error.value) {
    const { upgradeUrl, versionNumber, createdAt } = data.value || {}
    androidUrl.value = upgradeUrl
    appVersion.value = versionNumber
    appTime.value = dayjs(createdAt).format('YYYY.M.D')
    console.log(androidUrl.value, appVersion.value)
  }
}
const kukeAiUrl = ref('')
const judgePageMasterIdValid__ = async () => {
  const { data, error } = await judgePageMasterIdValid()
  if (!error.value) {
    kukeAiUrl.value = data.value?.kukeAiUrl
  }
}

const jumpUrl = ref('')
const toJump = () => {
  if (os?.isPhone) {
    const userAgent = navigator.userAgent.toLowerCase()
    // 判断是否在微信中打开
    if (userAgent.includes('micromessenger')) {
      Message('请前往AppStore下载【库课网校】App')
      return
    } else {
      jumpUrl.value = 'https://m.kuke99.com/receive-page-h5/index?landNum=10242&landType=1'
    }
  } else {
    // jumpUrl.value = androidUrl.value
    jumpUrl.value = 'https://m.kuke99.com/receive-page-h5/index?landNum=10242&landType=1'
  }
  console.log(os, jumpUrl.value, 'app下载')
  window.open(jumpUrl.value)
}
const isShowBottom = useCookie<string>('isShowBottom')
const isMoveClass = ref(!isShowBottom.value)
if (!isShowBottom.value) {
  isShowBottom.value = '1'
}

const changeMove = () => {
  isMoveClass.value = !isMoveClass.value
}
// const closeKukeAi = () => {
//   kukeAiUrl.value = ''
// }

const scrollState = inject('scrollState') as { isScroll: boolean }
watch(() => scrollState.isScroll, (newV) => {
  if (newV) {
    isMoveClass.value = false
  }
})
// const botHeight = ref(0)
onMounted(async () => {
  await nextTick()
  await Promise.all([
    getData(),
    judgePageMasterIdValid__()
  ])
  // setTimeout(() => {
  //   botHeight.value = document.getElementsByClassName('tabber')[0]?.offsetHeight || 60
  //   console.log(botHeight.value, 'botHeight.value222')
  // }, 100)
})
// onNuxtReady(async () => {
//   await Promise.all([
//     getData(),
//     judgePageMasterIdValid__()
//   ])
// })
</script>

<style lang="scss" scoped>
.fade-enter {
  position: fixed;
  bottom: 120px;
  bottom: calc(constant(safe-area-inset-bottom) + 120px);
    /*兼容 IOS<11.2*/
    bottom: calc(env(safe-area-inset-bottom) + 120px);
    /*兼容 IOS>11.2*/
  width: 7.5rem;
  box-shadow: 0 -0.05rem 0.08rem 0 rgba(0, 0, 0, 0.1);
  border-radius: 0.3rem 0.3rem 0 0;
  z-index: 11;
  transition: all .5s;

}

.fade-enter-img {
  padding: 0 24px;
  height: 2.5rem;
  box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.15);
  border: 2px solid #FFFFFF;
}
.background-up{
  background: linear-gradient(180deg, rgba(235, 35, 48, 0.2) 0%, rgba(235, 35, 48, 0.1) 12%, rgba(255, 255, 255, 0) 34%), #FFFFFF;
}
.fade-enter-content {
  display: flex;
  flex-direction: column;
}

.fade-enter-content .icon-arrows {
  display: flex;
  justify-content: center;
  align-items: center;
  height: .55rem;
  width: 100%;
}

.fade-enter-content .icon-arrows div {
  width: 0.48rem;
  height: 0.16rem;
}

.fade-enter-content .placeholder {
  padding-bottom: 24px;
  border-bottom: 1px solid #ddd;

  img {
    width: 204px;
    height: 64px;
  }

  .down-btn {
    width: 136px;
    height: 48px;
    background: #EB2330;
    border-radius: 12px;
    text-align: center;
    line-height: 48px;
    font-family: PingFang SC;
    font-weight: 500;
    font-size: 24px;
    color: #FFFFFF;
    z-index: 110;
  }
}

.fade-enter-content .card-layout {
  display: flex;
  padding-top: 8px;

  .right-line {
    display: inline-block;
    position: relative;

    &:after {
      content: '';
      display: block;
      width: 1px;
      height: 14px;
      background-color: #eee;
      position: absolute;
      top: 50%;
      right: -12px;
      transform: translateY(-50%);
    }
  }
}

.fade-enter-content .card-layout a {
  font-size: 22px;
  font-weight: 400;
  color: #666;
  line-height: 28px;
  margin-right: 24px;
}

.fade-enter-content .card-layout span {
  font-size: 22px;
  font-weight: 400;
  color: #666;
  line-height: 28px;
  margin-right: 24px;
}

.fade-enter-content .icon-arrows-top {
  animation: all 500ms forwards;
  transform: rotate(90deg);
}

.fade-enter-content .icon-arrows-bottom {
  animation: all 500ms forwards;
  transform: rotate(-90deg);
}

.show-popup-half {
  transform: translate3d(0, 0, 0);
}

.unshown-popup-half {
  transform: translate3d(0, 80%, 0);
}

.show-popup-all {
  animation: show-all 1000ms forwards;
}

.unshown-popup-all {
  animation: unshown-all 1000ms forwards;
}
.hide-bottom{
  transform: translate3d(0, 80%, 0);
    background: #fff;
    opacity: 1;
}
/* 显示 */
@keyframes show-half {
  from {
    opacity: 0.1;
    transform: translate3d(0, 80%, 0);
  }

  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

/* 隐藏 */
@keyframes unshown-half {
  0% {
    transform: translate3d(0, 0, 0);
  }

  90% {
    transform: translate3d(0, 80%, 0);
    opacity: 0.5;
  }

  100% {
    transform: translate3d(0, 80%, 0);
    background: #fff;
    opacity: 1;
  }
}

/* 显示 */
@keyframes show-all {
  from {
    opacity: 0.1;
    transform: translate3d(0, 100%, 0);
  }

  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

/* 隐藏 */
@keyframes unshown-all {
  0% {
    transform: translate3d(0, 0, 0);
  }

  100% {
    transform: translate3d(0, 80%, 0);
  }
}

@keyframes rotate-top {
  from {
    opacity: 0;
    transform: rotate(-180deg);
  }

  to {
    opacity: 1;
    transform: rotate(0deg);
  }
}

@keyframes rotate-bottom {
  from {
    opacity: 0;
    transform: rotate(-180deg);
  }

  to {
    opacity: 1;
    transform: rotate(0deg);
  }
}
.kukeAi {
  position: absolute;
  // top: -64px;
  // top: -30%;
  top: -120px;
  left: 50%;
  // height: 60px;
  // line-height: 60px;
  // text-align: center;
  // transform: translate(-50%, -50%);
  transform: translateX(-50%);
  //
  &__entry {
    //
  }
  &__nologin {
    //
  }

}
</style>
