import { useTrackEvent } from './useTrackEvent'
import { useUserStore } from '~/stores/user.store'
const { track } = useTrackEvent()

// [AI-GEN] 业务类型常量定义
const BUSINESS_TYPES = {
  ORDER: '1', // 订单
  QUESTION_BANK: '2', // 题库
  OTHER: '3' // 题库已购
} as const

// [AI-GEN] 目标类型常量定义
const TARGET_TYPES = {
  DAILY_PRACTICE: 1, // 每日练习
  PAPER: 2, // 试卷
  CHAPTER: 3, // 章节知识点
  FIXED_EXAM: 4, // 模块刷题
  MODULE: 99 // 模块
} as const

// [AI-GEN] 辅助函数：获取业务来源名称
const getBusinessSourceName = (type: string): string => {
  switch (type) {
    case BUSINESS_TYPES.QUESTION_BANK:
      return '学习中心'
    case BUSINESS_TYPES.ORDER:
      return '订单'
    default:
      return '题库已购'
  }
}

// [AI-GEN] 辅助函数：判断是否为模块类型
const isModuleType = (targetType: number): boolean => {
  return targetType === TARGET_TYPES.MODULE
}

// [AI-GEN] 辅助函数：获取产品类型名称
const getProductTypeName = (targetType: number): string => {
  return isModuleType(targetType) ? '模块' : '单品'
}

// 题库已购模块、单品、非试卷包和网课的试卷需要判断题库权益，跳转不能传商品id ！！！
export const useJumpQuestionPurchased = () => {
  const { courseApi } = useApi()
  const userStore = useUserStore()
  const { isXcx } = useAppConfig()
  const createPurchasedLink = async (type: '1' | '2' | '3', id: string, specificationItemId?:string) => {
    // [AI-GEN] 参数说明：
    // type: '1' = 订单来源, '2' = 题库/学习中心来源, '3' = 其他来源
    // id: 商品ID或目标ID
    // specificationItemId: 规格项ID（可选）
    try {
      const { data, error } = await courseApi.getTagsByGoodsIdProt({
        targetType: type,
        targetId: id,
        specificationItemId
      })

      if (!error.value) {
        const { targetType, targetId, moduleManageId, studyLevel1, subjectType, region, academicSection, examFormat, directionType, isPublish } = data.value
        // targetType 目标类型 1每日练习 2试卷 3章节知识点 4模块刷题 99模块
        const baseQuery = {
          // goodsMasterId: type === '1' ? id : goodsMasterId,
          moduleManageId,
          studyLevel1,
          subjectType: String(subjectType),
          region: String(region),
          academicSection: String(academicSection),
          examFormat: String(examFormat),
          directionType: String(directionType),
          isPublish
        }
        if (targetType !== 99) {
          Object.assign(baseQuery, { purchased: '2', from: '1' })
        }
        let path = ''
        // 模块 `/question-bank/purchased`
        // 试卷 `/question-bank/paper/detail`
        // 固定刷题 `/question-bank/fixed-exam/catalog`
        // 教材 `/question-bank/purchased/textbook`

        // type 2 题库，1 订单 ;
        // targetType 99 模块，其他是单品;

        // [AI-GEN] 使用辅助函数区分题库和订单来源
        const categoryName = getBusinessSourceName(type)

        // [AI-GEN] 使用辅助函数区分模块和单品类型
        const productType = getProductTypeName(targetType)

        // [AI-GEN] 更精准的埋点信息
        track({
          category: categoryName,
          action: `跳转题库${productType}`,
        })
        switch (targetType) {
          case 2:
            path = `/question-bank/paper/detail?testpaperId=${targetId}&${new URLSearchParams(baseQuery).toString()}`
            if (isXcx) {
              userStore.jumpToQuestionBank(path)
            } else {
              navigateTo(path)
            }
            break
          case 3:
            path = `/question-bank/purchased/textbook?textbookId=${targetId}&${new URLSearchParams(baseQuery).toString()}`
            if (isXcx) {
              userStore.jumpToQuestionBank(path)
            } else {
              navigateTo(path)
            }
            break
          case 4:
            path = `/question-bank/fixed-exam/catalog?brushVersionId=${targetId}&${new URLSearchParams(baseQuery).toString()}`
            if (isXcx) {
              userStore.jumpToQuestionBank(path)
            } else {
              navigateTo(path)
            }
            break
          case 99: {
            const moduleQuery = createPurchasedParamsFromCheckList((data.value as any).checkList || [])
            path = `/question-bank/purchased?${new URLSearchParams({ ...baseQuery, ...moduleQuery }).toString()}`
            if (isXcx) {
              userStore.jumpToQuestionBank(path)
            } else {
              navigateTo(path)
            }
            break
          }
        }
      }
    } catch (error) {
      console.log(error)
    }
  }
  /**
   * 专用于
   * 学习中心-我的班级页
   * 学习中心-题库单品权益
   * 商品购买成功页
   * 商品详情-题库模块套餐商品页
   * */
  const createPurchasedLinkByLearnCenterIndex = async (_baseQuery: Record<string, any> = {}, body = {}, type: string) => {
    try {
      const { data, error } = await courseApi.getTagsByGoodsRightProt(body || {})

      if (!error.value) {
        const { targetType, targetId, moduleManageId, studyLevel1, subjectType, region, academicSection, examFormat, directionType, isPublish } = data.value
        // targetType 目标类型 1每日练习 2试卷 3章节知识点 4模块刷题 99模块
        const baseQuery = {
          // goodsMasterId,
          ..._baseQuery,
          moduleManageId,
          studyLevel1,
          subjectType,
          region,
          academicSection,
          examFormat,
          directionType,
          isPublish
        }
        if (targetType !== 99) {
          Object.assign(baseQuery, { purchased: '2', from: '1' })
        }
        let path = ''
        // 模块 `/question-bank/purchased`
        // 试卷 `/question-bank/paper/detail`
        // 固定刷题 `/question-bank/fixed-exam/catalog`
        // 教材 `/question-bank/purchased/textbook`

        // type 2 题库，'learn-center' 学习中心 ;
        // targetType 99 模块，其他是单品;

        // [AI-GEN] 使用辅助函数区分模块和单品类型
        const productType = getProductTypeName(targetType)

        // [AI-GEN] 更精准的埋点信息
        if (type === '2') {
          track({
            category: '题库',
            action: `跳转题库${productType}`,
          })
        }

        switch (targetType) {
          case 2:
            path = `/question-bank/paper/detail?testpaperId=${targetId}&${new URLSearchParams(baseQuery).toString()}`
            if (isXcx) {
              userStore.jumpToQuestionBank(path)
            } else {
              navigateTo(path)
            }
            break
          case 3:
            path = `/question-bank/purchased/textbook?textbookId=${targetId}&${new URLSearchParams(baseQuery).toString()}`
            if (isXcx) {
              userStore.jumpToQuestionBank(path)
            } else {
              navigateTo(path)
            }
            break
          case 4:
            path = `/question-bank/fixed-exam/catalog?brushVersionId=${targetId}&${new URLSearchParams(baseQuery).toString()}`
            if (isXcx) {
              userStore.jumpToQuestionBank(path)
            } else {
              navigateTo(path)
            }
            break
          case 99: {
            const moduleQuery = createPurchasedParamsFromCheckList((data.value as any).checkList || [])
            path = `/question-bank/purchased?${new URLSearchParams({ ...baseQuery, ...moduleQuery }).toString()}`
            if (isXcx) {
              userStore.jumpToQuestionBank(path)
            } else {
              navigateTo(path)
            }
            break
          }
        }
      }
    } catch (error) {
      console.log(error)
    }
  }

  // [AI GEN] 创建购买链接的参数
  const createPurchasedParamsFromCheckList = (checkList: any[]) => {
    const params: Record<string, any> = {}
    checkList.forEach((item) => {
      if (item.field) {
        params[item.field] = item.id
      }
    })

    return params
  }

  return {
    createPurchasedLinkByLearnCenterIndex,
    createPurchasedLink
  }
}
