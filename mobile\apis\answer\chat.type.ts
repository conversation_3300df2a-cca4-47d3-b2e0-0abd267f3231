import type{ ListParams, QuestionItem } from './types'
export interface FirstSendChat {
    problemDescription:string
    productId:number|string
    // cateId:string
    // cateName:string
    [key:string]:any
}

export interface SendChat {
    requestId:string
    answerContent:string
    type:number
    userId:string
    productId:number|string
}

export interface ChatLogListParams extends ListParams{
    requestId:string
}

/**
 * item消息记录
 */
export interface ChatHistoryResItem {
    id:string
    userId:string
    userName:string
    answerContent:string
    createdAt:string
    /**
     * 处理状态1：未回复，2：已回复，3：追问未回复，4：追问已回复，5：已流转，6：已完成
     */
    status:number
    /**
     * 对话类型1：提问,2：回答，3：追问
     */
    type:number
    /**
     * 提问方式，1：人工方式，2：拍搜方式，3：拍搜转人工方式
     */
    requestWay:number
    /**
     * 回复方式1：人工回复，2：试题推送回复，3：ai回复"
     */
    answerWay:number
    /**
     * 内容标识 1：人工，2：系统
     */
    contentFlag:number,
    /**
     *  (题库信息)
     */
    questionIds:string[],

    /**
     * 题库卡片信息
     */
    questionCard?:QuestionItem[]
}

export interface ChatHistoryRes {
    total:number
    userName:string
    userId:string
    avatar:string
    currentTeacherName:string
    historyNodeList:ChatHistoryResItem[],

}
