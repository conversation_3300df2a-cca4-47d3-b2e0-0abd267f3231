export const useQuestionBankMultipleTag = () => {
  const route = useRoute()

  /**
   * 路由传入标签相关参数
   */
  const purchasedTag = computed(() => {
    const { moduleManageId, moduleId, studyLevel1, subjectType, region, directionType, academicSection, examFormat } = route.query
    return {
      moduleManageId: moduleManageId as string || moduleId as string,
      studyLevel1: studyLevel1 ? Number(studyLevel1) : 0,
      subjectType: subjectType ? Number(subjectType) : 0,
      region: region ? Number(region) : 0,
      directionType: directionType ? Number(directionType) : 0,
      academicSection: academicSection ? Number(academicSection) : 0,
      examFormat: examFormat ? Number(examFormat) : 0
    }
  })

  /**
   * 无标签或标签不全视为需要弹框手动确认标签
   */
  const isMultipleTag = computed(() => {
    const { subjectType, region, directionType, academicSection, examFormat } = purchasedTag.value
    return !subjectType && !region && !directionType && !academicSection && !examFormat
  })

  /**
   * 路由上是否存在标签
   */
  const isHasTagInRoute = computed(() => {
    // 判断路由上是否存在标签项
    const tags = ['subjectType', 'region', 'directionType', 'academicSection', 'examFormat']
    const routeKeys = Object.keys(route.query)

    if (routeKeys.some(key => tags.includes(key) && route.query[key] !== '0' && route.query[key])) {
      return true
    }
    return false
  })

  /**
   * 路由上是否存在分类
   */
  const isHasCategoryInRoute = computed(() => {
    // 判断路由上是否存在分类项
    const categories = ['studyLevel1']
    const routeKeys = Object.keys(route.query)

    if (routeKeys.some(key => categories.includes(key) && route.query[key] && route.query[key] !== '0')) {
      return true
    }
    return false
  })

  /**
   * 试卷ID
   */
  const testpaperId = computed(() => route.query.testpaperId as string)

  /**
   * 固定刷题版本 1021191999801040896
   */
  const brushVersionId = computed(() => route.query.brushVersionId as string)

  /**
   * 教材id
   */
  const textbookId = computed(() => route.query.textbookId as string)

  /**
   * 是否在已购页面
   */
  const isPurchasedPage = computed(() => route.query.from === '1')

  /**
   * 资源是否上架
   */
  const isPublish = computed(() => route.query.isPublish === '1')

  return {
    isPublish,
    isPurchasedPage,
    isHasTagInRoute,
    purchasedTag,
    isMultipleTag,
    testpaperId,
    brushVersionId,
    textbookId,
    isHasCategoryInRoute,
  }
}
