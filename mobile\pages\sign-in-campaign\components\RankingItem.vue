<template>
  <!-- [AI-GEN] 排行榜项目 -->
  <div
    class="ranking-item"
  >
    <div class="ranking-number font-gilroy-bold">
      <span
        v-if="item.rank <= 3"
        class="ranking-medal"
        :class="`ranking-medal-${item.rank}`"
      >
        {{ item.rank }}
      </span>
      <span v-else class="ranking-normal">{{ item.rank }}</span>
    </div>

    <div class="user-info">
      <div class="user-avatar">
        <img :src="item.avatar" :alt="item.nickname">
      </div>
      <div class="user-details">
        <div class="user-nickname">
          {{ item.nickname }}
        </div>
        <!-- <div class="user-desc">
                    {{ item.description }}
                  </div> -->
      </div>
    </div>

    <div class="ranking-score">
      <span>累计签到</span>
      <span class="score-value">{{ ' ' }}{{ item.score }}{{ ' ' }}
      </span>
      <span class="score-unit">{{ tab.unit }}
      </span>
    </div>
  </div>
</template>

<script setup lang="ts">
//
// Tab 配置
interface TabConfig {
  title: string
  unit: string
  type: 'cumulative' | 'consecutive'
}

// 排行榜数据项接口
interface RankingItem {
  id: number
  rank: number
  nickname: string
  avatar: string
  description: string
  score: number
}

/* const props = */
defineProps({
  item: {
    type: Object as PropType<RankingItem>,
    required: true
  },
  tab: {
    type: Object as PropType<TabConfig>,
    required: true
  }
})// 页面初始化
onMounted(() => {
  //
})
</script>

<style lang="scss" scoped>

// 排行榜项目样式
.ranking-item {
  display: flex;
  align-items: center;
  padding: 16px 24px;
  margin: 8px 0;
  background: linear-gradient( 90deg, #FEF6C7 0%, #FCFCF4 100%);
  border-radius: 32px;
  // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  border: 2px solid #fff;

  &:hover,&.is-active {
    // transform: translateY(-2px);
    // box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    border: 2px solid #977215;
  }
  &.is-bg-white {
    background: white;
    border: 2px solid #977215;
    width: 702px;
    margin: 0 auto;
  }
}

// 排名数字样式
.ranking-number {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 18px;
  flex-shrink: 0;
}

.ranking-medal {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-weight: bold;
  font-size: 14px;

  &.ranking-medal-1 {
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    box-shadow: 0 2px 8px rgba(255, 215, 0, 0.4);
  }

  &.ranking-medal-2 {
    background: linear-gradient(135deg, #c0c0c0, #e8e8e8);
    box-shadow: 0 2px 8px rgba(192, 192, 192, 0.4);
  }

  &.ranking-medal-3 {
    background: linear-gradient(135deg, #cd7f32, #daa520);
    box-shadow: 0 2px 8px rgba(205, 127, 50, 0.4);
  }
}

.ranking-normal {
  font-size: 18px;
  font-weight: bold;
  color: #666;
}

// 用户信息样式
.user-info {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
}

.user-avatar {
  width: 52px;
  height: 52px;
  margin-right: 18px;
  flex-shrink: 0;

  img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
    border: 1px solid #fff;
  }
}

.user-details {
  flex: 1;
  min-width: 0;
}

.user-nickname {
line-height: 34px;
font-size: 28px;
font-weight: 400;
color: #844414;
  // margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.user-desc {
  font-size: 14px;
  color: #999;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 分数样式
.ranking-score {
  text-align: right;
  flex-shrink: 0;
  font-size: 24px;
  color: #BA8E5F;
  line-height: 30px;
}

.score-value {
  // font-size: 24px;
  // font-weight: bold;
  // color: #BA8E5F;
  // line-height: 1;
}

.score-unit {
  // font-size: 24px;
  // color: #BA8E5F;
  // margin-top: 2px;
}

// 响应式设计
// @media (max-width: 375px) {
//   .ranking-item {
//     padding: 12px;
//     margin: 8px 0;
//   }

//   .user-avatar {
//     width: 40px;
//     height: 40px;
//   }

//   .user-nickname {
//     font-size: 15px;
//   }

//   .score-value {
//     font-size: 18px;
//   }
// }
</style>
