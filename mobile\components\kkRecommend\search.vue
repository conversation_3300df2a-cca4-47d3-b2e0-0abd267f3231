<template>
  <div v-if="recommendList?.length" class="recommend search__suggest">
    <div class="border-gradient pl-[16px]">
      <div class="text-[#111] flex h-[88px] items-center">
        <!-- <TipTitle title="好课推荐" text-color="#111" /> -->
        <img
          :src="Icon3"
          alt=""
          class="w-[40px] h-[40px] mr-[4px]"
        >
        <span class="search__suggest-title">好课推荐</span>
      </div>
    </div>

    <div class="search__suggest-list" :style="{ 'background-color': bgColor }">
      <good-cell v-for="(item, index) in recommendList" :key="index" :goods="item" :is-search="isSearch" :is-course="true" />
    </div>
  </div>
</template>

<script setup lang="ts">
import Icon3 from '@/assets/images/search/icon-title__3.png'
import { getCourseRecommend } from '@/apis/search'
const route = useRoute()
const { usableId } = useLearnTarget()

const props = withDefaults(defineProps<{
  isSearch: boolean
  bgColor?: string,
  requestFrom?: undefined | string // 请求来源 学习中心该接口不校验学习目标 学习中心传1 其他默认不传
}>(), {
  isSearch: false,
  bgColor: '#fff',
  requestFrom: undefined
})

const localId = useCookie<string | null>('LT')
// 好课推荐
const recommendList = ref([])
const courseRecommend = async () => {
  const { data } = await getCourseRecommend({
    pageMasterId: route.query?.pageId || route.query?.lt || localId.value || usableId.value,
    requestFrom: props.requestFrom
  }) as any
  recommendList.value = data.value?.list
}

onMounted(() => {
  nextTick(() => {
    // 保持客户端与服务端渲染一致，数据在Mounted后加载
    courseRecommend()
  })
})
</script>
<style lang="scss">
.search__suggest-title{
  font-size: 32px;
  font-weight: 500;
}
.search__suggest-list {
  a .goods-cell {
    padding: 0 20px;
    .img {
      flex-shrink: 0;
    }
    .goods-cell-info-title{
      width: 366px;
    }
    .goods-cell-info-package{
      width: 366px;
    }
  }
  a:first-child .goods-cell {
    padding-top: 0;
  }
}
</style>
