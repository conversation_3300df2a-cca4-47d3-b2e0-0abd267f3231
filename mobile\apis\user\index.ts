import type {
  LoginParams,
  WeAppLoginParams,
  LoginResultModel,
  WeAppOpenIdModel,
  ThirdLoginResultModel,
  userInfoResultModel,
  userWxPhotoModel,
  RemoveMobileModel,
  UpdatePwdModel,
  ForgetPasswordModel,
  LogoffModel,
  BindThirdModel,
  MessageDTO,
  MessageConfigResVO,
  MessageTypeResVO,
  MessageInfoListDTO,
  MessageInfoListResVO,
  MessageDetailDTO,
  MessageDetailResVO,
  WechatQRCodeResVO,
  WechatAttentionDTO,
  WechatAttentionResVO,
  WechatLoginDTO,
  checkAuthDTO,
  UserIndentifyDTO,
  MessageCountResVO
} from './types'
export enum UserApi {
  login = '/kukecoreuser/wap/user/passwordLogin',
  smsLogin = '/kukecoreuser/wap/user/smsLogin',
  logout = '/kukecoreuser/wap/user/logoutProt',
  getUserInfo = '/kukecoreuser/wap/user/getUserInfoProt',
  getUserWxPhoto = '/kukecoreuser/wap/user/getUserWxPhotoProt',
  thirdLogin = '/kukecoreuser/wap/oauth/thirdLogin',
  weAppOpenId = '/kukecoreuser/wap/wmp/oauth/wxOpenid',
  weAppLogin = '/kukecoreuser/wap/wmp/oauth/login',
  bindMobile = '/kukecoreuser/wap/oauth/bindMobile',
  bindThirdAccount = '/kukecoreuser/wap/oauth/bindThirdProt',
  getUserWxBindStatus = '/kukecoreuser/wap/user/getUserWxBindStatusProt',
  removeBindMobile = '/kukecoreuser/wap/oauth/removeBindProt',
  updatePassword = '/kukecoreuser/wap/user/updatePasswordProt',
  forgetPassword = '/kukecoreuser/wap/user/forgetPassword',
  updateUserInfo = '/kukecoreuser/wap/user/updateUserInfoProt',
  logoff = '/kukecoreuser/wap/user/removeAccountProt',
  collect = '/kukecoreuser/wap/collect/listProt',
  delCollect = '/kukecoreuser/wap/collect/deleteProt',
  print = '/kukecoreuser/wap/historyRecords/listProt',
  wechatQrCode = '/kukemessage/kmMessageUserConfig/getWechatOfficialQrcodeProt',
  getMsgConfig = '/kukemessage/kmMessageUserConfig/getUserConfigProt',
  setMsgConfig = '/kukemessage/kmMessageUserConfig/userMessageConfigProt',
  messageType = '/kukemessage/wap/kmMessage/listMessageTypeProt',
  messageList = '/kukemessage/wap/kmMessage/listCenterMessagesProt',
  messageDetail = '/kukemessage/wap/kmMessage/getMessageDetailProt',
  updateMsgStatus = '/kukemessage/wap/kmMessage/updateMessageStatusProt',
  updateOneMsgStatus = '/kukemessage/wap/kmMessage/updateOneMessageStatusProt',
  isAttentionWechat = '/kukecoreuser/wap/user/woaAttentionStatus',
  wxAuthLogin = '/kukecoreuser/wap/woa/oauth/login',
  bindWxAuthLogin = '/kukecoreuser/wap/woa/oauth/bindThird',
  messageCount = '/kukemessage/wap/kmMessage/getMessageCountProt',
  checkAuthStatus = '/kukecoreuser/wap/oauth/checkAuthStatus',
  loginCheckAuthStatus = '/kukecoreuser/pc/oauth/checkBindStatus',
  createdUserIndent = 'kukecoreuser/pc/oauth/createUserIdentityProt',
  getUserWxBindStatusByClientProt='/kukecoreuser/wap/user/getUserWxBindStatusByClientProt'
}
// 登录
export async function login (body: LoginParams) {
  return useHttp<LoginResultModel>(UserApi.login, {
    method: 'post',
    body,
    encrypt: 'AES'
  })
}

// 微信小程序登录
export async function weAppLogin (body: WeAppLoginParams) {
  return useHttp<LoginResultModel>(UserApi.weAppLogin, {
    method: 'post',
    body,
  })
}

// 获取信息小程序的opendId
export async function weAppOpenId (body: WeAppLoginParams) {
  return useHttp<WeAppOpenIdModel>(UserApi.weAppOpenId, {
    method: 'post',
    body,
  })
}

// 验证码登录
export async function smsLogin (body: LoginParams) {
  return useHttp<LoginResultModel>(UserApi.smsLogin, {
    method: 'post',
    body,
    encrypt: 'AES'
  })
}
// 退出登录
export async function logout (body?: any) {
  return useHttp<void>(UserApi.logout, {
    method: 'post',
    body,
  })
}

// 获取用户信息
export async function getUserInfo (body?: any) {
  return useHttp<userInfoResultModel>(UserApi.getUserInfo, {
    method: 'post',
    body,
  })
}
// 获取用户头像
export async function getUserWxPhoto (client:any) {
  // return useHttp<userWxPhotoModel>(UserApi.getUserWxPhoto, {
  //   method: 'post'
  // })
  // const url = '/kukecoreuser/' + `${client}/user/getUserWxPhotoProt`
  return useHttp<userWxPhotoModel>(`/kukecoreuser/${client}/user/getUserWxPhotoProt`, {
    method: 'post'
  })
}
// 获取用户绑定信息
export async function thirdLogin (body: BindThirdModel) {
  return useHttp<{ data: ThirdLoginResultModel }>(UserApi.thirdLogin, {
    method: 'post',
    body,
    isShowLoading: true,
    encrypt: 'AES'
  })
}
// 绑定用户第三方信息
export async function bindMobile (body: any) {
  return useHttp<LoginResultModel>(UserApi.bindMobile, {
    method: 'post',
    body,
    encrypt: 'AES'
  })
}
// 用户信息绑定第三方信息
export async function loginBindMobile (body: BindThirdModel) {
  return useHttp<ThirdLoginResultModel>(UserApi.bindThirdAccount, {
    method: 'post',
    body,
  })
}
// 微信环境内更新用户绑定信息（用于app绑定后，wap端更新，防止微信公众号未通知）
export async function getUserWxBindStatus (body: any) {
  return useHttp<any>(UserApi.getUserWxBindStatus, {
    method: 'post',
    body
  })
}
// 解绑第三方信息
export async function removeBindMobile (body: any) {
  return useHttp<RemoveMobileModel>(UserApi.removeBindMobile, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}
// 忘记密码
export async function forgetPassword (body: any) {
  return useHttp<ForgetPasswordModel>(UserApi.forgetPassword, {
    method: 'post',
    body,
  })
}
// 修改密码
export async function updatePassword (body: any) {
  return useHttp<UpdatePwdModel>(UserApi.updatePassword, {
    method: 'post',
    body,
    encrypt: 'AES'
  })
}

// 修改/编辑用户信息[头像，昵称]
export async function updateUserInfo (body: any) {
  return useHttp<void>(UserApi.updateUserInfo, {
    method: 'post',
    body,
  })
}

// 注销账户
export async function logoff (body: any) {
  return useHttp<LogoffModel>(UserApi.logoff, {
    method: 'post',
    body,
  })
}

// 我的收藏
export async function myCollect (body: any) {
  return useHttp<any>(UserApi.collect, {
    method: 'post',
    body,
    transform: res => res.data
  })
}

// 删除收藏
export async function delCollect (body: any) {
  return useHttp<any>(UserApi.delCollect, {
    method: 'post',
    body
  })
}

export type IFootprintItem = {
  id: string
  content: string[]
  goodsTitle: string
  goodsImg: string
  goodsPrice: string
  isBook: 0 | 1
  isSell: 0 | 1
  isShelf: 0 | 1
  isTrialClass: 0 | 1
}

/** 我的足迹 */
export async function myPrint () {
  return useHttp<{ list: IFootprintItem[] }>(UserApi.print, {
    method: 'post',
    transform: res => res.data,
    default: () => ({ list: [] }),
    isShowLoading: true
  })
}

// 获取公众号二维码
export async function getWechatQrCode () {
  return useHttp<{ data: WechatQRCodeResVO }>(UserApi.wechatQrCode, {
    method: 'post',
    body: {},
    isShowLoading: true
  })
}

// 获取消息设置
export async function getMsgConfig () {
  return useHttp<{ data: MessageConfigResVO }>(UserApi.getMsgConfig, {
    method: 'post'
  })
}

// 消息设置
export async function setMsgConfig (body: MessageDTO) {
  return useHttp<any>(UserApi.setMsgConfig, {
    method: 'post',
    body
  })
}

// 获取消息类型列表
export async function getMsgTypeList (body: any) {
  return useHttp<{ data: MessageTypeResVO }>(UserApi.messageType, {
    method: 'post',
    isShowLoading: true,
    body
  })
}

// 获取对应类型消息列表
export async function getMsgList (body: MessageInfoListDTO) {
  return useHttp<{ data: MessageInfoListResVO }>(UserApi.messageList, {
    method: 'post',
    body
  })
}

// 获取消息详情
export async function getMsgDetail (body: MessageDetailDTO) {
  return useHttp<{ data: MessageDetailResVO }>(UserApi.messageDetail, {
    method: 'post',
    body
  })
}

// 更新消息已读
export async function updateMsgStatus (body: MessageInfoListDTO) {
  return useHttp<any>(UserApi.updateMsgStatus, {
    method: 'post',
    body
  })
}

// 更新单个消息已读状态
export async function updateOneMsgStatus (body: MessageDetailDTO) {
  return useHttp<any>(UserApi.updateOneMsgStatus, {
    method: 'post',
    body
  })
}

// 获取微信公众号关注状态
export async function getAttentionWechatStatus (body: WechatAttentionDTO) {
  return useHttp<{ data: WechatAttentionResVO }>(UserApi.isAttentionWechat, {
    method: 'post',
    body
  })
}

// pc端/wap端非微信扫码登录流程
export async function wxAuthLogin (body: WechatLoginDTO, headers = {}) {
  return useHttp<any>(UserApi.wxAuthLogin, {
    method: 'post',
    body,
    headers,
  })
}

// pc端/wap端已登录扫码流程
export async function bindWxAuthLogin (body: UserIndentifyDTO, headers = {}) {
  return useHttp<any>(UserApi.bindWxAuthLogin, {
    method: 'post',
    body,
    headers,
  })
}

// 查询微信授权状态
export async function checkAuthStatus (body: any) {
  return useHttp<{ data: checkAuthDTO }>(UserApi.checkAuthStatus, {
    method: 'post',
    body,
  })
}

// 获取消息数量
export async function getMessageCount (body = {}) {
  return useHttp<MessageCountResVO>(UserApi.messageCount, {
    body,
    transform: res => res.data,
    isShowLoading: true
  })
}

// 已登录查看授权状态
export async function loginCheckAuthStatus (body: any) {
  return useHttp<any>(UserApi.loginCheckAuthStatus, {
    method: 'post',
    body,
  })
}

// 获取临时授权身份令牌
export async function createdUserIndentify (body: any) {
  return useHttp<any>(UserApi.createdUserIndent, {
    method: 'post',
    body,
  })
}

// 获取用户绑定微信状态根据当前所在客户端
export async function getUserWxBindStatusByClientProt () {
  return useHttp<any>(UserApi.getUserWxBindStatusByClientProt, {
    method: 'post',
    transform: res => res.data
  })
}
