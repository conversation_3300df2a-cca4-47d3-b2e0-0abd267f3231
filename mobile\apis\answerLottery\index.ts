/**
 * 答题抽奖
 * -
 * 活动详情
 */
export async function questionLotteryDetail (body: {questionLotteryId:string}) {
  return useHttp<any>('/kukecoupon/wap/marketingGoods/questionLotteryDetail', {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 答题抽奖
 * -
 * 答题验证
 */
export async function questionLotteryVerify (body: {questionLotteryId:string}) {
  return useHttp<any>('/kukecoupon/wap/marketingGoods/questionLotteryVerify', {
    method: 'post',
    body,
    transform: res => res.data,
  })
}
