import type { CourseDetailModel } from '../course/types'
import type {
  addOrderModel, addOrderParams, getMinimalisticParams,
  OrderCancelParams,
  OrderParams,
  OrderRefundParams,
  MaxMoneyParams,
  OrderAddressParams,
  OrderPay,
  BuyGoodsInfoParams, BuyGoodsInfoModel,
  FetchQueryShippingPriceParams,
  ModifyAddressProt,
  // IDictItem,
  FetchPreOrderInfoParams,
  FetchPreOrderInfoDTO,
  FetchAddMultiOrderParams,
  FetchAddMultiOrderDTO,
} from './types'

export enum OrderApi {
  addOrder = '/kukeonlineorder/wap/order/addOrderProt',
  getMinimalistic = '/kukecoregoods/wap/goods/getSimpleDetail',
  listOrder = '/kukeonlineorder/wap/order/myOrderListProt',
  detailOrder = '/kukeonlineorder/wap/order/getOrderDetail',
  canelOrder = '/kukeonlineorder/wap/order/cancelOrderProt',
  receiptOrder = '/kukeonlineorder/wap/order/receiptOrderProt',
  deleteOrder = '/kukeonlineorder/wap/order/deleteOrderProt',
  normalRefundOrder = '/kukeonlineorder/wap/order/normalRefundOrderProt',
  cancelApplyRefund = '/kukeonlineorder/wap/order/cancelApplyRefundOrderProt',
  cancelReason = '/kukeonlineorder/getEnums/cancelReason',
  refundReason = '/kukeonlineorder/getEnums/refundReason',
  maxRefundMoney = '/kukeonlineorder/wap/order/countMaxRefundMoneyProt',
  priceDifference = '/kukeonlineorder/wap/order/isRefundDifference',
  allStatus = '/kukeonlineorder/wap/order/orderStsLog',
  packageList = '/kukeonlineorder/wap/order/getOrderExpress',
  packageListV2 = '/kukeonlineorder/wap/order/getMyOrderExpress',
  packageDetail = '/kukeonlineorder/wap/order/getOrderExpressDetail',
  buyGoodsInfo = '/kukeonlineorder/wap/order/getSimpleOrder',
  selectAddress = '/kukeonlineorder/wap/order/updateOrderAddressProt',
  processPayment = '/kukeonlineorder/wap/order/payOrderProt',
  getOrderCount = '/kukeonlineorder/wap/order/myOrderCountProt',
  getPayStatusProt = '/kukeonlineorder/wap/order/getPayStatusProt',
  getGroupGoods = '/kukecoregoods/wap/goods/getGroupGoods',
  queryShippingPrice = '/kukeonlineorder/wap/order/queryShippingPrice',
  getUserBuyUnitGoodsByOrderSn='/kukecoregoods/wap/userBuyUnitGoods/getByOrderSn',
  getOrderDeliveryPageProt = '/kukeonlineorder/wap/order/getOrderDeliveryPageV2Prot',
  modifyDeliveryAddressProt = '/kukeonlineorder/wap/order/modifyDeliveryAddressProt',
  fetchPreOrderInfo = '/kukecoregoods/wap/userCart/preOrderProt',
  fetchAddMultiOrder = '/kukeonlineorder/wap/order/addOrdersProt',
  buyGoodsInfoByCart = '/kukeonlineorder/wap/order/getMergedSimpleOrder',
  getUserBuyUnitGoodsByCart='/kukecoregoods/wap/userBuyUnitGoods/getByOrderSns',
  getQuestionBankOrderDetail = '/kukecorequestion/wap/fee/preOrderInfoProt',
  getQuestionVerifyRight = '/kukecorequestion/wap/kqQuestionRight/verifyQuestionRight',
}
// 生成订单
export async function addOrder (body: addOrderParams) {
  return useHttp<addOrderModel>(OrderApi.addOrder, {
    method: 'post',
    body,
    watch: false
  })
}

// 获取支付前的商品信息
export async function getMinimalistic (body: getMinimalisticParams) {
  return useHttp<CourseDetailModel>(OrderApi.getMinimalistic, {
    method: 'post',
    body
  })
}
// 订单列表
export async function getOrderList (body: any) {
  return useHttp<any>(OrderApi.listOrder, {
    method: 'post',
    body,
    transform: res => res.data,
    isShowLoading: true
  })
}
// 订单详情
export async function getOrderDetail (body: any) {
  return useHttp<any>(OrderApi.detailOrder, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}
// 取消订单
export async function canelOrder (body: OrderCancelParams) {
  return useHttp<any>(OrderApi.canelOrder, {
    method: 'post',
    body,
  })
}
// 确认收货
export async function receiptOrder (body: OrderParams) {
  return useHttp<any>(OrderApi.receiptOrder, {
    method: 'post',
    body,
  })
}
// 删除订单
export async function deleteOrder (body: OrderParams) {
  return useHttp<any>(OrderApi.deleteOrder, {
    method: 'post',
    body,
  })
}
// 售后申请
export async function normalRefundOrder (body: OrderRefundParams) {
  return useHttp<any>(OrderApi.normalRefundOrder, {
    method: 'post',
    body,
  })
}
// 撤销申请
export async function cancelApplyRefund (body: OrderParams) {
  return useHttp<any>(OrderApi.cancelApplyRefund, {
    method: 'post',
    body,
  })
}
// 订单-取消原因下拉
export async function cancelReasonList () {
  return useHttp<any>(OrderApi.cancelReason, {
    method: 'post',
    transform: (res) => {
      return res?.data?.map((item: any) => {
        item.label = item.cancelReasonDesc
        item.value = item.cancelReason
        return item
      })
    },
    default () {
      return []
    },
  })
}
// 订单售后-退款原因下拉
export async function refundReasonList () {
  return useHttp<any>(OrderApi.refundReason, {
    method: 'post',
    transform: (res) => {
      return res?.data?.map((item: any) => {
        item.label = item.refundReasonDesc
        item.value = item.refundReason
        return item
      })
    },
    default () {
      return []
    },
  })
}
// 订单售后-最多退款金额
export async function maxRefundMoney (body: MaxMoneyParams) {
  return useHttp<any>(OrderApi.maxRefundMoney, {
    method: 'post',
    body,
  })
}
// 订单包裹列表
export async function getPackageList (body: OrderParams) {
  return useHttp<any>(OrderApi.packageList, {
    method: 'post',
    body,
    transform: res => res.data,

  })
}
// 订单包裹列表
export async function getPackageListV2 (body: OrderParams) {
  return useHttp<any>(OrderApi.packageListV2, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}
// 订单包裹详情信息
export async function getPackageDetail (body: OrderParams) {
  return useHttp<any>(OrderApi.packageDetail, {
    method: 'post',
    body,
    transform: res => res.data,

  })
}
// 订单详情所有状态
export async function getAllStatus (body: OrderParams) {
  return useHttp<any>(OrderApi.allStatus, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}
// 订单是否可以退差价
export async function getPriceDifference (body: OrderParams) {
  return useHttp<any>(OrderApi.priceDifference, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}
// 更换地址信息
export async function setSelectAddress (body: OrderAddressParams) {
  return useHttp<any>(OrderApi.selectAddress, {
    method: 'post',
    body,
  })
}

export async function processPayment (body: OrderPay) {
  return useHttp<addOrderModel>(OrderApi.processPayment, {
    method: 'post',
    body,
  })
}

// 购买后回调详情
export async function getBuyGoodsInfo (body: BuyGoodsInfoParams) {
  return useHttp<BuyGoodsInfoModel>(OrderApi.buyGoodsInfo, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}
// 购物车 购买后回调详情
export async function getBuyGoodsInfoByCart (body: BuyGoodsInfoParams) {
  return useHttp<BuyGoodsInfoModel>(OrderApi.buyGoodsInfoByCart, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}
export async function getOrderCount (body = {}) {
  return useHttp<BuyGoodsInfoModel>(OrderApi.getOrderCount, {
    body,
    transform: res => res.data,
    isShowLoading: true
  })
}
// 支付—获取订单支付状态
export async function getPayStatusProt (body = {}) {
  return useHttp<BuyGoodsInfoModel>(OrderApi.getPayStatusProt, {
    body,
    transform: res => res.data,
  })
}
// 分享获取拼团商品详情
export async function getGroupGoods (body = {}) {
  return useHttp<BuyGoodsInfoModel>(OrderApi.getGroupGoods, {
    body,
    transform: res => res.data,
  })
}

/**
 * 查询—查询运费
 * @param body
 * @returns
 */
export async function queryShippingPrice (body: FetchQueryShippingPriceParams) {
  return useHttp<any>(OrderApi.queryShippingPrice, {
    body,
    transform: res => res.data,
  })
}

export async function getUserBuyUnitGoodsByOrderSn (body: {orderSn?:string}) {
  return useHttp<any>(OrderApi.getUserBuyUnitGoodsByOrderSn, {
    method: 'post',
    body,
    transform: res => res.data,

  })
}
// 多订单权益
export async function getUserBuyUnitGoodsByCart (body: {orderSns?:string[]}) {
  return useHttp<any>(OrderApi.getUserBuyUnitGoodsByCart, {
    method: 'post',
    body,
    transform: res => res.data,

  })
}
// 查询—查看邮寄列表
export async function getOrderDeliveryPageProt (body: any) {
  return useHttp<any>(OrderApi.getOrderDeliveryPageProt, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

// 更新-更新资料邮寄地址
export async function modifyDeliveryAddressProt (body: ModifyAddressProt) {
  return useHttp<any>(OrderApi.modifyDeliveryAddressProt, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 请求预下单信息
 * @param body
 * @returns
 */
export async function fetchPreOrderInfo (body : FetchPreOrderInfoParams) {
  return useHttp<FetchPreOrderInfoDTO>(OrderApi.fetchPreOrderInfo, {
    method: 'post',
    body,
    transform: (res: any) => res.data,
    watch: false
  })
}

/**
 * 请求 新增多个订单
 * @param body
 * @returns
 */
export async function fetchAddMultiOrder (body : FetchAddMultiOrderParams) {
  return useHttp<FetchAddMultiOrderDTO[]>(OrderApi.fetchAddMultiOrder, {
    method: 'post',
    body,
    transform: (res: any) => res.data
  })
}

/**
 * 获取题库订单信息
 */
export async function getQuestionBankOrderDetail (body: {id: string}) {
  return useHttp<any>(OrderApi.getQuestionBankOrderDetail, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 根据当前订单判断权益是否下发
 */
export async function getQuestionOrderBuyUnitGoods (body: {sourceId: string, goodsNum: number}) {
  return useHttp<any>(OrderApi.getQuestionVerifyRight, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}
