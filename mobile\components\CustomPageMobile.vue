<template>
  <NuxtErrorBoundary @error="someErrorLogger">
    <!-- class="CustomPageMobile" -->
    <template v-if="components?.length">
      <template v-for="item in components" :key="item.id">
        <template v-if="item.assemblyType === 26">
          <!-- {{ item.props }} -->
          <KKUserInfoWap v-bind="item.props">
            <template #vip>
              <slot name="vip" />
            </template>
          </KKUserInfoWap>
        </template>
        <template v-else-if="[6, 40].includes(item.assemblyType)">
          <!-- -->
        </template>
        <!-- <learn-target-label v-if="!!learnTargetInfo.enable" title="点击选择学习目标" :lt-items="ltItems" /> -->
        <template v-else-if="item.assemblyType === 24 && route.path !== '/me'">
          <!-- -->
        </template>

        <!-- 富文本 -->
        <template v-else-if="item.assemblyType === 38">
          <KKRichText :props="item.props" />
        </template>

        <component
          v-bind="{
            id: item.id,
            styleConfig: styleConfig,
            isLogin: !!userStore.isLogin,
            ...item.props
          }"
          :is="item.componentName"
          v-else-if="item.assemblyType !== 35"
        />
        <ClientOnly v-else>
          <KKQuestionBank :title="title" :style-config="styleConfig" :item="item" />
        </ClientOnly>
      </template>
    </template>
    <ClientOnly v-else>
      <LazyKKCEmptyConfig />
    </ClientOnly>
  </NuxtErrorBoundary>
</template>

<script setup lang="ts">
import KKQuestionBank from './KKQuestionBank.vue'
import { useUserStore } from '~/stores/user.store'
const someErrorLogger = (err) => {
  console.log('%c [ err ]-58', 'font-size:13px; background:pink; color:#bf2c9f;', err)
}
const userStore = useUserStore()
const route = useRoute()

const { isXcx, appId, lesseeId } = useAppConfig()
interface IStyle {
  bothSideSpace?: String,
  componentSpace?: String,
}
interface Props {
  //  promiseList?: Promise<any>[],
  styleConfig?: IStyle,
  // ltItems?: any[],
  components: any[],
  title?: string
}
const props = withDefaults(defineProps<Props>(), {
  // ltItems: () => ([]),
  styleConfig: () => ({})
})
onMounted(() => {
  console.log(props.components, 'props.components222')
  if (isXcx) { return }
  if (+appId === 1 && !lesseeId) {
    const components = props.components?.find(item => item.componentName === 'KKEssentialToolWap')
    const tempData1 = components?.props?.personCenterTools?.selectDataList?.find(item => item.type === 1)
    const kmCustomFunctionInfo = tempData1?.kmCustomFunctionInfo
    if (kmCustomFunctionInfo && kmCustomFunctionInfo.dicSkipId === 18 && kmCustomFunctionInfo.url && (kmCustomFunctionInfo.url.includes('.qiyukf.com') || kmCustomFunctionInfo.url.includes('.qiyukf'))) {
      kmCustomFunctionInfo.dicSkipId = 999
    }
  }
})

//

// const {
//   learnTargetInfo,
// } = useLearnTarget()

// useSeoMeta({
//   title: props.componentsData?.value?.pageName,
// })

// 拦截a标签跳转
usePreventDefaultLink()

</script>

<style lang="scss">
//
</style>
