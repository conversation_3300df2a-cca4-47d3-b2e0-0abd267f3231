import type { CustomGoodsInfo } from '~/pages/course/types'
import type { PointsCenterStatusType, PointsDetailsType, PointsGoodsType, PointsTaskType, Props, UserPointsType, UserRedeemRecordType, WriteOffSchoolType } from '~/pages/points/types/index'

export const pointsApi = {
  /**
   * @description 获取用户积分信息和积分规则
   * 用于获取用户的积分余额、积分规则等信息。
   */
  getUserPointsInfoProt: '/kukemarketing/wap/points/userPointsCount',

  /**
   * @description 获取积分兑换商品列表
   * 用于获取积分商城中可兑换的商品列表。
   */
  getPointsRedeemListProt: '/kukemarketing/wap/points/pointsExchangeGoods/page/list',
  /**
   * @description 积分兑换优惠券商品列表
   * 用于获取积分商城中可兑换的商品列表。
   */
  getPointsRedeemCouponListProt: '/kukemarketing/wap/points/pointsExchangeGoods/coupon/page/list',

  /**
   * @description 获取积分任务列表
   * 获取用户可完成的积分任务列表。
   */
  getPointsTaskListProt: '/kukemarketing/wap/points/pointsTaskList',

  /**
   * @description 获取指定商品列表
   * 用于获取积分商城中指定类别或条件下的商品列表。
   */
  getPointsGiveListProt: '/kukemarketing/wap/points/pointsAppointGoods/page/list',

  /**
   * @description 商品列表搜索
   * 提供积分商品列表的搜索功能，根据关键词搜索商品。
   */
  getPointsListByKeywordProt: '/kukemarketing/wap/points/pointsGoods/search/list',

  /**
   * @description 获取用户兑换记录列表
   * 用于获取用户的积分兑换记录。
   */
  getUserRedeemRecordListProt: '/kukemarketing/wap/points/pointsExchangeGoodsRecord/page/listProt',

  /**
   * @description 获取兑换详情
   * 获取指定积分兑换商品的详情信息。
   */
  getPointsRedeemDetailsProt: '/kukemarketing/wap/points/pointsExchangeGoodsRecord/getDetailProt',

  /**
   * @description 获取积分明细
   * 获取用户积分的详细记录，包括发放、消耗等。
   */
  getPointsDetailsListProt: '/kukemarketing/wap/points/pointsDetailsList',

  /**
   * @description 获取核销校区列表
   * 获取参与积分商品兑换的校区列表。
   */
  getPointsGoodsWriteOffSchoolListProt: '/kukemarketing/wap/points/schoolPageList',

  /**
   * @description 获取指定商品的兑换详情
   * 获取积分商城中指定商品的详细信息，包含商品描述、兑换要求等。
   */
  getPointsGoodsDetailsProt: '/kukemarketing/wap/points/pointsExchangeGoods/detail',

  /**
   * @description 确认兑换
   * 用户确认兑换商品时调用，通常用于确认商品信息和兑换数量。
   */
  handleConfirmPointsRedeemProt: '/kukemarketing/wap/points/pointsExchangeGoods/confirmExchangeProt',

  /**
   * @description 获取积分商城状态
   * 获取积分商城的状态信息，如是否开启、是否维护等。
   */
  getPointsCenterStatusProt: '/kukemarketing/wap/points/pointsShopStatus',

  /**
   * @description 获取积分商城商品搜索页面
   * 提供积分商品搜索页面的相关信息和搜索结果。
   */
  getPointsSearchListProt: '/kukemarketing/wap/points/pointsGoods/search/list',
  /**
   * @description 积分抵现
   * http://yapi.kukewang.com:9005/project/273/interface/api/67898
   */
  pointsDiscountProt: '/kukemarketing/wap/points/pointsDiscountProt',

  /**
   * @description 积分抵现(题库)
   * http://yapi.kukewang.com:9005/project/273/interface/api/68558
   */
  pointsDiscountQuestionBankProt: '/kukemarketing/wap/points/pointsDiscountQuestionProt',

  /**
   * @description 获取积分兑换订单确认页
   * 获取积分兑换商品的订单确认信息，如订单编号、商品信息等。
   */
  getPointsRedeemOrderDetailsProt: '/kukemarketing/wap/points/pointsExchangeGoods/getPoints'
}

export interface ApiPageResponse<P = any> {
  code: string;
  msg: string;
  time: string;
  data: {
    count: number;
    list?: P;
  };
}

/**
 * @description 用户积分和积分规则
 *
 * @param body
 * @returns
 */
export async function getUserPointsInfoProt () {
  return useHttp<ApiResponse<UserPointsType>>(pointsApi.getUserPointsInfoProt, {
    method: 'post',
    body: {}
  })
}

/**
 * @description 积分兑换商品列表
 *
 * @param body
 * @returns
 */
export async function getPointsRedeemListProt (body: Props) {
  return useHttp<ApiPageResponse<PointsGoodsType[]>>(pointsApi.getPointsRedeemListProt, {
    method: 'post',
    body
  })
}
/**
 * @description 积分兑换优惠券商品列表
 *
 * @param body
 * @returns
 */
export async function getPointsRedeemCouponListProt (body: Props) {
  return useHttp<ApiPageResponse<PointsGoodsType[]>>(pointsApi.getPointsRedeemCouponListProt, {
    method: 'post',
    body
  })
}

/**
 * @description 获得积分任务列表
 *
 * @param body
 * @returns
 */
export async function getPointsTaskListProt (body?: Props) {
  return useHttp<ApiPageResponse<PointsTaskType[]>>(pointsApi.getPointsTaskListProt, {
    method: 'post',
    body
  })
}

/**
 * @description 获取购买商品赠送积分列表
 *
 * @param body
 * @returns
 */
export async function getPointsGiveListProt (body: Props) {
  return useHttp<ApiPageResponse<PointsGoodsType[]>>(pointsApi.getPointsGiveListProt, {
    method: 'post',
    body
  })
}

/**
 * @description 商品列表搜索
 *
 * @param body
 * @returns
 */
export async function getPointsListByKeywordProt (body: Props) {
  return useHttp<ApiPageResponse<PointsGoodsType[]>>(pointsApi.getPointsListByKeywordProt, {
    method: 'post',
    body
  })
}

/**
 * @description 用户兑换记录
 *
 * @param body
 * @returns
 */
export async function getUserRedeemRecordListProt (body: Props) {
  return useHttp<ApiPageResponse<UserRedeemRecordType[]>>(pointsApi.getUserRedeemRecordListProt, {
    method: 'post',
    body
  })
}

/**
 * @description 兑换详情
 *
 * @param body
 * @returns
 */
export async function getPointsRedeemDetailsProt (body: Props) {
  return useHttp<ApiResponse<UserRedeemRecordType>> (pointsApi.getPointsRedeemDetailsProt, {
    method: 'post',
    body
  })
}

/**
 * @description 积分明细
 *
 * @param body
 * @returns
 */
export async function getPointsDetailsListProt (body: Props) {
  return useHttp<ApiPageResponse<PointsDetailsType[]>> (pointsApi.getPointsDetailsListProt, {
    method: 'post',
    body
  })
}

/**
 * @description 核销校区列表
 *
 * @param body
 * @returns
 */
export async function getPointsGoodsWriteOffSchoolListProt (body: Props) {
  return useHttp<ApiPageResponse<WriteOffSchoolType[]>> (pointsApi.getPointsGoodsWriteOffSchoolListProt, {
    method: 'post',
    body
  })
}

/**
 * @description 积分兑换商品/购买指定商品的详情信息
 *
 * @param body
 * @returns
 */
export async function getPointsGoodsDetailsProt (body: Props) {
  return useHttp<ApiResponse<CustomGoodsInfo>>(pointsApi.getPointsGoodsDetailsProt, {
    method: 'post',
    body,
  })
}

/**
 * @description 积分兑换确认
 *
 * @param body
 * @returns
 */
export async function handleConfirmPointsRedeemProt (body: Props) {
  return useHttp<ApiResponse>(pointsApi.handleConfirmPointsRedeemProt, {
    method: 'post',
    body,
    isShowLoading: true
  })
}

/**
 * @description 积分商城的状态
 *
 * @returns
 */
export async function getPointsCenterStatusProt () {
  return useHttp<ApiResponse<PointsCenterStatusType>>(pointsApi.getPointsCenterStatusProt, {
    method: 'post',
    body: {}
  })
}

/**
 * @description 积分商城的状态
 *
 * @returns
 */
export async function getPointsSearchListProt (body: Props) {
  return useHttp<ApiPageResponse<PointsCenterStatusType>>(pointsApi.getPointsSearchListProt, {
    method: 'post',
    body,
    isShowLoading: true
  })
}

/**
 * @description 积分兑换详情页的积分信息
 *
 * @returns
 */
export async function getPointsRedeemOrderDetailsProt (body: Props) {
  return useHttp<ApiResponse<CustomGoodsInfo>>(pointsApi.getPointsRedeemOrderDetailsProt, {
    method: 'post',
    body
  })
}
type pointsDiscountProtType = {
  status: number // 商城积分抵现状态 0:关闭 1:开启
  ruleStatus: number // 规则积分抵现状态 0:关闭 1:开启
  userPoints: number // 用户可用积分
  availablePoints: number // 可抵现最大积分
  orderMoney: number // 可抵现最大金额
  offsetThresholdType: 1|2 // 抵现门槛类型 1无门槛 2有门槛
  orderSatisfyMoney: number // 门槛类型为2时 订单金额限制
}

/**
 * @description 积分抵现
 *
 * @returns
 */
export async function pointsDiscountProt (body: Props) {
  return useHttp<ApiResponse<pointsDiscountProtType>>(pointsApi.pointsDiscountProt, {
    method: 'post',
    body
  })
}

/**
 * @description 积分抵现(题库)
 *
 *@returns
 */
export async function pointsDiscountQuestionBankProt (body: Props) {
  return useHttp<ApiResponse<pointsDiscountProtType>>(pointsApi.pointsDiscountQuestionBankProt, {
    method: 'post',
    body
  })
}
