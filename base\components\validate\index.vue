<template>
  <div class="kk-form-wrap">
    <KKCForm>
      <div class="flex  form-item">
        <label class="form-label">{{ attrs.label }}</label>
        <div class="input-wrap">
          <div v-if="isArea" :class="['form-input', customeIpt ? customeIpt : '', !isError ? 'is-invalid' : '']">
            <slot />
          </div>
          <input
            v-else
            v-bind="attrs"
            ref="ipt"
            :value="iptRef.value"
            :disabled="iptRef.disabled"
            :maxlength="maxlength"
            :class="['form-input', customeIpt ? customeIpt : '', !isError ? 'is-invalid' : '']"
            @blur="changeValue"
            @input="getchangeValue"
          >
          <div v-if="!isError" class="invalid-feedback">
            {{ iptRef.message }}
          </div>
        </div>
      </div>
    </KKCForm>
  </div>
</template>
<script lang='ts' setup>
import { type ruleProp, emitter, ruleReg } from '../form/hooks'
const attrs = useAttrs()
const prop = withDefaults(defineProps<{
  rules: ruleProp[]
  modelValue: string
  labelWidth?: string
  customeIpt?: string
  labelPosition?: 'left' | 'center' | 'right'
  isArea?:boolean
  disabled?:boolean
  maxlength?:number | string
}>(), { labelWidth: '78px', labelPosition: 'right', isArea: false, maxlength: '' })

const emit = defineEmits(['update:modelValue'])
const isError = ref<undefined | boolean>(true)
const iptRef = reactive({
  value: prop.modelValue || '',
  message: '',
  disabled: prop.disabled || false
})
watch(() => prop.modelValue,
  (val) => {
    if (prop.isArea) {
      iptRef.value = prop.modelValue
      changeValue()
    }
    iptRef.value = val
    iptRef.disabled = prop.disabled || false
  })
const getchangeValue = (e: Event) => {
  const val = (e.target as HTMLInputElement).value
  iptRef.value = val
  emit('update:modelValue', iptRef.value)
}
const changeValue = () => {
  isError.value = true
  if (!prop.rules) { return true }
  const res = prop.rules.every((item: ruleProp) => {
    let pass = false
    iptRef.message = item.message
    switch (item.type) {
      case 'required':
        pass = (iptRef.value !== '' && iptRef.value !== undefined && iptRef.value !== null)
        break
      case 'email':
        pass = (ruleReg[item.type].test(iptRef.value))
        break
      case 'phone':
        pass = (ruleReg[item.type].test(iptRef.value))
        break
      case 'validate':
        pass = (item.validateFun(iptRef.value))
        break
      default:
        pass = false
        break
    }
    return pass
  })
  isError.value = res
  return res
}
onMounted(() => {
  // TODO 内存泄漏可疑点: 及时去除事件绑定
  emitter.publish('markeingListenSubmit', changeValue)
})
defineExpose({ iptRef })
</script>
<style scoped lang="scss">
.kk-form-wrap {
  .form-label {
    display: inline-block;
    width: v-bind(labelWidth);
    text-align: v-bind(labelPosition);
    @apply text-[#666] mr-[5px] text-[12px] my-[6px];
    line-height: 32px;
  }

  .input-wrap {
    @apply inline-block relative;
    width: calc(100% - v-bind(labelWidth));
    input:disabled{
      color: #999
    }
  }

  .form-input {
    @apply pl-[10px] my-[6px] w-full;
    box-sizing: border-box;
    height: 32px;
    background: #F7F7F7;
    border-radius: 6px;
    border: 1px solid transparent;
    outline: none;

    &.is-invalid {
      border: 1px solid #DB373F;
    }
  }

  .invalid-feedback {
    margin-left: 10px;
    color: #EB2330;
    font-size: 12px;
    bottom: 0px;
    line-height: 1;
  }
}
</style>
