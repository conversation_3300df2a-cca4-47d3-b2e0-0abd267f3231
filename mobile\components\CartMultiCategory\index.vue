<!-- 多品选择 -->
<template>
  <div class="cart-mulity-wrap">
    <!-- 选择多规格 -->
    <KKPopup
      ref="popupRef"
      class="customer_popup"
      position="bottom"
      box-padding="0"
      :close-icon-size="32"
      @close="onClose"
    >
      <template #header>
        <div class="border-b border-b-[#EEEEEE] pb-[24px] pt-[24px] px-[24px]">
          <div class="text-red special-font price">
            <span v-if="isShowMarketing">
              <span class="symbol font-gilroy-bold">
                {{ ActivityTypeName[marketingInfo?.activityType - 1] }}￥
              </span>
              <span class="font-gilroy-bold">
                {{ marketingInfo?.activityPrice }}
              </span>
              <del class="text-[#999] font-gilroy-bold ml-[32px]  text-[28px]">
                ￥{{ courseInfo?.goodsPresentPrice }}</del>
            </span>
            <span v-else>
              <span class="symbol font-gilroy-bold">￥</span>
              <span class="font-gilroy-bold text-[48px]">
                <template v-if="courseStore.isSku">
                  {{ courseInfo?.goodsPresentPrice }}</template>
                <template v-else>
                  {{ courseStore?.spuInfo?.priceRange }}
                </template>
              </span>
            </span>
          </div>
          <!-- 其他费用 -->
          <div v-if="isShowOtherCost" class="mt-[12px]">
            <div class="text-[22px] text-[#666] px-[12px] py-[10px] rounded-[12px] bg-[#f7f7f7]">
              <span>不包含 </span>
              <span
                v-if="Number(courseInfo?.teachingMaterialPrice) && fieldInfo?.teachingMaterialPrice"
              >教材费
                ￥{{ courseInfo?.teachingMaterialPrice }} </span>
              <span
                v-if="Number(courseInfo?.roomPrice) && fieldInfo?.roomPrice"
              >住宿费 ￥{{ courseInfo?.roomPrice }}
              </span>
              <span
                v-if="Number(courseInfo?.otherPrice) && fieldInfo?.otherPrice"
              >
                其他费用 ￥{{ courseInfo?.otherPrice }}
              </span>
            </div>
          </div>
          <div class="mt-[12px] line-clamp-2 text-[#111] text-[28px] font-medium leading-[34px]">
            <template v-if="courseStore.isSku">
              {{ courseInfo?.goodsTitle }}
            </template>
            <template v-else>
              {{ courseStore.spuInfo?.goodsTitle }}
            </template>
          </div>
        </div>
      </template>

      <div class="px-[16px] pt-[36px] pb-[24px]">
        <CourseGoodsSpecs
          v-for="(specsItem, index) in key"
          :key="index"
          :name-index="index"
          :list="specsItem"
          :goods-master-id="courseInfo?.id"
          @click="(name) => onSpecsClick(name, index)"
        />
      </div>

      <!-- 分校  omo 面授 面授套餐 展示-->
      <div v-if="isShowSchool && courseStore.courseSkuId" class="px-[16px]">
        <div class="text-[#111] text-[28px] mb-[24px] font-[500]">
          请选择分校
        </div>
        <CourseGoodsSchool
          :list="courseInfo?.goodsAreas"
          :active-value="schoolId"
          @click="(item) => onSchoolClick(item)"
        />
      </div>

      <div class="flex justify-between items-center h-[88px] px-[24px] number-box w-[100%]">
        <span class="text-[28px] leading-[34px] text-[#111]">购买数量</span>
        <NumberBox
          :model-value="cartNumber"
          :size="'normal'"
          :disabled="isShowSchool || isNotBtnStatus || isBtnDisabled"
          :enable-toast="enableToast"
          :toast-text="toastText"
          @change="onChange"
        />
      </div>
      <SubmitBar
        model="buy"
        :show-collect="false"
        :show-single-cart="showSingleCart"
        :cart-disabled="isShowSchool || isNotBtnStatus || isBtnDisabled"
        :is-show-share="false"
        @click="onSubmit"
      />
    </KKPopup>
  </div>
</template>

<script setup lang="ts">
import { emitter } from '../../../base/utils/mitt'
import type { SchoolType } from '@/apis/course/types'
import { GoodsType, ResourceTypes } from '@/apis/course/types'
import { KKPopup } from '@/components/KKPopup/types'
import { useUserStore } from '~/stores/user.store'
import { useCartStore } from '~/stores/cart.store'
import { useCourseStore } from '~/stores/course.store'

const cartStore = useCartStore()
const userStore = useUserStore()
const courseStore = useCourseStore()
const { cartApi } = useApi()
const { query: { internalSystemId, audition } } = useRoute()

const { initSku, key, isGetAllSku } = useSKU()

const courseInfo = computed(() => courseStore.courseInfo)
const fieldInfo = computed(() => courseStore.fieldInfo)
const marketingInfo = computed(() => courseStore.marketingInfo)

const props = defineProps<{
    showSingleCart?:string,
    cartId?: string
    type?:string,
    arr?:any[],
    quantity?: number,
    isBtnDisabled?:boolean
  }>()
// 首次进入重置
watch(() => key.value, (newV) => {
  const { arr } = props
  if (props.type === 'cart' && newV.length && isGetAllSku.value) {
    arr?.forEach((el, index) => {
      const { values } = newV[index]
      const valIndex = values.findIndex((item:string) => item === el)
      newV[index].itemIndex = valIndex
    })
  }
  isGetAllSku.value = false
}, {
  immediate: true,
  deep: true
})
// 多规格 加入购物车按钮禁用
// 暂不可售，暂不可购买
// 营销活动 分销，拼团正在活动中
// 0元商品
const isNotBtnStatus = computed(() => {
  const { btnStatus, goodsPresentPrice } = courseStore.courseInfo
  const { isMarketing, activityType, activityStatus } = courseStore.marketingInfo
  const isZero = ['0', '0.0', '0.00'].includes(goodsPresentPrice)
  const isNotBuyBtn = [BtnStatus.NotAvailable, BtnStatus.Overdue].includes(btnStatus)
  const isNotMarketing = activityType !== ActivityType.Seckill && activityStatus === 2
  return isZero || isNotBuyBtn || (isMarketing ? isNotMarketing : false)
})

// 面授课相关课程类型
const FaceToFaceCourse = [ResourceTypes.OMOPackage, ResourceTypes.FaceToFaceCourse, ResourceTypes.FacePackage]
// 是否展示分校
const isShowSchool = computed(() => {
  return FaceToFaceCourse.includes(Number(courseStore.courseInfo.resourceTypes))
})
// 是否展示其他费用
const isShowOtherCost = computed(() => {
  return (FaceToFaceCourse.includes(Number(courseStore.courseInfo.resourceTypes)) &&
  isShowPrice.value) && (courseStore.isSku)
})
const isShowPrice = computed(() => {
  const { teachingMaterialPrice, roomPrice, otherPrice } = courseStore.courseInfo
  const {
    teachingMaterialPrice: fieldTeachingMaterialPrice,
    roomPrice: fieldRoomPrice,
    otherPrice: fieldOtherPrice
  } = courseStore.fieldInfo
  return (Number(teachingMaterialPrice) || Number(roomPrice) || Number(otherPrice)) &&
  (fieldTeachingMaterialPrice || fieldRoomPrice || fieldOtherPrice)
})

const selectedName = ref('')
const schoolId = ref('')
const schoolName = ref('')

// 选择分校
const onSchoolClick = (item: SchoolType) => {
  schoolId.value = item.id
  schoolName.value = item.departmentName
  const nameArr = selectedName.value.split('_')
  const length = nameArr.length
  if (nameArr[length - 1] !== item.departmentName) {
    selectedName.value = selectedName.value + '_' + item.departmentName
    courseStore.setSpuInfo({ ...courseStore.spuInfo, skuLabel: selectedName.value })
  }
  emits('schoolClick', item)
}
watch(
  () => courseStore.courseSkuId,
  () => {
    selectedName.value = courseStore.spuInfo.skuLabel
    schoolId.value = ''
    schoolName.value = ''
  },
  {
    deep: true,
    immediate: true,
  }
)

  interface InjectDataType {
    showCartSpecs: Ref<boolean>;
    getMarketingInfo: Function
  }
const injectData: InjectDataType = inject('info')!
const { showCartSpecs, getMarketingInfo } = injectData

const popupRef = ref<InstanceType<typeof KKPopup>>()
const onClose = debounceFunc(() => {
  showCartSpecs.value = false
  if (props.type === 'cart') {
    emitter.emit('closeCartPop')
  }
}, 500, false)
watch(
  () => [showCartSpecs.value, popupRef.value],
  async ([newVal, newPop]) => {
    if (newVal && newPop) {
      if (props.type === 'detail') {
        cartNumber.value = 1
      }
      await getMarketingInfo(courseStore.courseSkuId)
      popupRef.value?.showPopup()
    }
  }, {
    immediate: true,
    deep: true
  }
)

  interface Values {
    name: string;
    idx: number;
  }
const emits = defineEmits<{
    (e: 'click', value: Values): void;
    (e: 'schoolClick', value: SchoolType): void;
    (e: 'cartClick'): void;
    (e: 'handleClick'): void;
  }>()
  // 商品数量改变
const cartNumber = ref(1)
if (props.quantity) {
  cartNumber.value = props.quantity!
}
const enableToast = ref(false)
const toastText = ref('')
const onChange = (val: number) => {
  cartNumber.value = val
  if (val >= 20) {
    enableToast.value = true
    toastText.value = '每件商品最多可加购20个哦～'
  }
}
//  切换规格 按钮不可点击
const onSpecsClick = (name: string, idx: number): void => {
  isGetAllSku.value = false
  cartNumber.value = 1
  emits('click', { name, idx })
}

const onSubmit = async (type:string) => {
  const { goodsType, id } = courseStore.courseInfo
  if (goodsType === GoodsType.MultiSpecification && !courseStore.isSku) {
    Message('请先选择商品规格')
    return false
  }
  if (type === 'add') {
    const { error, data } = await cartApi.fetchAddCart({
      goodsMasterId: id,
      specificationItemId: courseStore.courseSkuId,
      quantity: cartNumber.value as number
    })
    if (!error.value) {
      const { stock, errorMessage } = data.value
      handleAsync(stock, errorMessage)
    }
  } else {
    const { error, data } = await cartApi.fetchUpdateCartSpec({
      id: props.cartId as string,
      goodsMasterId: id,
      specificationItemId: courseStore.courseSkuId,
      quantity: cartNumber.value as number
    })
    if (!error.value) {
      const { stock, errorMessage } = data.value
      handleAsync(stock, errorMessage)
    }
  }
}
const handleAsync = async (stock:number, errorMessage:string) => {
  if (errorMessage) {
    if (stock > 0) {
      if (cartNumber.value as number > stock) {
        cartNumber.value = stock
      }
    }
    if (stock === 0) {
      emits('handleClick')
    }
    return Message(errorMessage)
  } else {
    Message('已加入购物车~')
    await cartStore.fetchCartCount()
    popupRef.value?.hide()
    if (props.type === 'cart') {
      emitter.emit('closeCartPop')
    }
  }
}
/**
   * @description 是否展示营销标识
   */
const isShowMarketing = computed(() => {
  // spu 商品展示 spu 的价格区间
  if (!courseStore?.isSku) { return false }

  if (marketingInfo.value?.isMarketing) {
    const isGroup = marketingInfo.value?.groupProperty?.isGroup
    if (marketingInfo.value?.activityType === ActivityType.Group &&
    (!isGroup || isGroup === 'originBuy')) {
      return false
    }
    return true
  } else {
    return false
  }
})

const init = () => {
  const { goodsType } = courseStore.courseInfo
  const isCartSku = props.type === 'detail'
  const isCrm = goodsType !== GoodsType.MultiSpecification ||
  (internalSystemId && +internalSystemId === 26 && audition)
  if (isCrm) { return }
  const { specs, id, specification } = courseStore.courseInfo
  // 初始化sku参数
  if (!isCartSku) {
    initSku({ specs, goodsMasterId: id, defaultSku: specification, defaultInfo: courseStore.courseInfo })
  }
  userStore.isLogin && cartStore.fetchCartCount()
}

onMounted(() => {
  init()
})

</script>

<style scoped lang="scss">
.cart-mulity-wrap {
  .price {
    height: 48px;
    line-height: 48px;
    font-size: 48px;

    .symbol {
      font-size: 28px;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
    }
  }
  .number-box{
    width: 750px;
  }
}

:deep(.van-popup) {
  height: 62vh;
}

</style>
