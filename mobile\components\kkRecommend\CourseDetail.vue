<template>
  <div class="course-detail-recommend">
    <div class="course-detail-recommend-title">
      <div
        v-if="showTitle == 1"
        class="ml-[20px] flex mt-[25px] items-center"
      >
        <span
          v-if="titleIcon"
          class="mr-[8px]"
        >
          <img
            :src="titleIcon"
            alt=""
            class="w-[42px] h-[42px]"
          >
        </span>
        <span
          class="text-[#111] font-medium text-[32px] leading-[40px]"
        >{{ title }}</span>
      </div>
    </div>

    <div
      class="px-[20px] pb-[24px]"
    >
      <GoodCellCourseDetail
        v-for="(item, index) in list"
        :key="index"
        :goods="item"
        :is-search="false"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
withDefaults(defineProps<{
  list?: any[],
  showTitle?: number,
  title?: string,
  bgColor?: string,
  titleIcon?: string
}>(), {
  list: () => [],
  showTitle: 1,
  bgColor: '#fff',
  title: '',
  titleIcon: ''
})

</script>

<style lang="scss" scoped>
.course-detail-recommend{
  width: 718px;
  margin: 0 auto;
  background-color: #fff;
  background: linear-gradient( 180deg, #FFECE8 0%, #FFFFFF 100px, #FFFFFF 100%);
  border-radius: 24px;
  margin-top: 24px;
}
.course-detail-recommend-title{
  border-top-left-radius: 24px;
  border-top-right-radius: 24px;
  border: 2px solid #fff;
  border-bottom: none;
  //
}

:deep(.goods-cell) {
  //
}
</style>
