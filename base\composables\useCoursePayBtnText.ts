import { useCourseStore } from '~/stores/course.store'
import { ResourceTypes } from '@/apis/course/types'

export default function useCoursePayBtnText () {
  // const query = useRoute().query
  const courseStore = useCourseStore()

  // 面授课/OMO/面授套餐
  const FaceToFaceCourse = [ResourceTypes.OMOPackage, ResourceTypes.FaceToFaceCourse, ResourceTypes.FacePackage]

  // 是否展示其他费用
  const isFaceToFaceCourse = computed(() => {
    return FaceToFaceCourse.includes(Number(courseStore.courseInfo.resourceTypes))
  })

  // 是否展示模板配置按钮文案(立即购买和立即领取展示模板配置按钮文案)
  const isShowTemplateBtnName = computed(() => {
    const { btnStatus } = courseStore.courseInfo
    return [BtnStatus.BuyNow, BtnStatus.Learn].includes(btnStatus)
  })
  const memberInfo = computed(() => {
    return courseStore.memberInfo || {}
  })
  /**
   * 根据商品状态和模板配置返回按钮文案
   * @returns 按钮文案
   */
  const getCoursePayBtnText = () => {
    const {
      btnStatusName,
      goodsPresentPrice,
      isBuy,
      teachingMaterialPrice = 0,
      roomPrice = 0,
      otherPrice = 0,
      // bookStock,
      // resourceTypes,
      btnStatus
    } = courseStore.courseInfo
    const { freeGoodsName, payGoodsName, id } = courseStore.configData

    /**
     * 如果是: 购买了会员 && 会员优惠后0元 && 按钮是【立即领取】
     * */
    // TODO 注意PC端没有会员逻辑，注意参数容错
    // TODO 对接会员价
    const isUserMember = memberInfo.value.isMember === 1
    const goodsPresentPriceMember = +memberInfo.value.price === 0
    // 从会员中心进入-辨识是会员免费商品
    // if (query.customType === 'MEMBER_FREE' && isUserMember && +goodsPresentPriceMember === 0) {
    if (isUserMember && goodsPresentPriceMember) {
      /* // 会员免费商品 并且 图书库存 bookStock === 0
      if (+resourceTypes === 3 && bookStock === 0) {
        return '库存不足'
      } */
      return '立即领取'
    }
    // 商品关联了模板且没有购买或者领取过显示模板配置的文案
    const otherCost = Number(teachingMaterialPrice) || Number(roomPrice) || Number(otherPrice)
    // 商品未购买且按钮状态除了暂不可购买和暂不可售
    const _notBuy = !isBuy && ![BtnStatus.NotAvailable, BtnStatus.Overdue].includes(btnStatus)
    const notBuy = isFaceToFaceCourse.value ? (isFaceToFaceCourse.value && _notBuy) : _notBuy
    // 开发环境 http://localhost:3000/course/709722970990583808
    // http://kk.kuke99.com:9001/zentao/bug-view-81369.html
    // if (isFaceToFaceCourse.value) {
    //   notBuy = notBuy && true
    // }
    // 商品模板有数就 && 不属于营销商品 && (是面授课 && 没有购买过 && 没有其他费用) || 立即购买和立即领取按钮状态展示模板配置按钮文案 && 没有购买的商品不可购买或者不可售的按钮状态不展示模板按钮文案配置
    if (id && !courseStore.marketingInfo.isMarketing && ((isFaceToFaceCourse.value && !isBuy && !otherCost) || isShowTemplateBtnName.value) && notBuy) {
      return +goodsPresentPrice === 0 ? freeGoodsName : payGoodsName
    }
    return btnStatusName
  }

  return {
    getCoursePayBtnText
  }
}
