<!-- eslint-disable vue/require-default-prop -->
<template>
  <van-popup
    v-model:show="visible"
    :class="['popup', position === 'center' ? '' : 'safe-area']"
    :closeable="closeable"
    :position="position"
    round
    :close-on-click-overlay="closeOnClickOverlay"
    :style="{
      width: width ?
        px2rem(width + 'px') :
        (position === 'center' ? '5.6rem' : '7.5rem'),
      height: _height,
      maxHeight: maxHeight,
      background: bgColor,
      borderRadius:borderRadiusStyle,
      left: 0,
      right: 0,
      margin: '0 auto'
    }"
    :z-index="zIndex"
    @click-close-icon.stop="closeIcon"
    @click-overlay.stop="closeIcon"
    @opened="opened"
  >
    <ClientOnly>
      <div class="popup-box " :style="{ padding: px2rem(boxPadding), }">
        <div v-if="slots.header" class="">
          <slot name="header" />
          <KKCIcon
            v-if="isClose"
            name="icon-com_guangbi"
            color="closeIconColor"
            :size="closeIconSize"
            class="absolute right-[24px] top-[36px]"
            @click.stop="hide"
          />
        </div>
        <template v-else>
          <div v-if="title" class="popup-box-title text-[#111] mx-[0] text-center font-bold">
            {{ title }}
          </div>
          <div v-if="subTitle" class="text-[24px] text-[#999] mb-[24px]">
            {{ subTitle }}
          </div>
        </template>
        <slot />
        <div
          v-if="position === 'center' && isShowBtn"
          class="popup-box-bottom flex justify-center"
          :class="hideCloseButton ? 'items-center' : 'justify-between'"
          :style="footerStyle"
        >
          <div
            v-if="!hideCloseButton"
            :style="{ width: px2rem(closeBtnWidth + 'px')
            }"
            class="bottom bg-[#F2F2F2] text-[#111]"
            @click="hide"
          >
            {{ cancelText }}
          </div>
          <div
            class="bottom text-white bg-brand"
            :style="{ width: px2rem(confirmBtnWidth + 'px'), fontWeight: confirmBtnFontWeight }"
            :class="[isOnlyConfirm ? '!w-[100%]' : '', isDisabledConfirmText ? 'bg-brand/40' : '']"
            @click="confirmHandle"
          >
            {{ confirmText }}
          </div>
        </div>
      </div>
    </ClientOnly>
  </van-popup>
</template>

<script setup lang="ts">
interface Props {
  footerStyle?: { [key: string]: string | number };
  title?: string;
  subTitle?: string;
  confirmText?: string;
  cancelText?: string;
  position?: 'top' | 'bottom' | 'right' | 'left' | 'center';
  height?: number | string;
  maxHeight?: string;
  bgColor?: string;
  boxPadding?: string;
  isClose?: boolean;
  closeable?: boolean;
  closeIcon?: () => void;
  closeIconSize?: number;
  closeIconColor?: string;
  hideCloseButton?: boolean;
  isOnlyConfirm?: boolean;
  closeOnClickOverlay?: boolean;
  width?: number;
  zIndex?:number;
  isDisabledConfirmText?:boolean;
  isShowBtn?:boolean;
  closeBtnWidth?:string;
  confirmBtnWidth?:string;
  confirmBtnFontWeight?: 'normal' | 'bold' | 'bolder' | 'lighter' | number;
  teleport?:string | Element
}
const props = withDefaults(defineProps<Props>(), {
  footerStyle: undefined,
  title: '这是弹层的内容',
  subTitle: '',
  confirmText: '确认',
  cancelText: '取消',
  position: 'center',
  bgColor: '#fff',
  boxPadding: '31px',
  isClose: true,
  closeable: false,
  closeIcon: () => { },
  closeIconSize: 48,
  closeIconColor: '#999',
  hideCloseButton: false,
  isOnlyConfirm: false,
  closeOnClickOverlay: true,
  zIndex: 9999,
  isDisabledConfirmText: false, // 是否禁止确认按钮
  isShowBtn: true, // 是否显示底部按钮
  closeBtnWidth: '',
  confirmBtnWidth: '',
  confirmBtnFontWeight: 'normal',
  maxHeight: '90%',
  teleport: '-'
})

const borderRadiusStyle = computed(() => {
  switch (props.position) {
    case 'top':
      return '0 0 .24rem .24rem'
    case 'bottom':
      return '.24rem .24rem 0 0'
    case 'right':
      return '0 .24rem .24rem 0'
    case 'left':
      return '.24rem 0 0 .24rem'
    case 'center':
      return '.24rem'
    default:
      return '.24rem'
  }
})

const slots = useSlots()
const emit = defineEmits<{
  (e: 'ok'): void;
  (e: 'close', val?: string): void;
  (e: 'opened'): void
}>()
const visible = ref(false)
const showPopup = () => {
  visible.value = true
}
const hide = () => {
  visible.value = false
  emit('close')
}
const _height = computed(() => {
  return typeof props.height === 'number' ? props.height + 'px' : props.height
})
watch(
  () => visible.value,
  (newVal) => {
    if (!newVal) {
      emit('close', 'cancel')
    }
  }
)

const confirmHandle = () => {
  if (props.isDisabledConfirmText) {
    return
  }
  emit('ok')
  hide()
}

const opened = () => {
  emit('opened')
}

defineExpose({
  visible,
  showPopup,
  hide,
})
</script>

<style lang="scss" scoped>

.popup-box {
  width: 100%;
  max-height: calc(100vh - 80px);
  touch-action: pan-y;
  // overflow-y: auto;   可能与translate 冲突导致不可触摸滑动

  &-title {
    // height: 44px;
    font-size: 36px;
    // line-height: 44px;
    margin: 34px 0;
  }

  &-bottom {
    .bottom {
      width: 238px;
      height: 80px;
      line-height: 80px;
      border-radius: 16px;
      font-size: 28px;
      text-align: center;
    }
  }
}
</style>
