import type {
  submitQuestType,
  chapterSubmitQuestType,
  TargetResultId,
  DoNextRoundProtBodyType,
  finishSubmitQuestType,
} from '../types'
import filterQuestionBankBody from '../../../../base/utils/filterBody'

enum Api {
  // 章节练习---开始练习
  chapterStart = '/kukecorequestion/wap/chapter/startProt',
  // 章节练习---继续练习
  chapterDoContinue = '/kukecorequestion/wap/chapter/doContinueProt',
  // 章节练习---再次练习
  chapterDoAgain = '/kukecorequestion/wap/chapter/doAgainProt',
  // 章节练习---提交答案
  chapterSubmitQuestion = '/kukecorequestion/wap/chapter/submitQuestionProt',
  // 章节练习---中途提交
  chapterSubmitLeave = '/kukecorequestion/wap/chapter/submitLeaveProt',
  // 章节练习---完成提交
  chapterSubmitFinish = '/kukecorequestion/wap/chapter/submitFinishProt',
  // 章节练习---报告页
  chapterReportProt = '/kukecorequestion/wap/chapter/reportProt',
  // 章节练习---开启下一轮
  chapterDoNextRoundProt = '/kukecorequestion/wap/chapter/doNextRoundProt',
}

/**
 * 章节练习---开始练习
 *
 * @param {StartType} body - 请求参数
 * @return {Promise<any>} - 返回数据
 */
export async function getChapterStart (body: any) {
  return useHttp<any>(Api.chapterStart, {
    method: 'post',
    body: filterQuestionBankBody(body),
    transform: res => res.data,
  })
}

/**
 * 章节练习---继续练习
 *
 * @param {TargetResultId} body - 请求参数
 * @return {Promise<any>} - 返回数据
 */
export async function getChapterDoContinue (body: any) {
  return useHttp<any>(Api.chapterDoContinue, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 章节练习---再次练习
 *
 * @param {TargetResultId} body - 请求参数
 * @return {Promise<any>} - 返回数据
 */
export async function getChapterDoAgain (body: any) {
  return useHttp<any>(Api.chapterDoAgain, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 章节练习---提交答案
 *
 * @param {submitQuestType} body - 请求参数
 * @return {Promise<any>} - 返回数据
 */
export async function chapterSubmitQuestion (body: submitQuestType) {
  const { ptypeValueId, ...params } = body
  const isMultipleChoice = [2, 3].includes(ptypeValueId)
  return useHttp<any>(Api.chapterSubmitQuestion, {
    method: 'post',
    body: params,
    transform: res => res.data,
    watch: false,
    isShowLoading: isMultipleChoice,
    isShowMask: isMultipleChoice
  })
}

/**
 * 章节练习---中途提交
 *
 * @param {submitLeaveType} body - 请求参数
 * @return {Promise<any>} - 返回数据
 */
export async function chapterSubmitLeave (body: chapterSubmitQuestType) {
  return useHttp<any>(Api.chapterSubmitLeave, {
    method: 'post',
    body,
    transform: res => res.data,
    watch: false,
  })
}

/**
 * 章节练习---完成提交
 *
 * @param {submitFinishType} body - 请求参数
 * @return {Promise<any>} - 返回数据
 */
export async function chapterSubmitFinish (body: finishSubmitQuestType) {
  return useHttp<any>(Api.chapterSubmitFinish, {
    method: 'post',
    body,
    transform: res => res.data,
    watch: false
  })
}

/**
 * 章节练习---报告页
 *
 * @param {TargetResultId} body - 请求参数
 * @return {Promise<any>} - 返回数据
 */
export async function getChapterReportProt (body: TargetResultId) {
  return useHttp<any>(Api.chapterReportProt, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 章节练习---开启下一轮
 *
 * @param {TargetResultId} body - 请求参数
 * @return {Promise<any>} - 返回数据
 */
export async function chapterDoNextRoundProt (body: DoNextRoundProtBodyType) {
  return useHttp<any>(Api.chapterDoNextRoundProt, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}
