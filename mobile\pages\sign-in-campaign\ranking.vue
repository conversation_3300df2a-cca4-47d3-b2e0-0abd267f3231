<!-- 签到打卡活动排行榜 -->
<template>
  <div class="sign-in-campaign">
    <NavBar :content="'规则规则规则规则'" class="" show-config title="签到打卡活动排行榜" right-text="规则" />

    <!-- [AI-GEN] Tab 切换 -->
    <van-tabs
      v-model:active="activeTab"
      class="ranking-tabs"
      title-active-color="var(--kkc-brand)"
      title-inactive-color="#666"
      @change="onTabChange"
    >
      <van-tab
        v-for="(tab, index) in tabs"
        :key="index"
        :title="tab.title"
        :name="index"
      >
        <!-- 排行榜列表 -->
        <van-pull-refresh
          v-model="tabsData[index].refreshing"
          class="ranking-list-container"
          @refresh="onRefresh(index)"
        >
          <van-list
            v-model:loading="tabsData[index].loading"
            :finished="tabsData[index].finished"
            finished-text="没有更多了"
            :immediate-check="false"
            class="ranking-list"
            @load="onLoad(index)"
          >
            <!-- 排行榜项目 -->
            <div
              v-for="item in tabsData[index].list"
              :key="item.id"
              class="ranking-item"
            >
              <div class="ranking-number">
                <span
                  v-if="item.rank <= 3"
                  class="ranking-medal"
                  :class="`ranking-medal-${item.rank}`"
                >
                  {{ item.rank }}
                </span>
                <span v-else class="ranking-normal">{{ item.rank }}</span>
              </div>

              <div class="user-info">
                <div class="user-avatar">
                  <img :src="item.avatar" :alt="item.nickname">
                </div>
                <div class="user-details">
                  <div class="user-nickname">
                    {{ item.nickname }}
                  </div>
                  <div class="user-desc">
                    {{ item.description }}
                  </div>
                </div>
              </div>

              <div class="ranking-score">
                <div class="score-value">
                  {{ item.score }}
                </div>
                <div class="score-unit">
                  {{ tab.unit }}
                </div>
              </div>
            </div>

            <!-- 空状态 -->
            <div v-if="!tabsData[index].loading && tabsData[index].list.length === 0" class="empty-state">
              <div class="empty-icon">
                📊
              </div>
              <div class="empty-text">
                暂无排行榜数据
              </div>
            </div>
          </van-list>
        </van-pull-refresh>
      </van-tab>
    </van-tabs>
  </div>
</template>

<script setup lang="ts">
// /sign-in-campaign/ranking
import { ref, reactive, onMounted } from 'vue'
import { useClockInWxShareInfo } from './hooks/useClockIn'

// 页面元数据
definePageMeta({
  ignoreLearnTarget: true,
})

// Tab 配置
interface TabConfig {
  title: string
  unit: string
  type: 'cumulative' | 'consecutive'
}

const tabs: TabConfig[] = [
  { title: '累计签到榜', unit: '天', type: 'cumulative' },
  { title: '连续签到榜', unit: '天', type: 'consecutive' }
]

// 排行榜数据项接口
interface RankingItem {
  id: number
  rank: number
  nickname: string
  avatar: string
  description: string
  score: number
}

// Tab 数据状态接口
interface TabData {
  list: RankingItem[]
  loading: boolean
  refreshing: boolean
  finished: boolean
  page: number
  hasMore: boolean
}

// 当前激活的 tab
const activeTab = ref(0)

// 每个 tab 的数据状态
const tabsData = reactive<TabData[]>(
  tabs.map(() => ({
    list: [],
    loading: false,
    refreshing: false,
    finished: false,
    page: 1,
    hasMore: true
  }))
)

// [AI-GEN] Mock 数据生成函数
const generateMockData = (page: number, tabType: 'cumulative' | 'consecutive'): RankingItem[] => {
  const pageSize = 20
  const startIndex = (page - 1) * pageSize
  const mockData: RankingItem[] = []

  // 生成头像 URL 列表
  const avatars = [
    'https://img.yzcdn.cn/vant/cat.jpeg',
    'https://img.yzcdn.cn/vant/tree.jpg',
    'https://fastly.jsdelivr.net/npm/@vant/assets/apple-1.jpeg',
    'https://fastly.jsdelivr.net/npm/@vant/assets/apple-2.jpeg',
    'https://fastly.jsdelivr.net/npm/@vant/assets/apple-3.jpeg'
  ]

  for (let i = 0; i < pageSize; i++) {
    const rank = startIndex + i + 1
    const baseScore = tabType === 'cumulative' ? 100 : 50
    const randomScore = Math.floor(Math.random() * baseScore) + (baseScore - rank)

    mockData.push({
      id: rank,
      rank,
      nickname: `用户${rank.toString().padStart(3, '0')}`,
      avatar: avatars[i % avatars.length],
      description: tabType === 'cumulative' ? `累计签到 ${randomScore} 天` : `连续签到 ${randomScore} 天`,
      score: randomScore
    })
  }

  return mockData
}

// 模拟 API 请求
const fetchRankingData = async (tabIndex: number, page: number): Promise<{ data: RankingItem[], hasMore: boolean }> => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 800))

  const tabType = tabs[tabIndex].type
  const data = generateMockData(page, tabType)

  // 模拟分页结束条件（假设最多有 5 页数据）
  const hasMore = page < 5
  console.log('[ { data, hasMore } ] >', { data, hasMore })
  return { data, hasMore }
}

// 加载数据
const loadData = async (tabIndex: number, isRefresh = false) => {
  const tabData = tabsData[tabIndex]
  console.log('[ tabData.loading ] >', tabData.loading)
  if (tabData.loading) { return }

  try {
    tabData.loading = true

    const page = isRefresh ? 1 : tabData.page
    const { data, hasMore } = await fetchRankingData(tabIndex, page)

    if (isRefresh) {
      // 刷新时重置数据
      tabData.list = data
      tabData.page = 1
    } else {
      // 加载更多时追加数据
      tabData.list.push(...data)
    }

    tabData.page = page + 1
    tabData.hasMore = hasMore
    tabData.finished = !hasMore
  } catch (error) {
    console.error('加载排行榜数据失败:', error)
  } finally {
    tabData.loading = false
    tabData.refreshing = false
  }
}

// Tab 切换事件
const onTabChange = (index: number) => {
  activeTab.value = index

  // 如果当前 tab 没有数据，则加载数据
  if (tabsData[index].list.length === 0) {
    loadData(index, true)
  }
}

// 下拉刷新
const onRefresh = (tabIndex: number) => {
  loadData(tabIndex, true)
}

// 上拉加载更多
const onLoad = (tabIndex: number) => {
  console.log('[ onLoad tabIndex: ] >', tabIndex)
  if (tabsData[tabIndex].hasMore) {
    loadData(tabIndex, false)
  }
}

// 打卡 -- 微信分享
const { setWxShareInfo } = useClockInWxShareInfo()

// 页面初始化
onMounted(() => {
  // 加载第一个 tab 的数据
  loadData(0, true)

  // layout/default.vue 延迟了200ms执行，所以这里需要延迟200ms执行
  setTimeout(() => {
    // if (isWeChat() || isXcx) {
    setWxShareInfo()
    // }
  }, 300)
})
</script>

<style lang="scss" scoped>
// [AI-GEN]
.sign-in-campaign {
  min-height: 100vh;
  background-color: #f7f8fa;
}

// Tab 样式
.ranking-tabs {
  background-color: #fff;

  :deep(.van-tabs__wrap) {
    border-bottom: 1px solid #ebedf0;
  }

  :deep(.van-tab) {
    font-size: 16px;
    font-weight: 500;
  }

  :deep(.van-tabs__line) {
    background-color: var(--kkc-brand);
    height: 3px;
    border-radius: 2px;
  }
}

// 排行榜列表容器
.ranking-list-container {
  min-height: calc(100vh - 140px);
}

.ranking-list {
  padding: 0 16px;
}

// 排行榜项目样式
.ranking-item {
  display: flex;
  align-items: center;
  padding: 16px;
  margin: 12px 0;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  }
}

// 排名数字样式
.ranking-number {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  flex-shrink: 0;
}

.ranking-medal {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-weight: bold;
  font-size: 14px;

  &.ranking-medal-1 {
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    box-shadow: 0 2px 8px rgba(255, 215, 0, 0.4);
  }

  &.ranking-medal-2 {
    background: linear-gradient(135deg, #c0c0c0, #e8e8e8);
    box-shadow: 0 2px 8px rgba(192, 192, 192, 0.4);
  }

  &.ranking-medal-3 {
    background: linear-gradient(135deg, #cd7f32, #daa520);
    box-shadow: 0 2px 8px rgba(205, 127, 50, 0.4);
  }
}

.ranking-normal {
  font-size: 18px;
  font-weight: bold;
  color: #666;
}

// 用户信息样式
.user-info {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
}

.user-avatar {
  width: 48px;
  height: 48px;
  margin-right: 12px;
  flex-shrink: 0;

  img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid #f0f0f0;
  }
}

.user-details {
  flex: 1;
  min-width: 0;
}

.user-nickname {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.user-desc {
  font-size: 14px;
  color: #999;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 分数样式
.ranking-score {
  text-align: right;
  flex-shrink: 0;
}

.score-value {
  font-size: 20px;
  font-weight: bold;
  color: var(--kkc-brand);
  line-height: 1;
}

.score-unit {
  font-size: 12px;
  color: #999;
  margin-top: 2px;
}

// 空状态样式
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.empty-text {
  font-size: 16px;
  color: #999;
}

// 响应式设计
@media (max-width: 375px) {
  .ranking-item {
    padding: 12px;
    margin: 8px 0;
  }

  .user-avatar {
    width: 40px;
    height: 40px;
  }

  .user-nickname {
    font-size: 15px;
  }

  .score-value {
    font-size: 18px;
  }
}
</style>
