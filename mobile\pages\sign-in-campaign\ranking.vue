<!-- 签到打卡活动排行榜 -->
<template>
  <div class="sign-in-campaign">
    <NavBar :content="'规则规则规则规则'" class="" show-config title="签到打卡活动排行榜" right-text="规则" />

    <!-- [AI-GEN] Tab 切换 -->
    <van-tabs
      v-model:active="activeTab"
      offset-top="0.88rem"
      sticky
      type="line"
      :border="false"
      :line-height="0"
      class="ranking-tabs"
      title-active-color="#111"
      title-inactive-color="#111"
      @change="onTabChange"
    >
      <van-tab
        v-for="(tab, index) in tabs"
        :key="index"
        :title="tab.title"
        :name="index"
      >
        <!-- 排行榜列表 -->
        <van-pull-refresh
          v-model="tabsData[index].refreshing"
          class="ranking-list-container"
          @refresh="onRefresh(index)"
        >
          <van-list
            v-model:loading="tabsData[index].loading"
            :finished="tabsData[index].finished"
            finished-text="没有更多了"
            :immediate-check="false"
            class="ranking-list"
            @load="onLoad(index)"
          >
            <!-- 排行榜项目 -->
            <template
              v-for="item in tabsData[index].list"
              :key="item.id"
            >
              <RankingItem :item="item" :tab="tab" />
            </template>

            <!-- 空状态 -->
            <div
              v-if="!tabsData[index].loading && tabsData[index].list.length === 0"
              class="empty-state"
            >
              <div class="empty-icon">
                <img src="@/assets/icon__data-empty.png" alt="">
              </div>
              <div class="empty-text">
                暂无用户上榜哦，快去抢占第一名吧~
              </div>
            </div>
            <div
              v-if="!tabsData[index].loading && tabsData[index].list.length === 3"
              class="empty-state"
            >
              <div class="empty-icon">
                <img src="@/assets/icon__data-empty.png" alt="">
              </div>
              <div class="empty-text">
                暂无更多排名哦～
              </div>
            </div>
          </van-list>
        </van-pull-refresh>
        <div class="fixed bottom-0 left-[50%] w-[750px] translate-x-[-50%] pb-[22px]">
          <!-- TODO 兼容ios 全面屏 -->
          <RankingItem :item="currentUserRankingItem" class="is-bg-white" :tab="tab" />
        </div>
      </van-tab>
    </van-tabs>
  </div>
</template>

<script setup lang="ts">
// /sign-in-campaign/ranking
import { ref, reactive, onMounted } from 'vue'
import { useClockInWxShareInfo } from './hooks/useClockIn'
import RankingItem from './components/RankingItem.vue'
import NavBar from './components/NavBar.vue'

// 页面元数据
definePageMeta({
  ignoreLearnTarget: true,
})

// Tab 配置
interface TabConfig {
  title: string
  unit: string
  type: 'cumulative' | 'consecutive'
}

const tabs: TabConfig[] = [
  { title: '累计签到榜', unit: '天', type: 'cumulative' },
  { title: '连续签到榜', unit: '天', type: 'consecutive' }
]

// 排行榜数据项接口
interface IRankingItem {
  id: number
  rank: number
  nickname: string
  avatar: string
  description: string
  score: number
}

// Tab 数据状态接口
interface TabData {
  list: IRankingItem[]
  loading: boolean
  refreshing: boolean
  finished: boolean
  page: number
  hasMore: boolean
}
const currentUserRankingItem = computed(() => {
  return {
    id: 1,
    rank: 1,
    nickname: '用户1',
    avatar: 'https://img.yzcdn.cn/vant/cat.jpeg',
    description: '累计签到 100 天',
    score: 100
  }
})

// 当前激活的 tab
const activeTab = ref(0)

// 每个 tab 的数据状态
const tabsData = reactive<TabData[]>(
  tabs.map(() => ({
    list: [],
    loading: false,
    refreshing: false,
    finished: false,
    page: 1,
    hasMore: true
  }))
)

// [AI-GEN] Mock 数据生成函数
const generateMockData = (page: number, tabType: 'cumulative' | 'consecutive'): IRankingItem[] => {
  const pageSize = 20
  const startIndex = (page - 1) * pageSize
  const mockData: IRankingItem[] = []

  // 生成头像 URL 列表
  const avatars = [
    'https://img.yzcdn.cn/vant/cat.jpeg',
    'https://img.yzcdn.cn/vant/tree.jpg',
    'https://fastly.jsdelivr.net/npm/@vant/assets/apple-1.jpeg',
    'https://fastly.jsdelivr.net/npm/@vant/assets/apple-2.jpeg',
    'https://fastly.jsdelivr.net/npm/@vant/assets/apple-3.jpeg'
  ]

  for (let i = 0; i < pageSize; i++) {
    const rank = startIndex + i + 1
    const baseScore = tabType === 'cumulative' ? 100 : 50
    const randomScore = Math.floor(Math.random() * baseScore) + (baseScore - rank)

    mockData.push({
      id: rank,
      rank,
      nickname: `用户${rank.toString().padStart(3, '0')}`,
      avatar: avatars[i % avatars.length],
      description: tabType === 'cumulative' ? `累计签到 ${randomScore} 天` : `连续签到 ${randomScore} 天`,
      score: randomScore
    })
  }

  return mockData
}

// 模拟 API 请求
const fetchRankingData = async (tabIndex: number, page: number): Promise<{ data: IRankingItem[], hasMore: boolean }> => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 800))

  const tabType = tabs[tabIndex].type
  const data = generateMockData(page, tabType)

  // 模拟分页结束条件（假设最多有 5 页数据）
  const hasMore = page < 5
  console.log('[ { data, hasMore } ] >', { data, hasMore })
  return { data, hasMore }
}

// 加载数据
const loadData = async (tabIndex: number, isRefresh = false) => {
  const tabData = tabsData[tabIndex]
  console.log('[ tabData.loading ] >', tabData.loading)

  // 防止重复加载
  if (tabData.loading) {
    console.log('正在加载中，跳过重复请求')
    return
  }

  // 如果不是刷新且已经没有更多数据，直接返回
  if (!isRefresh && tabData.finished) {
    console.log('已经没有更多数据，跳过加载')
    return
  }

  try {
    tabData.loading = true
    console.log('开始加载数据，isRefresh:', isRefresh, 'page:', tabData.page)

    const page = isRefresh ? 1 : tabData.page
    const { data, hasMore } = await fetchRankingData(tabIndex, page)
    console.log('数据加载完成，data length:', data.length, 'hasMore:', hasMore)

    if (isRefresh) {
      // 刷新时重置数据和状态
      tabData.list = data
      tabData.page = 2 // 下一页从第2页开始
      tabData.finished = false // 重置完成状态
    } else {
      // 加载更多时追加数据
      tabData.list.push(...data)
      tabData.page = page + 1
    }

    // 更新状态
    tabData.hasMore = hasMore
    tabData.finished = !hasMore

    console.log('数据状态更新完成，hasMore:', hasMore, 'finished:', tabData.finished, 'nextPage:', tabData.page)
  } catch (error) {
    console.error('加载排行榜数据失败:', error)
    // 发生错误时也要重置加载状态
    tabData.finished = true
  } finally {
    // 确保加载状态被重置
    tabData.loading = false
    tabData.refreshing = false
    console.log('加载状态重置完成')
  }
}

// Tab 切换事件
const onTabChange = (index: number) => {
  console.log('切换到 tab:', index)
  activeTab.value = index

  const tabData = tabsData[index]

  // 如果当前 tab 没有数据且不在加载中，则加载数据
  if (tabData.list.length === 0 && !tabData.loading) {
    console.log('tab 无数据，开始加载')
    loadData(index, true)
  } else {
    console.log('tab 已有数据或正在加载中，跳过加载')
  }
}

// 下拉刷新
const onRefresh = (tabIndex: number) => {
  console.log('下拉刷新 tab:', tabIndex)
  const tabData = tabsData[tabIndex]

  // 重置状态
  tabData.finished = false
  tabData.hasMore = true

  loadData(tabIndex, true)
}

// 上拉加载更多
const onLoad = (tabIndex: number) => {
  console.log('[ onLoad tabIndex: ] >', tabIndex)
  const tabData = tabsData[tabIndex]

  // 如果没有更多数据，直接设置 finished 状态并重置 loading
  if (!tabData.hasMore) {
    tabData.finished = true
    tabData.loading = false
    return
  }

  // 如果正在加载中，避免重复加载
  if (tabData.loading) {
    return
  }

  loadData(tabIndex, false)
}

// 打卡 -- 微信分享
const { setWxShareInfo } = useClockInWxShareInfo()

// 页面初始化
onMounted(() => {
  // 加载第一个 tab 的数据
  loadData(0, true)

  // layout/default.vue 延迟了200ms执行，所以这里需要延迟200ms执行
  setTimeout(() => {
    // if (isWeChat() || isXcx) {
    setWxShareInfo()
    // }
  }, 300)
})
</script>

<style lang="scss" scoped>
// [AI-GEN]
.sign-in-campaign {
  min-height: 100vh;
  background-color: #fff;
  background-image: url('~/assets/images/signInCampaign/ranking-ng.png');
  background-repeat: no-repeat;
  background-position: top center;
      background-size: contain;
}

// Tab 样式
.ranking-tabs {
  // background-color: #fff;
  .van-tabs  {
    // --van-tabs-line-height: initial;
  }

  :deep(.van-tabs__wrap) {
    width: 702px;
    margin: 0 auto;
    // --van-tabs-line-height: initial;
    --van-tabs-line-height: 72px;
    background: rgba(0,0,0,0.1);
    border-radius: 26px 26px 26px 26px;
  }
  :deep(.van-tabs__nav) {
    // height: 72px;
    --van-tabs-nav-background: transparent;
    padding: 4px;
    // border-bottom: 1px solid #ebedf0;
//     background-color: #fff;
//     width: 702px;
// height: 72px;
  }

  :deep(.van-tab) {
    // font-size: 16px;
    // font-weight: 500;
  }

  :deep(.van-tabs__line) {
    // background-color: var(--kkc-brand);
    // height: 3px;
    // border-radius: 2px;
    display: none;
  }
  :deep(.van-tab) {
    border-radius: 24px 24px 24px 24px;
    height: 64px;
    line-height: 64px;
  }
  :deep(.van-tab--active) {
    background-color: #fff;
    // height: 3px;
    // border-radius: 2px;
    // border-radius: 24px 24px 24px 24px;
    // height: 64px;
    // line-height: 64px;
  }
  :deep(.van-tab__text) {
// width: 140px;
height: 34px;
font-family: PingFang SC;
font-weight: 400;
font-size: 28px;
color: #111111;
line-height: 34px;
  }
  :deep(.van-sticky) {
    padding: 12px 0;
  }
  :deep(.van-sticky--fixed) {
    background-color: #fff;
  }
}

// 排行榜列表容器
.ranking-list-container {
  min-height: calc(100vh - 88px - 96px);
}

.ranking-list {
  padding: 0 24px;
}

// 空状态样式
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.empty-icon {
  font-size: 48px;
  // margin-bottom: 16px;
  // opacity: 0.6;
}

.empty-text {
width: 532px;
height: 34px;
// font-family: PingFang SC, PingFang SC;
font-weight: 400;
font-size: 28px;
color: #666666;
line-height: 34px;
text-align: center;
// font-style: normal;
// text-transform: none;
}

</style>
