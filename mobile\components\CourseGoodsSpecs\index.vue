<template>
  <div>
    <!-- 商品规格 -->
    <div class="specs-title text-[28px] text-[#111] font-medium leading-[34px] ml-[8px] mb-[16px]">
      {{ list.name }}
    </div>

    <ul class="flex flex-wrap">
      <li
        v-for="(item,ind) in list.values"
        :key="ind"
        :class="['specs-item', !isDisSpecValue(item, nameIndex) ? 'disabled' : '', list.itemIndex === ind ? 'active' : '' ]"
        @click="changeSpecValue(item, nameIndex, ind)"
      >
        {{ item }}
      </li>
    </ul>
  </div>
</template>

<script setup lang="ts">
import { useCourseStore } from '~/stores/course.store'
const { isDisSpecValue, switchItem } = useSKU()
const route = useRoute()
const { internalSystemId } = route?.query

const courseStore = useCourseStore()
const kkIsAudition = useCookie<string>(IS_AUDITION)
const props = defineProps<{
  list: any,
  nameIndex: number,
  goodsMasterId?:string
}>()

const emits = defineEmits<{
  (e: 'click', event: string): void
}>()

// 改变规格值
const changeSpecValue = async (name: string, nameIndex: number, ind:number) => {
  if (Number(kkIsAudition.value) === 1 && Number(internalSystemId) === 26) { return }
  // crm的分享商品，不能切换子规格，获客码商品可以切换
  if (route?.query.sharePrice && Number(internalSystemId) === 26 && !route?.query.targetCode) { return }
  // scrm的分享商品，不能切换子规格
  if (route?.query.sharePrice && Number(internalSystemId) === 37) { return }
  if (!isDisSpecValue(name, nameIndex)) { return false }
  await switchItem(nameIndex, ind, props.goodsMasterId)
  emits('click', courseStore?.spuInfo?.goodsTitle)
}

</script>

<style scoped lang="scss">
.specs-item {
  padding: 15px 16px;
  margin: 0 8px 16px 8px;
  // height: 72px;
  margin-bottom: 24px;
  font-size: 26px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #111111;
  // line-height: 68px;
  border-radius: 12px;
  background: #F7F7F7;
  border: 2px solid transparent;

  &.active {
    font-weight: 500;
    border-style: solid;
    border-width: 2px;
    // background: #FFF1F0;
    @apply text-brand border-brand bg-brand/5;
  }

  &:active {
    background: #F2F2F2;
  }
  &.disabled {
    color: rgba(17, 17, 17, 30%);
  }

}
</style>
