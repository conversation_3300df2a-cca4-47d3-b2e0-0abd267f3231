// 购买方法
import { Decimal } from 'decimal.js'
import useCrmSharePrice from '../../base/composables/useCrmSharePrice'
import { GoodsType, ResourceTypes } from '@/apis/course/types'
import { PayStatus } from '#imports'
import { useUserStore } from '~/stores/user.store'
import type { CourseDetailModel } from '@/apis/course/types'
import type { addOrderParams, addOrderModel, OrderPay, PayInfoType, GoodsParamInfo } from '@/apis/order/types' // PayResult
import type { ActivityRes, OrderMarketingInfo } from '~/apis/course/activetyTypes'
import useGetAgentQuery from '@/composables/useGetAgentQuery'
import { useLiveStore } from '~/stores/live.store'

interface OptionTypes {
  isSend: number | string, // 下单必填
  specsId: string, // 规格id
  addressInfo: any, // 收货地址信息
  payInfo?: PayInfoType, // 支付信息 下单必填
  marketingInfo?: any,
  goodsInfos?: GoodsParamInfo[]
  callBack?: Function // 商品异常的回调
  userRemark?: string // 备注
  memberInfo?: any
}

// 跳转收银台后回跳地址
const backRedictUrlsMap = {
  default: '/shopping/pay-succ',
  priceConpun: '/activity/pricecoupon/success',
  memberCenter: '/member-center/order/result',
}
export const buyEffect = (params: any) => {
  const {
    public: {
      PAY_LOCALHOST,
    }
  } = useRuntimeConfig()
  // TODO state
  const { appId, lesseeId, isXcx } = useAppConfig()
  const wxOpenId = useCookie('WX_OPEN_ID')
  const localWxMiniAppId = useCookie('WX_APP_ID')
  const { orderApi, courseApi } = useApi()
  const route = useRoute()
  const disabled = ref(false)
  const { info, options }: { info: Ref<CourseDetailModel>; options: OptionTypes; } = params

  const { addressInfo, marketingInfo, callBack, isSend, goodsInfos: goodsInfosCopy, userRemark, pointsInfo, memberInfo } = toRefs(options)
  const payInfo = options?.payInfo ? toRef(options?.payInfo) : ref<PayInfoType>({ shouldPay: undefined })
  const { id, specsId: specificationItemId, redirectUri, agentCode, agentMType, agentMId, poolId, saleAdminId, shareId, sharePrice, internalSystemId, cartId, wxMiniAppId, targetCode } = route.query as any

  const goodsMasterId = id ?? unref(info)?.id
  // 多规格的子规格 id
  const specsItemId = computed(() => {
    return params.options.specsId || specificationItemId
  })
  const liveStore = useLiveStore()
  const { sharePriceFlag } = useCrmSharePrice()
  const userStore = useUserStore()
  const shouldPay = computed(() => {
    if (payInfo?.value?.shouldPay !== undefined) {
      if (payInfo?.value?.shouldPay === '0.00' || payInfo?.value?.shouldPay === '0.0' || payInfo?.value?.shouldPay === '0') {
        payInfo.value.shouldPay = 0
      }
      return payInfo?.value?.shouldPay
    }

    const { goodsPresentPrice = 0, teachingMaterialPrice = 0, roomPrice = 0, otherPrice = 0 } = unref(info)
    let goodsPrice = Number(goodsPresentPrice)
    if (marketingInfo?.value?.activityStatus === 2) {
      if (marketingInfo?.value?.activityType === ActivityType.Group && marketingInfo.value.groupProperty?.isGroup === 'groupBuy') {
        goodsPrice = Number(marketingInfo?.value?.activityPrice)
      } else if (marketingInfo?.value?.isMarketing && marketingInfo?.value?.activityType !== ActivityType.Group) {
        goodsPrice = Number(marketingInfo?.value?.activityPrice)
      }
    }
    const teachingMaterialPriceVisible = Number(teachingMaterialPrice)
    const roomPriceVisible = Number(roomPrice)
    const otherPriceVisible = Number(otherPrice)
    let priceDecimal = new Decimal(goodsPrice)
    if (teachingMaterialPriceVisible) { priceDecimal = priceDecimal.add(new Decimal(teachingMaterialPriceVisible)) }
    if (roomPriceVisible) { priceDecimal = priceDecimal.add(new Decimal(roomPriceVisible)) }
    if (otherPriceVisible) { priceDecimal = priceDecimal.add(new Decimal(otherPriceVisible)) }
    return Number(priceDecimal)
  })

  const btnText = ref('')
  watch(() => info, (newVal) => {
    if (newVal) {
      btnText.value = newVal.value?.btnStatusName
    }
  }, {
    deep: true,
    immediate: true
  })

  // 订单确认
  const handleBuyBtn = async (targetCodeShareId?:string) => {
    const {
      activityId,
      activityType,
    } = handleMarketingInfo(unref(info), marketingInfo as Ref<ActivityRes>)
    const { btnStatus } = unref(info)
    console.log('🚀 ~ handleBuyBtn ~ unref(info):', unref(info))

    const state = userStore.isLoginFn()
    if (state) {
      disabled.value = !disabled.value
      const status = checkoutGoodsBtnStatus(btnStatus)
      if (!status) {
        return
      }
      // 面授 omo 面授套餐需要判断 其他费用也不为0
      const {
        teachingMaterialPrice = 0,
        roomPrice = 0,
        otherPrice = 0,
        goodsPresentPrice = 0,
        resourceTypes,
        deliveryMethod
      } = unref(info)
      const isResourse = [ResourceTypes.OMOPackage, ResourceTypes.FaceToFaceCourse, ResourceTypes.FacePackage].includes(Number(resourceTypes))
      const isShowPrice = Number(teachingMaterialPrice) || Number(roomPrice) || Number(otherPrice) || Number(goodsPresentPrice)
      const isOtherPrice = !(isResourse && Number(isShowPrice))
      // 如果是0元的并且没有发货信息的 则直接调用提交订单接口领取成功
      if (!shouldPay.value && !deliveryMethod && isOtherPrice) {
        handleCreateOrder()
        return
      }
      // 检查商品状态
      const goodsStatus = await checkGoods(activityType, activityId)
      if (goodsStatus) { return }
      const { id, receiveSchoolId, receiveSchoolName } = unref(info)
      // const { agentCode = '', agentMType = '', agentMId = '' } = useRoute().query
      // 兼容代理商传参
      const { getUrlParams, hasData } = useGetAgentQuery()
      let agentParams
      if (hasData.value) {
        agentParams = getUrlParams()
      }
      let queryInfo: any = {
        id,
        specsId: specsItemId.value,
        receiveSchoolId,
        receiveSchoolName,
        agentCode,
        agentMType,
        agentMId,
        ...(hasData.value && { ...agentParams }),
        'kk-system': useRoute().query['kk-system'],
        poolId,
        saleAdminId,
        shareId,
        sharePrice,
        internalSystemId,
        targetCode,
      }
      queryInfo = handleMarketingInfo(unref(info), marketingInfo as Ref<ActivityRes>, queryInfo).queryInfo

      // 判断如果是crm的改价分享商品，带上标识
      if (sharePriceFlag && sharePriceFlag()) {
        queryInfo.sharePrice = 1
      }

      // crm分享试听多规格商品链接，从链接中取specificationItemId传递到下单页
      const { specificationItemId, audition } = route?.query
      if (internalSystemId && Number(internalSystemId) === 26) {
        if (audition && specificationItemId) {
          queryInfo.specsId = specificationItemId
        }
        // 获客码商品下单
        if (targetCode && targetCodeShareId) {
          queryInfo.shareId = targetCodeShareId
        }
      }

      if (cartId) {
        queryInfo.cartId = cartId
      }
      const { sharePriceId } = route.query
      navigateTo({
        path: '/shopping/buy-now',
        query: {
          ...queryInfo,
          internalSystemId,
          ...(sharePriceId && { sharePriceId })
        },
      })
      disabled.value = !disabled.value
    }
  }
  const getModifiedGoodsPrice = () => {
    const { goodsPresentPrice, modifiedGoodsPrice } = unref(info)
    if (agentMType === 'CP') {
      return goodsPresentPrice
    } else if (sharePriceFlag && sharePriceFlag()) {
      return modifiedGoodsPrice
    } else {
      return null
    }
  }

  // 创建订单
  const handleCreateOrderFn = async (isCustomJump = false) => {
    if (!goodsMasterId) {
      Message('商品id参数不存在')
      return
    }

    const state = userStore.isLoginFn()
    if (state) {
      const { activityType, activityId, buyAtMarkupId, teamId, couponId, buyGiveId, createOrderGetCoupon } = handleOrderMarketingInfo(marketingInfo as Ref<OrderMarketingInfo>)
      if ((isSend?.value || unref(info)?.deliveryMethod) && !addressInfo?.value) {
        Message('请选择收货地址')
        return
      }
      let deliveryInfo
      if (addressInfo?.value) {
        const {
          receiver,
          receiveMobile,
          provinceId,
          cityId, countyId,
          address: addressDetail
        } = addressInfo?.value
        deliveryInfo = {
          receiver, receiveMobile, provinceId, cityId, countyId, addressDetail
        }
      }
      // 下单支付信息必填
      if (!payInfo?.value) {
        throw new Error(`buyEffect.ts: 支付信息不能为空 payInfo:${payInfo?.value}`)
      }
      if (payInfo?.value?.shouldPay === undefined) {
        payInfo.value.shouldPay = 0
      }
      const { receiveSchoolId, receiveSchoolName, deliveryMethod, goodsTitle } = unref(info)
      // 判断拼团是原价购买还是拼团购买 isGroup==='originBuy' 就是原价购买兼容0元商品是拼团直接下单情况
      const flag = marketingInfo?.value?.groupProperty?.isGroup === 'originBuy'
      const params: addOrderParams = {
        deliveryInfo,
        payInfo: payInfo!.value!,
        goodsInfos: [
          {
            goodsMasterId,
            specificationItemId: specsItemId.value,
            receiveSchoolId: receiveSchoolId || '',
            receiveSchoolName: receiveSchoolName || '',
            saleType: 1,
            modifiedGoodsPrice: getModifiedGoodsPrice()
          },
          ...goodsInfosCopy?.value as GoodsParamInfo[] || []
        ],
        marketingInfo: {
          couponId,
          teamId,
          activityType: flag ? undefined : activityType,
          activityId: flag ? undefined : activityId,
          buyAtMarkupId,
          buyGiveId,
          userCouponId: marketingInfo?.value.userCouponId
        },
        userRemark: userRemark?.value,
        pointsInfo: unref(pointsInfo),
        memberInfo: unref(memberInfo),
        orderSource: route.query['kk-system'] as string || ''
      }
      console.log('[ params ] >', params)
      // 下单平台标识
      // http://yapi.kukewang.com:9005/project/705/interface/api/32607
      // if (params.orderSource && typeof params.orderSource !== 'string') {
      if (!isValidKkSystem(params.orderSource)) {
        params.orderSource = ''
        // if (!params.orderSource) {
        if (isDingTalk) {
          console.log('[ isDingTalk: ] >', isDingTalk)
          useCookie('kk-system').value = ''
        } else {
          useCookie('kk-system').value = null
        }
        console.log('[ typeof kk-system ] >', typeof useCookie('kk-system').value)
        // }
      }
      if (liveStore.state.isGoodsByLive) {
        params.clientType = 10
      }
      // crm分享相关
      if (poolId) {
        params.crmInfo = {
          poolId,
          saleAdminId,
          shareId,
          targetCode
        }
      }
      // SCRM分享商品参加，shareId有，没有客户池
      if (Number(shareId) && !poolId) {
        params.sCrmInfo = {
          shareId
        }
      }
      // 代理商分享相关
      const { sharePriceId } = route.query
      if (agentCode) {
        params.agentInfo = {
          agentCode,
          agentMType,
          agentMId,
          ...(sharePriceId && { sharePriceId })
        }
      }
      // 推广分享相关
      const extensionId = route.query['kk-extension'] as string || ''
      if (extensionId) {
        params.extensionInfo = {
          extensionId,
        }
      }

      disabled.value = true
      const { data, error } = await orderApi.addOrder(params)
      if (!error.value) {
        liveStore.setGoodsByLive(0)
        // 租户标识  订单id  回调url需encodeURIComponent
        const { orderSn, cateIds } = data.value.data

        // 买赠商品需要主动发放优惠券
        if (buyGiveId) {
          createOrderGetCoupon && createOrderGetCoupon(orderSn, goodsMasterId, specsItemId.value, shouldPay.value)
        }
        const { extension1 } = useCluesExtension()
        // 订单编号、商品名称、订单金额、订单状态
        updateBehaviorRecordsFn({
          orderSn, // 订单编号
          receivablePrice: payInfo.value.shouldPay, // 订单应收金额
          orderStatus: (!shouldPay.value && !deliveryMethod) ? 9 : 4, // 订单状态 要区分是否是0元订单 如果是0元订单则是‘已完成’ 9 否则是 ‘待支付’ 4  （枚举值为大B端值）
          goodsMasterId: unref(goodsMasterId),
          goodsTitle, // 商品标题
          isPromote: 0, // 是否推广 0 否 1 是
          extension1: extension1.value,
          cateIds // 专业分类id
        })
        // 如果支付金额0 则直接返回支付成功页面 不进行收银台的跳转
        console.log(params)
        // 如果是购物车跳转商品，需删除购物车数据
        if (cartId) {
          const { cartApi } = useApi()
          await cartApi.fetchDeleteCart({
            ids: [cartId]
          })
        }
        if (isCustomJump) { return orderSn }
        if (!shouldPay.value) {
          navigateTo({
            path: '/shopping/pay-succ',
            query: {
              orderSn,
              internalSystemId
            }
          })
          return
        }

        toCashier(orderSn)
      }
    }
  }

  const handleCreateOrder = debounceFunc(handleCreateOrderFn, 1000, true)

  const updateBehaviorRecordsFn = async (options: any) => {
    await updateBehaviorRecords(ActionEnum.CreateOrder, SourceEnum.Wap, {
      userMobile: userStore.getUserInfo?.mobile, // 用户手机号
      productId: appId, // 产品线
      clientType: isXcx ? '5' : '7',
      ...options
    })
  }

  // 跳转收银台
  const toCashier = (orderSn: string, orderSns?:string, backRedictUrlsMapKey?:keyof typeof backRedictUrlsMap) => {
    console.log(backRedictUrlsMapKey, 77)
    const orderNoKey = orderSns ? 'orderSns' : 'orderSn'
    const orderNoValue = orderSns || orderSn
    console.log(PAY_LOCALHOST, 'PAY_LOCALHOSTPAY_LOCALHOSTPAY_LOCALHOST')
    const redirectUrl = backRedictUrlsMap[backRedictUrlsMapKey || 'default']
    const currentHost = encodeURIComponent(`${location.origin}${redirectUrl}?${orderNoKey}=${orderNoValue}&internalSystemId=${internalSystemId}`)
    const wxMiniOpenId = wxOpenId.value
    const href = `${PAY_LOCALHOST}?${orderNoKey}=${orderNoValue}&lesseeId=${lesseeId}&appId=${appId}&wxMiniOpenId=${wxMiniOpenId}&wxMiniAppId=${localWxMiniAppId.value}&redirectUri=${currentHost}&internalSystemId=${internalSystemId}`
    window.location.href = href
  }

  /**
   * 跳转收银台
   * @param orderSn 单个订单号
   * @param orderSns 订单号集合
   * @param replace 是否替换当前页面，默认false
   */
  const jumpCashier = ({ orderSn, orderSns, replace = false }: { orderSn?: string, orderSns?: string, replace?: boolean }) => {
    if (!orderSn && !orderSns) {
      return Message('订单号不能为空')
    }
    const orderNumberKey = orderSns ? 'orderSns' : 'orderSn'
    const orderNumberValue = orderSns || orderSn

    // 获取当前地址
    const currentHost = encodeURIComponent(`${location.origin}/shopping/pay-succ?${orderNumberKey}=${orderNumberValue}&internalSystemId=${internalSystemId}`)
    // 获取微信小程序的openId
    const wxMiniOpenId = wxOpenId.value
    // 构建查询参数
    const queryParams = {
      [orderNumberKey]: orderNumberValue,
      lesseeId,
      appId,
      wxMiniOpenId,
      redirectUri: currentHost,
      internalSystemId,
      wxMiniAppId: localWxMiniAppId.value
    }

    // 判断是否为本地支付
    const href = `${PAY_LOCALHOST}?${new URLSearchParams(queryParams).toString()}`
    // 判断是否替换当前地址
    replace ? window.location.replace(href) : (window.location.href = href)
  }

  // 公众号支付
  const wxJsApiPay = (data: any) => {
    /* eslint-disable */
    WeixinJSBridge.invoke(
      'getBrandWCPayRequest', {
      appId: data.appId, // 公众号名称，由商户传入
      timeStamp: data.timestamp, // 时间戳，自1970年以来的秒数
      nonceStr: data.nonceStr, // 随机串
      package: data.packageStr,
      signType: 'RSA',
      paySign: data.sign // 微信签名
    },
      function (res) {
        if (res.err_msg == 'get_brand_wcpay_request:ok') {
          // 使用以上方式判断前端返回,微信团队郑重提示：
          // res.err_msg将在用户支付成功后返回ok，但并不保证它绝对可靠。
          // alert('支付调起')
        } else {
          disabled.value = false
        }
      })
  }
  // 支付方法
  const handlePayBtn = async () => {

    disabled.value = true
    const { code } = route.query
    if (code && !wxOpenId.value) {
      const { data, error: err } = await useHttp<any>(ApisCashier.getOpenId, {
        body: { code },
        transform: input => input.data,
      })

      wxOpenId.value = data.value.openId
      if (err.value) disabled.value = false
    }

    const body: any = {
      ...params.options,
    }
    body.settleMode = params.options.settleMode.value
    if (params.options.settleMode.value === PayStatus.WX_MINI) { // 小程序
      body.openId = wxOpenId.value
      body.wxAppId = wxMiniAppId
    } else {
      body.openId = wxOpenId.value
      body.wxAppId = undefined
    } 

    // 兼容购物车 
    const payApi = params.options.orderSn ? ApisCashier.onPay : ApisCashier.onPayByCart
    const { data, error } = await useHttp<any>(payApi, {
      body,
      transform: input => input?.data
    })
    if (error.value) {
      disabled.value = false
    }
    // 图书在app内打开
    if (os?.isBookInApp) {
      if (data.value && data.value.settleMode === PayStatus.WX_H5) {
        const params = JSON.stringify(unref(data))
        if (os?.isAndroid) {
          window.android?.goToWxPay(params)
        } else if (os?.isPhone || os?.isTablet) {
          window.webkit?.messageHandlers.goToWxPay.postMessage(params)
        } else if (os?.isHarmonyOS){
          window.harmony?.goToWxPay(params)
        }
        disabled.value = false
        return
      }
      if (data.value && data.value.settleMode === PayStatus.ALI_MOBILE) {
        const params = JSON.stringify({ payInfo: data.value.payInfo })
        if (os?.isAndroid) {
          window.android?.goToAliPay(params)
        } else if (os?.isPhone || os?.isTablet) {
          window.webkit?.messageHandlers.goToAliPay.postMessage(params)
        } else if (os?.isHarmonyOS){
          window.harmony?.goToAliPay(params)
        }
        return
      }
    }
    // 微信jsapi支付
    if (data.value && data.value.settleMode === PayStatus.WX_JSAPI) {
      wxJsApiPay(data.value)
      return
    }
    // 小程序支付
    if (data.value && data.value.settleMode === PayStatus.WX_MINI) {
      wxMiniprogramPay(data.value)
      return
    }
    paySuccess(data.value)
  }

  // 小程序支付
  const wxMiniprogramPay = (data: any) => {
    const pay_info = {
      timeStamp: data?.timestamp || '',
      nonceStr: data?.nonceStr || '',
      package: data?.packageStr || '',
      signType: 'RSA',
      paySign: data?.sign || ''
    }
    wx.miniProgram.navigateTo({
      url: `/pages/pay/index?pay_info=${encodeURIComponent(JSON.stringify(pay_info))}`,
      success: function () {

      },
      fail: function () {
      },
      complete: function () {
        disabled.value = false
      },
    })
  }

  // 检查商品状态
  const checkGoods = async (activityType?: any, activityId?: any) => {
    let state = false
    const params = {
      id: goodsMasterId || unref(info)?.id,
      goodsSpecificationItemId: specsItemId.value,
      activityType: undefined,
      activityId: undefined,
      deptId: unref(info)?.receiveSchoolId
    }
    if (marketingInfo && marketingInfo?.value) {
      const { isMarketing, activityStatus, groupProperty } = marketingInfo?.value
      if ((isMarketing && activityStatus === 2 && activityType !== ActivityType.Group) || (activityType === ActivityType.Group && groupProperty?.isGroup === 'groupBuy')) {
        params.activityType = activityType
        params.activityId = activityId
      }
    }

    // crm分享试听多规格商品链接，从链接中取specificationItemId
    const { specificationItemId, audition } = route?.query
    if (internalSystemId && Number(internalSystemId) === 26 && audition && specificationItemId) {
      params.goodsSpecificationItemId = specificationItemId
    }

    const { data, error } = await courseApi.checkGoodsStatus(params)
    if (!error.value) {
      const { goodsMsg, activityMsg } = data.value.data
      if (goodsMsg) {
        Message(goodsMsg)
        state = true
      }
      if (activityMsg) {
        callBack?.value && callBack?.value(data.value.data)
        state = true
      }
    }
    return state
  }


  // 检查按钮状态
  const checkoutGoodsBtnStatus = (btnStatus: number): boolean => {
    let goodsTypes: number | undefined
    let resourceType: string
    let specificationSingleMasterId: string
    let userBuyUnitGoodsId: string
    console.log("🚀 ~ checkoutGoodsBtnStatus ~ unref(info):", unref(info));
    if (unref(info)) {
      const { goodsType, resourceTypes, specificationSingleGoodsMasterId, userBuyUnitGoodsId: goodsId } = unref(info)
      goodsTypes = goodsType
      resourceType = resourceTypes
      specificationSingleMasterId = specificationSingleGoodsMasterId
      userBuyUnitGoodsId = goodsId as string
    }
    const statusMap = {
      [BtnStatus.BuyNow]: () => {
        console.log('立即购买11', isIncludeBook())

        // 包含 库存为0的时候 不可购买
        return isIncludeBook()
      },
      [BtnStatus.ContinueBuy]: () => {
        console.log('继续购买')
        return isIncludeBook()
      },
      [BtnStatus.Buyed]: () => {
        console.log('goodsTypes111', goodsTypes)
        handleJump(resourceType,  specificationSingleMasterId, userBuyUnitGoodsId)
        return false
      },
      [BtnStatus.Learn]: () => console.log('立即领取'),
      [BtnStatus.Receive]: () => {
        Message('已领取，不要太贪心哦~')
        return false
      },
      [BtnStatus.LearnNow]: () => {
        console.log('goodsTypes222', goodsTypes)
        handleJump(resourceType,  specificationSingleMasterId, userBuyUnitGoodsId)
        return false
      },
      [BtnStatus.ComingSoon]: () => {
        Message('该商品暂未开售~')
        return false
      },
      [BtnStatus.NotAvailable]: () => {
        Message('该商品暂不可购买~')
        return false
      },
      [BtnStatus.Overdue]: () => {
        Message('该商品暂不可售~')
        return false
      },
      default: () => console.log('未知状态'),
    }
    const handler = statusMap[btnStatus] || statusMap.default
    const result = handler() as unknown as boolean // 调用状态处理函数

    return result !== false // 返回值不等于 false 时返回 true，否则返回 false
  }

  // 判断商品类型跳转
  const handleJump = (resourceType: string, singleGoodsMasterId: string, userBuyUnitGoodsId: string) => {
    console.log("🚀 ~ handleJump ~ singleGoodsMasterId:", singleGoodsMasterId);
    const {id,goodsType, resourceTypes,specificationSingleGoodsMasterId} = unref(info)
    // 多规格中单品网课
    if (goodsType === GoodsType.MultiSpecification && +resourceTypes === ResourceTypes.OnlineCourse) {
      navigateTo(`/learn-center/live-detail?id=${singleGoodsMasterId}`)
      return
    }
     // 音频
     if (goodsType === GoodsType.Audio && +resourceTypes === ResourceTypes.Audio) {
      navigateTo(`/learn-center/audio-detail?userBuyUnitGoodsId=${userBuyUnitGoodsId}&id=${id}`)
      return
    }
    // 多规格中单品试卷
    if (goodsType === GoodsType.MultiSpecification && +resourceTypes === ResourceTypes.Examination) {
      navigateTo(`/learn-center/paper-list?id=${singleGoodsMasterId}`)
    }
    // 套餐  除 omo套餐 面授套餐 16音频套餐 20 电子书套餐
    else if (['5', '6', '7', '8', '9', '10', '16','20'].includes(resourceType as any) || goodsType === GoodsType.Package) {
      navigateTo(`/learn-center/myClass?userBuyUnitGoodsId=${userBuyUnitGoodsId}`)
    }
    // 单品试卷
    else if (+resourceType === ResourceTypes.Examination) {
      navigateTo(`/learn-center/paper-list?userBuyUnitGoodsId=${userBuyUnitGoodsId}&id=${id}`)
    }
    // 音频
    else if (resourceType === '15') {
      navigateTo(`/learn-center/audio-detail?userBuyUnitGoodsId=${userBuyUnitGoodsId}&id=${specificationSingleGoodsMasterId || id}`)
    } 
    // 电子书
    else if (+resourceType === ResourceTypes.Ebook) {
      navigateTo(`/learn-center/ebook-detail?userBuyUnitGoodsId=${userBuyUnitGoodsId}&id=${specificationSingleGoodsMasterId || id}`)
    }
    else if (resourceType === '11') {
      // http://localhost:3006/course/759066697458335744
      const { goodsQuestion } = unref(info)!
      console.log('[ goodsQuestion ] >', goodsQuestion)
      // Message('resourceType=11 属于题库，待对接')
      const {
        targetType, moduleManageId, validityMonth, targetId,
        // goodsMasterId,
        cateId,
        subjectId,
        regionId,
        academicSectionId,
        examFormatId,
        directionId
      } = goodsQuestion || {}
      const { createPurchasedLinkByLearnCenterIndex } = useJumpQuestionPurchased()
      createPurchasedLinkByLearnCenterIndex(
        { expiry: validityMonth },
        {
          moduleManageId,
          targetId,
          targetType,
          subjectType: subjectId,
          studyLevel1: cateId,
          region: regionId,
          academicSection: academicSectionId,
          examFormat: examFormatId,
          directionType: directionId,
        })
    } else {
      navigateTo(`/learn-center/live-detail?userBuyUnitGoodsId=${userBuyUnitGoodsId}&id=${id}`)
    }
  }

  const isIncludeBook = () => {
    const { includeBook,bookStock } = unref(info)
    if (includeBook && bookStock === 0 && Number(marketingInfo?.value.activityStatus)!==2) {
      Message('图书库存不足，请联系客服~')
      return false
    } else {
      return true
    }
  }

  // 订单支付成功
  const paySuccess = (result: addOrderModel) => {
    const sn = result.orderSn || route.query.orderSn
    const sns = result.orderSns || route.query.orderSns
    const orderKey = sns ? `orderSns=${encodeURIComponent(JSON.stringify(sns))}` : `orderSn=${sn}`
    disabled.value = false
    // 如果是免费跳转支付成功页面
    if (result.settleMode === PayStatus.WX_H5 || result.settleMode === PayStatus.WX_MINI) {
      const pathUrl = `${location.origin}?${orderKey}&lesseeId=${lesseeId}&appId=${appId}&redirectUri=${redirectUri}`
      window.location.href = `${result.payInfo}&redirect_url=${encodeURIComponent(pathUrl)}`
    } else {
      window.location.href = `${result.payInfo}`
    }

  }

  // 订单支付   1.2.0 版本废弃方法 改用收银台
  const orderPay = async (order: OrderPay) => {
    disabled.value = true
    const { error, data } = await orderApi.processPayment(order)
    if (!error.value) {
      const result = data.value.data as addOrderModel
      paySuccess(result)
    } else {
      disabled.value = false
    }
  }

  return {
    jumpCashier,
    btnText,
    disabled,
    orderPay,
    handleBuyBtn,
    toCashier,
    handleCreateOrder,
    handlePayBtn,
    handleCreateOrderFn,
    checkGoods
  }
}

function isValidKkSystem(str:unknown) {
  return ['1', '2', '3', '4', '7', '8'].includes(String(str))
}