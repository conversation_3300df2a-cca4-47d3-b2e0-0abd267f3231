<template>
  <section class="floating-widget">
    <!-- [AI-GEN] 主悬浮窗容器 -->
    <div class="widget-container">
      <!-- 客服 -->
      <div
        class="widget-item"
        @click="openCustomerService"
      >
        <img :src="kefu" class="w-[88px] h-[88px]" alt="客服">
      </div>

      <!-- 回顶部 - 条件显示 -->
      <div
        v-if="showBackToTop"
        class="widget-item back-to-top"
        @click="scrollToTop"
      >
        <img :src="top" class="w-[88px] h-[88px]" alt="顶部">
      </div>
    </div>
  </section>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import kefu from '@/assets/images/home/<USER>'
import top from '@/assets/images/home/<USER>'

// 响应式数据
const showBackToTop = ref(false)

// 监听滚动事件
const handleScroll = () => {
  showBackToTop.value = window.scrollY > window.innerHeight
}

// 回到顶部
const scrollToTop = () => {
  window.scrollTo({
    top: 0,
    behavior: 'smooth'
  })
}

// 跳转链接
const openCustomerService = () => {
  // 库课网校没有小程序
  window.open('https://im7a2314e.7x24cc.com/phone_webChat.html?accountId=N000000036901&chatId=7019b8fa-6369-405a-b382-fe7f1a303a6a', '_blank')
  // Message('客服地址未提供')
}

//

// 生命周期钩子
onMounted(() => {
  window.addEventListener('scroll', handleScroll)
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})
</script>

<style scoped lang="scss">
.floating-widget {
  position: relative;
}

.widget-container {
  position: fixed;
  right: 24px;
  /* top: 50%; */
  bottom: 420px;
  /* transform: translateY(-50%); */
  z-index: 1000;
  word-break: break-all;
}

.widget-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: background-color 0.2s ease;
  /* border-bottom: 1px solid #f3f4f6; */
  position: relative;
  margin-bottom: 24px;
}

.widget-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.widget-item:hover {
  /* background-color: #f9fafb; */
}

.icon {
  width: 24px;
  height: 24px;
  color: #6b7280;
}

//

.qr-code {
  width: 128px;
  height: 128px;
  background-color: #f3f4f6;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
}

//

/* 回顶部特殊样式 */
.back-to-top {
  animation: fadeInUp 0.3s ease;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

</style>
