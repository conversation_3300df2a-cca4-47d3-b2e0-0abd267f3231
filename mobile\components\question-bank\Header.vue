<template>
  <!-- 答题卡导航条区 -->
  <div class="bg-white" :class="!isShowExamTitle ? 'h-[88px]' : 'h-[158px]'">
    <div
      class="bg-white w-[750px] fixed z-[99]"
      :class="[isAnalysis ? 'h-[154px]' : 'h-[158px]', !isShowExamTitle ? '!h-auto' : '']"
    >
      <div class="h-[88px] flex justify-between items-center px-[24px]">
        <div class="flex">
          <!-- v-if="!isXcx" -->
          <KKCIcon name="icon-com_return" :size="48" @click="handleBack" />
          <UsedTime
            v-if="!isAnalysis"
            ref="useTimeRef"
            :is-front="isFront"
            :time="time"
            :limit-time="limitTime"
            :is-show-pause-btn="isShowPauseBtn"
            @pause="timePause"
            @end="timeEnd"
          />
        </div>

        <div class="flex justify-end">
          <QuestionBankShare
            v-if="isShowShare"
            :id="current.id"
            :icon-size="32"
            :cid="current.ptypeValueId === 7 ? current.childQuestions[0].id : ''"
            :title="current.stem"
            :type="1"
          />
          <div
            v-if="isShowCorrection"
            class="flex flex-col items-center ml-[35px]"
            @click="handleCorrection"
          >
            <KKCIcon name="icon-jiucuo" :size="32" />
            <span class="text-[22px] text-[#111] pt-[6px]">纠错</span>
          </div>
          <div
            v-if="current.isExercise"
            class="flex flex-col items-center ml-[35px]"
            @click="handleReset"
          >
            <KKCIcon name="icon-zhongzuo" :size="32" />
            <span class="text-[22px] text-[#111] pt-[6px]">重做</span>
          </div>
          <div
            v-if="isShowCollect && isHasStudyLevel1InRouter"
            class="flex flex-col items-center ml-[35px]"
            @click="handleCollect"
          >
            <KKCIcon
              :name="
                parseInt(current.collectId) > 0 ? 'icon-yishoucang' : 'icon-shoucang'
              "
              :size="32"
              :color="parseInt(current.collectId) > 0 ? '#FFC556' : ''"
            />
            <span class="text-[22px] text-[#111] pt-[6px]">收藏</span>
          </div>
          <div
            v-if="isShowAnswerCard"
            class="flex flex-col items-center ml-[35px]"
            @click="handleExamCard"
          >
            <KKCIcon name="icon-datika1" :size="32" />
            <span class="text-[22px] text-[#111] pt-[6px]">答题卡</span>
          </div>
          <div
            v-if="!isAnalysis && !current.isExercise"
            class="flex flex-col items-center ml-[35px]"
            @click.stop="submitExam(1)"
          >
            <KKCIcon name="icon-jiaojuan" :size="32" />
            <span class="text-[22px] text-[#111] pt-[6px]">交卷</span>
          </div>
        </div>
      </div>
      <div
        v-if="isShowExamTitle"
        class="h-[64px] flex justify-between items-center px-[24px] shadow-bottom"
      >
        <span class="text-[26px] text-[#999] examTitle">{{ examTitle }}</span>
        <span class="text-[22px] text-[#999]"><span class="text-[28px] text-[#111]">{{ currentSelectNum }}</span>/{{ totalNum }}</span>
      </div>
      <div v-if="!isAnalysis">
        <van-progress
          :percentage="percentage"
          class="progress-shadow"
          :stroke-width="4"
          color="#15CCC0"
          track-color="#EEEEEE"
          :show-pivot="false"
        />
      </div>
    </div>
    <!-- 底部弹框 -->
    <!-- height="calc(100vh - 88px)" -->

    <KKPopup
      ref="examCardRef"
      position="bottom"
      title="答题卡"
      :is-close="false"
      bg-color="#fff"
      box-padding="0"
      :style="{ overflow: 'hidden' }"
      @close="handleClosePopup"
    >
      <template #header>
        <div />
      </template>
      <!-- 答题卡区域 -->
      <!-- 模拟试卷 -->
      <QuestionBankAnwserCard
        ref="answerCardRef"
        :ques-list="quesList"
        :model="model"
        :sort-num="currentNum"
        class="mt-[-24px]"
        :is-analysis="isAnalysis"
        :is-exam-paper="isExamPaper"
        :show-type="showType"
        :status1-text="status1Text"
        :status2-text="status2Text"
        :status3-text="status3Text"
        :status4-text="status4Text"
        :status6-text="status6Text"
        :module-type="moduleType"
        :is-show-corrected="isShowCorrected"
        @select="select"
        @load-more="loadMore"
      />
      <!-- 交卷按钮 -->
      <div v-if="!isAnalysis" class="flex h-[120px] justify-center items-center">
        <div class="submitQuestionBank !py-[12px] my-[12px] safe-area">
          <KKCButton class="w-[700px]" type="primary" @click.stop="submitExam(2)">
            {{ submitText }}
          </KKCButton>
        </div>
      </div>
    </KKPopup>
    <!-- 纠错弹窗 -->
    <KKPopup
      ref="correctionRef"
      class="correction-popup"
      position="bottom"
      title="纠错"
      :is-close="false"
      bg-color="#fff"
      :style="{ overflow: 'hidden' }"
    >
      <template #header>
        <div />
      </template>
      <QuestionBankCorrection
        :id="current.id"
        ref="questionBankCorrection"
        @success="onCancel"
      >
        <template #footer>
          <div class="submitQuestionBank safe-area flex justify-between">
            <KKCButton class="w-[340px] !bg-[#f2f2f2] !border-none" @click="onCancel">
              取消
            </KKCButton>
            <KKCButton class="w-[340px] !ml-0" type="primary" @click="onSubmit">
              确定
            </KKCButton>
          </div>
        </template>
      </QuestionBankCorrection>
    </KKPopup>
  </div>
</template>

<script setup lang="ts">
import type { PropType } from 'vue'
import { pauseAudio } from '../../../base/utils/audio'
import { flat } from '../../../base/utils/tools'
import type { questType } from './type'
import { useUserStore } from '~/stores/user.store'
import type { PauseEmitType } from '@/types/questionTime'
import { useQuestionBankStore } from '~/stores/question.bank.store'
import { useSetQuestionBankShare } from '~/composables/useSetQuestionBankShare'
// const { isXcx } = useAppConfig()
const route = useRoute()
const routeQuery = route.query

const questionBankStore = useQuestionBankStore()
const { track } = useTrackEvent()

const { questionBankApi } = useApi()
const props = defineProps({
  quesList: {
    type: Array as PropType<questType[]>,
    default: () => [],
  },
  totalNum: {
    type: Number,
    default: 0,
  },
  // 用户当前切换的题
  currentNum: {
    type: Number,
    default: 0,
  },
  // 是否展示计时组件+是否显示答题卡底部按钮
  isAnalysis: {
    type: Boolean,
    default: false,
  },
  // true:正计时 false:倒计时
  isFront: {
    type: Boolean,
    default: true,
  },
  // 倒计时/正计时初始值 单位s
  time: {
    type: Number,
    default: 0,
  },
  examTitle: {
    type: String,
    default: '',
  },
  examTagsName: {
    type: String,
    default: '',
  },
  behaviorTime: {
    type: Number,
    default: 0,
  },
  moduleTitle: {
    type: String,
    default: '',
  },
  // 练习模式 一题一页1  考试模式 通栏2
  model: {
    type: Number,
    default: 1,
  },
  // 用户当前切换的题
  sortNum: {
    type: Number,
    default: 1,
  },
  // 是否是试卷类型，默认不是
  isExamPaper: {
    type: Boolean,
    default: false,
  },
  targetResultId: {
    type: String,
    default: '',
  },
  nowStartTime: {
    type: String,
    default: '',
  },
  submitText: {
    type: String,
    default: '交卷',
  },
  current: {
    type: Object,
    default: () => ({}),
  },
  showType: {
    type: Array,
    default: () => [],
  },
  isShowCollect: {
    type: Boolean,
    default: true,
  },
  status1Text: {
    type: String,
    default: '未答',
  },
  status2Text: {
    type: String,
    default: '已答',
  },
  status3Text: {
    type: String,
    default: '正确',
  },
  status4Text: {
    type: String,
    default: '错误',
  },
  status6Text: {
    type: String,
    default: '半对',
  },
  isShowCorrection: {
    type: Boolean,
    default: true,
  },
  moduleType: {
    type: Number,
    default: 0,
  },
  // 是否显示答题卡
  isShowAnswerCard: {
    type: Boolean,
    default: true,
  },
  // 是否显示标题进度
  isShowExamTitle: {
    type: Boolean,
    default: true,
  },
  // 倒计时总时长
  limitTime: {
    type: Number,
    default: 0,
  },
  // 是否显示暂停按钮
  isShowPauseBtn: {
    type: Boolean,
    default: true,
  },
  /**
   * 是否显示分享
   */
  isShowShare: {
    type: Boolean,
    default: true,
  },
  /**
   * 是否显示批改标识
   */
  isShowCorrected: {
    type: Boolean,
    default: false,
  },
})
const currentSelectNum = computed(() => {
  return props.currentNum
})
const percentage = computed(() => {
  return props.totalNum ? (currentSelectNum.value / props.totalNum) * 100 : 0
})
const userStore = useUserStore()
const emit = defineEmits<{
  (e: 'submit'): void;
  (e: 'select', sort: number, bigSort?: number): void;
  // end只用于倒计时结束
  (e: 'end', data: PauseEmitType): void;
  // 可用于倒计时+正计时
  (e: 'pause', data: PauseEmitType): void;
  // 答題卡關閉
  (e: 'closePopup'): void;
  // 答題卡打開
  (e: 'openPopup'): void;
  // 收藏
  (e: 'collect', val: string, current: questType): void;
  // 重做
  (e: 'redo'): void;
  // 答题卡滚动加载更多
  (e: 'loadMore'): void;
}>()
const isHasStudyLevel1InRouter = computed(
  () => routeQuery.studyLevel1 !== '0' && !!routeQuery.studyLevel1
)
// 倒计时或 正计时 组件
const useTimeRef = ref()
const timePause = (data: PauseEmitType) => {
  doUsedTime.value = data.usedTime
  emit('pause', data)
}

/**
 * 作答时长
 */
const doUsedTime = ref(0)

const timeEnd = (data: PauseEmitType) => {
  document.activeElement?.blur()
  nextTick(() => {
    emit('end', data)
  })
}
const pause = () => {
  if (useTimeRef.value) {
    useTimeRef.value.pause()
  }
}
const start = () => {
  useTimeRef?.value.start()
}
// 返回上一级页面
const handleBack = () => {
  questionBankStore.setShouldPrompt(false)
  if (!props.isShowPauseBtn && !props.isAnalysis) {
    // 在联考（补考除外）和统考时，在返回页面时（返回键、左滑返回）弹窗提醒，确定后返回上一页。弹窗点击外部和点击按钮关闭。
    Dialog({
      title: '确定离开吗？',
      message: '离开后时间不可暂停，作答时长结束后自动交卷，在结束前可以继续答题',
      confirmText: '确定',
      cancelText: '取消',
      onConfirm: () => {
        handleBackTo()
      },
    })
  } else {
    handleBackTo()
  }
}

/**
 * 返回上一页
 */
const handleBackTo = () => {
  questionBankStore.setIsActiveLeave(true)
  if (useTimeRef?.value) {
    // 倒计时停止
    pause()
  }
  nextTick(() => {
    const {
      examTitle,
      examTagsName,
      behaviorTime,
      targetResultId,
      currentNum,
      nowStartTime,
      moduleType,
      current,
      moduleTitle,
    } = props
    const { doObjectCount, rightCount, doAllCount } = getChapterSubmitLeaveData()
    pauseAudio()
    questionBankBack({
      targetResultId,
      currentNum,
      nowStartTime,
      usedTime: doUsedTime.value,
      moduleType,
      extension6: examTagsName,
      extension7: examTitle,
      behaviorTime,
      doObjectCount,
      rightCount,
      doAllCount,
      moduleTitle,
      questionId: current.ptypeValueId !== 7 ? current.id : current.childQuestions[0].id,
    })
  })
}

// 获取章节已做未做等信息
const getChapterSubmitLeaveData = () => {
  const { moduleType, quesList } = props
  let doObjectCount = 0
  let rightCount = 0
  let noDocount = 0
  let doAllCount = 0
  if (moduleType === 3) {
    const list = flat(quesList.map(item => item.questionList))
    // 章节练习暂存部分参数
    list.forEach((question: any) => {
      // 3 正确 4 错误 6 半对
      if ([3, 4, 6].includes(question.logStatus)) {
        doObjectCount++
        if (question.logStatus === 3) {
          rightCount++
        }
      }

      if (
        question.logStatus !== 2 &&
        question.logStatus !== 3 &&
        question.logStatus !== 4
      ) {
        noDocount++
      }
    })

    doAllCount = list.length - noDocount
  }

  return {
    doObjectCount,
    rightCount,
    doAllCount,
  }
}

const examCardRef = ref(null)
const handleExamCard = () => {
  emit('openPopup')
  examCardRef.value?.showPopup()
  // 做题->答题卡 埋点
  track({
    category: '题库',
    action: '答题卡',
  })
}
// 交卷
const submitExam = (type: number) => {
  document.activeElement?.blur()
  nextTick(() => {
    examCardRef.value?.hide()
    // 调接口
    emit('submit')
  })
  if (type === 1) {
    // 做题->交卷 埋点
    track({
      category: '题库',
      action: '交卷',
    })
  } else {
    // 做题->答题卡-交卷 埋点
    track({
      category: '题库',
      action: '答题卡-交卷',
    })
  }
}
// 监听页面显示隐藏
const { isHidden } = useVisibilityChange('')
watch(isHidden, (hidden: boolean) => {
  if (hidden) {
    pauseAudio()
  }
})
const select = (sort: number, bigSort?: number) => {
  // currentSelectNum.value = sort
  examCardRef.value?.hide()
  emit('select', sort, bigSort)
}

const loadMore = () => {
  emit('loadMore')
}

// 更改答题卡中材料题的做题状态
const answerCardRef = ref()
const changeStatus = (logStatus: number, questionItem: any, isAnalyze?: string) => {
  console.log('changeStatus changeStatus', logStatus, questionItem)
  if (props.model === 1) {
    answerCardRef.value?.changeStatus(logStatus, questionItem, isAnalyze)
  } else {
    answerCardRef.value?.changeStatus(logStatus, questionItem)
  }
}
const handleClosePopup = () => {
  emit('closePopup')
}

// 计算属性，判断用户是否登录
const isLogin = computed(() => {
  return !!userStore.isLogin
})
// 纠错
const correctionRef = ref()
const questionBankCorrection = ref()
const handleCorrection = () => {
  if (!isLogin.value) {
    userStore.isLoginFn()
    return
  }
  correctionRef.value?.showPopup()
  // 做题->纠错 埋点
  track({
    category: '题库',
    action: '纠错',
  })
}
const onCancel = () => {
  questionBankCorrection.value?.reset()
  correctionRef.value?.hide()
}
const onSubmit = debounceFunc(
  () => {
    questionBankCorrection.value?.submit()
  },
  500,
  true
)
// 重做
const handleReset = debounceFunc(
  () => {
    // 做题->重答 埋点
    track({
      category: '题库',
      action: '重答',
      label: '',
    })
    emit('redo')
  },
  500,
  true
)

// 错误处理函数
const handleError = (error: any) => {
  console.log('error', error)
  Message('操作失败，请稍后重试')
}
// 收藏
const handleCollect = debounceFunc(
  async () => {
    try {
      const { current } = props
      if (parseInt(current.collectId) > 0) {
        // 取消收藏逻辑
        // if (current.ptypeValueId === 7) {
        //   await cancelCollect(current.childQuestions[0].collectId)
        // } else {
        //   await cancelCollect(current.collectId)
        // }
        // 取消收藏，材料题取消收藏也改为父级collectId
        await cancelCollect(current.collectId)
        // 做题->取消收藏 埋点
        track({
          category: '题库',
          action: '取消收藏',
        })
      } else {
        // 收藏逻辑
        await collectQuestion(parseRouteQuery(routeQuery), current)
        // 做题->收藏 埋点
        track({
          category: '题库',
          action: '收藏',
        })
      }
    } catch (error) {
      handleError(error)
    }
  },
  500,
  true
)

// 解析routeQuery并验证
const parseRouteQuery = (query: any) => {
  const valid = [
    'studyLevel1',
    'studyLevel2',
    'studyLevel3',
    'studyLevel4',
    'academicSection',
    'examFormat',
    'region',
    'subjectType',
    'directionType',
  ]
  const result: { [key: string]: any } = {}
  valid.forEach((key) => {
    const value = query[key]
    if (value) {
      result[key] = isNaN(Number(value)) ? 0 : Number(value)
    } else {
      result[key] = 0
    }
  })
  return result
}

// 取消收藏的逻辑
const cancelCollect = async (collectId: string) => {
  const { error } = await questionBankApi.deleteUserCollectQuestionProt({
    id: collectId,
  })
  if (!error.value) {
    emit('collect', '0', {
      ...props.current
    })
    Message('取消收藏成功')
  } else {
    handleError(error)
  }
}

// 收藏问题的逻辑
const collectQuestion = async (queryParams: { [key: string]: any }, current: any) => {
  const {
    studyLevel1,
    subjectType,
    directionType,
    academicSection,
    examFormat,
    region,
  } = queryParams
  const body = {
    questionId: current?.id,
    parentId: current?.parentId,
    ptypeValueId: current?.ptypeValueId,
    state: getCurrentState(current),
    studyLevel1,
    subjectType,
    directionType,
    academicSection,
    examFormat,
    region,
    answer: getCurrentAnswer(current),
  }
  if (current.ptypeValueId === 7) {
    body.parentId = current.id
    body.questionId = current.childQuestions[0].id
    body.answer = getCurrentAnswer(current.childQuestions[0])
    body.state = getCurrentState(current.childQuestions[0])
  }
  const { data, error } = await questionBankApi.addUserCollectQuestionProt(body)
  if (!error.value) {
    emit('collect', data.value.id, current)
    Message('收藏成功')
  } else {
    handleError(error)
  }
}

// 根据当前状态和答案获取收藏状态
const getCurrentState = (current: any) => {
  let result: number
  // 如果没有当前状态或者当前状态为0，则进行以下判断
  if (current.logStatus === 0 || !current.logStatus) {
    // 如果当前有日志回答，则result为2
    if (current.logAnswer) {
      result = 2
      // 如果当前题目类型值为6或7，则result为5
    } else if (['6', '7'].includes(current.ptypeValueId as string)) {
      result = 5
      // 其他情况，result为1
    } else {
      result = 1
    }
    // 否则，直接返回当前状态
  } else {
    result = current.logStatus
  }
  return result
}

// 根据当前答案获取答案字段值
const getCurrentAnswer = (current: any) => {
  return Object.prototype.toLocaleString.call(current.logAnswer) === '[object Object]'
    ? JSON.stringify(current.logAnswer)
    : current.logAnswer
}

defineExpose({ changeStatus, pause, start })

const { setQuestionBankHead, setQuestionBankShareInfo } = await useSetQuestionBankShare({
  title: props.examTitle,
})
if (props.isAnalysis) {
  setQuestionBankHead()
  setQuestionBankShareInfo()
}

if (os?.isApp) {
  // app环境返回上一页
  questionBankBackForApp(props)
}
</script>

<style scoped>
.examTitle {
  width: 544px;
  white-space: nowrap;
  display: inline-block;
  text-overflow: ellipsis;
  overflow: hidden;
}

.timing {
  opacity: 0 !important;
}

:deep(.van-progress) {
  border-radius: 0;
}

.shadow-bottom {
  box-shadow: 0 8px 15px 0px rgba(196, 201, 212, 0.05);
  /* box-shadow: 0 8px 15px 0px rgba(12, 75, 212, 0.5); */
}

:deep(.popup) {
  height: calc(100% - 168px);
}

:deep(.popup-box) {
  height: 100%;
}

:deep(.correction-popup) {
  height: auto;
}

:deep(.correction-popup .popup-box) {
  height: auto;
  padding-right: 0 !important;
  padding-bottom: 0 !important;
}

.submitQuestionBank {
  z-index: 21;
  /* position: fixed; */
  bottom: 0;
  left: 50%;
  /* transform: translateX(-50%); */
  right: 0;
  width: 700px;
  /* 80+24+24 */
  /* height: 128px; */
  padding-top: 24px;
  box-sizing: border-box;
  /* background-color: #ffffff; */
}

:deep(.tooter-pay-box) {
  height: calc(112px + 24px + 24px);
}
</style>
