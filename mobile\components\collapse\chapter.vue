<template>
  <div :class="[bem()]" :data-level="item.level" @click="handleAction(item)">
    <div :class="[bem('main')]">
      <div v-if="item.liveStatus === CourseLiveStatus.LivingStreaming" class="tag_living_second_level">
        直播中
      </div>
      <div :class="[bem('main', 'title')]">
        {{ item.name }}
      </div>
    </div>
    <div v-if="item?.children?.length" :class="[bem('right'), 'flex', 'items-center']">
      <KKCIcon class="animate-180" :class="{ 'rotate-180': isOpen }" name="icon-xiangqing-zhankai" :size="32" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { CourseLiveStatus } from './type'
import type { IItem } from './type'
const bem = useBem('catalog-chapter')
defineProps<{
  item: IItem
  isOpen: boolean
}>()

const emits = defineEmits<{
  (e: 'status', event: IItem): void;
  (e: 'action', event: IItem): void;
}>()
const handleAction = (item: IItem) => {
  emits('action', item)
}
</script>

<style lang="scss" scoped>
.catalog-chapter {
  display: flex;
  align-items: center;
  position: relative;
  min-height: 80px;
  // line-height: 80px;
  // background: #ebebeb;
  background: #f5f6f9;
  padding: 20px 24px;
  border-radius: 16px;
  justify-content: space-between;

  &__right {
    cursor: pointer;
  }

  &__main {
    width: 100%;
      overflow: hidden;
    .tag_living_second_level {
      flex-shrink: 0;
      width: 82px;
      height: 36px;
      line-height: 34px;
      text-align: center;
      margin-right: 8px;
      border-radius: 8px;
      font-size: 22px;
      float: left;
      color: var(--kkc-brand-text);
      border: 1px solid var(--kkc-brand-text);
    }

    &--title {
      font-size: 28px;
      font-weight: 500;
      line-height: 36px;
      max-width: 600px;
      // title 展示完整不需要超出显示...
      // @apply truncate;
    }
  }
}
</style>
