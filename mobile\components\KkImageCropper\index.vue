<template>
  <div class="img-cropper" style="width: 100%;">
    <VueCropper
      ref="cropper"
      :img="url"
      :max-img-size="maxSize"
      v-bind="mergeObjects"
      @imgLoad="handleImgLoad"
      @cropMoving="handleCropMove"
    />
  </div>
</template>
<script setup lang="ts">
import { useCropHooks } from './useCropHooks'
import type { Single } from './vueCropper'
import VueCropper from './vueCropper'

const { commonApi } = useApi()
const { computedScaleMult } = useCropHooks()
const props = defineProps<{
    url:string,
    // 裁剪配置
    cropperOption?: object,
    getQusetionCoordinate?:Function
}>()

const emits = defineEmits<{
  (e:'emitsResult', val:any):void
  (e:'emitsImgLoad', val:any):void
}>()

const mergeObjects = computed(() => {
  return Object.assign(
    {
      // img: '', // 裁剪图片的地址
      outputSize: 1, // 裁剪生成图片的质量(可选0.1 - 1)
      outputType: 'png', // 裁剪生成图片的格式（jpeg || png || webp）
      info: false, // 图片大小信息
      canScale: true, // 图片是否允许滚轮缩放
      autoCrop: true, // 是否默认生成截图框
      autoCropWidth: 750, // 默认生成截图框宽度
      autoCropHeight: 240, // 默认生成截图框高度
      // fixed: true, // 是否开启截图框宽高固定比例
      // fixedNumber: [5, 3], // 截图框的宽高比例
      full: false, // false按原比例裁切图片，不失真
      fixedBox: false, // 固定截图框大小，不允许改变
      canMove: false, // 上传图片是否可以移动
      canMoveBox: true, // 截图框能否拖动
      original: false, // 上传图片按照原始比例渲染
      centerBox: true, // 截图框是否被限制在图片里面
      high: false, // 是否按照设备的dpr 输出等比例图片
      infoTrue: true, // true为展示真实输出图片宽高，false展示看到的截图框宽高
      maxImgSize: 'auto', // 限制图片最大宽度和高度
      enlarge: 5, // 图片根据截图框输出比例倍数
      // mode: 'cover', // 图片默认渲染方式
      mode: 'contain', // 图片默认渲染方式
      // limitMinSize: [1, 1], // 设置最小裁切框宽度
    },
    props.cropperOption,
  )
})

/**
 *
 * 图片旋转
 */

const cropper = ref<typeof VueCropper | any>()

const maxSize = ref(750)

const singleData = ref<Single|any>()

const rotateCropper = () => {
  cropper.value?.rotateRight()
  const { h, w, trueHeight, trueWidth, scale } = cropper.value

  // 旋转奇数次旋转，90°、270°
  // const isRotate = r.value % 2
  const isRotate = cropper.value?.rotate % 2
  /**
   * 计算缩放倍数
   */
  let scaleMult = 1

  // 偶数次旋转，判断渲染宽真实宽、渲染高与真实高 相比的缩放值
  if (!isRotate) {
    scaleMult = computedScaleMult(h, trueHeight, w, trueWidth)
    // 奇数次旋转，判断渲染宽与真实高、渲染高与真实宽 相比的缩放值
  } else if (isRotate) {
    scaleMult = computedScaleMult(h, trueWidth, w, trueHeight)
  }
  /**
   * 旋转后相对于旋转前的缩放倍数
   */
  // const rotateAfterMult = Math.max(computedRatio(trueHeight * scaleMult, trueHeight * cropper.value.scale), computedRatio(trueWidth * scaleMult, trueWidth * cropper.value.scale))
  console.log(scaleMult, 'scaleMult11111', scale)

  cropper.value.scale = scaleMult

  /**
   * 计算旋转后题目框的坐标变化
   */
  if (!singleData.value) { return }
  const { single } = singleData.value
  if (!single?.[0]) {
    return
  }
  // const { itemPositionShow } = single[0]

  // for (const key of itemPositionShow) {
  //   const key0 = key[0]
  //   const key1 = key[1]
  //   key[0] = Math.ceil(imgCenterPointCoor.value[0] - (key1 - imgCenterPointCoor.value[1]))
  //   key[1] = Math.ceil(imgCenterPointCoor.value[1] + (key0 - imgCenterPointCoor.value[0]))
  // }
  // // 排列坐标
  // single[0].itemPositionShow = arrangePoints(itemPositionShow)

  console.log(singleData.value.single[0])
  nextTick(() => {
    changeCropFrame(singleData.value, scale)
  })
}

// const handelRealTime = (data) => {
//   // console.log(data, 'realtime')
// }

/**
 *
 * 裁剪后图片
 */

const ossInstance = ref(null)

const createImgBlob = () => {
  Loading(true)
  // cropper.value?.getCropData((data) => {
  //   console.log(data, 'base64')
  // })
  cropper.value?.getCropBlob(async (data:any) => {
    console.log(data, '裁剪数据')

    // 转换 Blob 为 File 对象
    const dataType = data?.type ? data?.type : 'image/png'
    const file = new File([data], 'image.png', { type: dataType })
    // 实例化oss
    const { data: dataOss } = await commonApi.fetchOSSConfig()
    ossInstance.value = initOSS(dataOss || {})

    // 将图片上传到阿里云OSS并返回上传结果给客户端
    const result = await (unref(ossInstance) as any).put(
      generateFilename(file, '/answerHive/pictures/'),
      file
    )
    console.log(result, 'emitsResult')
    Loading(false)
    emits('emitsResult', result)
  })
}
/**
 * 原图中心点坐标，已原图左上角为坐标原点
 */
const imgCenterPointCoor = ref<number[]>([0, 0])
const changeCropFrame = (cropSingle:Single, cScale:number) => {
  singleData.value = cropSingle
  const { single, code } = cropSingle
  if (!single?.[0] || Number(code) !== 20000) {
    return
  }
  cropper.value.goAutoCrop()
  nextTick(() => {
    const { cropW, cropH, trueHeight, trueWidth, scale, getImgAxis, rotate, getCropAxis } = cropper.value
    console.log(cropW, cropH, trueWidth, trueHeight, scale, rotate, cScale, '图片坐标裁剪框')

    /**
   * 旋转的三角函数
   */
    const rotateSin = Math.sin(90 * rotate * (Math.PI / 180))
    const threshold = 1e-10
    /**
   * 旋转前后的缩放比例改变量
   */
    const rotateAfterChange = rotateSin && Math.abs(rotateSin) > threshold ? scale - cScale : 0
    const afterH = (trueHeight * rotateAfterChange) / 2
    const afterW = (trueWidth * rotateAfterChange) / 2
    console.log(rotateSin, 'rotateSin')
    console.log(rotateAfterChange, afterH, 'afterH', afterW, 'afterW', rotateSin, '=========')
    const coorX = trueWidth / 2
    const coorY = trueHeight / 2
    imgCenterPointCoor.value = [coorX, coorY]

    const { itemPositionShow } = single[0]
    const positionShow = itemPositionShow
    /**
   * 计算题目框选的长宽
   * positionShow [左上、右上、右下、左下]
   */
    const singleW = Math.abs((positionShow[2][0] - positionShow[0][0]))
    const singleH = Math.abs((positionShow[2][1] - positionShow[0][1]))
    if (!singleW || !singleH) { return }
    // 图片左上角相对于页面左上角的坐标
    const { x1, y1, x2, y2 } = getImgAxis()
    console.log(x1, y1, x2, y2, '图片基于容器的坐标点')
    // 裁剪框基于容器的坐标点
    const { x1: xa1, y1: ya1, x2: xa2, y2: ya2 } = getCropAxis()
    console.log(xa1, ya1, xa2, ya2, '裁剪框基于容器的坐标点')
    const cropperCropBox = document.getElementsByClassName('cropper-crop-box')[0]
    cropperCropBox.classList.add('cropper-crop-box-transition')
    console.log(singleW, 'singleW', singleH, 'singleH')
    console.log(cropW, 'cropW', cropH, 'cropH')
    const scaleMult = scale
    console.log(scaleMult, 'scaleMult')
    cropper.value.cropW = rotate % 2 === 0 ? Math.ceil(singleW * scaleMult) : Math.ceil(singleH * scaleMult)
    cropper.value.cropH = rotate % 2 === 0 ? Math.ceil(singleH * scaleMult) : Math.ceil(singleW * scaleMult)
    console.log(cropper.value.cropOffsertX, cropper.value.cropOffsertY)
    console.log(singleW * scaleMult, singleH * scaleMult)
    const cropOffsertX = Math.floor(positionShow[0][0] * scaleMult)
    const cropOffsertY = Math.floor(positionShow[0][1] * scaleMult)
    console.log(cropOffsertX, cropOffsertY, 'cropOffsertX, cropOffsertY')
    // const isY = cropOffsertY > 0 ? -1 : 1
    // cropper.value.cropOffsertX = cropOffsertX + x1 + (rotate === 3 ? afterW : -afterW)
    // cropper.value.cropOffsertY = cropOffsertY + y1 - afterH
    console.log(rotate, '是否旋转')

    // 图片缩放之后的宽高
    const scaleW = trueWidth * scale
    const scaleH = trueHeight * scale

    const scaleX1 = Math.floor(positionShow[0][0] * scaleMult)
    const scaleX2 = Math.floor(positionShow[2][0] * scaleMult)
    const scaleY1 = Math.floor(positionShow[0][1] * scaleMult)
    const scaleY2 = Math.floor(positionShow[2][1] * scaleMult)

    const { x, y } = changeRotateXY(rotate, scaleW, scaleH, scaleX1, scaleX2, scaleY1, scaleY2)

    cropper.value.cropOffsertX = x1 + x
    cropper.value.cropOffsertY = y1 + y
    console.log(cropper.value.cropOffsertX, 'cropOffsertX', cropper.value.cropOffsertY, 'cropOffsertY')
    console.log(cropper.value.cropW, 'cropW', cropper.value.cropH, 'cropH')
    console.log(positionShow[0], positionShow[1], positionShow[2], positionShow[3])
    console.log(cropper.value)
    setTimeout(() => {
      cropperCropBox.classList.remove('cropper-crop-box-transition')
    }, 500)
  })
}
const changeRotateXY = (rotate:number, scaleW:number, scaleH:number, scaleX1:number, scaleX2:number, scaleY1:number, scaleY2:number) => {
  let x = 0
  let y = 0
  switch (rotate) {
    case 0 :
      x = scaleX1
      y = scaleY1
      break
    case 1 :
      x = scaleH - scaleY2
      y = scaleX1
      break
    case 2 :
      x = scaleW - scaleX2
      y = scaleH - scaleY2
      break
    case 3 :
      x = scaleY1
      y = scaleW - scaleX2
      break
  }
  return { x, y }
}
const handleCropMove = () => {
  const { getImgAxis, getCropAxis } = cropper.value
  console.log(getCropAxis(), 'getCropAxis')
  console.log(getImgAxis(), 'getImgAxis')
}

const handleImgLoad = (res:any) => {
  console.log(res, '裁剪图片是否加载·1完成')
  emits('emitsImgLoad', res)
}
defineExpose({
  rotateCropper,
  createImgBlob,
  changeCropFrame,
  cropper
})

onMounted(async () => {
  await nextTick()
  // if (props.getQusetionCoordinate) {
  //   props.getQusetionCoordinate(props.url)
  // }
})

useHead({
  script: [
    {
      src: 'https://gosspublic.alicdn.com/aliyun-oss-sdk-6.17.1.min.js',
    },
  ],
})

</script>
<style scoped lang="scss">
.img-cropper{
  :deep(.vue-cropper){
    background: #000;
    width: 90%;
    margin: 0 auto;
    .cropper-box{
      .cropper-box-canvas{
        object-fit: contain;
        img{
          width: 100%;
          // height: 100%;
          object-fit: contain;
        }
      }
    }
    .cropper-crop-box-transition{
      transition: width 0.5s ease, height 0.5s ease,transform 0.5s ease; /* 添加过渡效果 */
      // transition: transform 0.5s ease; /* 添加过渡效果 */
      transform-origin: center center
    }
    .cropper-crop-box>span:last-child{
      .crop-line{
        display: none;
      }
      .crop-point{
        border-radius: 0px !important;
        display: inline-block;
      }
      .point2,
      .point7{
        width: 100%;
        height: 0.1rem;
        background:transparent;
        transform: translate(-50%, -50%);
      }
      .point2::before,
      .point7::before{
        content:'';
        position: absolute;
        width: 0.5rem;
        height: 0.1rem;
        left: 50%;
        top: 50%;
        background:#fff;
        transform: translate(-50%);
      }
      .point4,
      .point5{
        width: 0.1rem;
        height: 100%;
        background:transparent;
        transform: translate(0, -50%);
      }
      .point4::before,
      .point5::before{
        content:'';
        position: absolute;
        width: 0.1rem;
        height: 0.5rem;
        left: 50%;
        top: 50%;
        background:#fff;
        transform: translate(-50%,-50%);
      }
      .point1{
        width: 0.38rem;
        height: 0.38rem;
        left:-7px;
        top:-7px;
        background: url('@/assets/images/answer/point-left-top.png') no-repeat;
      }
      .point3{
        width: 0.38rem;
        height: 0.38rem;
        right:-7px;
        top:-7px;
        background: url('@/assets/images/answer/point-right-top.png') no-repeat 100% 0%;
      }
      .point6{
        width: 0.38rem;
        height: 0.38rem;
        left:-7px;
        bottom:-7px;
        background: url('@/assets/images/answer/point-left-bottom.png') no-repeat 0% 100%;
      }
      .point8{
        width: 0.38rem;
        height: 0.38rem;
        right:-7px;
        bottom:-7px;
        background: url('@/assets/images/answer/point-right-bottom.png') no-repeat 100% 100%;
      }
    }
  }
}
</style>
