<template>
  <div>
    <p
      v-if="radioVisible"
      class="flex items-center text-[24px]"
      @click="handleClickOpen"
    >
      <KKCIcon
        :name="radioStatus?'icon-xieyi-xuanzhong':'icon-xieyi-weixuanzhong'"
        :size="54"
        :class="radioStatus?'text-brand':'text-[#ccc]'"
      />
      我已阅读并同意
      <span class="text-[24px] leading-[30px] text-brand">《售后政策》</span>
    </p>
    <Teleport to="body">
      <KKPopup
        ref="afterSaleRef"
        title=""
        position="center"
        bg-color="#fff"
        :confirm-text="isDisabledConfirmText?'需浏览完后同意协议':'我已同意并阅读'"
        cancel-text="关闭"
        :is-close="true"
        :close-icon-size="36"
        :is-disabled-confirm-text="isDisabledConfirmText"
        :is-show-btn="isShowBtn"
        close-btn-width="188"
        confirm-btn-width="298"
        @ok="confirm"
        @opened="opened"
        @close="close"
      >
        <template #header>
          <div class="text-[32px] text-[#111111] font-bold text-center">
            售后政策
          </div>
        </template>
        <div :id="`saleContent-${keyIndex}`" class="max-h-[653px] w-[100%] text-[24px] text-[#666] leading-[36px] overflow-y-auto my-[32px] px-[9px]">
          <p>
            用户在库课网校任意客户端，包含但不限于PC端网站、WAP版网站、APP等，选择商品提交订单并支付，库课网校即认为用户已充分理解和同意本协议的全部内容，并签署了本协议，本协议已在用户与本公司之间产生合同法律效力，用户使用本平台服务的全部活动将受到本协议的约束并应承担相应的责任和义务。
            售后政策条例如下，请您仔细阅读。
          </p>
          <p class="my-[16px]">
            •退班退课政策
          </p>
          <div class="my-[8px]">
            <strong>1.退款流程：</strong>
            <p>在个人中心订单列表点击【申请售后】→等待工作人员电话核实相关信息→系统退款</p>
          </div>
          <div class="my-[8px]">
            <strong>2.退款方式：</strong>
            <p>
              由于本网站课程为数字化产品，根据《消费者权益保护法》第二十五条规定，数字化产品不适用七天无理由退货。为了充分保证用户权益，退款比例遵循如下：
              自付款成功开始算起→t=购买课程时间
            </p>
          </div>
          <ul class="my-[8px]">
            <li
              v-for="(item,index) in tContent"
              :key="index"
              class="flex justify-around leading-[36px]"
              style="border:1px solid #ccc;border-bottom: none;"
              :style="index===4&&'border-bottom:1px solid #ccc;'"
            >
              <p class="w-[60%] text-center">
                {{ item.time }}
              </p>
              <p class="w-[40%] text-center">
                {{ item.title }}
              </p>
            </li>
          </ul>
          <ul>
            <li>
              <strong>注：</strong>
            </li>
            <li>
              (1) 退款只支持微信、支付宝等方式购买的网课，通过苹果设备充值库课币等方式购买，因苹果平台规则限制，无法进行退款，用户请谨慎下单。
            </li>
            <li>(2) 购买网课中附送教材的，教材不予退还，退款时将扣除图书全额费用与运费。</li>
            <li>
              (3) 退款时效：<br>
              a、用户购买单独课程商品（不含纸质资料），首次发起退款，且在3小时内，支持即时到账，系统自动打款；<br>
              b.其它任意场景下，用户发起退款，客服、财务审核完成后，将在10个工作日内退款至付款账户。
            </li>
            <li>(4) 二次退款须知：用户首次购买某一商品后发起退款，适用正常退款规则；二次购买同一课程或者含有该课程的套餐，发起退款后审核期为30个工作日，同时将扣除实际支付金额的30% 。</li>
            <li>
              (5) 实物商品退款说明：<br>
              a. 若用户发起退款的商品中，包含实物商品，并且在退款到账前，用户已签收实物商品，那么在实施退款时，用户将承担“自购买至退款”过程中所有物流费用，该费用将从原退款金额中进行扣除（库课网校将实物商品寄给用户的物流费用默认为10元）。若寄回的商品物流费用，用户在寄出时已完成支付，库课网校将不会扣除已支付的物流费用。<br>
              b. 实物商品或包含实物的商品，在库课网校收到实物后，将应退款项原路退至用户的支付账户中。
            </li>
            <li>附：库课网校视频课程观看包含有效期限，请您购买后在有效期限内进行学习，因个人原因导致课程过期，库课网校不提供退款服务，敬请谅解。</li>
          </ul>

          <div class="mt-[16px]">
            <strong>•如有疑问，请咨询库课网校客服</strong>
            <p>
              库课网校客服接听电话时间：8：00-12：00，13：30-17：30，其他时间来电，问题若无法解决，请谅解！
              客服电话：400-6529-888
            </p>
          </div>
        </div>
      </KKPopup>
    </Teleport>
  </div>
</template>
<script setup lang="ts">

const props = withDefaults(defineProps<{
  radioVisible?: boolean,
  keyIndex?:string
}>(), {
  radioVisible: true,
  keyIndex: ''
})

const tContent = ref<{time:string, title:string}[]>(
  [
    {
      time: 't<=3小时',
      title: '全额'
    },
    {
      time: '3小时<t<=24小时',
      title: '80%'
    },
    {
      time: '24小时<t<=48小时',
      title: ' 50%'
    },
    {
      time: '48小时<t<=72小时',
      title: '20%'
    },
    {
      time: '超过72小时',
      title: '不予退款'
    }
  ]
)
const emits = defineEmits<{
  (e: 'ok'): void;
}>()
// 协议勾选状态
const radioStatus = ref<boolean>(false)
// 是否展示底部按钮
const isShowBtn = ref<boolean>(true)
// 按钮是否置灰
const isDisabledConfirmText = ref<boolean>(true)

const afterSaleRef = ref<any>({})
// 打开协议弹窗
const handleClickOpen = () => {
  afterSaleRef.value?.showPopup()
}
const confirm = () => {
  console.log('已阅读并同意')
  radioStatus.value = true
  isShowBtn.value = false
  emits('ok')
}
const saleContentDom = ref<HTMLElement>()
const scrollBottom = () => {
  // 当滚动条滚动时，检查是否到达了底部
  // +5是给予误差值，防止因计算错误导致无法触发
  if (saleContentDom.value!.scrollHeight - saleContentDom.value!.scrollTop <= saleContentDom.value!.clientHeight + 5) {
    console.log('已经触底！')
    isDisabledConfirmText.value = false
  }
}
const listenerScroll = () => {
  saleContentDom.value?.addEventListener('scroll', scrollBottom)
}
// 打开弹窗获取滚动元素，并注册监听事件
const opened = () => {
  saleContentDom.value = document.getElementById(`saleContent-${props.keyIndex}`) as HTMLElement
  console.log('🚀 ~ close ~ saleContentDom:', saleContentDom)

  listenerScroll()
}
// 关闭弹窗销毁事件
const close = () => {
  saleContentDom.value?.removeEventListener('scroll', scrollBottom)
  if (!radioStatus.value) {
    isDisabledConfirmText.value = true
  }
  saleContentDom.value!.scrollTop = 0
}

defineExpose({
  handleClickOpen,
  radioStatus
})
</script>
