<template>
  <div class="stem-wrap">
    <div class="stem-title">
      <KKCHtmlMathJax :content="stemCom || ''" :is-answer-questions="true" />
    </div>
    <!-- 若题干有音频，则显示音频播放器 -->
    <KKCAudioPlayer
      v-if="stemSrc"
      class="bg-[#f1f2f3] !w-[100%] audio-wrap"
      :icon-name="'icon-yinpinbofang'"
      :playing-icon="'icon-yinpinzanting'"
      :icon-size="48"
      :url="stemSrc"
    />
  </div>
</template>
<script lang="ts" setup>

const props = defineProps<{
    stem: string | undefined;
    stemSrc?:string
}>()

const stemCom = computed(() => {
  // stem 过滤掉audio
  return props.stem?.replace(/<audio.*?<\/audio>/g, '')
})

</script>
<style lang="scss" scoped>

.stem-wrap{
    margin-bottom: 24px;

    .stem-title{
        font-size: 24px;
        color: #000000;
        margin-bottom: 20px;
        word-break: break-all;
    }

    .audio-wrap{
        border-radius: 8px 40px 40px 40px!important;
    }
}

</style>
