export const useScrollViewBack = (className?: string) => {
  const route = useRoute()
  // 使用正则匹配全部-替换为_
  const sessionStorageKey = `PST_${String(route.name).replace(/-/g, '_').toLocaleUpperCase()}`

  const scrollViewRef = ref<HTMLElement | Window>()
  const scrollTop = ref(0)

  /***
   * 记录当前scrollTop
   *
   * @param top 需要记录的scrollTop值
   * */
  const scrollViewRecord = (top?: string) => {
    sessionStorage.setItem(sessionStorageKey, top || scrollTop.value.toString())
  }

  /***
   * 返回至存储scrollTop位置
   * */
  const scrollViewBack = () => {
    const sessionScrollTop = sessionStorage.getItem(sessionStorageKey)
    scrollViewRef.value?.scrollTo(0, Number(sessionScrollTop))
  }

  const scrollViewScroll = (e: Event) => {
    scrollTop.value = className ? (e.target as HTMLElement)?.scrollTop : (e.target as Window)?.scrollingElement.scrollTop
  }

  onUnmounted(() => {
    scrollViewRecord()
    scrollViewRef.value?.removeEventListener('scroll', scrollViewScroll)
  })

  onMounted(() => {
    scrollViewRef.value = className ? document.querySelector(`.${className}`) as HTMLElement : window
    scrollViewRef.value?.addEventListener('scroll', scrollViewScroll)
  })

  return {
    scrollViewRef,
    scrollViewRecord,
    scrollViewBack
  }
}
