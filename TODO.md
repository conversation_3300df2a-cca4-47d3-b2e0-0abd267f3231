# TODO
- `useZIndex` 管理悬浮、弹窗类组件
- 加强 ts 类型标注
  - 接口ts类型
  - 避免使用any
- 加密重构到 `server/middleware`
- `<NuxtIsland>`组件
- `useFetch` 迁移到 `useLazyFetch`
- 异步加载组件



库课云C端V3.9-会员二期&打卡 （进度：10%）
1. feat：用户登录token验证逻辑调整
2. feat：移动端顶部导航组件样式四功能对接
3. feat：同时有顶部导航、学习目标和分类导航时，顶部导航、学习目标和分类导航需要吸顶
4. fix：首页点击选择学习目标逻辑
5. fix：个人中心、学习中心、自定义页面不展示顶部导航的问题


库课云C端V3.9-会员二期&打卡 （进度：15%，风险：暂无）
1. feat：m端学习计划课时、学习资料和作业从竖向排列改为横向tab切换；
2. feat：展开每个阶段时，每个课时tab下的章节课时目录，默认需要全部展开 
3. feat: UI优化--首页改版全国分校、学习中心课程有更新
4. fix：个人中心、学习中心、自定义页面不展示顶部导航的问题


库课云C端V3.9-会员二期&打卡 （进度：25%，风险：暂无）
1. feat(接口对接): m端首页分类导航接口对接
2. feat：合力客服-显示用户在库课云展示的用户ID
3. feat: 套餐有效期：如果套餐有“学员有效期”，套餐内的题库模块有单独有效期; 套餐商品详情页，目录里的题库模块、题库模块单品去掉有效期字段展示
4. feat: 对接运营中心PC端顶部导航的样式二
5. feat: pc端底部导航组件友情链接


库课云C端V3.9-会员二期&打卡 （进度：35%）
feat：顶部导航组件、分类导航组件联调
feat：m端打卡--微信和小程序推广规则
feat: m端顶部导航样式四支持打开学习目标选择弹窗
perf: 分类导航切换支持loading效果，组件父盒子高度未撑开导致的图片展示问题



库课网校学生端V3.9-会员二期&打卡

本周工作
1. 完成m端、pc端顶部导航功能对接
2. 完成m端分类导航功能和接口对接
3. 打卡需求-打卡活动排行榜页面实现
4. 打卡需求-打卡推广功能实现
5. 打卡首页--签到打卡模块开发
6. 打卡首页--连续打卡、累计打卡
7. m端学习详情页-学习计划课时、学习资料和作业数据结构及tab切换功能

下周计划
1. 打卡活动排行榜页面接口对接和联调
2. 打卡--我的奖励接口对接和联调
3. 会员模块--会员日福利、人工答疑权益接口对接和联调
4. 打卡模块--首页相关接口对接和联调

开发进度：40%
风险：暂无