// Generated by auto imports
export {}
declare global {
  const AUDITION_VALIDITY: typeof import('../../../base/constants/index')['AUDITION_VALIDITY']
  const ActionEnum: typeof import('../../../base/constants/index')['ActionEnum']
  const ActivityType: typeof import('../../../base/constants/index')['ActivityType']
  const ActivityTypeName: typeof import('../../../base/constants/index')['ActivityTypeName']
  const AnswerStatus: typeof import('../../../base/constants/index')['AnswerStatus']
  const ApisAddress: typeof import('../../apis/address/index')['ApisAddress']
  const ApisCashier: typeof import('../../../base/apis/cashier/index')['ApisCashier']
  const ApisCommon: typeof import('../../../base/apis/common/index')['ApisCommon']
  const ApisHome: typeof import('../../apis/home')['ApisHome']
  const ApisLc: typeof import('../../apis/learn-center/index')['ApisLc']
  const ApisLt: typeof import('../../apis/learn-target/index')['ApisLt']
  const BooleanEnum: typeof import('../../apis/types')['BooleanEnum']
  const BtnStatus: typeof import('../../../base/constants/index')['BtnStatus']
  const CATE_ID_MAP: typeof import('../../../base/constants/db-redirect-kuke99')['CATE_ID_MAP']
  const CHINESE_NUMBERS: typeof import('../../../base/constants/index')['CHINESE_NUMBERS']
  const CITY_TREE_MAP: typeof import('../../../base/constants/db-redirect-kuke99')['CITY_TREE_MAP']
  const CLIENT_TYPE: typeof import('../../../base/utils/is')['CLIENT_TYPE']
  const CUSTOMIZED_TENANTS: typeof import('../../../base/composables/useCustomizedTenants')['CUSTOMIZED_TENANTS']
  const ClassMode: typeof import('../../types/study-plan')['ClassMode']
  const Classify: typeof import('../../composables/Classify')['Classify']
  const ClientType: typeof import('../../../base/constants/index')['ClientType']
  const DAY_999: typeof import('../../../base/utils/index')['DAY_999']
  const DEFAULT_INDEX: typeof import('../../../base/composables/useZIndex')['DEFAULT_INDEX']
  const Dialog: typeof import('../../composables/Dialog')['Dialog']
  const DicSkipId: typeof import('../../../base/constants/index')['DicSkipId']
  const DoStatus: typeof import('../../types/homework')['DoStatus']
  const EXTERNAL_USER_ID: typeof import('../../../base/constants/index')['EXTERNAL_USER_ID']
  const ExamAnswerType: typeof import('../../../base/constants/index')['ExamAnswerType']
  const ExamQuestionType: typeof import('../../../base/constants/index')['ExamQuestionType']
  const ExciseLabelEnum: typeof import('../../../base/constants/index')['ExciseLabelEnum']
  const ExciseModeEnum: typeof import('../../../base/constants/index')['ExciseModeEnum']
  const ExerciseModelPopup: typeof import('../../composables/ExerciseModelPopup')['ExerciseModelPopup']
  const FileSource: typeof import('../../types/homework-files')['FileSource']
  const GetCodeType: typeof import('../../../base/constants/index')['GetCodeType']
  const HOURS_24: typeof import('../../../base/utils/index')['HOURS_24']
  const HomeworkModel: typeof import('../../types/homework')['HomeworkModel']
  const INNERNAL_SYSTEM: typeof import('../../../base/constants/index')['INNERNAL_SYSTEM']
  const IS_AUDITION: typeof import('../../../base/constants/index')['IS_AUDITION']
  const IS_DEBUG_ENV: typeof import('../../../base/constants/index')['IS_DEBUG_ENV']
  const IS_DEV_ENV: typeof import('../../../base/constants/index')['IS_DEV_ENV']
  const IS_LOCAL_ENV: typeof import('../../../base/constants/index')['IS_LOCAL_ENV']
  const IS_STAGING_ENV: typeof import('../../../base/constants/index')['IS_STAGING_ENV']
  const LOC_SPEC_ID: typeof import('../../../base/constants/index')['LOC_SPEC_ID']
  const LT_LOCAL_KEY: typeof import('../../../base/constants/index')['LT_LOCAL_KEY']
  const LT_SEARCH_KEY: typeof import('../../../base/constants/index')['LT_SEARCH_KEY']
  const LearnStatus: typeof import('../../types/homework-files')['LearnStatus']
  const Loading: typeof import('../../../base/composables/Loading')['Loading']
  const MINS_60: typeof import('../../../base/utils/index')['MINS_60']
  const MarketingOrderTagMap: typeof import('../../../base/constants/index')['MarketingOrderTagMap']
  const MarketingOrderTagStatus: typeof import('../../../base/constants/index')['MarketingOrderTagStatus']
  const Message: typeof import('../../../base/composables/Message')['Message']
  const NEW_CATEGORY_LIST: typeof import('../../../base/constants/db-redirect-kuke99')['NEW_CATEGORY_LIST']
  const NetCode: typeof import('../../../base/constants/index')['NetCode']
  const OrderAction: typeof import('../../../base/constants/index')['OrderAction']
  const OrderStatus: typeof import('../../../base/constants/index')['OrderStatus']
  const OuterRelationType: typeof import('../../types/homework-files')['OuterRelationType']
  const POOL_ID: typeof import('../../../base/constants/index')['POOL_ID']
  const PROMOTION_SHARE_KEY: typeof import('../../../base/constants/index')['PROMOTION_SHARE_KEY']
  const PROMOTION_SHARE_URL: typeof import('../../../base/constants/index')['PROMOTION_SHARE_URL']
  const PayStatus: typeof import('../../../base/constants/index')['PayStatus']
  const QuestionDoStatusAlias: typeof import('../../../base/constants/questionBankType')['QuestionDoStatusAlias']
  const QuestionDoStatusFromLearnStatusName: typeof import('../../../base/constants/questionBankType')['QuestionDoStatusFromLearnStatusName']
  const SALE_ADMIN_ID: typeof import('../../../base/constants/index')['SALE_ADMIN_ID']
  const SECS_60: typeof import('../../../base/utils/index')['SECS_60']
  const SHARE_LINK_ID: typeof import('../../../base/constants/index')['SHARE_LINK_ID']
  const SHARE_PRICE: typeof import('../../../base/constants/index')['SHARE_PRICE']
  const STUDY_TYPE: typeof import('../../../base/constants/index')['STUDY_TYPE']
  const STUDY_TYPE__COURSE: typeof import('../../../base/constants/index')['STUDY_TYPE__COURSE']
  const STUDY_TYPE__TEST_QUESTIONS: typeof import('../../../base/constants/index')['STUDY_TYPE__TEST_QUESTIONS']
  const SYSTEM_TYPE: typeof import('../../../base/constants/index')['SYSTEM_TYPE']
  const ScoringWay: typeof import('../../types/homework')['ScoringWay']
  const ShoppingTypePopup: typeof import('../../composables/ShoppingTypePopup')['ShoppingTypePopup']
  const SourceEnum: typeof import('../../../base/constants/index')['SourceEnum']
  const TARGET_CODE: typeof import('../../../base/constants/index')['TARGET_CODE']
  const TeacherState: typeof import('../../types/homework')['TeacherState']
  const TextBookPopup: typeof import('../../composables/TextBookPopup')['TextBookPopup']
  const UserSelectionMode: typeof import('../../types/study-plan')['UserSelectionMode']
  const VideoType: typeof import('../../types/homework-files')['VideoType']
  const abortNavigation: typeof import('../../../node_modules/nuxt/dist/app')['abortNavigation']
  const addAudio: typeof import('../../../base/utils/audio')['addAudio']
  const addAudioUniquely: typeof import('../../../base/utils/audio')['addAudioUniquely']
  const addRouteMiddleware: typeof import('../../../node_modules/nuxt/dist/app')['addRouteMiddleware']
  const allowMultipleToast: typeof import('../../../node_modules/vant/lib/vant.cjs')['allowMultipleToast']
  const apiGetHome: typeof import('../../apis/home')['apiGetHome']
  const apis: typeof import('../../apis/index')['default']
  const assert: typeof import('../../../base/utils/is')['assert']
  const audioRegex: typeof import('../../../base/utils/audio')['audioRegex']
  const base642File: typeof import('../../../base/composables/fileChange')['base642File']
  const base64toBlob: typeof import('../../../base/utils/file')['base64toBlob']
  const buildUUID: typeof import('../../../base/utils/uuid')['buildUUID']
  const buyEffect: typeof import('../../composables/buyEffect')['buyEffect']
  const calculateTimeDifference: typeof import('../../../base/utils/index')['calculateTimeDifference']
  const cancelIdleCallback: typeof import('../../../node_modules/nuxt/dist/app')['cancelIdleCallback']
  const clamp: typeof import('../../../base/utils/is')['clamp']
  const clearError: typeof import('../../../node_modules/nuxt/dist/app')['clearError']
  const clearNuxtData: typeof import('../../../node_modules/nuxt/dist/app')['clearNuxtData']
  const clearNuxtState: typeof import('../../../node_modules/nuxt/dist/app')['clearNuxtState']
  const closeDialog: typeof import('../../../node_modules/vant/lib/vant.cjs')['closeDialog']
  const closeNotify: typeof import('../../../node_modules/vant/lib/vant.cjs')['closeNotify']
  const closeToast: typeof import('../../../node_modules/vant/lib/vant.cjs')['closeToast']
  const compareTagParams: typeof import('../../../base/utils/questionTools')['compareTagParams']
  const computed: typeof import('../../../node_modules/vue')['computed']
  const countFunction: typeof import('../../../base/composables/countdown')['countFunction']
  const createDecipher: typeof import('../../../base/utils/video')['createDecipher']
  const createError: typeof import('../../../node_modules/nuxt/dist/app')['createError']
  const customRef: typeof import('../../../node_modules/vue')['customRef']
  const debounceFunc: typeof import('../../../base/utils/debounce')['debounceFunc']
  const debounceFuncPlus: typeof import('../../../base/utils/debounce')['debounceFuncPlus']
  const decryptData: typeof import('../../../base/utils/video')['decryptData']
  const deepClone: typeof import('../../../base/utils/deepClone')['deepClone']
  const defaultNamespace: typeof import('../../../base/composables/useNamespace')['defaultNamespace']
  const defineAppConfig: typeof import('../../../node_modules/nuxt/dist/app')['defineAppConfig']
  const defineAsyncComponent: typeof import('../../../node_modules/vue')['defineAsyncComponent']
  const defineComponent: typeof import('../../../node_modules/vue')['defineComponent']
  const defineModel: typeof import('../../../node_modules/vue')['defineModel']
  const defineNuxtComponent: typeof import('../../../node_modules/nuxt/dist/app')['defineNuxtComponent']
  const defineNuxtLink: typeof import('../../../node_modules/nuxt/dist/app')['defineNuxtLink']
  const defineNuxtPlugin: typeof import('../../../node_modules/nuxt/dist/app')['defineNuxtPlugin']
  const defineNuxtRouteMiddleware: typeof import('../../../node_modules/nuxt/dist/app')['defineNuxtRouteMiddleware']
  const defineOptions: typeof import('../../../node_modules/vue')['defineOptions']
  const definePageMeta: typeof import('../../../node_modules/nuxt/dist/pages/runtime/composables')['definePageMeta']
  const definePayloadPlugin: typeof import('../../../node_modules/nuxt/dist/app')['definePayloadPlugin']
  const definePayloadReducer: typeof import('../../../node_modules/nuxt/dist/app')['definePayloadReducer']
  const definePayloadReviver: typeof import('../../../node_modules/nuxt/dist/app')['definePayloadReviver']
  const defineSlots: typeof import('../../../node_modules/vue')['defineSlots']
  const delAddress: typeof import('../../apis/address/index')['delAddress']
  const downLoadFileRename: typeof import('../../../base/composables/common')['downLoadFileRename']
  const downloadFile: typeof import('../../../base/composables/common')['downloadFile']
  const dragFun: typeof import('../../composables/dragFun')['dragFun']
  const editAddress: typeof import('../../apis/address/index')['editAddress']
  const effect: typeof import('../../../node_modules/vue')['effect']
  const effectScope: typeof import('../../../node_modules/vue')['effectScope']
  const emitter: typeof import('../../../base/utils/mitt')['emitter']
  const extractedAudio: typeof import('../../../base/utils/audio')['extractedAudio']
  const fetchAddOrdersBehaviorRecord: typeof import('../../../base/composables/common')['fetchAddOrdersBehaviorRecord']
  const filterBody: typeof import('../../../base/utils/filterBody')['default']
  const flat: typeof import('../../../base/utils/tools')['flat']
  const formatDateWithDateObject: typeof import('../../../base/utils/tools')['formatDateWithDateObject']
  const formatDecimal: typeof import('../../../base/utils/number')['formatDecimal']
  const formatDuring: typeof import('../../../base/utils/index')['formatDuring']
  const formatNumberWithoutTrailingZero: typeof import('../../../base/utils/number')['formatNumberWithoutTrailingZero']
  const formatTheme: typeof import('../../../base/composables/useTheme')['formatTheme']
  const formatTimeByPattern: typeof import('../../../base/utils/tools')['formatTimeByPattern']
  const formatTimeDuration: typeof import('../../../base/utils/index')['formatTimeDuration']
  const formatTodayWatchDuration: typeof import('../../../base/utils/index')['formatTodayWatchDuration']
  const generateFilename: typeof import('../../../base/composables/useOss')['generateFilename']
  const getAddressDetail: typeof import('../../apis/address/index')['getAddressDetail']
  const getAddressList: typeof import('../../apis/address/index')['getAddressList']
  const getAdvertisementPhotoList: typeof import('../../apis/home')['getAdvertisementPhotoList']
  const getAppManifest: typeof import('../../../node_modules/nuxt/dist/app')['getAppManifest']
  const getAreaList: typeof import('../../apis/address/index')['getAreaList']
  const getArrayLast: typeof import('../../../base/utils/is')['getArrayLast']
  const getAssemblyForWx: typeof import('../../apis/home')['getAssemblyForWx']
  const getClassifyConfigEnable: typeof import('../../apis/learn-target/index')['getClassifyConfigEnable']
  const getComponentInteractiveInfo: typeof import('../../composables/useBusinessInteractive')['getComponentInteractiveInfo']
  const getCourseUpdateStatus: typeof import('../../apis/home')['getCourseUpdateStatus']
  const getCurReceivingInfo: typeof import('../../apis/address/index')['getCurReceivingInfo']
  const getCurrentInstance: typeof import('../../../node_modules/vue')['getCurrentInstance']
  const getCurrentScope: typeof import('../../../node_modules/vue')['getCurrentScope']
  const getCurrentTime: typeof import('../../../base/utils/index')['getCurrentTime']
  const getCustomPageNavigationStatus: typeof import('../../apis/home')['getCustomPageNavigationStatus']
  const getCustomPageReleaseDetails: typeof import('../../apis/home')['getCustomPageReleaseDetails']
  const getDefaultAddressProt: typeof import('../../apis/address/index')['getDefaultAddressProt']
  const getDefaultOfficialRecommend: typeof import('../../apis/home')['getDefaultOfficialRecommend']
  const getDeviceEnv: typeof import('../../../base/utils/device')['getDeviceEnv']
  const getDeviceOfUA: typeof import('../../../base/utils/is')['getDeviceOfUA']
  const getExamStatusName: typeof import('../../../base/utils/examUtils')['getExamStatusName']
  const getFormDisabled: typeof import('../../../base/composables/form')['getFormDisabled']
  const getGoodsResourceType: typeof import('../../apis/learn-center/index')['getGoodsResourceType']
  const getKukeMarketingInfo: typeof import('../../apis/home')['getKukeMarketingInfo']
  const getLaunchAd: typeof import('../../apis/home')['getLaunchAd']
  const getLearningTargetById: typeof import('../../apis/learn-target/index')['getLearningTargetById']
  const getLearningTargetDefaultId: typeof import('../../apis/learn-target/index')['getLearningTargetDefaultId']
  const getLearningTargetList: typeof import('../../apis/learn-target/index')['getLearningTargetList']
  const getLivePlatform: typeof import('../../../base/composables/useLivePlatformType')['getLivePlatform']
  const getLivePlatformByPc: typeof import('../../../base/composables/useLivePlatformType')['getLivePlatformByPc']
  const getLivePlatformByWap: typeof import('../../../base/composables/useLivePlatformType')['getLivePlatformByWap']
  const getNextDayStartTimeDate: typeof import('../../../base/composables/useAdCountdown')['getNextDayStartTimeDate']
  const getPopUpAd: typeof import('../../apis/home')['getPopUpAd']
  const getQuestionDoStatusFromLearnStatusName: typeof import('../../../base/utils/tools')['getQuestionDoStatusFromLearnStatusName']
  const getReleasePersonCenterORNavigationDetails: typeof import('../../apis/home')['getReleasePersonCenterORNavigationDetails']
  const getRouteRules: typeof import('../../../node_modules/nuxt/dist/app')['getRouteRules']
  const getRuleListByIdParams: typeof import('../../apis/home')['getRuleListByIdParams']
  const getStudyPlanStageInfoProt: typeof import('../../apis/learn-center/index')['getStudyPlanStageInfoProt']
  const getUnitStatusProt: typeof import('../../apis/learn-center/index')['getUnitStatusProt']
  const getUserLearningTarget: typeof import('../../apis/learn-target/index')['getUserLearningTarget']
  const globalVariable: typeof import('../../../base/utils/globalVariable')['default']
  const h: typeof import('../../../node_modules/vue')['h']
  const handleClipboard: typeof import('../../../base/utils/clipboard')['handleClipboard']
  const handleMarketingInfo: typeof import('../../composables/handleMarketingInfo')['default']
  const handleOrderMarketingInfo: typeof import('../../composables/handleOrderMarketingInfo')['default']
  const handleQuestionTitle: typeof import('../../../base/utils/tools')['handleQuestionTitle']
  const handleToPaperPage: typeof import('../../../base/utils/examUtils')['handleToPaperPage']
  const handleWeChatPreview: typeof import('../../composables/useWeChat')['handleWeChatPreview']
  const hasInjectionContext: typeof import('../../../node_modules/vue')['hasInjectionContext']
  const hasOwn: typeof import('../../../base/utils/is')['hasOwn']
  const hexToRgb: typeof import('../../../base/utils/index')['hexToRgb']
  const informationLabelMap: typeof import('../../../base/constants/informationLabelMap')['informationLabelMap']
  const initOSS: typeof import('../../../base/composables/useOss')['initOSS']
  const inject: typeof import('../../../node_modules/vue')['inject']
  const injectHead: typeof import('../../../node_modules/@unhead/vue')['injectHead']
  const isClient: typeof import('../../../base/utils/is')['isClient']
  const isDdefine: typeof import('../../../base/utils/is')['isDdefine']
  const isDef: typeof import('../../../base/utils/is')['isDef']
  const isDefined: typeof import('../../../base/utils/is')['isDefined']
  const isDingTalk6_5: typeof import('../../../base/utils/is')['isDingTalk6_5']
  const isDingTalk: typeof import('../../../base/utils/is')['isDingTalk']
  const isFillQuestion: typeof import('../../../base/utils/tools')['isFillQuestion']
  const isIOS: typeof import('../../../base/utils/is')['isIOS']
  const isIframe: typeof import('../../../base/utils/is')['isIframe']
  const isInteger: typeof import('../../composables/index')['isInteger']
  const isKukeCloud: typeof import('../../../base/composables/common')['isKukeCloud']
  const isKukeCloudAppWebviewBrowser: typeof import('../../../base/utils/is')['isKukeCloudAppWebviewBrowser']
  const isMaterial: typeof import('../../../base/utils/tools')['isMaterial']
  const isMultipleChoice: typeof import('../../../base/utils/tools')['isMultipleChoice']
  const isNuxtError: typeof import('../../../node_modules/nuxt/dist/app')['isNuxtError']
  const isOpenBankAnalyze: typeof import('../../../base/utils/tools')['isOpenBankAnalyze']
  const isPC: typeof import('../../../base/utils/is')['isPC']
  const isPrerendered: typeof import('../../../node_modules/nuxt/dist/app')['isPrerendered']
  const isProxy: typeof import('../../../node_modules/vue')['isProxy']
  const isQQBrowser: typeof import('../../../base/utils/is')['isQQBrowser']
  const isQQBrowserWebView: typeof import('../../../base/utils/is')['isQQBrowserWebView']
  const isQuarkBrowser: typeof import('../../../base/utils/is')['isQuarkBrowser']
  const isRadioChoice: typeof import('../../../base/utils/tools')['isRadioChoice']
  const isReactive: typeof import('../../../node_modules/vue')['isReactive']
  const isReadonly: typeof import('../../../node_modules/vue')['isReadonly']
  const isRef: typeof import('../../../node_modules/vue')['isRef']
  const isShallow: typeof import('../../../node_modules/vue')['isShallow']
  const isShowBankAnalyze: typeof import('../../../base/utils/tools')['isShowBankAnalyze']
  const isSogouBrowser: typeof import('../../../base/utils/is')['isSogouBrowser']
  const isUCBrowser: typeof import('../../../base/utils/is')['isUCBrowser']
  const isVivoBrowser: typeof import('../../../base/utils/is')['isVivoBrowser']
  const isVue2: typeof import('../../../node_modules/nuxt/dist/app/compat/vue-demi')['isVue2']
  const isVue3: typeof import('../../../node_modules/nuxt/dist/app/compat/vue-demi')['isVue3']
  const isWAP: typeof import('../../../base/utils/is')['isWAP']
  const isWxBrowser: typeof import('../../../base/utils/is')['isWxBrowser']
  const isXcxBrowser: typeof import('../../../base/utils/is')['isXcxBrowser']
  const judgePageMasterIdValid: typeof import('../../apis/learn-target/index')['judgePageMasterIdValid']
  const kgUserStudyPlanSelectionSwitchProt: typeof import('../../apis/learn-center/index')['kgUserStudyPlanSelectionSwitchProt']
  const loadPayload: typeof import('../../../node_modules/nuxt/dist/app')['loadPayload']
  const loggerError: typeof import('../../../base/composables/useHttp')['loggerError']
  const markDownItKateX: typeof import('../../composables/markDownItKateX')['markDownItKateX']
  const markRaw: typeof import('../../../node_modules/vue')['markRaw']
  const maskedMobileNumber: typeof import('../../../base/composables/isRegexp')['maskedMobileNumber']
  const mergeModels: typeof import('../../../node_modules/vue')['mergeModels']
  const moneyInput: typeof import('../../../base/composables/isRegexp')['moneyInput']
  const navigateTo: typeof import('../../../node_modules/nuxt/dist/app')['navigateTo']
  const nextTick: typeof import('../../../node_modules/vue')['nextTick']
  const noop: typeof import('../../../base/utils/is')['noop']
  const notNullish: typeof import('../../../base/utils/is')['notNullish']
  const now: typeof import('../../../base/utils/is')['now']
  const numberToChinese: typeof import('../../../base/utils/number')['numberToChinese']
  const objectToUrlParams: typeof import('../../../base/utils/index')['objectToUrlParams']
  const onActivated: typeof import('../../../node_modules/vue')['onActivated']
  const onBeforeMount: typeof import('../../../node_modules/vue')['onBeforeMount']
  const onBeforeRouteLeave: typeof import('../vue-router')['onBeforeRouteLeave']
  const onBeforeRouteUpdate: typeof import('../vue-router')['onBeforeRouteUpdate']
  const onBeforeUnmount: typeof import('../../../node_modules/vue')['onBeforeUnmount']
  const onBeforeUpdate: typeof import('../../../node_modules/vue')['onBeforeUpdate']
  const onDeactivated: typeof import('../../../node_modules/vue')['onDeactivated']
  const onErrorCaptured: typeof import('../../../node_modules/vue')['onErrorCaptured']
  const onMounted: typeof import('../../../node_modules/vue')['onMounted']
  const onNuxtReady: typeof import('../../../node_modules/nuxt/dist/app')['onNuxtReady']
  const onRenderTracked: typeof import('../../../node_modules/vue')['onRenderTracked']
  const onRenderTriggered: typeof import('../../../node_modules/vue')['onRenderTriggered']
  const onScopeDispose: typeof import('../../../node_modules/vue')['onScopeDispose']
  const onServerPrefetch: typeof import('../../../node_modules/vue')['onServerPrefetch']
  const onUnmounted: typeof import('../../../node_modules/vue')['onUnmounted']
  const onUpdated: typeof import('../../../node_modules/vue')['onUpdated']
  const orderEffect: typeof import('../../composables/useOrder')['orderEffect']
  const os: typeof import('../../../base/composables/common')['os']
  const passwordReg: typeof import('../../../base/composables/isRegexp')['passwordReg']
  const pauseAudio: typeof import('../../../base/utils/audio')['pauseAudio']
  const persistedState: typeof import('../../../node_modules/@pinia-plugin-persistedstate/nuxt/dist/runtime/storages')['persistedState']
  const postAddress: typeof import('../../apis/address/index')['postAddress']
  const postBodyEncryption: typeof import('../../../base/composables/usePostBodyEncryPtion')['postBodyEncryption']
  const prefetchComponents: typeof import('../../../node_modules/nuxt/dist/app')['prefetchComponents']
  const preloadComponents: typeof import('../../../node_modules/nuxt/dist/app')['preloadComponents']
  const preloadPayload: typeof import('../../../node_modules/nuxt/dist/app')['preloadPayload']
  const preloadRouteComponents: typeof import('../../../node_modules/nuxt/dist/app')['preloadRouteComponents']
  const productSideEnum: typeof import('../../../base/constants/index')['productSideEnum']
  const provide: typeof import('../../../node_modules/vue')['provide']
  const proxyRefs: typeof import('../../../node_modules/vue')['proxyRefs']
  const px2rem: typeof import('../../../base/utils/px2rem')['default']
  const queryProductDetailThirdList: typeof import('../../apis/home')['queryProductDetailThirdList']
  const questionBankBack: typeof import('../../composables/useQuestionBankBack')['questionBankBack']
  const questionBankBackForApp: typeof import('../../composables/useQuestionBackForApp')['questionBankBackForApp']
  const rand: typeof import('../../../base/utils/is')['rand']
  const reactive: typeof import('../../../node_modules/vue')['reactive']
  const readonly: typeof import('../../../node_modules/vue')['readonly']
  const receiveCoupon: typeof import('../../composables/receiveCoupon')['receiveCoupon']
  const recursion: typeof import('../../../base/composables/form')['recursion']
  const redirectKuke99Url: typeof import('../../../base/utils/redirectKuke99Url')['redirectKuke99Url']
  const ref: typeof import('../../../node_modules/vue')['ref']
  const refreshNuxtData: typeof import('../../../node_modules/nuxt/dist/app')['refreshNuxtData']
  const regPhone: typeof import('../../../base/composables/isRegexp')['regPhone']
  const reloadNuxtApp: typeof import('../../../node_modules/nuxt/dist/app')['reloadNuxtApp']
  const removeAudio: typeof import('../../../base/utils/audio')['removeAudio']
  const removeUpdateRecordProt: typeof import('../../apis/home')['removeUpdateRecordProt']
  const requestIdleCallback: typeof import('../../../node_modules/nuxt/dist/app')['requestIdleCallback']
  const resetDialogDefaultOptions: typeof import('../../../node_modules/vant/lib/vant.cjs')['resetDialogDefaultOptions']
  const resetNotifyDefaultOptions: typeof import('../../../node_modules/vant/lib/vant.cjs')['resetNotifyDefaultOptions']
  const resetToastDefaultOptions: typeof import('../../../node_modules/vant/lib/vant.cjs')['resetToastDefaultOptions']
  const resolveComponent: typeof import('../../../node_modules/vue')['resolveComponent']
  const saveLearningTarget: typeof import('../../apis/learn-target/index')['saveLearningTarget']
  const setDialogDefaultOptions: typeof import('../../../node_modules/vant/lib/vant.cjs')['setDialogDefaultOptions']
  const setNotifyDefaultOptions: typeof import('../../../node_modules/vant/lib/vant.cjs')['setNotifyDefaultOptions']
  const setPageLayout: typeof import('../../../node_modules/nuxt/dist/app')['setPageLayout']
  const setResponseStatus: typeof import('../../../node_modules/nuxt/dist/app')['setResponseStatus']
  const setToastDefaultOptions: typeof import('../../../node_modules/vant/lib/vant.cjs')['setToastDefaultOptions']
  const shallowReactive: typeof import('../../../node_modules/vue')['shallowReactive']
  const shallowReadonly: typeof import('../../../node_modules/vue')['shallowReadonly']
  const shallowRef: typeof import('../../../node_modules/vue')['shallowRef']
  const showConfirmDialog: typeof import('../../../node_modules/vant/lib/vant.cjs')['showConfirmDialog']
  const showDialog: typeof import('../../../node_modules/vant/lib/vant.cjs')['showDialog']
  const showError: typeof import('../../../node_modules/nuxt/dist/app')['showError']
  const showFailToast: typeof import('../../../node_modules/vant/lib/vant.cjs')['showFailToast']
  const showImagePreview: typeof import('../../../node_modules/vant/lib/vant.cjs')['showImagePreview']
  const showLoadingToast: typeof import('../../../node_modules/vant/lib/vant.cjs')['showLoadingToast']
  const showMoreArrow: typeof import('../../../base/composables/showMoreArrow')['showMoreArrow']
  const showNotify: typeof import('../../../node_modules/vant/lib/vant.cjs')['showNotify']
  const showSuccessToast: typeof import('../../../node_modules/vant/lib/vant.cjs')['showSuccessToast']
  const showToast: typeof import('../../../node_modules/vant/lib/vant.cjs')['showToast']
  const splitNumber: typeof import('../../../base/utils/number')['splitNumber']
  const statisticalPublicAdvertExposure: typeof import('../../apis/learn-target/index')['statisticalPublicAdvertExposure']
  const teacherInfoEffect: typeof import('../../composables/useTeacherInfo')['teacherInfoEffect']
  const timeKeepingShow: typeof import('../../composables/timeKeepingShow')['timeKeepingShow']
  const timestamp: typeof import('../../../base/utils/is')['timestamp']
  const toAnswerBase64: typeof import('../../../base/utils/tools')['toAnswerBase64']
  const toBase64: typeof import('../../../base/utils/tools')['toBase64']
  const toChineseNum: typeof import('../../../base/utils/tools')['toChineseNum']
  const toRaw: typeof import('../../../node_modules/vue')['toRaw']
  const toRef: typeof import('../../../node_modules/vue')['toRef']
  const toRefs: typeof import('../../../node_modules/vue')['toRefs']
  const toValue: typeof import('../../../node_modules/vue')['toValue']
  const triggerRef: typeof import('../../../node_modules/vue')['triggerRef']
  const unref: typeof import('../../../node_modules/vue')['unref']
  const updateAppConfig: typeof import('../../../node_modules/nuxt/dist/app')['updateAppConfig']
  const updateBehaviorRecords: typeof import('../../../base/composables/common')['updateBehaviorRecords']
  const updateDefaultAddress: typeof import('../../apis/address/index')['updateDefaultAddress']
  const useAdCountdownRAF: typeof import('../../../base/composables/useAdCountdown')['useAdCountdownRAF']
  const useAddQuestionBankOrderBehaviorRecords: typeof import('../../../base/composables/useAddQuestionBankOrderBehaviorRecords')['useAddQuestionBankOrderBehaviorRecords']
  const useAnswerService: typeof import('../../composables/useAnswerService')['useAnswerService']
  const useApi: typeof import('../../composables/index')['useApi']
  const useApiBaseUrl: typeof import('../../../base/composables/useApiBaseUrl')['default']
  const useAppConfig: typeof import('../../../node_modules/nuxt/dist/app')['useAppConfig']
  const useAsyncData: typeof import('../../../node_modules/nuxt/dist/app')['useAsyncData']
  const useAttrs: typeof import('../../../node_modules/vue')['useAttrs']
  const useAuditionValidity: typeof import('../../../base/composables/useAuditionValidity')['default']
  const useBeforeunload: typeof import('../../../base/composables/useBeforeunload')['useBeforeunload']
  const useBehaviorRecord: typeof import('../../../base/composables/useBehaviorRecord')['useBehaviorRecord']
  const useBem: typeof import('../../../base/composables/useBem')['useBem']
  const useBusinessComp: typeof import('../../../base/composables/useBusinessComp')['default']
  const useCaptchaVerify: typeof import('../../../base/composables/useCaptchaVerify')['default']
  const useCheckSchool: typeof import('../../composables/useCheckSchool')['useCheckSchool']
  const useCheckSchoolNotButtonStatus: typeof import('../../composables/useCheckSchool')['useCheckSchoolNotButtonStatus']
  const useCheckSchoolNotVerifyLogin: typeof import('../../composables/useCheckSchool')['useCheckSchoolNotVerifyLogin']
  const useCluesExtension: typeof import('../../../base/composables/useCluesExtension')['default']
  const useCompatibleIOSBlur: typeof import('../../composables/useCompatibleIOSBlur')['useCompatibleIOSBlur']
  const useComponentEvents: typeof import('../../composables/useComponentEvents')['useComponentEvents']
  const useComponentEventsHome: typeof import('../../composables/useComponentEventsHome')['useComponentEventsHome']
  const useComputedQuestionCount: typeof import('../../../base/composables/useComputedQuestionCount')['useComputedQuestionCount']
  const useCookie: typeof import('../../../node_modules/nuxt/dist/app')['useCookie']
  const useCourseId: typeof import('../../../base/composables/useCourseId')['default']
  const useCoursePayBtnText: typeof import('../../../base/composables/useCoursePayBtnText')['default']
  const useCourseUpdateStatus: typeof import('../../composables/useCourseUpdateStatus')['useCourseUpdateStatus']
  const useCpClueSource: typeof import('../../composables/useCpClueSource')['useCpClueSource']
  const useCrmSharePrice: typeof import('../../../base/composables/useCrmSharePrice')['default']
  const useCssModule: typeof import('../../../node_modules/vue')['useCssModule']
  const useCssVars: typeof import('../../../node_modules/vue')['useCssVars']
  const useCustomPageWap: typeof import('../../composables/useCustomPageWap')['default']
  const useCustomizedTenants: typeof import('../../../base/composables/useCustomizedTenants')['default']
  const useDeviceDetection: typeof import('../../../base/composables/useDeviceDetection')['default']
  const useDownloadZip: typeof import('../../composables/useDownloadZip')['useDownloadZip']
  const useError: typeof import('../../../node_modules/nuxt/dist/app')['useError']
  const useFetch: typeof import('../../../node_modules/nuxt/dist/app')['useFetch']
  const useFetchOptions: typeof import('../../../base/composables/useFetchOptions')['default']
  const useFileView: typeof import('../../../base/composables/useFileView')['useFileView']
  const useGetAgentQuery: typeof import('../../composables/useGetAgentQuery')['default']
  const useGetProductSide: typeof import('../../../base/composables/usePageTrack')['useGetProductSide']
  const useHeLiKefu: typeof import('../../composables/useHeLiKefu')['useHeLiKefu']
  const useHead: typeof import('../../../node_modules/@unhead/vue')['useHead']
  const useHeadSafe: typeof import('../../../node_modules/@unhead/vue')['useHeadSafe']
  const useHistoryState: typeof import('../../../base/composables/useHistoryState')['useHistoryState']
  const useHttp: typeof import('../../../base/composables/useHttp')['useHttp']
  const useHttpClient: typeof import('../../../base/composables/useHttpClient')['useHttpClient']
  const useJumpQuestionPurchased: typeof import('../../composables/useJumpQuestionPurchased')['useJumpQuestionPurchased']
  const useKKNews: typeof import('../../composables/useBusinessInteractive')['useKKNews']
  const useKeyboardOpen: typeof import('../../composables/useKeyboardOpen')['useKeyboardOpen']
  const useKuKeCloud: typeof import('../../../base/composables/useKuKeCloud')['useKuKeCloud']
  const useKuke99DefaultHomeData: typeof import('../../composables/index')['useKuke99DefaultHomeData']
  const useLabelSwitch: typeof import('../../composables/useLabelSwitch')['default']
  const useLazyAsyncData: typeof import('../../../node_modules/nuxt/dist/app')['useLazyAsyncData']
  const useLazyFetch: typeof import('../../../node_modules/nuxt/dist/app')['useLazyFetch']
  const useLearnTarget: typeof import('../../../base/composables/useLearnTarget')['useLearnTarget']
  const useLearnTargetFullpath: typeof import('../../../base/composables/useLearnTargetFullpath')['useLearnTargetFullpath']
  const useLearnTargetState: typeof import('../../../base/composables/useLearnTargetState')['useLearnTargetState']
  const useLearnTargetTree: typeof import('../../../base/composables/useLearnTarget')['useLearnTargetTree']
  const useLink: typeof import('../vue-router')['useLink']
  const useLocalStorage: typeof import('../../../base/composables/useAdCountdown')['useLocalStorage']
  const useMemberUtils: typeof import('../../composables/useUserMember')['useMemberUtils']
  const useMemberWxShareInfo: typeof import('../../composables/useUserMember')['useMemberWxShareInfo']
  const useMessage: typeof import('../../../base/composables/useMessage')['useMessage']
  const useModel: typeof import('../../../node_modules/vue')['useModel']
  const useNamespace: typeof import('../../../base/composables/useNamespace')['useNamespace']
  const useNuxtApp: typeof import('../../../node_modules/nuxt/dist/app')['useNuxtApp']
  const useNuxtData: typeof import('../../../node_modules/nuxt/dist/app')['useNuxtData']
  const useOnline: typeof import('../../../base/composables/useOnline')['useOnline']
  const useOrderSns: typeof import('../../composables/useOrderSns')['useOrderSns']
  const usePageTrack: typeof import('../../../base/composables/usePageTrack')['usePageTrack']
  const usePinia: typeof import('../../../node_modules/@pinia/nuxt/dist/runtime/composables')['usePinia']
  const usePointsClueSource: typeof import('../../composables/useCpClueSource')['usePointsClueSource']
  const usePreventDefaultLink: typeof import('../../composables/usePreventDefaultLink')['usePreventDefaultLink']
  const useQuestSortStem: typeof import('../../../base/composables/useQuestionBank')['useQuestSortStem']
  const useQuestionBank: typeof import('../../../base/composables/useQuestionBank')['useQuestionBank']
  const useQuestionBankClassifyInfo: typeof import('../../composables/useQuestionBankClassifyInfo')['useQuestionBankClassifyInfo']
  const useQuestionBankDataChange: typeof import('../../composables/useQuestionBankDataChange')['useQuestionBankDataChange']
  const useQuestionBankFillJudge: typeof import('../../../base/composables/useQuestionBankFillJudge')['useQuestionBankFillJudge']
  const useQuestionBankHandleEvents: typeof import('../../composables/useQuestionBankHandleEvents')['useQuestionBankHandleEvents']
  const useQuestionBankHelp: typeof import('../../../base/composables/useQuestionBankHelp')['useQuestionBankHelp']
  const useQuestionBankInit: typeof import('../../composables/useQuestionBankInit')['useQuestionBankInit']
  const useQuestionBankInitQuery: typeof import('../../composables/useQuestionBankInitQuery')['useQuestionBankInitQuery']
  const useQuestionBankList: typeof import('../../composables/useQuestionBankList')['useQuestionBankList']
  const useQuestionBankModuleIcon: typeof import('../../composables/useQuestionBankModuleIcon')['useQuestionBankModuleIcon']
  const useQuestionBankModuleInfo: typeof import('../../composables/useQuestionBankModuleInfo')['useQuestionBankModuleInfo']
  const useQuestionBankModulePrivilege: typeof import('../../composables/useQuestionBankModulePrivilege')['useQuestionBankModulePrivilege']
  const useQuestionBankMultipleTag: typeof import('../../composables/useQuestionBankMultipleTag')['useQuestionBankMultipleTag']
  const useQuestionBankPromotion: typeof import('../../../base/composables/useQuestionBankPromotion')['useQuestionBankPromotion']
  const useQuestionBankTag: typeof import('../../composables/useQuestionBankTag')['useQuestionBankTag']
  const useQuestionCorrected: typeof import('../../../base/composables/useQuestionCorrected')['useQuestionCorrected']
  const useQuestionExamModel: typeof import('../../../base/composables/useQuestionExamModel')['useQuestionExamModel']
  const useQuestionFilter: typeof import('../../composables/useQuestionFiltervajra')['useQuestionFilter']
  const useQuestionLandingPage: typeof import('../../composables/useQuestionLandingPage')['useQuestionLandingPage']
  const useQuestionListIsWhite: typeof import('../../composables/useQuestionListIsWhite')['useQuestionListIsWhite']
  const useQuestionPurchasedModuleTag: typeof import('../../composables/useQuestionPurchasedModuleTag')['useQuestionPurchasedModuleTag']
  const useQuestionVideoInfo: typeof import('../../../base/composables/useQuestionVideoInfo')['useQuestionVideoInfo']
  const useRecorder: typeof import('../../../base/composables/useRecorder')['useRecorder']
  const useRefreshQuestionListData: typeof import('../../composables/useRefreshQuestionListData')['useRefreshQuestionListData']
  const useRequestEvent: typeof import('../../../node_modules/nuxt/dist/app')['useRequestEvent']
  const useRequestFetch: typeof import('../../../node_modules/nuxt/dist/app')['useRequestFetch']
  const useRequestHeaders: typeof import('../../../node_modules/nuxt/dist/app')['useRequestHeaders']
  const useRequestURL: typeof import('../../../node_modules/nuxt/dist/app')['useRequestURL']
  const useRoute: typeof import('../../../node_modules/nuxt/dist/app')['useRoute']
  const useRouteLeaveGuard: typeof import('../../../base/composables/useNewBeforeUnload')['useRouteLeaveGuard']
  const useRouter: typeof import('../../../node_modules/nuxt/dist/app')['useRouter']
  const useRuntimeConfig: typeof import('../../../node_modules/nuxt/dist/app')['useRuntimeConfig']
  const useSKU: typeof import('../../composables/useSKU')['default']
  const useScrollPenetrate: typeof import('../../../base/composables/useScrollPenetrate')['useScrollPenetrate']
  const useScrollViewBack: typeof import('../../composables/useScrollViewBack')['useScrollViewBack']
  const useSelectExam: typeof import('../../composables/useSelectExam')['useSelectExam']
  const useSendBeacon: typeof import('../../../base/composables/useSendBeacon')['useSendBeacon']
  const useSeoMeta: typeof import('../../../node_modules/@unhead/vue')['useSeoMeta']
  const useServerHead: typeof import('../../../node_modules/@unhead/vue')['useServerHead']
  const useServerHeadSafe: typeof import('../../../node_modules/@unhead/vue')['useServerHeadSafe']
  const useServerSeoMeta: typeof import('../../../node_modules/@unhead/vue')['useServerSeoMeta']
  const useSetQuestionBankShare: typeof import('../../composables/useSetQuestionBankShare')['useSetQuestionBankShare']
  const useSlots: typeof import('../../../node_modules/vue')['useSlots']
  const useState: typeof import('../../../node_modules/nuxt/dist/app')['useState']
  const useTheme: typeof import('../../../base/composables/useTheme')['default']
  const useThirdJumpQuestionBank: typeof import('../../../base/composables/useThirdJumpQuestionBank')['useThirdJumpQuestionBank']
  const useTrackEvent: typeof import('../../composables/useTrackEvent')['useTrackEvent']
  const useTransitionState: typeof import('../../../node_modules/vue')['useTransitionState']
  const useUpdateQuestionBankBehaviorRecords: typeof import('../../../base/composables/useUpdateQuestionBankBehaviorRecords')['useUpdateQuestionBankBehaviorRecords']
  const useUserMember: typeof import('../../composables/useUserMember')['useUserMember']
  const useVaildLT: typeof import('../../composables/useVaildLT')['useVaildLT']
  const useVisibilityChange: typeof import('../../../base/composables/useVisibilityChange')['useVisibilityChange']
  const useWxShare: typeof import('../../composables/useWxShare')['useWxShare']
  const useZIndex: typeof import('../../../base/composables/useZIndex')['useZIndex']
  const userData: typeof import('../../composables/userData')['userData']
  const userDomainConfig: typeof import('../../../base/composables/userDomainConfig')['userDomainConfig']
  const userInfoProt: typeof import('../../apis/home')['userInfoProt']
  const userWxPhoto: typeof import('../../apis/index')['userWxPhoto']
  const validateNickname: typeof import('../../../base/composables/isRegexp')['validateNickname']
  const watch: typeof import('../../../node_modules/vue')['watch']
  const watchEffect: typeof import('../../../node_modules/vue')['watchEffect']
  const watchPostEffect: typeof import('../../../node_modules/vue')['watchPostEffect']
  const watchSyncEffect: typeof import('../../../node_modules/vue')['watchSyncEffect']
  const withCtx: typeof import('../../../node_modules/vue')['withCtx']
  const withDirectives: typeof import('../../../node_modules/vue')['withDirectives']
  const withKeys: typeof import('../../../node_modules/vue')['withKeys']
  const withMemo: typeof import('../../../node_modules/vue')['withMemo']
  const withModifiers: typeof import('../../../node_modules/vue')['withModifiers']
  const withScopeId: typeof import('../../../node_modules/vue')['withScopeId']
}
// for type re-export
declare global {
  // @ts-ignore
  export type { Component, ComponentPublicInstance, ComputedRef, ExtractPropTypes, ExtractPublicPropTypes, InjectionKey, PropType, Ref, MaybeRef, MaybeRefOrGetter, VNode } from '../../../node_modules/vue'
  import('../../../node_modules/vue')
  // @ts-ignore
  export type { IComponentData } from '../../composables/useComponentEvents'
  import('../../composables/useComponentEvents')
  // @ts-ignore
  export type { Query } from '../../composables/useGetAgentQuery'
  import('../../composables/useGetAgentQuery')
  // @ts-ignore
  export type { TrackEventOptions } from '../../composables/useTrackEvent'
  import('../../composables/useTrackEvent')
  // @ts-ignore
  export type { StoreUserInfoState } from '../../composables/userData'
  import('../../composables/userData')
  // @ts-ignore
  export type { ApisHome, ApisHome } from '../../apis/home'
  import('../../apis/home')
  // @ts-ignore
  export type { ApisLt } from '../../apis/learn-target/index'
  import('../../apis/learn-target/index')
  // @ts-ignore
  export type { ApisLc, IgetGoodsResourceType } from '../../apis/learn-center/index'
  import('../../apis/learn-center/index')
  // @ts-ignore
  export type { ApisAddress } from '../../apis/address/index'
  import('../../apis/address/index')
  // @ts-ignore
  export type { BooleanEnum, ApiResponse, GetAdvertisementPhotoListRequestData, GetAdvertisementPhotoListResponseData } from '../../apis/types'
  import('../../apis/types')
  // @ts-ignore
  export type { ResourceFile, StudyPlan, ResourcePackStage, DataPackageResponse, DataPackageReq } from 'D:/workspace/0.nuxt3/migration/kuke-cloud---next/mobile/types/data-package'
  import('D:/workspace/0.nuxt3/migration/kuke-cloud---next/mobile/types/data-package')
  // @ts-ignore
  export type { OuterRelationType, FileSource, VideoType, LearnStatus, FilesPageDTO, KssStudyProgress, FilesListVO, FilesListPageVO, Result, FilesListPageResponse } from '../../types/homework-files'
  import('../../types/homework-files')
  // @ts-ignore
  export type { HomeworkModel, ScoringWay, TeacherState, DoStatus, GetStudyPlanHomeworksReq, HomeworkInfo, GetStudyPlanHomeworksResponse } from '../../types/homework'
  import('../../types/homework')
  // @ts-ignore
  export type { SubmitVal } from 'D:/workspace/0.nuxt3/migration/kuke-cloud---next/mobile/types/loginTypes'
  import('D:/workspace/0.nuxt3/migration/kuke-cloud---next/mobile/types/loginTypes')
  // @ts-ignore
  export type { PauseEmitType } from 'D:/workspace/0.nuxt3/migration/kuke-cloud---next/mobile/types/questionTime'
  import('D:/workspace/0.nuxt3/migration/kuke-cloud---next/mobile/types/questionTime')
  // @ts-ignore
  export type { StudyProgress, StudyPlanFile } from 'D:/workspace/0.nuxt3/migration/kuke-cloud---next/mobile/types/study-plan-files'
  import('D:/workspace/0.nuxt3/migration/kuke-cloud---next/mobile/types/study-plan-files')
  // @ts-ignore
  export type { UserSelectionMode, ClassMode, GoodsModeDetail } from '../../types/study-plan'
  import('../../types/study-plan')
  // @ts-ignore
  export type { UseItem, toolsItem } from 'D:/workspace/0.nuxt3/migration/kuke-cloud---next/mobile/types/user'
  import('D:/workspace/0.nuxt3/migration/kuke-cloud---next/mobile/types/user')
  // @ts-ignore
  export type { SlideStyle, CaptchaVerifyResult, Captcha } from '../../../base/composables/useCaptchaVerify'
  import('../../../base/composables/useCaptchaVerify')
  // @ts-ignore
  export type { HttpOption } from '../../../base/composables/useHttp'
  import('../../../base/composables/useHttp')
  // @ts-ignore
  export type { ILearnTargetInfo } from '../../../base/composables/useLearnTarget'
  import('../../../base/composables/useLearnTarget')
  // @ts-ignore
  export type { UseNamespaceReturn } from '../../../base/composables/useNamespace'
  import('../../../base/composables/useNamespace')
  // @ts-ignore
  export type { defaultTrigger } from '../../../base/composables/usePageTrack'
  import('../../../base/composables/usePageTrack')
  // @ts-ignore
  export type { FillResult } from '../../../base/composables/useQuestionBankFillJudge'
  import('../../../base/composables/useQuestionBankFillJudge')
  // @ts-ignore
  export type { StoreAppDataState } from '../../../base/composables/userDomainConfig'
  import('../../../base/composables/userDomainConfig')
  // @ts-ignore
  export type { IDevice } from '../../../base/utils/device'
  import('../../../base/utils/device')
  // @ts-ignore
  export type { CLIENT_TYPE } from '../../../base/utils/is'
  import('../../../base/utils/is')
  // @ts-ignore
  export type { ApisCashier } from '../../../base/apis/cashier/index'
  import('../../../base/apis/cashier/index')
  // @ts-ignore
  export type { ApisCommon } from '../../../base/apis/common/index'
  import('../../../base/apis/common/index')
  // @ts-ignore
  export type { NewsCaegoryItem } from '../../../base/constants/db-redirect-kuke99'
  import('../../../base/constants/db-redirect-kuke99')
  // @ts-ignore
  export type { PayStatus, OrderAction, ExamQuestionType, ExamAnswerType, NetCode, DicSkipId, ActionEnum, ClientType, SourceEnum, GetCodeType, OrderStatus, AnswerStatus, BtnStatus, ActivityType, MarketingOrderTagStatus, ExciseLabelEnum, productSideEnum, BooleanEnum, ExciseModeEnum } from '../../../base/constants/index'
  import('../../../base/constants/index')
  // @ts-ignore
  export type { QuestionDoStatusFromLearnStatusName, QuestionModel } from '../../../base/constants/questionBankType'
  import('../../../base/constants/questionBankType')
  // @ts-ignore
  export type { GradualDirectionType, ITheme, StyleSetting } from 'D:/workspace/0.nuxt3/migration/kuke-cloud---next/base/constants/types'
  import('D:/workspace/0.nuxt3/migration/kuke-cloud---next/base/constants/types')
}
// for vue template auto import
import { UnwrapRef } from 'vue'
declare module 'vue' {
  interface ComponentCustomProperties {
    readonly AUDITION_VALIDITY: UnwrapRef<typeof import('../../../base/constants/index')['AUDITION_VALIDITY']>
    readonly ActionEnum: UnwrapRef<typeof import('../../../base/constants/index')['ActionEnum']>
    readonly ActivityType: UnwrapRef<typeof import('../../../base/constants/index')['ActivityType']>
    readonly ActivityTypeName: UnwrapRef<typeof import('../../../base/constants/index')['ActivityTypeName']>
    readonly AnswerStatus: UnwrapRef<typeof import('../../../base/constants/index')['AnswerStatus']>
    readonly ApisAddress: UnwrapRef<typeof import('../../apis/address/index')['ApisAddress']>
    readonly ApisCashier: UnwrapRef<typeof import('../../../base/apis/cashier/index')['ApisCashier']>
    readonly ApisCommon: UnwrapRef<typeof import('../../../base/apis/common/index')['ApisCommon']>
    readonly ApisHome: UnwrapRef<typeof import('../../apis/home')['ApisHome']>
    readonly ApisLc: UnwrapRef<typeof import('../../apis/learn-center/index')['ApisLc']>
    readonly ApisLt: UnwrapRef<typeof import('../../apis/learn-target/index')['ApisLt']>
    readonly BooleanEnum: UnwrapRef<typeof import('../../apis/types')['BooleanEnum']>
    readonly BtnStatus: UnwrapRef<typeof import('../../../base/constants/index')['BtnStatus']>
    readonly CATE_ID_MAP: UnwrapRef<typeof import('../../../base/constants/db-redirect-kuke99')['CATE_ID_MAP']>
    readonly CHINESE_NUMBERS: UnwrapRef<typeof import('../../../base/constants/index')['CHINESE_NUMBERS']>
    readonly CITY_TREE_MAP: UnwrapRef<typeof import('../../../base/constants/db-redirect-kuke99')['CITY_TREE_MAP']>
    readonly CLIENT_TYPE: UnwrapRef<typeof import('../../../base/utils/is')['CLIENT_TYPE']>
    readonly CUSTOMIZED_TENANTS: UnwrapRef<typeof import('../../../base/composables/useCustomizedTenants')['CUSTOMIZED_TENANTS']>
    readonly ClassMode: UnwrapRef<typeof import('../../types/study-plan')['ClassMode']>
    readonly Classify: UnwrapRef<typeof import('../../composables/Classify')['Classify']>
    readonly ClientType: UnwrapRef<typeof import('../../../base/constants/index')['ClientType']>
    readonly DAY_999: UnwrapRef<typeof import('../../../base/utils/index')['DAY_999']>
    readonly DEFAULT_INDEX: UnwrapRef<typeof import('../../../base/composables/useZIndex')['DEFAULT_INDEX']>
    readonly Dialog: UnwrapRef<typeof import('../../composables/Dialog')['Dialog']>
    readonly DicSkipId: UnwrapRef<typeof import('../../../base/constants/index')['DicSkipId']>
    readonly DoStatus: UnwrapRef<typeof import('../../types/homework')['DoStatus']>
    readonly EXTERNAL_USER_ID: UnwrapRef<typeof import('../../../base/constants/index')['EXTERNAL_USER_ID']>
    readonly ExamAnswerType: UnwrapRef<typeof import('../../../base/constants/index')['ExamAnswerType']>
    readonly ExamQuestionType: UnwrapRef<typeof import('../../../base/constants/index')['ExamQuestionType']>
    readonly ExciseLabelEnum: UnwrapRef<typeof import('../../../base/constants/index')['ExciseLabelEnum']>
    readonly ExciseModeEnum: UnwrapRef<typeof import('../../../base/constants/index')['ExciseModeEnum']>
    readonly ExerciseModelPopup: UnwrapRef<typeof import('../../composables/ExerciseModelPopup')['ExerciseModelPopup']>
    readonly FileSource: UnwrapRef<typeof import('../../types/homework-files')['FileSource']>
    readonly GetCodeType: UnwrapRef<typeof import('../../../base/constants/index')['GetCodeType']>
    readonly HOURS_24: UnwrapRef<typeof import('../../../base/utils/index')['HOURS_24']>
    readonly HomeworkModel: UnwrapRef<typeof import('../../types/homework')['HomeworkModel']>
    readonly INNERNAL_SYSTEM: UnwrapRef<typeof import('../../../base/constants/index')['INNERNAL_SYSTEM']>
    readonly IS_AUDITION: UnwrapRef<typeof import('../../../base/constants/index')['IS_AUDITION']>
    readonly IS_DEBUG_ENV: UnwrapRef<typeof import('../../../base/constants/index')['IS_DEBUG_ENV']>
    readonly IS_DEV_ENV: UnwrapRef<typeof import('../../../base/constants/index')['IS_DEV_ENV']>
    readonly IS_LOCAL_ENV: UnwrapRef<typeof import('../../../base/constants/index')['IS_LOCAL_ENV']>
    readonly IS_STAGING_ENV: UnwrapRef<typeof import('../../../base/constants/index')['IS_STAGING_ENV']>
    readonly LOC_SPEC_ID: UnwrapRef<typeof import('../../../base/constants/index')['LOC_SPEC_ID']>
    readonly LT_LOCAL_KEY: UnwrapRef<typeof import('../../../base/constants/index')['LT_LOCAL_KEY']>
    readonly LT_SEARCH_KEY: UnwrapRef<typeof import('../../../base/constants/index')['LT_SEARCH_KEY']>
    readonly LearnStatus: UnwrapRef<typeof import('../../types/homework-files')['LearnStatus']>
    readonly Loading: UnwrapRef<typeof import('../../../base/composables/Loading')['Loading']>
    readonly MINS_60: UnwrapRef<typeof import('../../../base/utils/index')['MINS_60']>
    readonly MarketingOrderTagMap: UnwrapRef<typeof import('../../../base/constants/index')['MarketingOrderTagMap']>
    readonly MarketingOrderTagStatus: UnwrapRef<typeof import('../../../base/constants/index')['MarketingOrderTagStatus']>
    readonly Message: UnwrapRef<typeof import('../../../base/composables/Message')['Message']>
    readonly NEW_CATEGORY_LIST: UnwrapRef<typeof import('../../../base/constants/db-redirect-kuke99')['NEW_CATEGORY_LIST']>
    readonly NetCode: UnwrapRef<typeof import('../../../base/constants/index')['NetCode']>
    readonly OrderAction: UnwrapRef<typeof import('../../../base/constants/index')['OrderAction']>
    readonly OrderStatus: UnwrapRef<typeof import('../../../base/constants/index')['OrderStatus']>
    readonly OuterRelationType: UnwrapRef<typeof import('../../types/homework-files')['OuterRelationType']>
    readonly POOL_ID: UnwrapRef<typeof import('../../../base/constants/index')['POOL_ID']>
    readonly PROMOTION_SHARE_KEY: UnwrapRef<typeof import('../../../base/constants/index')['PROMOTION_SHARE_KEY']>
    readonly PROMOTION_SHARE_URL: UnwrapRef<typeof import('../../../base/constants/index')['PROMOTION_SHARE_URL']>
    readonly PayStatus: UnwrapRef<typeof import('../../../base/constants/index')['PayStatus']>
    readonly QuestionDoStatusAlias: UnwrapRef<typeof import('../../../base/constants/questionBankType')['QuestionDoStatusAlias']>
    readonly QuestionDoStatusFromLearnStatusName: UnwrapRef<typeof import('../../../base/constants/questionBankType')['QuestionDoStatusFromLearnStatusName']>
    readonly SALE_ADMIN_ID: UnwrapRef<typeof import('../../../base/constants/index')['SALE_ADMIN_ID']>
    readonly SECS_60: UnwrapRef<typeof import('../../../base/utils/index')['SECS_60']>
    readonly SHARE_LINK_ID: UnwrapRef<typeof import('../../../base/constants/index')['SHARE_LINK_ID']>
    readonly SHARE_PRICE: UnwrapRef<typeof import('../../../base/constants/index')['SHARE_PRICE']>
    readonly STUDY_TYPE: UnwrapRef<typeof import('../../../base/constants/index')['STUDY_TYPE']>
    readonly STUDY_TYPE__COURSE: UnwrapRef<typeof import('../../../base/constants/index')['STUDY_TYPE__COURSE']>
    readonly STUDY_TYPE__TEST_QUESTIONS: UnwrapRef<typeof import('../../../base/constants/index')['STUDY_TYPE__TEST_QUESTIONS']>
    readonly SYSTEM_TYPE: UnwrapRef<typeof import('../../../base/constants/index')['SYSTEM_TYPE']>
    readonly ScoringWay: UnwrapRef<typeof import('../../types/homework')['ScoringWay']>
    readonly ShoppingTypePopup: UnwrapRef<typeof import('../../composables/ShoppingTypePopup')['ShoppingTypePopup']>
    readonly SourceEnum: UnwrapRef<typeof import('../../../base/constants/index')['SourceEnum']>
    readonly TARGET_CODE: UnwrapRef<typeof import('../../../base/constants/index')['TARGET_CODE']>
    readonly TeacherState: UnwrapRef<typeof import('../../types/homework')['TeacherState']>
    readonly TextBookPopup: UnwrapRef<typeof import('../../composables/TextBookPopup')['TextBookPopup']>
    readonly UserSelectionMode: UnwrapRef<typeof import('../../types/study-plan')['UserSelectionMode']>
    readonly VideoType: UnwrapRef<typeof import('../../types/homework-files')['VideoType']>
    readonly abortNavigation: UnwrapRef<typeof import('../../../node_modules/nuxt/dist/app')['abortNavigation']>
    readonly addAudio: UnwrapRef<typeof import('../../../base/utils/audio')['addAudio']>
    readonly addAudioUniquely: UnwrapRef<typeof import('../../../base/utils/audio')['addAudioUniquely']>
    readonly addRouteMiddleware: UnwrapRef<typeof import('../../../node_modules/nuxt/dist/app')['addRouteMiddleware']>
    readonly allowMultipleToast: UnwrapRef<typeof import('../../../node_modules/vant/lib/vant.cjs')['allowMultipleToast']>
    readonly apiGetHome: UnwrapRef<typeof import('../../apis/home')['apiGetHome']>
    readonly apis: UnwrapRef<typeof import('../../apis/index')['default']>
    readonly assert: UnwrapRef<typeof import('../../../base/utils/is')['assert']>
    readonly audioRegex: UnwrapRef<typeof import('../../../base/utils/audio')['audioRegex']>
    readonly base642File: UnwrapRef<typeof import('../../../base/composables/fileChange')['base642File']>
    readonly base64toBlob: UnwrapRef<typeof import('../../../base/utils/file')['base64toBlob']>
    readonly buildUUID: UnwrapRef<typeof import('../../../base/utils/uuid')['buildUUID']>
    readonly buyEffect: UnwrapRef<typeof import('../../composables/buyEffect')['buyEffect']>
    readonly calculateTimeDifference: UnwrapRef<typeof import('../../../base/utils/index')['calculateTimeDifference']>
    readonly cancelIdleCallback: UnwrapRef<typeof import('../../../node_modules/nuxt/dist/app')['cancelIdleCallback']>
    readonly clamp: UnwrapRef<typeof import('../../../base/utils/is')['clamp']>
    readonly clearError: UnwrapRef<typeof import('../../../node_modules/nuxt/dist/app')['clearError']>
    readonly clearNuxtData: UnwrapRef<typeof import('../../../node_modules/nuxt/dist/app')['clearNuxtData']>
    readonly clearNuxtState: UnwrapRef<typeof import('../../../node_modules/nuxt/dist/app')['clearNuxtState']>
    readonly closeDialog: UnwrapRef<typeof import('../../../node_modules/vant/lib/vant.cjs')['closeDialog']>
    readonly closeNotify: UnwrapRef<typeof import('../../../node_modules/vant/lib/vant.cjs')['closeNotify']>
    readonly closeToast: UnwrapRef<typeof import('../../../node_modules/vant/lib/vant.cjs')['closeToast']>
    readonly compareTagParams: UnwrapRef<typeof import('../../../base/utils/questionTools')['compareTagParams']>
    readonly computed: UnwrapRef<typeof import('../../../node_modules/vue')['computed']>
    readonly countFunction: UnwrapRef<typeof import('../../../base/composables/countdown')['countFunction']>
    readonly createDecipher: UnwrapRef<typeof import('../../../base/utils/video')['createDecipher']>
    readonly createError: UnwrapRef<typeof import('../../../node_modules/nuxt/dist/app')['createError']>
    readonly customRef: UnwrapRef<typeof import('../../../node_modules/vue')['customRef']>
    readonly debounceFunc: UnwrapRef<typeof import('../../../base/utils/debounce')['debounceFunc']>
    readonly debounceFuncPlus: UnwrapRef<typeof import('../../../base/utils/debounce')['debounceFuncPlus']>
    readonly decryptData: UnwrapRef<typeof import('../../../base/utils/video')['decryptData']>
    readonly deepClone: UnwrapRef<typeof import('../../../base/utils/deepClone')['deepClone']>
    readonly defaultNamespace: UnwrapRef<typeof import('../../../base/composables/useNamespace')['defaultNamespace']>
    readonly defineAppConfig: UnwrapRef<typeof import('../../../node_modules/nuxt/dist/app')['defineAppConfig']>
    readonly defineAsyncComponent: UnwrapRef<typeof import('../../../node_modules/vue')['defineAsyncComponent']>
    readonly defineComponent: UnwrapRef<typeof import('../../../node_modules/vue')['defineComponent']>
    readonly defineModel: UnwrapRef<typeof import('../../../node_modules/vue')['defineModel']>
    readonly defineNuxtComponent: UnwrapRef<typeof import('../../../node_modules/nuxt/dist/app')['defineNuxtComponent']>
    readonly defineNuxtLink: UnwrapRef<typeof import('../../../node_modules/nuxt/dist/app')['defineNuxtLink']>
    readonly defineNuxtPlugin: UnwrapRef<typeof import('../../../node_modules/nuxt/dist/app')['defineNuxtPlugin']>
    readonly defineNuxtRouteMiddleware: UnwrapRef<typeof import('../../../node_modules/nuxt/dist/app')['defineNuxtRouteMiddleware']>
    readonly defineOptions: UnwrapRef<typeof import('../../../node_modules/vue')['defineOptions']>
    readonly definePageMeta: UnwrapRef<typeof import('../../../node_modules/nuxt/dist/pages/runtime/composables')['definePageMeta']>
    readonly definePayloadPlugin: UnwrapRef<typeof import('../../../node_modules/nuxt/dist/app')['definePayloadPlugin']>
    readonly definePayloadReducer: UnwrapRef<typeof import('../../../node_modules/nuxt/dist/app')['definePayloadReducer']>
    readonly definePayloadReviver: UnwrapRef<typeof import('../../../node_modules/nuxt/dist/app')['definePayloadReviver']>
    readonly defineSlots: UnwrapRef<typeof import('../../../node_modules/vue')['defineSlots']>
    readonly delAddress: UnwrapRef<typeof import('../../apis/address/index')['delAddress']>
    readonly downLoadFileRename: UnwrapRef<typeof import('../../../base/composables/common')['downLoadFileRename']>
    readonly downloadFile: UnwrapRef<typeof import('../../../base/composables/common')['downloadFile']>
    readonly dragFun: UnwrapRef<typeof import('../../composables/dragFun')['dragFun']>
    readonly editAddress: UnwrapRef<typeof import('../../apis/address/index')['editAddress']>
    readonly effect: UnwrapRef<typeof import('../../../node_modules/vue')['effect']>
    readonly effectScope: UnwrapRef<typeof import('../../../node_modules/vue')['effectScope']>
    readonly emitter: UnwrapRef<typeof import('../../../base/utils/mitt')['emitter']>
    readonly extractedAudio: UnwrapRef<typeof import('../../../base/utils/audio')['extractedAudio']>
    readonly fetchAddOrdersBehaviorRecord: UnwrapRef<typeof import('../../../base/composables/common')['fetchAddOrdersBehaviorRecord']>
    readonly filterBody: UnwrapRef<typeof import('../../../base/utils/filterBody')['default']>
    readonly flat: UnwrapRef<typeof import('../../../base/utils/tools')['flat']>
    readonly formatDateWithDateObject: UnwrapRef<typeof import('../../../base/utils/tools')['formatDateWithDateObject']>
    readonly formatDecimal: UnwrapRef<typeof import('../../../base/utils/number')['formatDecimal']>
    readonly formatDuring: UnwrapRef<typeof import('../../../base/utils/index')['formatDuring']>
    readonly formatNumberWithoutTrailingZero: UnwrapRef<typeof import('../../../base/utils/number')['formatNumberWithoutTrailingZero']>
    readonly formatTheme: UnwrapRef<typeof import('../../../base/composables/useTheme')['formatTheme']>
    readonly formatTimeByPattern: UnwrapRef<typeof import('../../../base/utils/tools')['formatTimeByPattern']>
    readonly formatTimeDuration: UnwrapRef<typeof import('../../../base/utils/index')['formatTimeDuration']>
    readonly formatTodayWatchDuration: UnwrapRef<typeof import('../../../base/utils/index')['formatTodayWatchDuration']>
    readonly generateFilename: UnwrapRef<typeof import('../../../base/composables/useOss')['generateFilename']>
    readonly getAddressDetail: UnwrapRef<typeof import('../../apis/address/index')['getAddressDetail']>
    readonly getAddressList: UnwrapRef<typeof import('../../apis/address/index')['getAddressList']>
    readonly getAdvertisementPhotoList: UnwrapRef<typeof import('../../apis/home')['getAdvertisementPhotoList']>
    readonly getAppManifest: UnwrapRef<typeof import('../../../node_modules/nuxt/dist/app')['getAppManifest']>
    readonly getAreaList: UnwrapRef<typeof import('../../apis/address/index')['getAreaList']>
    readonly getArrayLast: UnwrapRef<typeof import('../../../base/utils/is')['getArrayLast']>
    readonly getAssemblyForWx: UnwrapRef<typeof import('../../apis/home')['getAssemblyForWx']>
    readonly getClassifyConfigEnable: UnwrapRef<typeof import('../../apis/learn-target/index')['getClassifyConfigEnable']>
    readonly getComponentInteractiveInfo: UnwrapRef<typeof import('../../composables/useBusinessInteractive')['getComponentInteractiveInfo']>
    readonly getCourseUpdateStatus: UnwrapRef<typeof import('../../apis/home')['getCourseUpdateStatus']>
    readonly getCurReceivingInfo: UnwrapRef<typeof import('../../apis/address/index')['getCurReceivingInfo']>
    readonly getCurrentInstance: UnwrapRef<typeof import('../../../node_modules/vue')['getCurrentInstance']>
    readonly getCurrentScope: UnwrapRef<typeof import('../../../node_modules/vue')['getCurrentScope']>
    readonly getCurrentTime: UnwrapRef<typeof import('../../../base/utils/index')['getCurrentTime']>
    readonly getCustomPageNavigationStatus: UnwrapRef<typeof import('../../apis/home')['getCustomPageNavigationStatus']>
    readonly getCustomPageReleaseDetails: UnwrapRef<typeof import('../../apis/home')['getCustomPageReleaseDetails']>
    readonly getDefaultAddressProt: UnwrapRef<typeof import('../../apis/address/index')['getDefaultAddressProt']>
    readonly getDefaultOfficialRecommend: UnwrapRef<typeof import('../../apis/home')['getDefaultOfficialRecommend']>
    readonly getDeviceEnv: UnwrapRef<typeof import('../../../base/utils/device')['getDeviceEnv']>
    readonly getDeviceOfUA: UnwrapRef<typeof import('../../../base/utils/is')['getDeviceOfUA']>
    readonly getExamStatusName: UnwrapRef<typeof import('../../../base/utils/examUtils')['getExamStatusName']>
    readonly getFormDisabled: UnwrapRef<typeof import('../../../base/composables/form')['getFormDisabled']>
    readonly getGoodsResourceType: UnwrapRef<typeof import('../../apis/learn-center/index')['getGoodsResourceType']>
    readonly getKukeMarketingInfo: UnwrapRef<typeof import('../../apis/home')['getKukeMarketingInfo']>
    readonly getLaunchAd: UnwrapRef<typeof import('../../apis/home')['getLaunchAd']>
    readonly getLearningTargetById: UnwrapRef<typeof import('../../apis/learn-target/index')['getLearningTargetById']>
    readonly getLearningTargetDefaultId: UnwrapRef<typeof import('../../apis/learn-target/index')['getLearningTargetDefaultId']>
    readonly getLearningTargetList: UnwrapRef<typeof import('../../apis/learn-target/index')['getLearningTargetList']>
    readonly getLivePlatform: UnwrapRef<typeof import('../../../base/composables/useLivePlatformType')['getLivePlatform']>
    readonly getLivePlatformByPc: UnwrapRef<typeof import('../../../base/composables/useLivePlatformType')['getLivePlatformByPc']>
    readonly getLivePlatformByWap: UnwrapRef<typeof import('../../../base/composables/useLivePlatformType')['getLivePlatformByWap']>
    readonly getNextDayStartTimeDate: UnwrapRef<typeof import('../../../base/composables/useAdCountdown')['getNextDayStartTimeDate']>
    readonly getPopUpAd: UnwrapRef<typeof import('../../apis/home')['getPopUpAd']>
    readonly getQuestionDoStatusFromLearnStatusName: UnwrapRef<typeof import('../../../base/utils/tools')['getQuestionDoStatusFromLearnStatusName']>
    readonly getReleasePersonCenterORNavigationDetails: UnwrapRef<typeof import('../../apis/home')['getReleasePersonCenterORNavigationDetails']>
    readonly getRouteRules: UnwrapRef<typeof import('../../../node_modules/nuxt/dist/app')['getRouteRules']>
    readonly getRuleListByIdParams: UnwrapRef<typeof import('../../apis/home')['getRuleListByIdParams']>
    readonly getStudyPlanStageInfoProt: UnwrapRef<typeof import('../../apis/learn-center/index')['getStudyPlanStageInfoProt']>
    readonly getUnitStatusProt: UnwrapRef<typeof import('../../apis/learn-center/index')['getUnitStatusProt']>
    readonly getUserLearningTarget: UnwrapRef<typeof import('../../apis/learn-target/index')['getUserLearningTarget']>
    readonly globalVariable: UnwrapRef<typeof import('../../../base/utils/globalVariable')['default']>
    readonly h: UnwrapRef<typeof import('../../../node_modules/vue')['h']>
    readonly handleClipboard: UnwrapRef<typeof import('../../../base/utils/clipboard')['handleClipboard']>
    readonly handleMarketingInfo: UnwrapRef<typeof import('../../composables/handleMarketingInfo')['default']>
    readonly handleOrderMarketingInfo: UnwrapRef<typeof import('../../composables/handleOrderMarketingInfo')['default']>
    readonly handleQuestionTitle: UnwrapRef<typeof import('../../../base/utils/tools')['handleQuestionTitle']>
    readonly handleToPaperPage: UnwrapRef<typeof import('../../../base/utils/examUtils')['handleToPaperPage']>
    readonly handleWeChatPreview: UnwrapRef<typeof import('../../composables/useWeChat')['handleWeChatPreview']>
    readonly hasInjectionContext: UnwrapRef<typeof import('../../../node_modules/vue')['hasInjectionContext']>
    readonly hasOwn: UnwrapRef<typeof import('../../../base/utils/is')['hasOwn']>
    readonly hexToRgb: UnwrapRef<typeof import('../../../base/utils/index')['hexToRgb']>
    readonly informationLabelMap: UnwrapRef<typeof import('../../../base/constants/informationLabelMap')['informationLabelMap']>
    readonly initOSS: UnwrapRef<typeof import('../../../base/composables/useOss')['initOSS']>
    readonly inject: UnwrapRef<typeof import('../../../node_modules/vue')['inject']>
    readonly injectHead: UnwrapRef<typeof import('../../../node_modules/@unhead/vue')['injectHead']>
    readonly isClient: UnwrapRef<typeof import('../../../base/utils/is')['isClient']>
    readonly isDdefine: UnwrapRef<typeof import('../../../base/utils/is')['isDdefine']>
    readonly isDef: UnwrapRef<typeof import('../../../base/utils/is')['isDef']>
    readonly isDefined: UnwrapRef<typeof import('../../../base/utils/is')['isDefined']>
    readonly isDingTalk6_5: UnwrapRef<typeof import('../../../base/utils/is')['isDingTalk6_5']>
    readonly isDingTalk: UnwrapRef<typeof import('../../../base/utils/is')['isDingTalk']>
    readonly isFillQuestion: UnwrapRef<typeof import('../../../base/utils/tools')['isFillQuestion']>
    readonly isIOS: UnwrapRef<typeof import('../../../base/utils/is')['isIOS']>
    readonly isIframe: UnwrapRef<typeof import('../../../base/utils/is')['isIframe']>
    readonly isInteger: UnwrapRef<typeof import('../../composables/index')['isInteger']>
    readonly isKukeCloud: UnwrapRef<typeof import('../../../base/composables/common')['isKukeCloud']>
    readonly isKukeCloudAppWebviewBrowser: UnwrapRef<typeof import('../../../base/utils/is')['isKukeCloudAppWebviewBrowser']>
    readonly isMaterial: UnwrapRef<typeof import('../../../base/utils/tools')['isMaterial']>
    readonly isMultipleChoice: UnwrapRef<typeof import('../../../base/utils/tools')['isMultipleChoice']>
    readonly isNuxtError: UnwrapRef<typeof import('../../../node_modules/nuxt/dist/app')['isNuxtError']>
    readonly isOpenBankAnalyze: UnwrapRef<typeof import('../../../base/utils/tools')['isOpenBankAnalyze']>
    readonly isPC: UnwrapRef<typeof import('../../../base/utils/is')['isPC']>
    readonly isPrerendered: UnwrapRef<typeof import('../../../node_modules/nuxt/dist/app')['isPrerendered']>
    readonly isProxy: UnwrapRef<typeof import('../../../node_modules/vue')['isProxy']>
    readonly isQQBrowser: UnwrapRef<typeof import('../../../base/utils/is')['isQQBrowser']>
    readonly isQQBrowserWebView: UnwrapRef<typeof import('../../../base/utils/is')['isQQBrowserWebView']>
    readonly isQuarkBrowser: UnwrapRef<typeof import('../../../base/utils/is')['isQuarkBrowser']>
    readonly isRadioChoice: UnwrapRef<typeof import('../../../base/utils/tools')['isRadioChoice']>
    readonly isReactive: UnwrapRef<typeof import('../../../node_modules/vue')['isReactive']>
    readonly isReadonly: UnwrapRef<typeof import('../../../node_modules/vue')['isReadonly']>
    readonly isRef: UnwrapRef<typeof import('../../../node_modules/vue')['isRef']>
    readonly isShallow: UnwrapRef<typeof import('../../../node_modules/vue')['isShallow']>
    readonly isShowBankAnalyze: UnwrapRef<typeof import('../../../base/utils/tools')['isShowBankAnalyze']>
    readonly isSogouBrowser: UnwrapRef<typeof import('../../../base/utils/is')['isSogouBrowser']>
    readonly isUCBrowser: UnwrapRef<typeof import('../../../base/utils/is')['isUCBrowser']>
    readonly isVivoBrowser: UnwrapRef<typeof import('../../../base/utils/is')['isVivoBrowser']>
    readonly isVue2: UnwrapRef<typeof import('../../../node_modules/nuxt/dist/app/compat/vue-demi')['isVue2']>
    readonly isVue3: UnwrapRef<typeof import('../../../node_modules/nuxt/dist/app/compat/vue-demi')['isVue3']>
    readonly isWAP: UnwrapRef<typeof import('../../../base/utils/is')['isWAP']>
    readonly isWxBrowser: UnwrapRef<typeof import('../../../base/utils/is')['isWxBrowser']>
    readonly isXcxBrowser: UnwrapRef<typeof import('../../../base/utils/is')['isXcxBrowser']>
    readonly judgePageMasterIdValid: UnwrapRef<typeof import('../../apis/learn-target/index')['judgePageMasterIdValid']>
    readonly kgUserStudyPlanSelectionSwitchProt: UnwrapRef<typeof import('../../apis/learn-center/index')['kgUserStudyPlanSelectionSwitchProt']>
    readonly loadPayload: UnwrapRef<typeof import('../../../node_modules/nuxt/dist/app')['loadPayload']>
    readonly loggerError: UnwrapRef<typeof import('../../../base/composables/useHttp')['loggerError']>
    readonly markDownItKateX: UnwrapRef<typeof import('../../composables/markDownItKateX')['markDownItKateX']>
    readonly markRaw: UnwrapRef<typeof import('../../../node_modules/vue')['markRaw']>
    readonly maskedMobileNumber: UnwrapRef<typeof import('../../../base/composables/isRegexp')['maskedMobileNumber']>
    readonly mergeModels: UnwrapRef<typeof import('../../../node_modules/vue')['mergeModels']>
    readonly moneyInput: UnwrapRef<typeof import('../../../base/composables/isRegexp')['moneyInput']>
    readonly navigateTo: UnwrapRef<typeof import('../../../node_modules/nuxt/dist/app')['navigateTo']>
    readonly nextTick: UnwrapRef<typeof import('../../../node_modules/vue')['nextTick']>
    readonly noop: UnwrapRef<typeof import('../../../base/utils/is')['noop']>
    readonly notNullish: UnwrapRef<typeof import('../../../base/utils/is')['notNullish']>
    readonly now: UnwrapRef<typeof import('../../../base/utils/is')['now']>
    readonly numberToChinese: UnwrapRef<typeof import('../../../base/utils/number')['numberToChinese']>
    readonly objectToUrlParams: UnwrapRef<typeof import('../../../base/utils/index')['objectToUrlParams']>
    readonly onActivated: UnwrapRef<typeof import('../../../node_modules/vue')['onActivated']>
    readonly onBeforeMount: UnwrapRef<typeof import('../../../node_modules/vue')['onBeforeMount']>
    readonly onBeforeRouteLeave: UnwrapRef<typeof import('../vue-router')['onBeforeRouteLeave']>
    readonly onBeforeRouteUpdate: UnwrapRef<typeof import('../vue-router')['onBeforeRouteUpdate']>
    readonly onBeforeUnmount: UnwrapRef<typeof import('../../../node_modules/vue')['onBeforeUnmount']>
    readonly onBeforeUpdate: UnwrapRef<typeof import('../../../node_modules/vue')['onBeforeUpdate']>
    readonly onDeactivated: UnwrapRef<typeof import('../../../node_modules/vue')['onDeactivated']>
    readonly onErrorCaptured: UnwrapRef<typeof import('../../../node_modules/vue')['onErrorCaptured']>
    readonly onMounted: UnwrapRef<typeof import('../../../node_modules/vue')['onMounted']>
    readonly onNuxtReady: UnwrapRef<typeof import('../../../node_modules/nuxt/dist/app')['onNuxtReady']>
    readonly onRenderTracked: UnwrapRef<typeof import('../../../node_modules/vue')['onRenderTracked']>
    readonly onRenderTriggered: UnwrapRef<typeof import('../../../node_modules/vue')['onRenderTriggered']>
    readonly onScopeDispose: UnwrapRef<typeof import('../../../node_modules/vue')['onScopeDispose']>
    readonly onServerPrefetch: UnwrapRef<typeof import('../../../node_modules/vue')['onServerPrefetch']>
    readonly onUnmounted: UnwrapRef<typeof import('../../../node_modules/vue')['onUnmounted']>
    readonly onUpdated: UnwrapRef<typeof import('../../../node_modules/vue')['onUpdated']>
    readonly orderEffect: UnwrapRef<typeof import('../../composables/useOrder')['orderEffect']>
    readonly os: UnwrapRef<typeof import('../../../base/composables/common')['os']>
    readonly passwordReg: UnwrapRef<typeof import('../../../base/composables/isRegexp')['passwordReg']>
    readonly pauseAudio: UnwrapRef<typeof import('../../../base/utils/audio')['pauseAudio']>
    readonly persistedState: UnwrapRef<typeof import('../../../node_modules/@pinia-plugin-persistedstate/nuxt/dist/runtime/storages')['persistedState']>
    readonly postAddress: UnwrapRef<typeof import('../../apis/address/index')['postAddress']>
    readonly postBodyEncryption: UnwrapRef<typeof import('../../../base/composables/usePostBodyEncryPtion')['postBodyEncryption']>
    readonly prefetchComponents: UnwrapRef<typeof import('../../../node_modules/nuxt/dist/app')['prefetchComponents']>
    readonly preloadComponents: UnwrapRef<typeof import('../../../node_modules/nuxt/dist/app')['preloadComponents']>
    readonly preloadPayload: UnwrapRef<typeof import('../../../node_modules/nuxt/dist/app')['preloadPayload']>
    readonly preloadRouteComponents: UnwrapRef<typeof import('../../../node_modules/nuxt/dist/app')['preloadRouteComponents']>
    readonly productSideEnum: UnwrapRef<typeof import('../../../base/constants/index')['productSideEnum']>
    readonly provide: UnwrapRef<typeof import('../../../node_modules/vue')['provide']>
    readonly proxyRefs: UnwrapRef<typeof import('../../../node_modules/vue')['proxyRefs']>
    readonly px2rem: UnwrapRef<typeof import('../../../base/utils/px2rem')['default']>
    readonly queryProductDetailThirdList: UnwrapRef<typeof import('../../apis/home')['queryProductDetailThirdList']>
    readonly questionBankBack: UnwrapRef<typeof import('../../composables/useQuestionBankBack')['questionBankBack']>
    readonly questionBankBackForApp: UnwrapRef<typeof import('../../composables/useQuestionBackForApp')['questionBankBackForApp']>
    readonly rand: UnwrapRef<typeof import('../../../base/utils/is')['rand']>
    readonly reactive: UnwrapRef<typeof import('../../../node_modules/vue')['reactive']>
    readonly readonly: UnwrapRef<typeof import('../../../node_modules/vue')['readonly']>
    readonly receiveCoupon: UnwrapRef<typeof import('../../composables/receiveCoupon')['receiveCoupon']>
    readonly recursion: UnwrapRef<typeof import('../../../base/composables/form')['recursion']>
    readonly redirectKuke99Url: UnwrapRef<typeof import('../../../base/utils/redirectKuke99Url')['redirectKuke99Url']>
    readonly ref: UnwrapRef<typeof import('../../../node_modules/vue')['ref']>
    readonly refreshNuxtData: UnwrapRef<typeof import('../../../node_modules/nuxt/dist/app')['refreshNuxtData']>
    readonly regPhone: UnwrapRef<typeof import('../../../base/composables/isRegexp')['regPhone']>
    readonly reloadNuxtApp: UnwrapRef<typeof import('../../../node_modules/nuxt/dist/app')['reloadNuxtApp']>
    readonly removeAudio: UnwrapRef<typeof import('../../../base/utils/audio')['removeAudio']>
    readonly removeUpdateRecordProt: UnwrapRef<typeof import('../../apis/home')['removeUpdateRecordProt']>
    readonly requestIdleCallback: UnwrapRef<typeof import('../../../node_modules/nuxt/dist/app')['requestIdleCallback']>
    readonly resetDialogDefaultOptions: UnwrapRef<typeof import('../../../node_modules/vant/lib/vant.cjs')['resetDialogDefaultOptions']>
    readonly resetNotifyDefaultOptions: UnwrapRef<typeof import('../../../node_modules/vant/lib/vant.cjs')['resetNotifyDefaultOptions']>
    readonly resetToastDefaultOptions: UnwrapRef<typeof import('../../../node_modules/vant/lib/vant.cjs')['resetToastDefaultOptions']>
    readonly resolveComponent: UnwrapRef<typeof import('../../../node_modules/vue')['resolveComponent']>
    readonly saveLearningTarget: UnwrapRef<typeof import('../../apis/learn-target/index')['saveLearningTarget']>
    readonly setDialogDefaultOptions: UnwrapRef<typeof import('../../../node_modules/vant/lib/vant.cjs')['setDialogDefaultOptions']>
    readonly setNotifyDefaultOptions: UnwrapRef<typeof import('../../../node_modules/vant/lib/vant.cjs')['setNotifyDefaultOptions']>
    readonly setPageLayout: UnwrapRef<typeof import('../../../node_modules/nuxt/dist/app')['setPageLayout']>
    readonly setResponseStatus: UnwrapRef<typeof import('../../../node_modules/nuxt/dist/app')['setResponseStatus']>
    readonly setToastDefaultOptions: UnwrapRef<typeof import('../../../node_modules/vant/lib/vant.cjs')['setToastDefaultOptions']>
    readonly shallowReactive: UnwrapRef<typeof import('../../../node_modules/vue')['shallowReactive']>
    readonly shallowReadonly: UnwrapRef<typeof import('../../../node_modules/vue')['shallowReadonly']>
    readonly shallowRef: UnwrapRef<typeof import('../../../node_modules/vue')['shallowRef']>
    readonly showConfirmDialog: UnwrapRef<typeof import('../../../node_modules/vant/lib/vant.cjs')['showConfirmDialog']>
    readonly showDialog: UnwrapRef<typeof import('../../../node_modules/vant/lib/vant.cjs')['showDialog']>
    readonly showError: UnwrapRef<typeof import('../../../node_modules/nuxt/dist/app')['showError']>
    readonly showFailToast: UnwrapRef<typeof import('../../../node_modules/vant/lib/vant.cjs')['showFailToast']>
    readonly showImagePreview: UnwrapRef<typeof import('../../../node_modules/vant/lib/vant.cjs')['showImagePreview']>
    readonly showLoadingToast: UnwrapRef<typeof import('../../../node_modules/vant/lib/vant.cjs')['showLoadingToast']>
    readonly showMoreArrow: UnwrapRef<typeof import('../../../base/composables/showMoreArrow')['showMoreArrow']>
    readonly showNotify: UnwrapRef<typeof import('../../../node_modules/vant/lib/vant.cjs')['showNotify']>
    readonly showSuccessToast: UnwrapRef<typeof import('../../../node_modules/vant/lib/vant.cjs')['showSuccessToast']>
    readonly showToast: UnwrapRef<typeof import('../../../node_modules/vant/lib/vant.cjs')['showToast']>
    readonly splitNumber: UnwrapRef<typeof import('../../../base/utils/number')['splitNumber']>
    readonly statisticalPublicAdvertExposure: UnwrapRef<typeof import('../../apis/learn-target/index')['statisticalPublicAdvertExposure']>
    readonly teacherInfoEffect: UnwrapRef<typeof import('../../composables/useTeacherInfo')['teacherInfoEffect']>
    readonly timeKeepingShow: UnwrapRef<typeof import('../../composables/timeKeepingShow')['timeKeepingShow']>
    readonly timestamp: UnwrapRef<typeof import('../../../base/utils/is')['timestamp']>
    readonly toAnswerBase64: UnwrapRef<typeof import('../../../base/utils/tools')['toAnswerBase64']>
    readonly toBase64: UnwrapRef<typeof import('../../../base/utils/tools')['toBase64']>
    readonly toChineseNum: UnwrapRef<typeof import('../../../base/utils/tools')['toChineseNum']>
    readonly toRaw: UnwrapRef<typeof import('../../../node_modules/vue')['toRaw']>
    readonly toRef: UnwrapRef<typeof import('../../../node_modules/vue')['toRef']>
    readonly toRefs: UnwrapRef<typeof import('../../../node_modules/vue')['toRefs']>
    readonly toValue: UnwrapRef<typeof import('../../../node_modules/vue')['toValue']>
    readonly triggerRef: UnwrapRef<typeof import('../../../node_modules/vue')['triggerRef']>
    readonly unref: UnwrapRef<typeof import('../../../node_modules/vue')['unref']>
    readonly updateAppConfig: UnwrapRef<typeof import('../../../node_modules/nuxt/dist/app')['updateAppConfig']>
    readonly updateBehaviorRecords: UnwrapRef<typeof import('../../../base/composables/common')['updateBehaviorRecords']>
    readonly updateDefaultAddress: UnwrapRef<typeof import('../../apis/address/index')['updateDefaultAddress']>
    readonly useAdCountdownRAF: UnwrapRef<typeof import('../../../base/composables/useAdCountdown')['useAdCountdownRAF']>
    readonly useAddQuestionBankOrderBehaviorRecords: UnwrapRef<typeof import('../../../base/composables/useAddQuestionBankOrderBehaviorRecords')['useAddQuestionBankOrderBehaviorRecords']>
    readonly useAnswerService: UnwrapRef<typeof import('../../composables/useAnswerService')['useAnswerService']>
    readonly useApi: UnwrapRef<typeof import('../../composables/index')['useApi']>
    readonly useApiBaseUrl: UnwrapRef<typeof import('../../../base/composables/useApiBaseUrl')['default']>
    readonly useAppConfig: UnwrapRef<typeof import('../../../node_modules/nuxt/dist/app')['useAppConfig']>
    readonly useAsyncData: UnwrapRef<typeof import('../../../node_modules/nuxt/dist/app')['useAsyncData']>
    readonly useAttrs: UnwrapRef<typeof import('../../../node_modules/vue')['useAttrs']>
    readonly useAuditionValidity: UnwrapRef<typeof import('../../../base/composables/useAuditionValidity')['default']>
    readonly useBeforeunload: UnwrapRef<typeof import('../../../base/composables/useBeforeunload')['useBeforeunload']>
    readonly useBehaviorRecord: UnwrapRef<typeof import('../../../base/composables/useBehaviorRecord')['useBehaviorRecord']>
    readonly useBem: UnwrapRef<typeof import('../../../base/composables/useBem')['useBem']>
    readonly useBusinessComp: UnwrapRef<typeof import('../../../base/composables/useBusinessComp')['default']>
    readonly useCaptchaVerify: UnwrapRef<typeof import('../../../base/composables/useCaptchaVerify')['default']>
    readonly useCheckSchool: UnwrapRef<typeof import('../../composables/useCheckSchool')['useCheckSchool']>
    readonly useCheckSchoolNotButtonStatus: UnwrapRef<typeof import('../../composables/useCheckSchool')['useCheckSchoolNotButtonStatus']>
    readonly useCheckSchoolNotVerifyLogin: UnwrapRef<typeof import('../../composables/useCheckSchool')['useCheckSchoolNotVerifyLogin']>
    readonly useCluesExtension: UnwrapRef<typeof import('../../../base/composables/useCluesExtension')['default']>
    readonly useCompatibleIOSBlur: UnwrapRef<typeof import('../../composables/useCompatibleIOSBlur')['useCompatibleIOSBlur']>
    readonly useComponentEvents: UnwrapRef<typeof import('../../composables/useComponentEvents')['useComponentEvents']>
    readonly useComponentEventsHome: UnwrapRef<typeof import('../../composables/useComponentEventsHome')['useComponentEventsHome']>
    readonly useComputedQuestionCount: UnwrapRef<typeof import('../../../base/composables/useComputedQuestionCount')['useComputedQuestionCount']>
    readonly useCookie: UnwrapRef<typeof import('../../../node_modules/nuxt/dist/app')['useCookie']>
    readonly useCourseId: UnwrapRef<typeof import('../../../base/composables/useCourseId')['default']>
    readonly useCoursePayBtnText: UnwrapRef<typeof import('../../../base/composables/useCoursePayBtnText')['default']>
    readonly useCourseUpdateStatus: UnwrapRef<typeof import('../../composables/useCourseUpdateStatus')['useCourseUpdateStatus']>
    readonly useCpClueSource: UnwrapRef<typeof import('../../composables/useCpClueSource')['useCpClueSource']>
    readonly useCrmSharePrice: UnwrapRef<typeof import('../../../base/composables/useCrmSharePrice')['default']>
    readonly useCssModule: UnwrapRef<typeof import('../../../node_modules/vue')['useCssModule']>
    readonly useCssVars: UnwrapRef<typeof import('../../../node_modules/vue')['useCssVars']>
    readonly useCustomPageWap: UnwrapRef<typeof import('../../composables/useCustomPageWap')['default']>
    readonly useCustomizedTenants: UnwrapRef<typeof import('../../../base/composables/useCustomizedTenants')['default']>
    readonly useDeviceDetection: UnwrapRef<typeof import('../../../base/composables/useDeviceDetection')['default']>
    readonly useDownloadZip: UnwrapRef<typeof import('../../composables/useDownloadZip')['useDownloadZip']>
    readonly useError: UnwrapRef<typeof import('../../../node_modules/nuxt/dist/app')['useError']>
    readonly useFetch: UnwrapRef<typeof import('../../../node_modules/nuxt/dist/app')['useFetch']>
    readonly useFetchOptions: UnwrapRef<typeof import('../../../base/composables/useFetchOptions')['default']>
    readonly useFileView: UnwrapRef<typeof import('../../../base/composables/useFileView')['useFileView']>
    readonly useGetAgentQuery: UnwrapRef<typeof import('../../composables/useGetAgentQuery')['default']>
    readonly useGetProductSide: UnwrapRef<typeof import('../../../base/composables/usePageTrack')['useGetProductSide']>
    readonly useHeLiKefu: UnwrapRef<typeof import('../../composables/useHeLiKefu')['useHeLiKefu']>
    readonly useHead: UnwrapRef<typeof import('../../../node_modules/@unhead/vue')['useHead']>
    readonly useHeadSafe: UnwrapRef<typeof import('../../../node_modules/@unhead/vue')['useHeadSafe']>
    readonly useHistoryState: UnwrapRef<typeof import('../../../base/composables/useHistoryState')['useHistoryState']>
    readonly useHttp: UnwrapRef<typeof import('../../../base/composables/useHttp')['useHttp']>
    readonly useHttpClient: UnwrapRef<typeof import('../../../base/composables/useHttpClient')['useHttpClient']>
    readonly useJumpQuestionPurchased: UnwrapRef<typeof import('../../composables/useJumpQuestionPurchased')['useJumpQuestionPurchased']>
    readonly useKKNews: UnwrapRef<typeof import('../../composables/useBusinessInteractive')['useKKNews']>
    readonly useKeyboardOpen: UnwrapRef<typeof import('../../composables/useKeyboardOpen')['useKeyboardOpen']>
    readonly useKuKeCloud: UnwrapRef<typeof import('../../../base/composables/useKuKeCloud')['useKuKeCloud']>
    readonly useKuke99DefaultHomeData: UnwrapRef<typeof import('../../composables/index')['useKuke99DefaultHomeData']>
    readonly useLabelSwitch: UnwrapRef<typeof import('../../composables/useLabelSwitch')['default']>
    readonly useLazyAsyncData: UnwrapRef<typeof import('../../../node_modules/nuxt/dist/app')['useLazyAsyncData']>
    readonly useLazyFetch: UnwrapRef<typeof import('../../../node_modules/nuxt/dist/app')['useLazyFetch']>
    readonly useLearnTarget: UnwrapRef<typeof import('../../../base/composables/useLearnTarget')['useLearnTarget']>
    readonly useLearnTargetFullpath: UnwrapRef<typeof import('../../../base/composables/useLearnTargetFullpath')['useLearnTargetFullpath']>
    readonly useLearnTargetState: UnwrapRef<typeof import('../../../base/composables/useLearnTargetState')['useLearnTargetState']>
    readonly useLearnTargetTree: UnwrapRef<typeof import('../../../base/composables/useLearnTarget')['useLearnTargetTree']>
    readonly useLink: UnwrapRef<typeof import('../vue-router')['useLink']>
    readonly useLocalStorage: UnwrapRef<typeof import('../../../base/composables/useAdCountdown')['useLocalStorage']>
    readonly useMemberUtils: UnwrapRef<typeof import('../../composables/useUserMember')['useMemberUtils']>
    readonly useMemberWxShareInfo: UnwrapRef<typeof import('../../composables/useUserMember')['useMemberWxShareInfo']>
    readonly useMessage: UnwrapRef<typeof import('../../../base/composables/useMessage')['useMessage']>
    readonly useModel: UnwrapRef<typeof import('../../../node_modules/vue')['useModel']>
    readonly useNamespace: UnwrapRef<typeof import('../../../base/composables/useNamespace')['useNamespace']>
    readonly useNuxtApp: UnwrapRef<typeof import('../../../node_modules/nuxt/dist/app')['useNuxtApp']>
    readonly useNuxtData: UnwrapRef<typeof import('../../../node_modules/nuxt/dist/app')['useNuxtData']>
    readonly useOnline: UnwrapRef<typeof import('../../../base/composables/useOnline')['useOnline']>
    readonly useOrderSns: UnwrapRef<typeof import('../../composables/useOrderSns')['useOrderSns']>
    readonly usePageTrack: UnwrapRef<typeof import('../../../base/composables/usePageTrack')['usePageTrack']>
    readonly usePinia: UnwrapRef<typeof import('../../../node_modules/@pinia/nuxt/dist/runtime/composables')['usePinia']>
    readonly usePointsClueSource: UnwrapRef<typeof import('../../composables/useCpClueSource')['usePointsClueSource']>
    readonly usePreventDefaultLink: UnwrapRef<typeof import('../../composables/usePreventDefaultLink')['usePreventDefaultLink']>
    readonly useQuestSortStem: UnwrapRef<typeof import('../../../base/composables/useQuestionBank')['useQuestSortStem']>
    readonly useQuestionBank: UnwrapRef<typeof import('../../../base/composables/useQuestionBank')['useQuestionBank']>
    readonly useQuestionBankClassifyInfo: UnwrapRef<typeof import('../../composables/useQuestionBankClassifyInfo')['useQuestionBankClassifyInfo']>
    readonly useQuestionBankDataChange: UnwrapRef<typeof import('../../composables/useQuestionBankDataChange')['useQuestionBankDataChange']>
    readonly useQuestionBankFillJudge: UnwrapRef<typeof import('../../../base/composables/useQuestionBankFillJudge')['useQuestionBankFillJudge']>
    readonly useQuestionBankHandleEvents: UnwrapRef<typeof import('../../composables/useQuestionBankHandleEvents')['useQuestionBankHandleEvents']>
    readonly useQuestionBankHelp: UnwrapRef<typeof import('../../../base/composables/useQuestionBankHelp')['useQuestionBankHelp']>
    readonly useQuestionBankInit: UnwrapRef<typeof import('../../composables/useQuestionBankInit')['useQuestionBankInit']>
    readonly useQuestionBankInitQuery: UnwrapRef<typeof import('../../composables/useQuestionBankInitQuery')['useQuestionBankInitQuery']>
    readonly useQuestionBankList: UnwrapRef<typeof import('../../composables/useQuestionBankList')['useQuestionBankList']>
    readonly useQuestionBankModuleIcon: UnwrapRef<typeof import('../../composables/useQuestionBankModuleIcon')['useQuestionBankModuleIcon']>
    readonly useQuestionBankModuleInfo: UnwrapRef<typeof import('../../composables/useQuestionBankModuleInfo')['useQuestionBankModuleInfo']>
    readonly useQuestionBankModulePrivilege: UnwrapRef<typeof import('../../composables/useQuestionBankModulePrivilege')['useQuestionBankModulePrivilege']>
    readonly useQuestionBankMultipleTag: UnwrapRef<typeof import('../../composables/useQuestionBankMultipleTag')['useQuestionBankMultipleTag']>
    readonly useQuestionBankPromotion: UnwrapRef<typeof import('../../../base/composables/useQuestionBankPromotion')['useQuestionBankPromotion']>
    readonly useQuestionBankTag: UnwrapRef<typeof import('../../composables/useQuestionBankTag')['useQuestionBankTag']>
    readonly useQuestionCorrected: UnwrapRef<typeof import('../../../base/composables/useQuestionCorrected')['useQuestionCorrected']>
    readonly useQuestionExamModel: UnwrapRef<typeof import('../../../base/composables/useQuestionExamModel')['useQuestionExamModel']>
    readonly useQuestionFilter: UnwrapRef<typeof import('../../composables/useQuestionFiltervajra')['useQuestionFilter']>
    readonly useQuestionLandingPage: UnwrapRef<typeof import('../../composables/useQuestionLandingPage')['useQuestionLandingPage']>
    readonly useQuestionListIsWhite: UnwrapRef<typeof import('../../composables/useQuestionListIsWhite')['useQuestionListIsWhite']>
    readonly useQuestionPurchasedModuleTag: UnwrapRef<typeof import('../../composables/useQuestionPurchasedModuleTag')['useQuestionPurchasedModuleTag']>
    readonly useQuestionVideoInfo: UnwrapRef<typeof import('../../../base/composables/useQuestionVideoInfo')['useQuestionVideoInfo']>
    readonly useRecorder: UnwrapRef<typeof import('../../../base/composables/useRecorder')['useRecorder']>
    readonly useRefreshQuestionListData: UnwrapRef<typeof import('../../composables/useRefreshQuestionListData')['useRefreshQuestionListData']>
    readonly useRequestEvent: UnwrapRef<typeof import('../../../node_modules/nuxt/dist/app')['useRequestEvent']>
    readonly useRequestFetch: UnwrapRef<typeof import('../../../node_modules/nuxt/dist/app')['useRequestFetch']>
    readonly useRequestHeaders: UnwrapRef<typeof import('../../../node_modules/nuxt/dist/app')['useRequestHeaders']>
    readonly useRequestURL: UnwrapRef<typeof import('../../../node_modules/nuxt/dist/app')['useRequestURL']>
    readonly useRoute: UnwrapRef<typeof import('../../../node_modules/nuxt/dist/app')['useRoute']>
    readonly useRouteLeaveGuard: UnwrapRef<typeof import('../../../base/composables/useNewBeforeUnload')['useRouteLeaveGuard']>
    readonly useRouter: UnwrapRef<typeof import('../../../node_modules/nuxt/dist/app')['useRouter']>
    readonly useRuntimeConfig: UnwrapRef<typeof import('../../../node_modules/nuxt/dist/app')['useRuntimeConfig']>
    readonly useSKU: UnwrapRef<typeof import('../../composables/useSKU')['default']>
    readonly useScrollPenetrate: UnwrapRef<typeof import('../../../base/composables/useScrollPenetrate')['useScrollPenetrate']>
    readonly useScrollViewBack: UnwrapRef<typeof import('../../composables/useScrollViewBack')['useScrollViewBack']>
    readonly useSelectExam: UnwrapRef<typeof import('../../composables/useSelectExam')['useSelectExam']>
    readonly useSendBeacon: UnwrapRef<typeof import('../../../base/composables/useSendBeacon')['useSendBeacon']>
    readonly useSeoMeta: UnwrapRef<typeof import('../../../node_modules/@unhead/vue')['useSeoMeta']>
    readonly useServerHead: UnwrapRef<typeof import('../../../node_modules/@unhead/vue')['useServerHead']>
    readonly useServerHeadSafe: UnwrapRef<typeof import('../../../node_modules/@unhead/vue')['useServerHeadSafe']>
    readonly useServerSeoMeta: UnwrapRef<typeof import('../../../node_modules/@unhead/vue')['useServerSeoMeta']>
    readonly useSetQuestionBankShare: UnwrapRef<typeof import('../../composables/useSetQuestionBankShare')['useSetQuestionBankShare']>
    readonly useSlots: UnwrapRef<typeof import('../../../node_modules/vue')['useSlots']>
    readonly useState: UnwrapRef<typeof import('../../../node_modules/nuxt/dist/app')['useState']>
    readonly useTheme: UnwrapRef<typeof import('../../../base/composables/useTheme')['default']>
    readonly useThirdJumpQuestionBank: UnwrapRef<typeof import('../../../base/composables/useThirdJumpQuestionBank')['useThirdJumpQuestionBank']>
    readonly useTrackEvent: UnwrapRef<typeof import('../../composables/useTrackEvent')['useTrackEvent']>
    readonly useTransitionState: UnwrapRef<typeof import('../../../node_modules/vue')['useTransitionState']>
    readonly useUpdateQuestionBankBehaviorRecords: UnwrapRef<typeof import('../../../base/composables/useUpdateQuestionBankBehaviorRecords')['useUpdateQuestionBankBehaviorRecords']>
    readonly useUserMember: UnwrapRef<typeof import('../../composables/useUserMember')['useUserMember']>
    readonly useVaildLT: UnwrapRef<typeof import('../../composables/useVaildLT')['useVaildLT']>
    readonly useVisibilityChange: UnwrapRef<typeof import('../../../base/composables/useVisibilityChange')['useVisibilityChange']>
    readonly useWxShare: UnwrapRef<typeof import('../../composables/useWxShare')['useWxShare']>
    readonly useZIndex: UnwrapRef<typeof import('../../../base/composables/useZIndex')['useZIndex']>
    readonly userData: UnwrapRef<typeof import('../../composables/userData')['userData']>
    readonly userDomainConfig: UnwrapRef<typeof import('../../../base/composables/userDomainConfig')['userDomainConfig']>
    readonly userInfoProt: UnwrapRef<typeof import('../../apis/home')['userInfoProt']>
    readonly userWxPhoto: UnwrapRef<typeof import('../../apis/index')['userWxPhoto']>
    readonly validateNickname: UnwrapRef<typeof import('../../../base/composables/isRegexp')['validateNickname']>
    readonly watch: UnwrapRef<typeof import('../../../node_modules/vue')['watch']>
    readonly watchEffect: UnwrapRef<typeof import('../../../node_modules/vue')['watchEffect']>
    readonly watchPostEffect: UnwrapRef<typeof import('../../../node_modules/vue')['watchPostEffect']>
    readonly watchSyncEffect: UnwrapRef<typeof import('../../../node_modules/vue')['watchSyncEffect']>
    readonly withCtx: UnwrapRef<typeof import('../../../node_modules/vue')['withCtx']>
    readonly withDirectives: UnwrapRef<typeof import('../../../node_modules/vue')['withDirectives']>
    readonly withKeys: UnwrapRef<typeof import('../../../node_modules/vue')['withKeys']>
    readonly withMemo: UnwrapRef<typeof import('../../../node_modules/vue')['withMemo']>
    readonly withModifiers: UnwrapRef<typeof import('../../../node_modules/vue')['withModifiers']>
    readonly withScopeId: UnwrapRef<typeof import('../../../node_modules/vue')['withScopeId']>
  }
}