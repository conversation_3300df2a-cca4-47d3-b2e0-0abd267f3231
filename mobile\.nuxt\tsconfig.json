// Generated by nuxi
{
  "compilerOptions": {
    "forceConsistentCasingInFileNames": true,
    "jsx": "preserve",
    "jsxImportSource": "vue",
    "target": "ESNext",
    "module": "ESNext",
    "moduleResolution": "Node",
    "skipLibCheck": true,
    "isolatedModules": true,
    "useDefineForClassFields": true,
    "strict": true,
    "noImplicitThis": true,
    "esModuleInterop": true,
    "allowJs": true,
    "noEmit": true,
    "resolveJsonModule": true,
    "allowSyntheticDefaultImports": true,
    "paths": {
      "~": [
        ".."
      ],
      "~/*": [
        "../*"
      ],
      "@": [
        ".."
      ],
      "@/*": [
        "../*"
      ],
      "~~": [
        ".."
      ],
      "~~/*": [
        "../*"
      ],
      "@@": [
        ".."
      ],
      "@@/*": [
        "../*"
      ],
      "assets": [
        "../assets"
      ],
      "assets/*": [
        "../assets/*"
      ],
      "public": [
        "../public"
      ],
      "public/*": [
        "../public/*"
      ],
      "#app": [
        "../../node_modules/nuxt/dist/app"
      ],
      "#app/*": [
        "../../node_modules/nuxt/dist/app/*"
      ],
      "vue-demi": [
        "../../node_modules/nuxt/dist/app/compat/vue-demi"
      ],
      "pinia": [
        "../../node_modules/pinia/dist/pinia"
      ],
      "#vue-router": [
        "./vue-router"
      ],
      "#imports": [
        "./imports"
      ],
      "#build": [
        "."
      ],
      "#build/*": [
        "./*"
      ],
      "#components": [
        "./components"
      ]
    }
  },
  "include": [
    "./nuxt.d.ts",
    "../**/*",
    "../../base/**/*",
    "../../**/*",
    "../../../../m/runtime",
    "../../node_modules/@nuxtjs/eslint-module/runtime",
    "../../node_modules/@pinia/nuxt/runtime",
    "../../node_modules/nuxt-icons/runtime",
    "../../node_modules/@vant/nuxt/runtime",
    "../../node_modules/nuxt-vite-legacy/runtime",
    "../../node_modules/@pinia-plugin-persistedstate/nuxt/runtime",
    "..",
    "../../node_modules/nuxt/dist/app",
    "../../node_modules/nuxt/dist/app/compat/vue-demi",
    "../../node_modules/pinia/dist/pinia"
  ],
  "exclude": [
    "../node_modules",
    "../../node_modules",
    "../../node_modules/nuxt/node_modules",
    "../../../../m/runtime/server",
    "../../node_modules/@nuxtjs/eslint-module/runtime/server",
    "../../node_modules/@pinia/nuxt/runtime/server",
    "../../node_modules/nuxt-icons/runtime/server",
    "../../node_modules/@vant/nuxt/runtime/server",
    "../../node_modules/nuxt-vite-legacy/runtime/server",
    "../../node_modules/@pinia-plugin-persistedstate/nuxt/runtime/server",
    "../dist",
    "dev"
  ]
}