<template>
  <span :class="[bem('content'), 'truncate']">{{ getNames }}</span>
</template>

<script setup lang="ts">
// 该组件即将废弃
const bem = useBem('home-navbar-learn-target')
const localId = useCookie<string>('LT')
const {
  isXcx
} = useAppConfig()
const {
  usableId,
} = useLearnTarget()

const body = {
  pageMasterId: localId.value || usableId.value,
  productSide: isXcx ? '5' : '7',
}
const { data: list } = await useHttp<object>('/kukemarketing/wap/pageMaster/getTypeByPageMasterId', {
  body,
  key: body.pageMasterId + 'getTypeByPageMasterId',
  transform: input => input.data,
})
const getNames = computed(() => list?.value?.map(v => v.categoryName)?.join(' · '))

</script>

<style lang="scss"></style>
