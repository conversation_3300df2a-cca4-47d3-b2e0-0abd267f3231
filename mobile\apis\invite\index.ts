import type { HelpFriendFetchParams, InviteRankingListFetchParams, ReceiveWelfareFetchParams, ShippingAddressFetchParams, ShippingAddressInfoDTO, InviteRankingListResponseData, ActivityStatusInfoDTO } from './types'

enum invite {
  getInviteFreeDetail = '/kukecoupon/wap/marketingGoods/inviteFree',
  getInviteFreePosterInfo = '/kukecoupon/wap/marketingGoods/generateInviteFreePosterProt',
  helpFriend = '/kukecoupon/wap/marketingGoods/friendHelp',
  getInviteRankingList = '/kukecoupon/wap/marketingGoods/inviteRanking',
  receiveWelfare = '/kukecoupon/wap/marketingGoods/userReceive',
  bindShippingAddress = '/kukecoupon/wap/marketingGoods/registerInviteFreeReceivingMagProt',
  getShippingAddress = '/kukecoupon/wap/marketingGoods/queryInviteFreeReceivingMagProt',
  getActivityStatus = '/kukecoupon/wap/marketingGoods/getActivityStatus',
  getInviteFreeShareInfo = 'kukecoupon/wap/marketingGoods/getInviteFreeShareInfo',
  getInviteRecord = 'kukecoupon/wap/marketingGoods/queryInviteFreeClaimRecord'
}

/**
 * 活动详情信息查询
 * @param body
 * @returns
 */
export async function getInviteFreeDetail (body: { id: string }) {
  return useHttp<any>(invite.getInviteFreeDetail, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/*
 *获取邀请海报信息
*/
export async function getInviteFreePosterInfo (body: {}) {
  return useHttp<any>(invite.getInviteFreePosterInfo, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 【C端】新用户助力
 * @param body
 * @returns
 */
export async function helpFriend (body: HelpFriendFetchParams) {
  return useHttp<any>(invite.helpFriend, {
    method: 'post',
    body,
  })
}

/**
 * 【C端】排行榜
 * @param body
 * @returns
 */
export async function getInviteRankingList (body: InviteRankingListFetchParams) {
  return useHttp<InviteRankingListResponseData>(invite.getInviteRankingList, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 领取福利
 * @param body
 * @returns
 */
export async function receiveWelfare (body: ReceiveWelfareFetchParams) {
  return useHttp<any>(invite.receiveWelfare, {
    method: 'post',
    body,
  })
}

/**
 * 【C端】用户登记收货信息
 * @param body
 * @returns
 */
export async function bindShippingAddress (body: ShippingAddressFetchParams) {
  return useHttp<any>(invite.bindShippingAddress, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 【C端】回显收货信息
 * @param body KeyId 自定义商品主键id
 * @returns
 */
export async function getShippingAddressInfo (body: {recordId:string}) {
  return useHttp<ShippingAddressInfoDTO>(invite.getShippingAddress, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 获取活动状态
 * @param body
 * @returns
 */
export async function getActivityStatus (body: {id:string}) {
  return useHttp<ActivityStatusInfoDTO>(invite.getActivityStatus, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 得到推广信息
 */
export async function getInviteFreeShareInfo (body: {id:string}) {
  return useHttp<any>(invite.getInviteFreeShareInfo, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 领取记录
 */
export async function getInviteRecord (body: {id:string}) {
  return useHttp<any>(invite.getInviteRecord, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}
