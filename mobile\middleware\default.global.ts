import { useUserStore } from '~/stores/user.store'
import { useLearnStore } from '~/stores/learn.store'
// const whiteSrollPath = ['/me', '/user/order']
export default defineNuxtRouteMiddleware(async (to, from) => {
  // const config = useRuntimeConfig()
  // const nuxtApp = useNuxtApp()
  // // eslint-disable-next-line @typescript-eslint/no-unused-vars
  // nuxtApp.$router.options.scrollBehavior = (to, from, savedPosition) => {
  //   // if (whiteSrollPath.includes(to.path)) {
  //   //   return { left: 0, top: 0 }
  //   // }
  //   // if (savedPosition) {
  //   //   return savedPosition
  //   // }
  //   return { left: 0, top: 0 }
  // }

  const userStore = useUserStore()
  const learnStore = useLearnStore()
  const { wxCode, qqCode, type } = useRoute().query
  // TODO state
  const { isXcx, logo2, theme } = useAppConfig()
  const themeColor = encodeURIComponent(theme?.brand?.color || '')
  const isLoginMiniProgram = useCookie<boolean>('isLoginMiniProgram', { default () { return false } })

  /**
   * 兼容自定义页面校验学习目标失败问题
  */

  if (to.path === '/') {
    learnStore.setPageMasterId('')
  } else if (to.path === '/special/free_space') {
    return navigateTo('/yun-free/1', { redirectCode: 301 })
  }
  // // 自定义页面不验证学习目标

  if (to.meta.ignoreLearnTarget || from.query?.pageType === '105') {
    learnStore.setIgnoreLearnTargetState(true)
    // 无学习目标，跳转到学习目标页面
    // if (to.meta.ignoreLearnTarget) { // [M端] 忽略验证学习目标的逻辑
    //   learnStore.setIgnoreLearnTargetState(true)
    // if (!learnTargetInfo.value.idOfCurrentUser && !localId.value && !to.query.lt && learnTargetInfo.value.enable) { // TODO idOfCurrentUser
    //   // console.warn('TODO 增加吐司提示')
    //   // if (isXcx) {
    //   //   nextTick(() => {
    //   //     userStore.jumpAppletWebPage('/learn-target')
    //   //   })
    //   //   return
    //   // }
    //   // return navigateTo('/learn-target')

    // }
    // }
  } else {
    learnStore.setIgnoreLearnTargetState(false)
  }

  const checkUserReg = /^\/user\//
  const checkLoginReg = /^\/login|\/wx|\/oauth/
  const whiteList = [
    '/login/sso-login',
    '/login/login-result',
    '/login/bind-mobile',
    '/login/wx-auth',
    '/question-bank/estimating/invite',
    '/question-bank/list',
    '/question-bank/fixed-exam/catalog',
    '/question-bank/paper/detail',
    '/question-bank/question/detail',
    // '/points',
    // '/points/mall',
    '/question-bank/help',
    '/question-bank/purchased/textbook',
    '/question-bank/fixed-exam/catalog',
    '/question-bank/search',
    '/question-bank/ai-exercise',
  ]
  // 题库路由校验规则
  const checkQuestionBankReg = /^\/question-bank\//

  if (checkUserReg.test(to.path) || (checkQuestionBankReg.test(to.path) && !whiteList.includes(to.path))) {
    if (!userStore.isLogin) {
      // let toPath = to.path
      let fromFullPath = from.fullPath
      // 题库三个本页面未登录情况下重定向跳转首页
      if (to.path.includes('/question-bank/exercise-book/')) {
        // toPath = '/'
        fromFullPath = '/'
      }
      if (isXcx) {
        if (isLoginMiniProgram.value) {
          return
        }
        isLoginMiniProgram.value = true
        wx.miniProgram.navigateBack({
          delta: 1,
        })
        wx.miniProgram.navigateTo({
          url: `/pages/login/index?redirect_uri=${encodeURIComponent(fromFullPath)}&logo2=${logo2 || ''}&themeColor=${themeColor}`,
          complete: function () {
            setTimeout(() => {
              isLoginMiniProgram.value = false
            }, 1000)
          },
        })
        return
      } else {
        // return location.replace(`${location.origin}/login?redirect_uri=${encodeURIComponent(from.fullPath)}`)
        return navigateTo({ path: '/login', query: { redirect_uri: encodeURIComponent(fromFullPath) } })
      }
    }
  }
  // 跳转商品详情页面短链生成长链
  const checkGoodsReg = /\/s\//
  // 检查是否包含 '/s/'
  if (checkGoodsReg.test(to.path)) {
    const startIdx = to.path.indexOf('/s/') + 3

    // 解码 URL
    let shortUrl
    try {
      const decodedUrl = decodeURIComponent(to.path)
      shortUrl = decodedUrl
    } catch (e) {
      shortUrl = to.path
    }

    // 查找商品两个字的位置
    const spaceIndex = shortUrl.indexOf('商品', startIdx)
    // 如果有商品两个字，则截取 '/s/' 到商品两个字之间的内容
    let param
    if (spaceIndex !== -1) {
      param = shortUrl.substring(startIdx, spaceIndex)
    } else {
      // 如果没有商品两个字，则截取 '/s/' 后面的所有内容
      param = to.path.substring(startIdx)
    }
    const { data } = await useHttp<string>(ApisCommon.getLongUrl, {
      method: 'post',
      body: {
        shortCode: param.trim(),
      },
      transform: res => res.data,
    })
    if (data.value) {
      return navigateTo(
        { path: data.value },
        {
          external: true,
          // 301 代表永久性转移 302 代表暂时性转移
          redirectCode: 302,
        }
      )
    }
  }

  if (checkLoginReg.test(to.path)) {
    if (userStore.isLogin && !whiteList.includes(to.path)) {
      return navigateTo('/')
    } else if (isXcx) {
      if (to.path !== '/login/weappLogin') {
        if (isLoginMiniProgram.value || to.fullPath.includes('/receive-page-h5/')) {
          return
        }
        isLoginMiniProgram.value = true
        wx.miniProgram.navigateTo({
          url: `/pages/login/index?redirect_uri=${encodeURIComponent(
            to.path
          )}&logo2=${logo2 || ''}&themeColor=${themeColor}`,
          complete: function () {
            setTimeout(() => {
              isLoginMiniProgram.value = false
            }, 1000)
          },
        })
        return
      }
    }
  }

  if (process.client) {
    if (qqCode || (wxCode && from.path !== '/login/login-result' && isWxBrowser)) {
      if (type === 'login') {
        const { data } = await userStore.thirdLogin({ code: (qqCode || wxCode) as string, oauthType: qqCode ? '1' : '2' })
        if (data.value?.data.isBindMobile === 0) {
          location.replace(`${location.origin}/login/bind-mobile`)
        }
      } else if (type === 'bind') {
        await userStore.loginBindThird({ code: (qqCode || wxCode) as string, oauthType: qqCode ? '1' : '2' })
        await userStore.getUserInfoData(false)
        if (qqCode) {
          if (isWxBrowser) {
            window.history.go(-2)
          } else {
            location.replace(`${location.origin}/user/edit-third`)
          }
        } else if (isXcx) {
          if (os?.isAndroid) {
            window.history.go(-1)
          } else {
            window.history.go(-2)
          }
        } else {
          window.history.back()
        }
      } else if (type === 'attention') {
        await userStore.loginBindThird({ code: wxCode as string, oauthType: '2' })
        await userStore.getUserInfoData(false)
        // 小程序授权后兼容问题
        if (isXcx) {
          if (os?.isAndroid) {
            window.history.go(-1)
          } else {
            window.history.go(-2)
          }
        } else {
          useRouter().back()
        }
        sessionStorage.setItem('isWxQrcode', '1')
      } else if (type === 'attentionWx') {
        const { data, error } = await userStore.loginBindThird({ code: wxCode as string, oauthType: '2' })
        await userStore.getUserInfoData(false)
        console.log('🚀 ~ defineNuxtRouteMiddleware ~ data, error:', data, error)
        // 代表当前绑定了多个微信号
        if (error?.value?.data.code === '23003') {
          // TODO
          console.log('呆在当前页')
        } else if (isXcx) {
        // 小程序授权后兼容问题
          if (os?.isAndroid) {
            window.history.go(-1)
          } else {
            window.history.go(-2)
          }
        } else {
          console.log('跳回去')
          useRouter().back()
        }
      }
    }
  }
})
