{"compilerOptions": {"forceConsistentCasingInFileNames": true, "strict": true, "noEmit": true, "skipLibCheck": true, "target": "ESNext", "module": "ESNext", "moduleResolution": "Node", "allowJs": true, "resolveJsonModule": true, "jsx": "preserve", "allowSyntheticDefaultImports": true, "jsxFactory": "h", "jsxFragmentFactory": "Fragment", "paths": {"#imports": ["./types/nitro-imports"], "~/*": ["../*"], "@/*": ["../*"], "~~/*": ["../*"], "@@/*": ["../*"], "#paths": ["../../node_modules/nuxt/dist/core/runtime/nitro/paths"], "~": ["./.."], "@": ["./.."], "~~": ["./.."], "@@": ["./.."], "assets": ["../assets"], "assets/*": ["../assets/*"], "public": ["../public"], "public/*": ["../public/*"], "pinia": ["../../node_modules/pinia/dist/pinia"]}}, "include": ["./types/nitro-nuxt.d.ts", "../../../../m/runtime/server", "../../node_modules/@nuxtjs/eslint-module/runtime/server", "../../node_modules/@pinia/nuxt/runtime/server", "../../node_modules/nuxt-icons/runtime/server", "../../node_modules/@vant/nuxt/runtime/server", "../../node_modules/nuxt-vite-legacy/runtime/server", "../../node_modules/@pinia-plugin-persistedstate/nuxt/runtime/server", "./types/nitro.d.ts", "../**/*", "../server/**/*"], "exclude": ["../node_modules", "../../node_modules", "../../node_modules/nuxt/node_modules", "../dist"]}