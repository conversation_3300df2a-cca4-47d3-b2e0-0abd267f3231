<template>
  <div>
    <div class=" relative">
      <div class="kk_audio_wrap" @touchstart="handleTouchStart" @touchend="handleTouchEnd">
        <img
          v-if="!curAudioPlayState"
          class="w-[48px] h-[48px] mr-[24px]  "
          :src="themeConfig.playIcon"
          alt=""
          @click.stop="playAudio"
        >
        <img v-else class="w-[48px] h-[48px] mr-[24px]  " :src="themeConfig.pauseIcon" alt="" @click.stop="pauseAudio">
        <div class="relative w-[200px] progress-wrap">
          <van-slider
            v-model="percentage"
            bar-height="4px"
            :active-color="themeConfig.activeColor"
            :inactive-color="themeConfig.inactiveColor"
            :class="position === 'left' ? 'left-audio':''"
            @change.stop="onChange"
            @drag-start.stop="handleStart"
            @drag-end.stop="handleEnd"
          />
        </div>
        <audio
          v-if="isLoad"
          :id="audioRef"
          :ref="el => {
            refFun(el)
          }
          "
          class="audio_wrap"
          @timeupdate="updateProgress"
        >
          <source :src="signUrl">
        </audio>
        <div class="ml-[24px] text-[24px]" :class="[themeConfig.timeColor]">
          <span>{{ curTime }}</span>
          <span class="mx-[2px]">/</span>
          <span>{{
            duration === "00:00" ? durationAudio(paramsDuration) : duration
          }}</span>
        </div>
      </div>
      <!-- 转文字文案 -->
      <!-- 未读 默认展示 -->
      <template v-if="state.manualServiceFlag && state.voiceToTextFlag">
        <div
          v-if="position === 'left' && ((!audioText && isRead === '0' && isReadIcon) || ( !audioText && isTransIcon && isRead === '1' )) "
          class="absolute flex items-center audio-text-tips"
          :class="position === 'left' ? 'tips-left' : 'tips-right'"
          @click="handleTransText"
        >
          <span class="circle mr-[12px]" />
          <span class="text-[20px] text-[#666] ">转文字</span>
        </div>
      </template>
    </div>
    <div v-if="isTransText" class="pt-[24px]">
      <div class="text-[32px] text-[#111] leading-[38px] pt-[24px] border-t-[1px] border-[#f0f0f0] border-solid">
        {{ audioText }}
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>

// icon
import { useAudio } from './hooks/useAudio'
import PlayIcon from '@/assets/images/answer/icon1.png'
import PauseIcon from '@/assets/images/answer/icon6.png'
import LeftPlayIcon from '@/assets/images/answer/left-play.png'
import LeftPauseIcon from '@/assets/images/answer/left-pause.png'
// store
import { useChatStore } from '~/stores/chat.store'
import { useAnswerStore } from '~/stores/answer.store'
const { state } = useAnswerStore()
const { updateCurPlayingMedia } = useChatStore()

const { answerApi } = useApi()

const props = withDefaults(
  defineProps<{
    audioRef?: string
    audioSrc: string
    isPrivate?: boolean // 是否私有
    signFun?: Function // 私有调用方法
    ossInstance?: any
    position?: string
    paramsDuration?: any,
    isRead: string,
    answerContent: any,
    id: string
  }>(),
  {
    audioRef: 'audioRef',
    isPrivate: true,
    signFun: () => {
      return () => { }
    },
    position: 'right',
    paramsDuration: '0',
    isRead: '0'
  }
)

const themeConfig = computed(() => {
  const playIcon = props.position === 'right' ? PlayIcon : LeftPlayIcon
  const pauseIcon = props.position === 'right' ? PauseIcon : LeftPauseIcon
  const activeColor = props.position === 'right' ? '#fff' : '#000'
  const inactiveColor = props.position === 'right' ? 'rgba(255,255,255,0.2)' : 'rgba(0, 0, 0, 0.20)'
  const timeColor = props.position === 'right' ? 'text-[#fff]' : 'text-[#111]'
  console.log(props.position)
  return {
    playIcon,
    pauseIcon,
    activeColor,
    inactiveColor,
    timeColor
  }
})
const emits = defineEmits<{
  (
    e: 'playeringRef',
    value: {
      playingRef: string
      ref: any
      src: string
    }
  ): void
}>()
const { transTime } = useAudio()
const { $dayjs: dayjs } = useNuxtApp()

const durationAudio = (time: string) => {
  return dayjs.unix(Number(time)).format('mm:ss')
}
/**
   * 当前audio ref
   */
const curAudioRef = ref()
const refFun = (el: any) => {
  curAudioRef.value = el
}
/**
   * 音频播放状态
   */
const curAudioPlayState = ref<boolean>(false)
/**
   * 是否是updateProgress事件触发的percentage变更
   */
const isUpdateProgres = ref<boolean>(false)
/**
   * 当前时长
   */
const curTime = ref<string>('00:00')
/**
   * 总时长
   */
const duration = ref<string>('00:00')
/**
   * 进度
   */
const percentage = ref<number>(0)
/**
   * 默认音量大小
   */
const voiceSize = ref<number>(0.8)

/**
 *
 * @param val
 * 点击进度条
 */

const onChange = () => {
  console.log('change')

  isUpdateProgres.value = false
  playAudio()
}

const handleStart = () => {
  console.log('start')

  pauseAudio()
}

const isLongPress = ref(false)

const handleEnd = () => {
  console.log('end')

  isUpdateProgres.value = false
  playAudio()
}

/**
   * 播放
   */
const playAudio = () => {
  console.log('play')
  isTransIcon.value = false
  isReadIcon.value = false
  if (!props.isPrivate || isLoad.value) {
    isUpdateProgres.value = false
    if (!curAudioPlayState || isUpdateProgres.value) { return }
    curAudioRef.value.currentTime =
      (curAudioRef.value.duration * percentage.value / 100) || 0
    console.log(curAudioRef.value.currentTime, 'curAudioRef.value.currentTime')

    curAudioRef.value.play()
    curAudioPlayState.value = true
  } else {
    asyncSignUrl()
  }
  updateCurPlayingMedia({
    playingRef: props.audioRef,
    option: {
      closePlayer
    }
  })
  handleRead()
  emits('playeringRef', {
    playingRef: props.audioRef,
    ref: curAudioRef,
    src: signUrl.value
  })
}
/**
   * 暂停
   */
const pauseAudio = () => {
  curAudioRef.value.pause()
  curAudioPlayState.value = false
  isUpdateProgres.value = false
}
/**
   * 静音
   */
const isMute = ref<boolean>(false)
const voiceAudio = () => {
  isMute.value = !isMute.value
  curAudioRef.value.volume = 0
}
/**
   * 声音
   */
const muteAudio = () => {
  isMute.value = !isMute.value
  curAudioRef.value.volume = voiceSize.value
}
/**
   * 更新进度条与当前播放时间
   */
const updateProgress = (e: any) => {
  isUpdateProgres.value = true
  const curProgress = (e.target.currentTime / e.target.duration) * 100
  percentage.value = Number(curProgress.toFixed(2))
  // console.log(percentage.value)
  curTime.value = transTime(e.target.currentTime)
  // 播放结束
  if (percentage.value >= 100) {
    curAudioPlayState.value = false
  }
}
/**
   * 获取总时长
   */
const getDuration = () => {
  const audioRef = curAudioRef.value
  audioRef.loop = false
  const nullFun = () => { }
  // 监听音频播放完毕
  audioRef.addEventListener('ended', nullFun, false)
  if (audioRef != null) {
    audioRef.oncanplay = function () {
      duration.value = transTime(audioRef.duration) // 计算音频时长
    }
    audioRef.volume = voiceSize.value // 设置音量50%
  }
  audioRef.removeEventListener('ended', nullFun)
}
/**
   * 暂停播放，并重置进度条
   */
const closePlayer = () => {
  if (!curAudioRef.value) { return }
  pauseAudio()
  curAudioRef.value.currentTime = 0
}
// 私有调用方法
const signUrl = ref<string>('')
const loading = ref<boolean>(false)
const isLoad = ref<boolean>(false)
const asyncSignUrl = async () => {
  if (!props.isPrivate) {
    signUrl.value = props.audioSrc
    return
  }
  if (isLoad.value) { return } // 如果已经加载，无需再次请求
  loading.value = true
  console.log(props.audioSrc, '===========')
  const { result } = await props.signFun(props.audioSrc, {}, props.ossInstance)
  if (result?.length) {
    signUrl.value = result[0]
    isLoad.value = true
    loading.value = false
    setTimeout(() => {
      getDuration()
      playAudio()
    })
  }
}

const isTransIcon = ref(false)

const touchTimer = ref()
/**
 * 长按开始
 */

const handleTouchStart = async (event: TouchEvent) => {
  event.preventDefault()

  touchTimer.value = setTimeout(() => {
    isLongPress.value = true
    isTransIcon.value = true
    isReadIcon.value = true
  }, 500)
}

/**
 * 长按结束
 */

const handleTouchEnd = (event: TouchEvent) => {
  console.log(event)

  event.preventDefault()

  if (touchTimer.value) {
    clearTimeout(touchTimer.value)
    touchTimer.value = null
  }
  if (isLongPress.value) {
    isLongPress.value = false
    console.log('Long press ended')
    // 在这里可以执行长按结束后的操作
  } else {
    // 如果不是长按，则执行点击操作
    handleClick(event)
  }
}
const handleClick = (event: TouchEvent) => {
  if (!isLongPress.value) {
    const target = event.target as HTMLElement
    if (target.tagName.toLowerCase() === 'img') {
      console.log('Clicked on img element')
      if (curAudioPlayState.value) {
        pauseAudio()
      } else {
        playAudio()
      }
    } else {
      console.log('Clicked on non-img element')
    }
  }
}

const isTransText = ref(false)

/**
* 转文字
*/

const audioText = ref('')

const isReadIcon = ref(true)

const handleTransText = async () => {
  const flag = await useAnswerService('manual', 'voice')
  if (flag) {
    return
  }
  Loading(true)
  const { result } = await props.signFun(props.audioSrc, {}, props.ossInstance)
  const url = result[0]
  const { data, error } = await answerApi.getAudioText({
    address: url
  })
  if (!error.value) {
    if (data.value?.status === 20000000) {
      if (data.value?.sentences?.length) {
        const { sentences } = data.value || {}
        sentences.forEach((item:any) => {
          audioText.value += item.text
        })
        handleRead()
      } else {
        Message('转文字失败')
      }
      isTransIcon.value = false
      isTransText.value = true
      isReadIcon.value = false
    }
  }
  Loading(false)
}

const handleRead = async () => {
  const readParams = {
    answerContent: JSON.stringify({
      ...props.answerContent,
      isRead: '1'
    }),
    answerId: props.id
  }
  await answerApi.setReadMsg(readParams)
}

onMounted(async () => {
  if (!props.isPrivate) {
    isLoad.value = true
    await nextTick()
    try {
      signUrl.value = props.audioSrc
      await getDuration()
    } catch (e) {
      console.log(e)
    }
  }
})
defineExpose({
  playAudio,
  pauseAudio,
  muteAudio,
  voiceAudio,
  closePlayer
})
</script>
<style lang="scss" scoped>
.kk_audio_wrap {
  display: flex;
  align-items: center;

  :deep(.van-slider__button) {
    width: 16px;
    height: 16px;
    box-shadow: none;
  }
  .left-audio{
    :deep(.van-slider__button) {
      background-color: #111;
    }
  }

}
.no-touch{
  touch-action: none;
}
.audio-text-tips {

  top: 50%;
  transform: translateY(-50%);

.circle {
  @apply bg-brand;
  width: 12px;
  height: 12px;
  border-radius: 50%;
}
}

.tips-left {
  right: -114px;
}

.tips-right {
  left: -114px;
}
</style>
