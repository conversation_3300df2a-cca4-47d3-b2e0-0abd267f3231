/**
 * @name: 发送请求
 * @param elseCb: 不支持sendBeacon时执行的回调函数
 * @description: 导出一个名为useSendBeacon的函数，该函数接受一个可选的elseCb参数，用于在不支持navigator.sendBeacon的浏览器中执行回调函数
 */
export const useSendBeacon = (elseCb?: () => void) => {
  // 定义一个名为navigatorSendBeacon的函数，该函数接受一个url和data参数，用于发送数据
  const navigatorSendBeacon = (url: string, data: any) => {
    // 判断浏览器是否支持navigator.sendBeacon
    if (navigator.sendBeacon) {
      // 如果支持，则使用navigator.sendBeacon发送数据
      navigator.sendBeacon(url, data)
    } else {
      // // 如果不支持，则使用fetch发送数据
      // fetch(url, {
      //   method: 'POST',
      //   body: data,
      // })
      // 如果elseCb存在，则执行elseCb回调函数
      elseCb && elseCb()
    }
  }

  // 返回一个包含navigatorSendBeacon的对象
  return {
    navigatorSendBeacon,
  }
}
