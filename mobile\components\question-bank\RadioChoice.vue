<!-- eslint-disable vue/no-v-html -->
<template>
  <!-- 单选题、判断题 -->
  <div :id="`ques-` + questionItem.id" class="radio-option">
    <div class="exam-quest-title">
      <span class="quest-title">
        {{ questionItem.chineseNum ? `${questionItem.chineseNum}、` : '' }}
        {{ questionTitle }}
      </span>
      <span v-if="(moduleType === 2 || moduleType === 0) && !isMaterial" class="explain" @click="onDescriptionClick">
        <span v-if="question.description">（{{ question.description }}）</span>&nbsp;
        <KKCIcon v-if="question.description?.length > 15" class="quest-title-right-icon" name="icon-wode-youjiantou" :size="24" />
      </span>
    </div>
    <div class="mt-[16px] mb-[48px] text-[#333] text-[32px] exam-html">
      <div v-img-preview="question.stem">
        <KKCHtmlMathJax
          :content="useQuestSortStem(
            question.stem,
            question.sort,
            moduleType,
            question.score
          )"
        />
      </div>

      <KKCAudioPlayer
        v-if="question.stemSrc"
        :icon-name="'icon-yinpinbofang'"
        :playing-icon="'icon-yinpinzanting'"
        :icon-size="48"
        :url="question.stemSrc"
        class="bg-[#f8f8f8]"
      />
    </div>
    <div
      v-for="(item, index) in metas"
      :key="index"
      v-img-preview="item"
      class="exam-model-default"
      :class="handleClassName(item.key)"
      @click="isEdit && selectControl(item.key)"
    >
      <KKCAudioPlayer
        v-if="item.valueSrc && item.value"
        :icon-name="'icon-yinpinbofang'"
        :playing-icon="'icon-yinpinzanting'"
        :icon-size="48"
        :url="item.valueSrc"
        class="mt-[14px]"
      />
      <!-- 非解析 -->
      <div v-if="isEdit" :class="['flex', 'px-[24px]', 'items-center', (item.valueSrc && !item.value) ? 'py-[22px]' : 'py-[30px]']">
        <!-- 练习模式 -->
        <template v-if="model === 1 || model === 3 || model === 4">
          <span class="select-status" :class="handleRadioClassName(item.key)">{{
            item.key
          }}</span>
          <KKCHtmlMathJax v-if="item.value" class="pl-[24px] chioceValue exam-html" :content="item.value" />
          <KKCAudioPlayer
            v-if="item.valueSrc && !item.value"
            class="flex-1 !pr-0"
            :icon-name="'icon-yinpinbofang'"
            :playing-icon="'icon-yinpinzanting'"
            :icon-size="48"
            :url="item.valueSrc"
          />
        </template>
        <!-- 考试模式 -->
        <template v-else>
          <span
            class="select-status"
            :class="{
              '!text-[#ffffff] !bg-[#4464ff]':
                currentSelectKey === item.key || logAnswer === item.key,
            }"
          >{{ item.key }}</span>
          <KKCHtmlMathJax v-if="item.value" class="pl-[24px] chioceValue exam-html" :content="item.value" />
          <KKCAudioPlayer
            v-if="item.valueSrc && !item.value"
            class="flex-1 !pr-0"
            :icon-name="'icon-yinpinbofang'"
            :playing-icon="'icon-yinpinzanting'"
            :icon-size="48"
            :url="item.valueSrc"
          />
        </template>
      </div>
      <!-- 解析模式 -->
      <div v-else :class="['flex', 'px-[24px]', 'items-center', (item.valueSrc && !item.value) ? 'py-[22px]' : 'py-[30px]']">
        <span
          class="select-status"
          :class="{
            '!text-[#fff] !bg-[#15CCC0]': isTrue(item.key),
            '!text-[#fff] !bg-[#EB2330]': isError(item.key),
          }"
        >{{ item.key }}</span>
        <KKCHtmlMathJax v-if="item.value" class="pl-[24px] chioceValue exam-html" :content="item.value" />
        <KKCAudioPlayer
          v-if="item.valueSrc && !item.value"
          class="flex-1 !pr-0"
          :icon-name="'icon-yinpinbofang'"
          :playing-icon="'icon-yinpinzanting'"
          :icon-size="48"
          :url="item.valueSrc"
        />
      </div>
    </div>
    <Teleport to="body">
      <QuestionBankPopupDescription v-if="description" :show="isShowDescription" :description="description" @ok="onDescriptionClick" />
    </Teleport>
  </div>
</template>

<script setup lang="ts">
const route = useRoute()
// 1每日一练,3 章节练习,2 试卷，处理数据逻辑不同
const moduleType = Number(route.query.moduleType)
const { track } = useTrackEvent()

const props = defineProps({
  question: {
    type: Object as PropType<any>,
    default: () => ({}),
  },
  // 选项是否可编辑 （做题可编辑，解析不可编辑）
  isEdit: {
    type: Boolean,
    default: true,
  },
  // 练习模式 一题一页1  考试模式 通栏2
  model: {
    type: Number,
    default: 1,
  },
  isExpand: {
    type: Boolean,
    default: false,
  },
  // true材料题，false 非材料题
  isMaterial: {
    type: Boolean,
    default: false,
  },
})

const questionItem = ref()
// 当前元素选中的值
const currentSelectKey = ref<string>('')
watch(
  () => props.question,
  (newVal) => {
    questionItem.value = newVal
    // 重置为空
    currentSelectKey.value = ''
  },
  {
    immediate: true,
    deep: true,
  }
)

// 处理选项
const metas = computed(() => {
  // questionItem.value.ptypeValueId === 5
  if ([ExamQuestionType.JUDGE].includes(questionItem.value.ptypeValueId)) {
    // 判断题需要组装选项
    return [
      { key: 'A', value: '正确' },
      { key: 'B', value: '错误' },
    ]
  } else {
    return JSON.parse(questionItem.value.metas)
  }
})

// 参考答案-正确答案
const trueAnswer = computed(() => {
  let answer = JSON.parse(questionItem.value.answer)[0]
  // props.question.ptypeValueId === 5
  if ([ExamQuestionType.JUDGE].includes(questionItem.value.ptypeValueId)) {
    answer = answer === '正确' ? 'A' : 'B'
  }
  return answer
})

// 我的答案
const logAnswer = computed(() => {
  if (questionItem.value.logAnswer) {
    if (typeof questionItem.value.logAnswer === 'string') {
      return JSON.parse(questionItem.value.logAnswer)[0]
    } else {
      return questionItem.value.logAnswer[0]
    }
  }
})
const emit = defineEmits<{
  (e: 'change', arr: any, state: number): void;
}>()

// 用户操作后选择的答案
// 选择题目id
const selectControl = debounceFunc(
  (key: string) => {
    // 添加埋点 - AI优化
    const trackData = {
      category: '题库',
      action: '',
      label: ''
    }

    if (questionTitle.value === '单选题') {
      trackData.action = '选项'
      trackData.label = key
    } else if (questionTitle.value === '判断题') {
      trackData.action = '判断题-选项'
      trackData.label = key === 'A' ? '正确' : '错误'
    }

    // 做题->选项 埋点
    if (trackData.action) {
      track(trackData)
    }

    // 背题模式不可做题
    if (props.model === 4) {
      return
    }

    // 已经选过答案 不可更换 解析已经展开（isAnalyze 1）不可再次做题
    // 快答模式，选过答案不可修改
    if ((props.model === 1 || props.model === 3) && questionItem.value.isAnalyze) {
      return
    }
    // 已选选项可取消
    const selectedKey = logAnswer.value === key ? '' : key
    currentSelectKey.value = selectedKey
    // 取消视为未做
    const queState = selectedKey ? (trueAnswer.value === key ? 3 : 4) : 1
    const arr = selectedKey ? [key] : []

    emit('change', arr, queState)
  },
  500,
  true
)

const isCorrect = (key: string, isAnalyze = false) => {
  // 判断当前key是否与trueAnswer值相等
  const isKeyMatchingTrueAnswer = key === trueAnswer.value
  // 判断是否进入编辑模式
  const isEditingDisabled = !props.isEdit
  // 判断是否解析模式或者已经选择key
  const isAnalyzingOrKeySelected = isAnalyze || currentSelectKey.value
  // 判断是否应该高亮答案
  const shouldHighlightAnswer = (isAnalyzingOrKeySelected && isKeyMatchingTrueAnswer) || (isEditingDisabled && isKeyMatchingTrueAnswer)
  return shouldHighlightAnswer
}

const isWrong = (key: string) => {
  return (key === logAnswer.value && logAnswer.value !== trueAnswer.value) || (currentSelectKey.value === key && key !== trueAnswer.value)
}

const isExamActive = (key: string) => {
  return key === logAnswer.value || currentSelectKey.value === key
}
// 处理整个选项高亮样式问题
const handleClassName = computed(() => (key: string) => {
  if (props.model === 4 && !questionItem.value.logAnswer && props.isEdit) {
    // 背题模式，不支持做题，但展示历史做题记录
    return ''
  }
  if (props.isEdit) {
    if (props.model === 1 || props.model === 3 || props.model === 4) {
      if (isCorrect(key, questionItem.value.isAnalyze)) {
        return 'correct-status' // 做对
      } else if (isWrong(key)) {
        return 'wrong-status' // 做错
      } else if (isExamActive(key)) {
        return 'exam-model-active' // 选择
      }
    } else if (isExamActive(key)) {
      return 'exam-model-active' // 选择
    }
  } else if (isCorrect(key)) {
    return 'correct-status' // 做对
  } else if (isWrong(key)) {
    return 'wrong-status' // 做错
  }
})

// 处理选项字母样式问题
const handleRadioClassName = computed(() => (key: string) => {
  if (props.model === 4 && !questionItem.value.logAnswer) {
    // 背题模式，不支持做题，但展示历史做题记录
    return ''
  }
  if (
    (questionItem.value.isAnalyze && key === trueAnswer.value) ||
    (currentSelectKey.value && key === trueAnswer.value)
  ) {
    return '!text-[#fff] !bg-[#15CCC0]'
  } else if (
    (currentSelectKey.value === key &&
      currentSelectKey.value !== trueAnswer.value) ||
    (logAnswer.value === key && logAnswer.value !== trueAnswer.value)
  ) {
    return '!text-[#fff] !bg-[#EB2330]'
  } else {
    return ''
  }
})
// 题型说明
const isShowDescription = ref(false)
const description = computed(() => questionItem.value.description)
const onDescriptionClick = () => {
  if ((moduleType === 2 || moduleType === 0) && !props.isMaterial && description.value) {
    isShowDescription.value = !isShowDescription.value
  }
}

const isTrue = (key: string) => {
  const base = key === trueAnswer.value
  if (props.model === 4 && props.isEdit) {
    // 背题模式，不支持做题，但展示历史做题记录
    return base && !!questionItem.value.logAnswer
  }
  return base
}

const isError = (key: string) => {
  const base = key === logAnswer.value && logAnswer.value !== trueAnswer.value
  if (props.model === 4 && props.isEdit) {
    // 背题模式，不支持做题，但展示历史做题记录
    return base && !!questionItem.value.logAnswer
  }
  return base
}

/**
 * 题型title
 */
const questionTitle = computed(() => {
  const { ptypeValueIdTitle, questionTypeValueIdTitle } = props.question
  return handleQuestionTitle(ptypeValueIdTitle || '', questionTypeValueIdTitle, moduleType)
})

</script>
<style scoped lang="scss">
// 做题模式的选项类名
// 默认状态
// .radio-option {
//   padding: 24px;
// }

.exam-model-default {
  @apply rounded-[16px] text-[32px] text-[#333333];
  background: #f7f8fc;
  border: 1px solid #eeeeee;
  margin-bottom: 32px;

  .select-status {
    @apply flex items-center justify-center bg-white font-bold w-[52px] h-[52px] rounded-[50%] inline-block;
  }
}

// 选中状态
.exam-model-active {
  background: #e8ecff !important;
}

.correct-status {
  background: #e3f9f6;
  border: 1px solid #15ccc0;
}

.wrong-status {
  background: #feeef0;
  border: 1px solid #eb2330;
}

.chioceValue {
  width: calc(100% - 52px);
}

.exam-html {
  word-wrap: break-word !important;
}
</style>
