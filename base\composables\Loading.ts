import { render, h } from 'vue'
import LoadingComponent from '../components/Loading/index.vue'
/**
 * Loading 页面加载动画
 * @param isShow 是否展示 loading
 * @param message loading 展示文案
 * @param isShowMask 是否展示遮罩层
 */
export const Loading = (isShow = true, message?:string, isShowMask?: boolean, bgColor?:string) => {
  const handleDestroy = () => {
    // 从 body 上移除组件
    if (process.client) {
      render(null, document.body)
    }
  }
  // 使用 h 函数创建 vnode
  const vnode = h(LoadingComponent, {
    message,
    destroy: handleDestroy,
    isShowMask,
    bgColor
  })
  if (isShow) {
    if (process.client) {
      render(vnode, document.body)
    }
  } else {
    handleDestroy()
  }
  // 使用 render 函数将 vnode 渲染为真实DOM并挂载到 body 上
}
