import type {
  QuestionLogPageBodyType,
  QuestionLogSubmitBodyType,
  QuestionLogLeaveBodyType,
  QuestionLogRemoveBodyType,
  QuestionErrorBaseType,
  QuestionErrorSubmitType,
  QuestionAddUserCollectV2BodyType,
  QuestionDeleteUserCollectBodyType,
  QuestionCountForQuestionType,
} from '../types'
import filterQuestionBankBody from '../../../../base/utils/filterBody'

enum Api {
  // 错题本收藏本历史记录本
  querySortLabelProt = '/kukecorequestion/wap/questioncount/querySortLabelsProt',
  getTotalNumQuestion = '/kukecorequestion/wap/questioncount/totalNumberQuestions',
  getTotalNumQuestionTypeProt = '/kukecorequestion/wap/questioncount/questionTypeSummaryProt',
  // 纠错类型
  getCorrectTypeProt = '/kukecorequestion/wap/baseType/listsProt',
  // 纠错提交
  doCorrectQuestionProt = '/kukecorequestion/wap/kqQuestion/addFeedbackProt',
  // 收藏本
  addUserCollectQuestionProt = '/kukecorequestion/wap/collect/addUserCollectProt',
  removeUserCollectQuestionProt = '/kukecorequestion/wap/collect/removeUserCollectProt',
  getUserCollectQuestionProt = '/kukecorequestion/wap/collect/showQuestionProt',
  doCollectQuestionProt = '/kukecorequestion/wap/collect/doQuestionProt',
  doAgainCollectQuestionProt = '/kukecorequestion/wap/collect/doAgainProt',
  submitCollectQuestionProt = '/kukecorequestion/wap/collect/submitQuestionProt',
  submitLeaveCollectQuestionProt = '/kukecorequestion/wap/collect/submitLeaveProt',
  // 收藏试题 v2
  addUserCollectQuestionProtV2 = '/kukecorequestion/wap/collect/addUserCollectV1Prot',
  // 错题本
  getWrongLogQuestionProt = '/kukecorequestion/wap/wronglog/showQuestionProt',
  doWrongLogQuestionProt = '/kukecorequestion/wap/wronglog/doQuestionProt',
  doAgainWrongLogQuestionProt = '/kukecorequestion/wap/wronglog/doAgainProt',
  submitWrongLogQuestionProt = '/kukecorequestion/wap/wronglog/submitQuestionProt',
  submitLeaveWrongLogQuestionProt = '/kukecorequestion/wap/wronglog/submitLeaveProt',
  removeWrongLogQuestionProt = '/kukecorequestion/wap/wronglog/removeQuestionProt',
  // 练习历史
  getHistoryQuestionProt = '/kukecorequestion/wap/questionglog/showQuestionProt',
  doHistoryQuestionProt = '/kukecorequestion/wap/questionglog/doQuestionProt',
  doAgainHistoryQuestionProt = '/kukecorequestion/wap/questionglog/doAgainProt',
  submitHistoryQuestionProt = '/kukecorequestion/wap/questionglog/submitQuestionProt',
  submitLeaveHistoryQuestionProt = '/kukecorequestion/wap/questionglog/submitLeaveProt',
  removeHistoryQuestionProt = '/kukecorequestion/wap/questionglog/removeQuestionProt',
}

/**
 * 获取分类标签
 *
 * @param {any} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function getSortLabelProt () {
  return useHttp<any>(Api.querySortLabelProt, {
    method: 'post',
    transform: res => res.data,
  })
}

/**
 * 三个本 - 数量统计
 *
 * @param {any} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function getTotalNumQuestion () {
  return useHttp<any>(Api.getTotalNumQuestion, {
    method: 'post',
    transform: res => res.data,
  })
}

/**
 * 三个本 - 试题类型下题数统计
 *
 * @param {QuestionCountForQuestionType} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function getTotalNumQuestionTypeProt (body: QuestionCountForQuestionType) {
  return useHttp<any>(Api.getTotalNumQuestionTypeProt, {
    method: 'post',
    body: filterQuestionBankBody(body),
    transform: res => res.data,
  })
}

/**
 * 收藏试题
 *
 * @param {QuestionAddUserCollectV2BodyType} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function addUserCollectQuestionProt (body: QuestionAddUserCollectV2BodyType) {
  return useHttp<any>(Api.addUserCollectQuestionProt, {
    method: 'post',
    body: filterQuestionBankBody(body),
    transform: res => res.data,
  })
}

/**
 * 收藏试题 v2
 *
 * @param {QuestionAddUserCollectV2BodyType} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function addUserCollectQuestionProtV2 (body: QuestionAddUserCollectV2BodyType) {
  return useHttp<any>(Api.addUserCollectQuestionProtV2, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 取消收藏
 *
 * @param {QuestionDeleteUserCollectBodyType} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function deleteUserCollectQuestionProt (body: QuestionDeleteUserCollectBodyType) {
  return useHttp<any>(Api.removeUserCollectQuestionProt, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 获取纠错类型
 *
 * @param {QuestionErrorBaseType} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function getCorrectTypeProt (body: QuestionErrorBaseType) {
  return useHttp<any>(Api.getCorrectTypeProt, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 纠错提交
 *
 * @param {QuestionErrorSubmitType} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function doCorrectQuestionProt (body: QuestionErrorSubmitType) {
  return useHttp<any>(Api.doCorrectQuestionProt, {
    method: 'post',
    body,
    transform: res => res.data,
    watch: false
  })
}

/**
 * 收藏列表
 *
 * @param {QuestionLogPageBodyType} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function getUserCollectQuestionProt (body: QuestionLogPageBodyType) {
  return useHttp<any>(Api.getUserCollectQuestionProt, {
    method: 'post',
    body: filterQuestionBankBody(body),
    transform: res => res.data,
  })
}

/**
 * 收藏刷题列表
 *
 * @param {QuestionLogPageBodyType} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function doCollectQuestionProt (body: QuestionLogPageBodyType) {
  return useHttp<any>(Api.doCollectQuestionProt, {
    method: 'post',
    body: filterQuestionBankBody(body),
    transform: res => res.data,
  })
}

/**
 * 收藏重新刷题
 *
 * @param {QuestionLogPageBodyType} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function doAgainCollectQuestionProt (body: QuestionLogPageBodyType) {
  return useHttp<any>(Api.doAgainCollectQuestionProt, {
    method: 'post',
    body: filterQuestionBankBody(body),
    transform: res => res.data,
  })
}

/**
 * 收藏提交
 *
 * @param {QuestionLogSubmitBodyType} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function submitCollectQuestionProt (body: QuestionLogSubmitBodyType) {
  const { ptypeValueId, ...params } = body
  const isMultipleChoice = [2, 3].includes(ptypeValueId)
  return useHttp<any>(Api.submitCollectQuestionProt, {
    method: 'post',
    body: params,
    transform: res => res.data,
    watch: false,
    isShowLoading: isMultipleChoice,
    isShowMask: isMultipleChoice
  })
}

/**
 * 收藏离开
 *
 * @param {QuestionLogLeaveBodyType} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function submitLeaveCollectQuestionProt (body: QuestionLogLeaveBodyType) {
  return useHttp<any>(Api.submitLeaveCollectQuestionProt, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 错题列表
 *
 * @param {QuestionLogPageBodyType} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function getWrongLogQuestionProt (body: QuestionLogPageBodyType) {
  return useHttp<any>(Api.getWrongLogQuestionProt, {
    method: 'post',
    body: filterQuestionBankBody(body),
    transform: res => res.data,
  })
}

/**
 * 错题刷题列表
 *
 * @param {QuestionLogPageBodyType} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function doWrongLogQuestionProt (body: QuestionLogPageBodyType) {
  return useHttp<any>(Api.doWrongLogQuestionProt, {
    method: 'post',
    body: filterQuestionBankBody(body),
    transform: res => res.data,
  })
}

/**
 * 错题重新刷题
 *
 * @param {QuestionLogPageBodyType} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function doAgainWrongLogQuestionProt (body: QuestionLogPageBodyType) {
  return useHttp<any>(Api.doAgainWrongLogQuestionProt, {
    method: 'post',
    body: filterQuestionBankBody(body),
    transform: res => res.data,
  })
}

/**
 * 错题提交
 *
 * @param {QuestionLogSubmitBodyType} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function submitWrongLogQuestionProt (body: QuestionLogSubmitBodyType) {
  const { ptypeValueId, ...params } = body
  const isMultipleChoice = [2, 3].includes(ptypeValueId)
  return useHttp<any>(Api.submitWrongLogQuestionProt, {
    method: 'post',
    body: params,
    transform: res => res.data,
    watch: false,
    isShowLoading: isMultipleChoice,
    isShowMask: isMultipleChoice
  })
}

/**
 * 错题离开
 *
 * @param {QuestionLogLeaveBodyType} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function submitLeaveWrongLogQuestionProt (body: QuestionLogLeaveBodyType) {
  return useHttp<any>(Api.submitLeaveWrongLogQuestionProt, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 移除错题
 *
 * @param {QuestionLogRemoveBodyType} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function removeWrongLogQuestionProt (body: QuestionLogRemoveBodyType) {
  return useHttp<any>(Api.removeWrongLogQuestionProt, {
    method: 'post',
    body,
    transform: res => res.data
  })
}

/**
 * 练习历史列表
 *
 * @param {QuestionLogPageBodyType} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function getHistoryQuestionProt (body: QuestionLogPageBodyType) {
  return useHttp<any>(Api.getHistoryQuestionProt, {
    method: 'post',
    body: filterQuestionBankBody(body),
    transform: res => res.data,
  })
}

/**
 * 练习历史列表
 *
 * @param {QuestionLogPageBodyType} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function doHistoryQuestionProt (body: QuestionLogPageBodyType) {
  return useHttp<any>(Api.doHistoryQuestionProt, {
    method: 'post',
    body: filterQuestionBankBody(body),
    transform: res => res.data,
  })
}

/**
 * 练习历史刷题
 *
 * @param {QuestionLogPageBodyType} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function doAgainHistoryQuestionProt (body: QuestionLogPageBodyType) {
  return useHttp<any>(Api.doAgainHistoryQuestionProt, {
    method: 'post',
    body: filterQuestionBankBody(body),
    transform: res => res.data,
  })
}

/**
 * 练习历史提交
 *
 * @param {QuestionLogSubmitBodyType} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function submitHistoryQuestionProt (body: QuestionLogSubmitBodyType) {
  const { ptypeValueId, ...params } = body
  const isMultipleChoice = [2, 3].includes(ptypeValueId)
  return useHttp<any>(Api.submitHistoryQuestionProt, {
    method: 'post',
    body: params,
    transform: res => res.data,
    watch: false,
    isShowLoading: isMultipleChoice,
    isShowMask: isMultipleChoice
  })
}

/**
 * 练习历史离开
 *
 * @param {QuestionLogLeaveBodyType} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function submitLeaveHistoryQuestionProt (body: QuestionLogLeaveBodyType) {
  return useHttp<any>(Api.submitLeaveHistoryQuestionProt, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 移除练习历史
 *
 * @param {QuestionLogRemoveBodyType} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function removeHistoryQuestionProt (body: QuestionLogRemoveBodyType) {
  return useHttp<any>(Api.removeHistoryQuestionProt, {
    method: 'post',
    body,
    transform: res => res.data
  })
}
