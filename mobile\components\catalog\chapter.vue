<template>
  <div :class="[bem()]" :data-level="item.level" @click="handleAction(item)">
    <div :class="[bem('main')]">
      <div v-if="item.liveStatus === CourseLiveStatus.LivingStreaming" :class="[bem('main', 'status')]">
        直播中
      </div>
      <div :class="[bem('main', 'title')]">
        {{ item.name }}
      </div>
    </div>
    <div :class="[bem('right')]">
      <KKCIcon
        class="animate-180"
        :class="{ 'rotate-180': isOpen }"
        name="icon-xiangqing-zhankai"
        :size="32"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { CourseLiveStatus } from './type'
import type { IItem } from './type'
const bem = useBem('catalog-chapter')
defineProps<{
  item: IItem
  isOpen: boolean
}>()
// 组件不需要配置 definePageMeta
// definePageMeta({
//   ignoreLearnTarget: true,
// })
const emits = defineEmits<{
  (e: 'status', event: IItem): void;
  (e: 'action', event: IItem): void;
}>()
const handleAction = (item: IItem) => {
  emits('action', item)
}
</script>

<style lang="scss" scoped>
.catalog-chapter {
  width:100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  min-height: 80px;
  background: #F5F6F9;
  padding: 20px 24px;
  border-radius: 16px;

  &__right {
    cursor: pointer;
  }

  &__main {
    width: 100%;
    overflow: hidden;
    &--status {
      width: 82px;
      height: 34px;
      line-height: 34px;
      text-align: center;
      margin-right: 8px;
      border-radius: 8px;
      font-size: 22px;
      color: var(--kkc-brand-text);
      flex-shrink: 0;
      float: left;
      border: 1px solid var(--kkc-brand-text);
    }

    &--title {
      font-size: 28px;
      font-weight: 500;
      line-height: 34px;
      max-width: 600px;
      // @apply truncate;
    }
  }
}
</style>
