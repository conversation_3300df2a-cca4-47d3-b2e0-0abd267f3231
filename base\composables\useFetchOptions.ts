import type { UseFetchOptions } from 'nuxt/app'
import type { KeysOf } from 'nuxt/dist/app/composables/asyncData'
import { createEncryption } from '@kukefe/kkutils'
import { loggerError } from './useHttp'
import { useUserStore } from '~/stores/user.store'

/**
 * @deprecated 废弃
 * @function useFetchOptions
 * @desc
 */
export default <T, ResT = T, DefaultT = T, PickT extends KeysOf<ResT> = KeysOf<ResT>>(url: string,
  opts: UseFetchOptions<IResponse<T>, ResT, PickT, DefaultT> = {}
): UseFetchOptions<IResponse<T>, ResT, PickT, DefaultT> => {
  const {
    baseUrlServer,
    public: {
      TOKEN_KEY,
      version,
      baseUrlClient = '',
    },
  } = useRuntimeConfig()
  //
  const {
    appId, lesseeId, isXcx,
    requestAppid = '', requestAppkey = '',
    isMobile, isPc, logo2
  } = useAppConfig()

  const userStore = useUserStore()
  const route = useRoute()
  const TOKEN = useCookie(TOKEN_KEY)
  const isLoginMiniProgram = useCookie<boolean>('isLoginMiniProgram')

  let baseURL = baseUrlServer || baseUrlClient
  if (process.server && process.env.KKC_BASE_URL_CUSTOM) {
    baseURL = process.env.KKC_BASE_URL_CUSTOM
  }
  const headers = {
    //
    'kk-version': version,
    'kk-request-id': `${new Date().getTime()}${buildUUID()}`,
    ...(lesseeId
      ? {
          'tenant-id': lesseeId
        }
      : {}),
    ...(appId
      ? {
          'kk-platform': appId
        }
      : {}),
  } as unknown as Record<string, string>

  /**
   *
   * @param "kk-terminal-type" { string }  - 客户端的类型
   * @param "kk-from" { string }  - 请求来源（请求客户端类型）
   * @param "kk-platform" { string }  - 平台
   * @param "kk-os" { string? }  - 客户端系统版本 ios12.2/pc和手机站传浏览器的版本号
   * @param "kk-modle" { string? }  - 客户端型号 iphone11/huawei mate10 /（PC端传浏览器的版本号）/h5能获取到机型就传机型获取不到就传手机所属系统
   */
  if (isMobile) {
    if (isXcx) {
      headers['kk-from'] = 'wechat-app'
    } else {
      headers['kk-from'] = 'h5'
    }
  } else if (isPc) {
    headers['kk-from'] = 'web'
  } else {
    console.error('kk-from 配置错误')
  }
  //
  const postBodyEncryption = createEncryption(requestAppid, requestAppkey)
  return {
    baseURL: /^(\/api\/|http)/.test(url) ? undefined : baseURL,
    headers,
    // key: url,
    method: 'POST',
    responseType: 'json',
    onRequest (ctx) {
      const { options } = ctx
      if (!options?.headers) {
        options.headers = {}
      }

      if (TOKEN.value) {
        options.headers['kk-token'] = TOKEN.value
      }
      if (options.headers['x-forwarded-for']) {
        options.headers['kk-ip'] = options.headers['x-forwarded-for']
      }
      // headers.set('kk-request-id', `${new Date().getTime()}${buildUUID()}`)
      if (options.method?.toLocaleLowerCase() === 'post' &&
        !url.startsWith('/api/')
      ) {
        options.body = postBodyEncryption(options.body, options.headers['kk-token'])
      }
      // loggerError(ctx, 'onRequest')
    },
    async onResponse (ctx) {
      const { response } = ctx

      // 刷新token 两个token同时生效
      const newToken = response.headers.get('kk-token')
      if (newToken && newToken !== TOKEN.value) {
        TOKEN.value = newToken
      }
      const data = response._data
      if (data && data.code) {
        if (data.code !== '10000') {
          loggerError(ctx, 'onResponse error')
          if (data.code === '11105' || data.code === '10041') {
            // console.warn(data.msg || '登录已失效，请重新登录')

            userStore.clearUserInfo()
            //
            const path = isMobile ? '/login' : '/'
            if (path === '/login' && isXcx) {
              if (!isLoginMiniProgram.value) {
                isLoginMiniProgram.value = true
                wx.miniProgram.navigateTo({
                  url: `/pages/login/index?redirect_uri=${encodeURIComponent(
                    route.fullPath
                  )}&logo2=${logo2 || ''}`,
                  complete: function () {
                    setTimeout(() => {
                      isLoginMiniProgram.value = false
                    }, 1000)
                  },
                })
              }
            } else {
              await navigateTo({
                path,
                query: {
                  // redirect_uri: route.fullPath
                }
              }, {
                external: true,
              })
            }
          } else {

            // console.warn(data.msg || data.message)
          }

          Message(data.msg || data.message)
          throw createError({
            statusCode: data.code,
            message: data.msg,
            data,
            // fatal: false
          })
        } else {
          // response._data = data.data
          // loggerError(ctx, 'onResponse success')
        }
      } else {
        loggerError(ctx, 'onResponse error server')
        throw createError({
          // statusCode: 500,
          statusCode: response.status,
          message: response.statusText,
          data,
        })
      }
    },
    ...opts
  }
}
