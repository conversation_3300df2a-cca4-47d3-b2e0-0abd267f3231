<template>
  <div class="point-wrap" :style="'fontSize:'+size+'px'">
    <div v-if="showLabel" class="label">
      解析
    </div>
    <div class="point-item">
      <KKCHtmlMathJax :content="analysisCom" :is-answer-questions="true" />
    </div>
    <!-- 若题干有音频，则显示音频播放器 -->
    <KKCAudioPlayer
      v-if="stemSrc"
      class="bg-[#f1f2f3] !w-[100%] audio-wrap"
      :icon-name="'icon-yinpinbofang'"
      :playing-icon="'icon-yinpinzanting'"
      :icon-size="48"
      :url="stemSrc"
    />
  </div>
</template>
<script lang="ts" setup>

const props = withDefaults(defineProps<{
  analysis: string,
  stemSrc?: string
  size?: number,
  showLabel?: boolean
}>(), {
  size: 28,
  showLabel: true
})

const analysisCom = computed(() => {
  //  过滤掉audio
  return props.analysis?.replace(/<audio.*?<\/audio>/g, '')
})

</script>
<style lang="scss" scoped>
  .point-wrap{
    margin-bottom: 24px;
    font-size: 24px;
    // display: flex;
    // align-items: start;
    // flex-wrap: wrap;

    .label{
      color: #111;
      font-weight: 500;
      width: 112px;
      text-align: left;
      // margin-right: 48px;
    }

    .point-item{
      flex: 1;
      color: #333;
      display: flex;
      flex-wrap: wrap;
      word-break: break-all;
    }

    .audio-wrap{
        border-radius: 8px 40px 40px 40px!important;
    }
  }
</style>
