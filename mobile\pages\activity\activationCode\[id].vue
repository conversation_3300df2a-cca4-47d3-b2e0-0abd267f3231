<template>
  <div class="relative">
    <NavBar v-if="!isXcx && !isKukeCloudAppWebview" :title="activityInfo?.activityName" />
    <img class="w-[100%] mb-[140px]" :src="activityInfo?.wapBackgroundPictureUrl" alt="">
    <!--  :class="(!isXcx && !isKukeCloudAppWebview)?'top-[1323px]':'top-[1243px]'" -->
    <div class="fixed w-[100%] left-[0] py-[16px] flex justify-center items-center bg-[#fff] activity_icon">
      <div class="flex w-[702px] items-center h-[108px] gap-[8px]">
        <div
          class="scan-style relative overflow-hidden h-[100%] text-[36px] flex flex-col flex-1 justify-center rounded-[24px] text-center cursor-pointer"
          :style="{
            background:activityInfo?.buttonBackgroundColor,
            color:activityInfo?.buttonWordColor
          }"
          @click="clickBtn()"
        >
          <span class="font-medium">{{ activityInfo?.activityButtonName }}</span>
        </div>
      </div>
    </div>
    <div class="rule_icon absolute text-[20px] text-[#fff] w-[40px] right-[0] top-[96px] py-[12px] px-[8px]" @click="activityRule">
      活动规则
    </div>
    <!-- 活动规则 -->
    <van-popup
      v-model:show="model"
      position="bottom"
      closeable
      round
      :style="{ height: '60%' }"
    >
      <div class="p-[32px]" style="height:calc(100% - 0.64rem)">
        <p class="text-center text-[34px] text-[#111] font-medium">
          活动规则
        </p>
        <div class="overflow-auto whitespace-pre-wrap h-[100%]" style="word-break:break-all">
          {{ activityInfo.activityExplain }}
        </div>
      </div>
    </van-popup>
  </div>
</template>
<script setup lang="ts">
import { useUserStore } from '~/stores/user.store'
const userStore = useUserStore()
const { isXcx, isKukeCloudAppWebview } = useAppConfig()
const { activationCodeApi } = useApi()
const route = useRoute()
const activityInfo = ref<any>()
/**
     * 活动id
     */
const id = ref<string>(route.params.id as string)
const getQuesDetail = async () => {
  const { data } = await activationCodeApi.activationCodeShare({ id: id.value })
  console.log(data.value, '===========')
  activityInfo.value = data.value
  Loading(false)
}
useHead({
  titleTemplate: () => {
    return activityInfo.value?.activityName || '领取激活码'
  }
})
/**
     * 活动规则
     */
const model = ref<boolean>(false)
const activityRule = () => {
  model.value = true
}

const clickBtn = async () => {
  if (!userStore.isLogin) {
    userStore.isLoginFn()
    return
  }
  try {
    const { error } = await activationCodeApi.activationCodeVerify({ id: id.value })
    if (!error.value) {
      console.log('立即领取')
      navigateTo({
        path: '/activity/activationCode/receive',
        query: {
          id: id.value
        }
      })
    }
  } catch (e) {
    console.log(e)
  }
}
Loading(true)
setTimeout(() => {
  getQuesDetail()
}, 500)
// onMounted(async () => {
//   await getQuesDetail()
// })
</script>
    <style lang="scss" scoped>
    .scan-style::before {
      will-change: transform; /* 提前告知浏览器动画属性 */
      backface-visibility: hidden; /* 避免闪烁 */
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 30%;
      height: 100%;
      background: linear-gradient(
        90deg,
        transparent,
        rgba(255,255,255,0.6)
      );
      transform: skewX(-20deg);
      animation: scan 3s linear infinite;
    }
    .scan-style-slow::before{
        animation: scan 2s linear infinite;
    }
    @keyframes scan {
      0% {
        left: -30%;
      }
      100% {
        left: 200%;
      }
    }
  .activity_icon{
      padding-bottom: calc(constant(safe-area-inset-bottom) + 16px) !important;
      padding-bottom: calc(env(safe-area-inset-bottom) + 16px) !important;
      bottom: 0;
    }
    .rule_icon{
    background: rgba(0, 0, 0, 0.30);
    border-radius: 12px 0 0 12px;
  }
    </style>
