import type { DefaultTagModel, getDefaultTagParams } from './types'

export enum TagsApi {
  getCenterDefaultTag = '/kukecorequestion/wap/moduleManageTagsV1/getCenterDefaultTag',
}

/**
 * 获取多个模块标签列表
 */
export async function getModuleDefaultTag (body: getDefaultTagParams) {
  return useHttp<DefaultTagModel>(TagsApi.getCenterDefaultTag, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}
