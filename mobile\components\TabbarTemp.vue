<template>
  <div class="tabber has-tabbar" :style="{ 'height': `${componentHeight}px` }">
    <div class="kb-tabbar kb-mobile kkc-tabbar">
      <div class="kb-business-tabbar kb-business-tabbar-1" style="background-color: rgb(255, 255, 255);">
        <nuxt-link
          class="kb-tabbar-item"
          :class="{
            'is-active':activeValue('/')
          }"
          :to="{
            path:'/',
            query:{
              miniVersion: $route.query.miniVersion
            }
          }"
        >
          <div class="kb-tabbar-item__icon">
            <div class="">
              <img v-if="activeValue('/')" :src="Icon1Active" alt="">
              <img v-else :src="Icon1" alt="">
            </div>
          </div>
          <div class="kb-tabbar-item__text">
            <p>首页</p>
          </div>
        </nuxt-link>
        <nuxt-link
          class="kb-tabbar-item"
          :class="{
            'is-active':activeValue('/courseList/4')
          }"
          :to="{
            path:'/courseList/4',
            query:{
              title: '商品中心',
              miniVersion: $route.query.miniVersion
            }
          }"
        >
          <div class="kb-tabbar-item__icon">
            <div class="">
              <img v-if="activeValue('/courseList/4')" :src="Icon2Active" alt="">
              <img v-else :src="Icon2" alt="">
            </div>
          </div>
          <div class="kb-tabbar-item__text">
            <p>商品中心</p>
          </div>
        </nuxt-link>
        <nuxt-link
          class="kb-tabbar-item"
          :class="{
            'is-active':activeValue('/me')
          }"
          :to="{
            path:'/me',
            query:{
              miniVersion: $route.query.miniVersion
            }
          }"
        >
          <div class="kb-tabbar-item__icon">
            <div class="">
              <img v-if="activeValue('/me')" :src="Icon3Active" alt="">
              <img v-else :src="Icon3" alt="">
            </div>
          </div>
          <div class="kb-tabbar-item__text">
            <p>我的</p>
          </div>
        </nuxt-link>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { useSystemStore } from '~/stores/system.store'
import Icon1 from '@/assets/images/home/<USER>/icon-1.png'
import Icon1Active from '@/assets/images/home/<USER>/icon-1-active.png'
import Icon2 from '@/assets/images/home/<USER>/icon-2.png'
import Icon2Active from '@/assets/images/home/<USER>/icon-2-active.png'
import Icon3 from '@/assets/images/home/<USER>/icon-3.png'
import Icon3Active from '@/assets/images/home/<USER>/icon-3-active.png'
const systemStore = useSystemStore()
const route = useRoute()

const activeValue = computed(() => {
  return (path:string) => {
    return route.path === path
  }
})
const componentHeight = ref(0)

const updateComponentHeight = () => {
  const component = document.querySelector('.kkc-tabbar')
  componentHeight.value = component?.clientHeight ?? 0
  if (componentHeight.value) { systemStore.setTabbarHeight(`${componentHeight.value}PX`) }
  console.log(componentHeight.value, '------------------')
}

onMounted(async () => {
  nextTick(async () => {
    const component = document.querySelector('.kkc-tabbar')
    if (component) {
      updateComponentHeight()
    }
  })
})
onUnmounted(() => {
})
onUpdated(() => {
  // mounted 中获取DOM时，component第一次加载组件并未渲染好为null
  // 所以放在updated中获取，在DOM更新时，进行高度获取与设置
  // 只要tabbar组件更新，即更新高度
  updateComponentHeight()
})

</script>

<style lang="scss">
.kb-tabbar-item.is-active{
  color: var(--kkc-brand);
  /* color: #eb2330; */
}
.tabber {
  &::after {
    display: block;
    content: '';
    height: 60px;
    padding-bottom: constant(safe-area-inset-bottom);
    /*兼容 IOS<11.2*/
    padding-bottom: env(safe-area-inset-bottom);
    /*兼容 IOS>11.2*/
  }
}

.has-tabbar {
  .kkc-tabbar {
    width: 7.5rem;
    position: fixed;
    left: 50%;
    transform: translateX(-50%);
    bottom: 0;
    z-index: 19;
  }
}
</style>
