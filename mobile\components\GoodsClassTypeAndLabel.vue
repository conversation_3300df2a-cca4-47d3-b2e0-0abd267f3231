<template>
  <div class="text-[32px] font-medium text-[#111] leading-[40px]">
    <p class="kkc-goods-title-truncate">
      <strong
        v-if="classTypeInfo"
        class="text-[22px] font-medium rounded-[8px] px-[8px] py-[2px] align-top"
        :style="{
          color: classTypeInfo.fontColor,
          backgroundColor: classTypeInfo.bgColor
        }"
      >
        {{ classTypeInfo.classTypeName }}
      </strong>
      {{ title }}
    </p>
  </div>
</template>

<script setup lang='ts'>
// types
import type { Props } from '~/pages/points/types'

withDefaults(defineProps<{
  // 班型信息
  classTypeInfo?: Props
  // 商品名称
  title: string,
  // 超出省略
  lineClamp?: number
}>(), {
  lineClamp: 2,
  classTypeInfo: undefined
})
</script>

<style lang="scss" scoped>
.kkc-goods-title-truncate {
  font-size: 28px;
  /* 多行省略：核心三件套 */
  display: -webkit-box;
  -webkit-box-orient: vertical;
  line-clamp: v-bind(lineClamp);
  -webkit-line-clamp: v-bind(lineClamp);
  overflow: hidden;
  text-overflow: ellipsis;

  /* 建议：保持正常换行，长词/长链接时再断词，避免影响省略号呈现 */
  white-space: normal;
  word-break: break-all;
  overflow-wrap: break-word;
}
</style>
