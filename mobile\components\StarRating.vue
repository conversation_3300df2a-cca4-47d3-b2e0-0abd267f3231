<template>
  <div class="rating-container">
    <div class="stars">
      <div v-for="n in 5" :key="n" class="star">
        <!-- 背景星星（未填充） -->
        <div class="star-bg">
          ★
        </div>
        <!-- 前景星星（填充） -->
        <div class="star-fill" :class="{ 'filled': rating >= n, 'half-filled': rating >= n - 0.5 && rating < n }">
          <span class="star-full">★</span>
          <span class="star-half">★</span>
        </div>
      </div>
    </div>
    <span v-if="isShowText" class="rating-value">{{ rating.toFixed(1) }}</span>
  </div>
</template>

<script setup lang="ts">
const props = defineProps<{
  value: number // 评分值，范围0-5
  isShowText?: boolean // 是否显示评分文本
}>()

// 计算评分值，确保在0-5范围内
const rating = computed(() => {
  return Math.min(Math.max(props.value, 0), 5)
})
</script>

<style lang="scss" scoped>
.rating-container {
  display: flex;
  align-items: center;
  gap: 8px;

  .stars {
    display: flex;
    // gap: 4px;

    .star {
      position: relative;
      width: 32px;
      height: 32px;

      // 背景星星（未填充状态）
      .star-bg {
        position: absolute;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #ebedf0;
        font-size: 32px;
        line-height: 1;
        z-index: 1;
      }

      // 前景星星（填充状态）
      .star-fill {
        position: absolute;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: flex-start; /* 从左边开始 */
        overflow: hidden;
        z-index: 2;

        .star-full {
          position: absolute;
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #ffaa00;
          font-size: 32px;
          line-height: 1;
          transition: opacity 0.2s ease;
          left: 0;
        }

        .star-half {
          position: absolute;
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #ffaa00;
          font-size: 32px;
          line-height: 1;
          transition: opacity 0.2s ease;
          left: 0;
          clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%); /* 默认显示全部 */
        }

        // 完全填充状态
        &.filled {
          .star-full {
            opacity: 1;
          }

          .star-half {
            opacity: 0;
          }
        }

        // 半填充状态
        &.half-filled {
          .star-half {
            clip-path: polygon(0 0, 50% 0, 50% 100%, 0 100%); /* 只显示左半部分 */
            width: 100%;
          }

          .star-full {
            opacity: 0;
          }
        }

        // 未填充状态
        &:not(.filled):not(.half-filled) {
          .star-full,
          .star-half {
            opacity: 0;
          }
        }
      }
    }
  }

  .rating-value {
    font-size: 14px;
    color: #666;
  }
}
</style>
