import { MarketingActivityTypeEnum, GoodsStatusEnum } from '@/apis/cart/types'
import type { IClassroomType, Sku } from '@/apis/cart/types'

export interface DeliveryInfoType {
  receiver: string,
  receiveMobile: string,
  provinceId: string,
  cityId: string,
  countyId: string,
  addressDetail: string,
}

// 创建订单 支付信息
export interface PayInfoType {
  reducePrice?: string,
  shouldPay: string | number | undefined,
  shippingPrice?: string,
  teachingMaterialPrice?: string, // 教材费
  roomPrice?: string, // 住宿费
  otherPrice?: string, // 其他费用
}
// 创建订单 秒杀信息
export interface MarketingInfo {
  couponId?: string | undefined,
  teamId?: string | undefined,
  activityType?: number | undefined,
  activityId?: string | undefined
  buyAtMarkupId?: string | undefined
  buyGiveId?: string | undefined
  userCouponId?: string | undefined;
}
// 创建订单 商品信息
export interface GoodsParamInfo {
  goodsMasterId: string,
  specificationItemId?: string,
  receiveSchoolId?: string, // 收款分校id
  receiveSchoolName?: string, // 收款分校名称
  saleType?: number
  modifiedGoodsPrice?: number | string
}

// 生成订单
export interface addOrderParams {
  payInfo: PayInfoType,
  deliveryInfo?: DeliveryInfoType,
  marketingInfo?: MarketingInfo,
  orderSource?: string
  specificationItemId?: string,
  goodsInfos?: GoodsParamInfo[]
  userRemark?: string
  crmInfo?:{
    poolId:string, // 客户池id
    saleAdminId:string// 销售id
    shareId:string// 分享链接id
    targetCode?:string // 获客码标识
  },
  sCrmInfo?:{
    shareId:string// 分享链接id
  },
  agentInfo?:{
    agentCode:string, // 代理商code
    agentMId:string, // 素材id
    agentMType:string, // 素材类型
  },
  extensionInfo?:{
    extensionId:string, // 推广id
  },
  clientType?: number
}
// 生成订单返回
export interface addOrderModel {
  data: { orderSn: any; cateIds: any }
  orderSn?: string,
  payInfo: string,
  orderId: string,
  orderSns?: string
}

// 获取支付前的订单信息
export interface getMinimalisticParams {
  id: string,
  goodsSpecificationItemId: string
  groupId?: string
  activityType?: number
  activityId?: string
  promotion?:any
}
export interface OrderCancelParams {
  orderSn: string;
  cancelReason: number;
  cancelRemark: string;
}
export interface OrderParams {
  orderSn?: string |undefined;
  id?: string |undefined;
}
export interface MaxMoneyParams {
  orderSn: string;
  refundType: number;
}

export interface OrderRefundParams {
  orderSn: string;
  refundType: number;
  refundMoney: number;
  refundReason: number;
  refundRemark?: string;
}
export interface IDictItem {
  label: string;
  value: number;
}

// 订单支付
export interface PayOrderParams {
  settleMode: string;
  orderSn?: string;
}

// 更换地址信息参数
export interface OrderAddressParams {
  orderSn: string;
  receiver: string;
  receiveMobile: string;
  provinceId: string;
  cityId: string;
  countyId: string;
  addressDetail: string;

}
export interface PayResult extends addOrderModel {
  payType: string,
}

export interface OrderPay {
  orderSn: string,
  settleMode: PayStatus
}

export interface BuyGoodsInfoParams {
  orderSn?: string,
  paymentSn?: string
  orderSns?: string[]
}

export interface BuyGoodsInfoModel {
  [key:string]:any
  goodsMasterId: string,
  orderSn?: string,
  goodsTitle?: string,
  specificationItemId: string,
  resourceTypes: string,
  resourceTypeNames: string,
  orderId: string,
  isGroupOrder?:boolean,
  orderSns?: string[],
  goodsTitles?: string[]
}

export interface DeliveryInfoParams {
  provinceId: string,
  cityId: string,
  countyId: string,
}
export interface FetchQueryShippingPriceParams {
  goodsInfo: GoodsParamInfo,
  deliveryInfo?: DeliveryInfoParams,
}

// 更新资料邮寄的参数
export interface ModifyAddressProt {
  id:string
  receiver:string
  receiveMobile:string
  provinceId:string
  cityId:string
  countyId:string
  addressDetail:string
}

// -----------------预下单相关 start------------------------------------------------------------
/**
 * 买赠商品项参数
 */
export interface BuyGiveGoodsItemParams {
  goodsMasterId?: string; // 商品主键 id，非必须
  specificationItemId?: string; // 子规格 id，多规格商品必传，非必
}

/**
 * 商品预下单买赠商品参数
 */
export interface PreOrderBuyGiveGoodsParams {
  activityId : string; // 买赠活动 id，必须
  goodsList?: BuyGiveGoodsItemParams[]; // 买赠勾选商品集合，非必须
  coupons?: { id: string } []; // 买赠勾选优惠券集合，非必须
}

/**
 * 加价购商品项参数
 */
export interface BuyAtMarkupGoodsItemParams {
  goodsMasterId: string; // 商品主键 id，必须
  specificationItemId?: string; // 子规格 id，多规格商品必传，非必
}

/**
 * 预下单加价购参数
 */
export interface PreOrderBuyAtMarkupParams {
  activityId: string; // 加价购活动 id，必须
  buyAtMarkupAmount: number; // 加购选中商品需要加价的金额，必须
  goods?: BuyAtMarkupGoodsItemParams | null; // 加价购勾选商品信息，必须
}

/**
 * 预下单商品参数
 */
export interface PreOrderGoodsParams {
  cartId?:string, // 购物车id
  goodsMasterId: string; // 商品主键 id，必须
  specificationItemId?: string; // 子规格 id，多规格商品必传，非必须
  quantity: number; // 数量，必须
  buyGive?:PreOrderBuyGiveGoodsParams; // 买赠信息,非必须
}

/**
 * 预下单订单相关参数
 */
export interface PreOrderParams {
  orgId:string; // 组织 id，必须
  orderRemark?: string; // 订单备注，非必须
  crmAdminId?: string; // 课程顾问ID，非必须
  couponId?: string; // 组织选用优惠券 id，非必须
  isUseCoupon?: boolean; // 是否使用优惠券，true：默认使用 false:不使用，非必须
  userCouponId?:string; // 用户有多个同类型优惠券时，区分用户选择的优惠券
  goodsList:PreOrderGoodsParams[]; // 组织下商品集合
  buyAtMarkup?:PreOrderBuyAtMarkupParams | null;// 加价购信息,非必须
}

export interface IDeliveryInfoParams {
  id?: string;
  receiver: string; // 必须，收货人
  receiveMobile: string; // 必须，收货电话
  provinceId: string; // 必须，省 id
  cityId: string; // 必须，市 id
  countyId: string; // 必须，县 id
  addressDetail: string; // 必须，详细地址
}

/**
 * 预下单信息请求参数
 */
export interface FetchPreOrderInfoParams {
  deliveryInfo?: IDeliveryInfoParams, // 收货信息
  orders: PreOrderParams[], // 预下单商品信息，按照组织进行分组
}

// --------------------------------------------
/**
 * 预下单 买赠商品项DTO
 */
export interface BuyGiveGoodsItemDTO {
  goodsMasterId: string; // 商品主键 id，必须
  specificationItemId?: string; // 子规格id，非必须
  specificationItemValue?: string; // 子规格项目值，非必须
  goodsName: string; // 商品名称，必须
  goodsImg: string; // 商品图片，必须
}

/**
 * 预下单 商品优惠券DTO
 */
export interface PreOrderGoodsCouponDTO {
  id: string;// 优惠券 id
  // name: string;// 优惠券名称
}

/**
 * 预下单买赠商品DTO
 */
export interface PreOrderBuyGiveGoodsDTO {
  activityId: string; // 买赠活动 id，必须
  buyGiveCount: number; // 买赠选中的赠品数量，必须
  goodsList?: BuyGiveGoodsItemDTO[]; // 买赠勾选商品集合，非必须
  coupons?: PreOrderGoodsCouponDTO []; // 买赠勾选优惠券集合，非必须
}

/**
 * 预下单加价购商品项 DTO
 */
export interface PreOrderBuyAtMarkupGoodsItemDTO {
  goodsMasterId: string; // 商品主键 id，必须
  specificationItemId: string; // 子规格id，必须
  specificationItemValue: string; // 子规格项目值，必须
  goodsName: string; // 商品名称，必须
  goodsPresentPrice: string
  skus?: Sku[]; // item 类型: object，必须
  goodsTitle: string; // 商品名称，必须
  goodsImg: string; // 商品图片，必须
  classroomType: IClassroomType; // 班型，必须
  quantity: number; // 商品数量，必须
  goodsDiscountAmount: number; // 商品到手价，必须
  status: GoodsStatusEnum; // 商品状态，必须
  isStock: number; // 是否库存限制（ 0 无限制 1 有限制）
  stock:number; // 库存数量
}

/**
 * @interface PreOrderBuyAtMarkupDTO
 * @description 加价购活动数据传输对象，包含活动信息、加价金额、门槛等。
 */
export interface PreOrderBuyAtMarkupDTO {
  /**
   * @property {string} activityId
   * @description 加价购活动 ID，必须提供。
   */
  activityId: string;

  /**
   * @property {number} buyAtMarkupAmount
   * @description 加购选中商品需要加价的金额，必须提供。
   */
  buyAtMarkupAmount: number;

  /**
   * @property {boolean} userCanParticipateBuyAtMarkup
   * @description 用户是否满足加价购活动门槛，必须提供。
   */
  userCanParticipateBuyAtMarkup: boolean;

  /**
   * @property {number} useThreshold
   * @description 使用门槛，必须提供。
   */
  useThreshold: number;

  /**
   * @property {PreOrderBuyAtMarkupGoodsItemDTO} [goods]
   * @description 加价购选中的商品，非必须。
   */
  goods?: PreOrderBuyAtMarkupGoodsItemDTO;
}

/**
 * @interface PreOrderGoodsDTO
 * @description 预订单商品数据传输对象，包含商品信息、数量、价格等。
 */
export interface PreOrderGoodsDTO {
  /**
   * @property {string} [cartId]
   * @description 用户购物车 ID，非必须。
   */
  cartId?: string;

  /**
   * @property {string} goodsMasterId
   * @description 主商品 ID，必须提供。
   */
  goodsMasterId: string;

  /**
   * @property {string} [specificationItemId]
   * @description 子规格 ID，非必须。
   */
  specificationItemId?: string;

  /**
   * @property {Sku[]} [skus]
   * @description SKU 列表，非必须。
   */
  skus?: Sku[];

  /**
   * @property {string} goodsTitle
   * @description 商品名称，必须提供。
   */
  goodsTitle: string;

  /**
   * @property {string} goodsImg
   * @description 商品图片 URL，必须提供。
   */
  goodsImg: string;

  /**
   * @property {IClassroomType} classroomType
   * @description 班型，必须提供。
   */
  classroomType: IClassroomType;

  /**
   * @property {number} quantity
   * @description 商品数量，必须提供。
   */
  quantity: number;

  /**
   * @property {number} goodsDiscountAmount
   * @description 商品到手价，必须提供。
   */
  goodsDiscountAmount: number;

  /**
   * @property {string} validityDateText
   * @description 商品有效期文本，必须提供。
   */
  validityDateText: string;

  /**
   * @property {MarketingActivityTypeEnum} [activityType]
   * @description 营销活动类型，非必须。
   */
  activityType?: MarketingActivityTypeEnum;

  /**
   * @property {string} [activityId]
   * @description 参加的营销活动 ID，非必须。
   */
  activityId?: string;

  /**
   * @property {PreOrderBuyGiveGoodsDTO} [buyGive]
   * @description 买赠勾选商品集合，非必须。
   */
  buyGive?: PreOrderBuyGiveGoodsDTO;

  /**
   * @property {GoodsStatusEnum} status
   * @description 商品状态，必须提供。
   */
  status: GoodsStatusEnum;

  /**
   * @property {number} isStock
   * @description 是否库存限制（0 无限制，1 有限制），必须提供。
   */
  isStock: number;

  /**
   * @property {number} stock
   * @description 库存数量，必须提供。
   */
  stock: number;

  /**
   * @property {number} preSaleStartTimestamp
   * @description 预售开始时间戳，必须提供。
   */
  preSaleStartTimestamp: number;
}

/**
 * @interface PreOrderDTO
 * @description 预订单数据传输对象，包含组织信息、订单详情、商品列表等。
 */
export interface PreOrderDTO {
  /**
   * @property {string} orgId
   * @description 组织 ID，必须提供。
   */
  orgId: string;

  /**
   * @property {string} orgName
   * @description 组织名称，必须提供。
   */
  orgName: string;

  /**
   * @property {string} [orderRemark]
   * @description 订单备注信息，非必须。
   */
  orderRemark?: string;

  /**
   * @property {string} [crmAdminId]
   * @description 课程顾问 ID，非必须。
   */
  crmAdminId?: string;

  /**
   * @property {boolean} needAddress
   * @description 是否需要地址，必须提供。
   */
  needAddress: boolean;

  /**
   * @property {boolean} [freightValid]
   * @description 是否支持用户选择的配送地址，默认为 true，非必须。
   */
  freightValid?: boolean;

  /**
   * @property {string} [freightValidText]
   * @description 不支持收货地址时的提示文案，非必须。
   */
  freightValidText?: string;

  /**
   * @property {string} freightTemplateId
   * @description 运费模板 ID，必须提供。
   */
  freightTemplateId: string;

  /**
   * @property {number} [freightFee]
   * @description 运费（元），非必须。
   */
  freightFee?: number;

  /**
   * @property {number} totalOrgGoodsCount
   * @description 组织内商品总数，必须提供。
   */
  totalOrgGoodsCount: number;

  /**
   * @property {number} totalOrgPayAmount
   * @description 组织内合计金额，必须提供。
   */
  totalOrgPayAmount: number;

  /**
   * @property {boolean} isUseCoupon
   * @description 组织内是否使用优惠券，默认为 true，必须提供。
   */
  isUseCoupon: boolean;

  /**
   * @property {boolean} userHaveCoupon
   * @description 是否有组织内可用优惠券，必须提供。
   */
  userHaveCoupon: boolean;

  /**
   * @property {string} [couponId]
   * @description 组织内勾选优惠券 ID，非必须。
   */
  couponId?: string;

  /**
   * @property {string} [userCouponId]
   * @description 用户有多个同类型优惠券时，区分用户选择的优惠券，非必须。
   */
  userCouponId?: string;

  /**
   * @property {number} couponAmount
   * @description 组织内优惠金额总和，必须提供。
   */
  couponAmount: number;

  /**
   * @property {PreOrderGoodsDTO[]} goodsList
   * @description 组织下商品集合，必须提供。
   */
  goodsList: PreOrderGoodsDTO[];

  /**
   * @property {PreOrderBuyAtMarkupDTO} [buyAtMarkup]
   * @description 加价购信息，非必须。
   */
  buyAtMarkup?: PreOrderBuyAtMarkupDTO;
}

/**
 * 送货信息数据传输对象
 */
export interface DeliveryInfoDTO {
  address: string; // 地址
  addressDetail: string; // 详细地址
  cityAliasName: string; // 城市别名
  cityId: string; // 城市ID
  cityName: string; // 城市名称
  countyAliasName: string; // 县/区别名
  countyId: string; // 县/区ID
  countyName: string; // 县/区名称
  id: string; // ID
  isDefault: number; // 是否默认地址
  provinceAliasName: string; // 省份别名
  provinceId: string; // 省份ID
  provinceName: string; // 省份名称
  receiveMobile: string; // 收货人手机号
  receiver: string; // 收货人姓名
}

/**
 * 预下单信息响应值
 */
export interface FetchPreOrderInfoDTO {
  needAddress: boolean, // 是否需要地址
  deliveryInfo?:DeliveryInfoDTO;
  preOrderKey: string; // 预下单生成的 key，必须
  totalAmount: number; // 商品总价，必须
  totalDiscountAmount: number; // 总计优惠金额，必须
  totalFreightAmount: number; // 总计运费，必须
  totalGoodsCount: number; // 商品总数，必须
  totalPayAmount: number; // 优惠后合计金额，必须
  orders: PreOrderDTO [], // 预下单商品信息，按照组织进行分组
}

// -----------------预下单相关 end------------------------------------------------------------

/**
 * 新增多个订单-请求参数
 */
export interface FetchAddMultiOrderParams {
  /**
   * 预下单 key
   */
  preOrderKey: string; // 必须

  /**
   * 应付金额
   */
  shouldPay: number; // 必须
}

/**
 * 订单商品数据传输对象
 */
export interface OrderGoodsDTO {
  /**
   * 专业分类ID(非必须)
   */
  cateIds: string;

  /**
   * 专业分类名称(非必须)
   */
  cateNames?: string;

  /**
   * 标签(非必须)
   */
  tags?: string;

  /**
   * 商品ID(必须)
   */
  goodsMasterId?: string;

  /**
   * 规格ID(必须)
  */
  specificationItemId?: string;

  /**
   * 商品标题(必须)
   */
  goodsTitle?: string;

  /**
   * 商品现价(必须)
   */
  goodsPresentPrice?: string;
}

/**
 * 新增多个订单响应数据传输对象
 */
export interface FetchAddMultiOrderDTO {
  /**
   * 订单编号，必须
   */
  orderSn: string;

  /**
   * 订单id，必须
   */
  orderId: string;

  /**
   * 应收金额，必须
   */
  shouldPay: string;

  /**
   * 订单状态，必须
   */
  generalOrderStatus: number;

  /**
   * 商品列表，必须
   */
  goodsList:OrderGoodsDTO[];
}
