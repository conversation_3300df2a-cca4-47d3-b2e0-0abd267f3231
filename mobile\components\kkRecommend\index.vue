<template>
  <div v-if="recommendList?.length" class="recommend" :class="[isSearch && 'search__suggest', isCourseRecommend && 'course-detail_recommend']">
    <div :class="isSearch ? 'border-gradient' : ''">
      <div class="ml-[24px]">
        <TipTitle v-if="isSearch" title="好课推荐" text-color="#B50007" />
        <TipTitle v-else title="好课推荐" img-icon="good-class" />
      </div>
    </div>

    <div class="rounded-[16px]" :class="[isCourseRecommend ? '!pb-[24px]' : '']">
      <good-cell
        v-for="(item, index) in recommendList"
        :key="index"
        :goods="item"
        :is-search="isSearch"
        :is-course-recommend="isCourseRecommend"
        :is-course="true"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
const route = useRoute()
const { searchApi } = useApi()
const { usableId } = useLearnTarget()

const props = withDefaults(defineProps<{
  isSearch: boolean
  bgColor: string,
  requestFrom: undefined | string // 请求来源 学习中心该接口不校验学习目标 学习中心传1 其他默认不传
  isCourseRecommend: boolean
}>(), {
  isSearch: false,
  isCourseRecommend: false,
  bgColor: '#fff',
  requestFrom: undefined
})

const localId = useCookie<string | null>('LT')
// 好课推荐
const recommendList = ref([])
const courseRecommend = async () => {
  const { data } = await searchApi.getCourseRecommend({
    pageMasterId: route.query?.pageId || route.query?.lt || localId.value || usableId.value,
    requestFrom: props.requestFrom
  }) as any
  recommendList.value = data.value?.list
}

onMounted(() => {
  nextTick(() => {
    // 保持客户端与服务端渲染一致，数据在Mounted后加载
    courseRecommend()
  })
})
</script>

<style lang="scss" scoped>
.course-detail_recommend {
  border-radius: 24px;
  background: linear-gradient( 180deg, #FFECE8 0%, #FFFFFF 10%, #FFFFFF 100%);
}

:deep(.goods-cell) {
  padding: 20px;
  .img {
    flex-shrink: 0;
  }
  .goods-cell-info-title{
    // width: 366px;
  }
  .goods-cell-info-package{
    // width: 366px;
  }
}
</style>
