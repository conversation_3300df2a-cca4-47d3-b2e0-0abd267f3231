import type { ClassBasicType } from '~/pages/question-bank/types/basic'

export const useQuestionBankClassifyInfo = () => {
  const { questionBankApi } = useApi()
  const route = useRoute()

  /**
   * 分类列表数据
   */
  const classList = ref<ClassBasicType[]>([])

  /**
   * 默认选中分类
   */
  const defaultStudyLevel = computed(() => {
    const { studyLevel1 } = route.query
    return Number(studyLevel1) || classList.value[0]?.studyLevel1
  })

  /**
   * 获取分类列表数据
   */
  const getClassifyListData = async (moduleManageId: string) => {
    try {
      const { data } = await questionBankApi.fetchCategoryListByModuleId({
        moduleManageId: moduleManageId as string
      })

      classList.value = data.value.list
    } catch (error) {
      console.warn(error)
    }
  }

  /**
   * 获取分类详情
   */
  const getClassifyDetailData = (categoryId: number) => {
    const info = classList.value.find(item => item.studyLevel1 === categoryId)
    if (info) {
      return info
    }
    return ''
  }
  /**
   * 获取分类详情
   */
  const getModuleInfoAndStudyLevelName = async (moduleManageId: string) => {
    const { data } = await questionBankApi.getModuleInfoAndStudyLevelName({
      moduleManageId: moduleManageId as string,
      studyLevel1: route.query.studyLevel1 as string,
    })
    return data.value
  }

  return {
    defaultStudyLevel,
    classList,
    getClassifyListData,
    getClassifyDetailData,
    getModuleInfoAndStudyLevelName
  }
}
