import { createApp } from 'vue'
import ConfirmDialog from '../components/ConfirmDialog.vue'

let instance: any

// 初始化弹窗实例
const initDialog = () => {
  if (!instance) {
    const container = document.createElement('div')
    document.body.appendChild(container)
    const app = createApp(ConfirmDialog)
    instance = app.mount(container)
  }
}

// 调用弹窗
export const showConfirmDialog = async (message: string): Promise<boolean> => {
  initDialog()
  return instance.open(message)
}
