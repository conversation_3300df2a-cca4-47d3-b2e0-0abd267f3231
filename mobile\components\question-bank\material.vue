<template>
  <div>
    <div class="exam-quest-title">
      <span class="quest-title">
        {{ question.chineseNum ? `${question.chineseNum}、` : '' }}
        {{ questionTitle }}
      </span>
      <span v-if="(moduleType === 2 || moduleType === 0)" class="explain" @click="onDescriptionClick">
        <span v-if="question.description">（{{ question.description }}）</span>&nbsp;
        <KKCIcon
          v-if="question.description?.length > 15"
          class="quest-title-right-icon"
          name="icon-wode-youjiantou"
          :size="24"
        />
      </span>
    </div>
    <div v-img-preview="question.stem" class="material-stem exam-html">
      <KKCHtmlMathJax :content="question.stem" />
    </div>
    <KKCAudioPlayer
      v-if="question.stemSrc"
      class="bg-[#f8f8f8]"
      :icon-name="'icon-yinpinbofang'"
      :playing-icon="'icon-yinpinzanting'"
      :icon-size="48"
      :url="question.stemSrc"
    />
    <!-- <van-floating-panel
      v-if="question.ptypeValueId == 7"
      v-model:height="height"
      :style="'left:' + current * 100 + 'vw !important'"
      :anchors="anchors"
    >
      <img class="drag-up" :src="dragup" /> -->
    <div class="pb-[40px]">
      <div v-for="(value, cindex) in question.childQuestions" :key="value.id" class="mt-[48px] relative">
        <div class="cl-num">
          <span>{{ value.cnum }}</span><span class="cl-total">/{{ value.ctotal }}</span>
        </div>
        <!-- 单选 || 判断 -->
        <QuestionBankRadioChoice
          v-if="value.ptypeValueId === 1 || value.ptypeValueId === 5"
          :question="value"
          :is-edit="isEdit"
          :model="model"
          :is-material="isMaterial"
          @change="(val, logStatus) => changeAnswer(val, logStatus, value, cindex)
          "
        />
        <!-- 多选 || 不定项 -->
        <QuestionBankMultipleChoice
          v-if="value.ptypeValueId === 2 || value.ptypeValueId === 3"
          :question="value"
          :is-edit="isEdit"
          :model="model"
          :is-material="isMaterial"
          @change="(val, logStatus) => changeAnswer(val, logStatus, value, cindex)
          "
        />
        <!-- 填空 || 简答 -->
        <QuestionBankFill
          v-if="value.ptypeValueId == 4 || value.ptypeValueId == 6"
          :question="value"
          :is-edit="isEdit"
          :is-drag="isDrag"
          :is-expand="isExpand"
          :model="model"
          :exam-mode-number="examModeNumber"
          :is-material="isMaterial"
          @getIsFocus="getIsFocus"
          @change="(val, logStatus) => changeAnswer(val, logStatus, value, cindex)
          "
        />
        <slot />
      </div>
    </div>
    <!-- </van-floating-panel> -->
    <Teleport to="body">
      <QuestionBankPopupDescription
        v-if="description"
        :show="isShowDescription"
        :description="description"
        @ok="onDescriptionClick"
      />
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import type { questType } from './type'
// import dragup from "@/assets/images/question-bank/dragup.png";
const route = useRoute()
// 1每日一练,3 章节练习,2 试卷，处理数据逻辑不同
const moduleType = Number(route.query.moduleType)
interface Props {
  question: questType; // 试题信息
  model?: number; // 1联系模式，2考试模式
  // current: number; // 当前题目位于全部题目的位置
  isEdit?: boolean; // 1可以操作，2纯展示
  examModeNumber?:number
  isExpand?: boolean // 是否展开
  isDrag?: boolean // 是否正在拖动
}
const props = withDefaults(defineProps<Props>(), { isEdit: true })
const emit = defineEmits<{
  (
    e: 'change',
    val: any[],
    status: number,
    item: questType,
    index: number
  ): void;
  (e: 'getIsFocus', bool: boolean): void;
}>()
// 填空和简答的是否聚焦
const getIsFocus = (val: boolean) => {
  // isInputFocus.value=true
  emit('getIsFocus', val)
}
// 是否是材料题
const isMaterial = ref(true)
// 拖拽面板
// const anchors = ref<number[]>([100]);
// // const height = ref(anchors[0]);
// // 计算面板停留的位置
// const getAnchors = (num: number) => {
//   const arr = [
//     0.3, 0.35, 0.4, 0.45, 0.5, 0.55, 0.6, 0.65, 0.7, 0.75, 0.8, 0.85, 0.9,
//   ];
//   const newArr = [100];
//   arr.forEach((v) => {
//     newArr.push(v * num);
//   });
//   return newArr;
// };
// const questionItem = computed(() => props.question) as ComputedRef<questType>
// 选择 、 判断题 、 填空 、 简答题的答案
const changeAnswer = (
  logAnswer: any,
  logStatus: number,
  cItem: any,
  cIndex: number
) => {
  emit('change', logAnswer, logStatus, cItem, cIndex)
}

// 题型说明
const isShowDescription = ref(false)
const description = computed(() => props.question.description)
const onDescriptionClick = () => {
  if ((moduleType === 2 || moduleType === 0) && description) {
    isShowDescription.value = !isShowDescription.value
  }
}

/**
 * 题型title
 */
const questionTitle = computed(() => {
  const { ptypeValueIdTitle, questionTypeValueIdTitle } = props.question
  return handleQuestionTitle(ptypeValueIdTitle || '', questionTypeValueIdTitle, moduleType)
})

onMounted(() => {
  // nextTick(() => {
  //   anchors.value = getAnchors(window.innerHeight);
  // });
})
</script>

<style scoped lang="scss">
.material-stem {
  font-size: 32px;
  word-wrap: break-word !important;
  color: #111;
  // margin-bottom: 210px;
}

:deep(.van-floating-panel) {
  .van-floating-panel__header {
    height: 0px;
    // .van-floating-panel__header-bar {
    //   height: 0px;
    // }
  }

  .van-floating-panel__content {
    .drag-up {
      width: 160px;
      height: 43px;
      position: absolute;
      top: -42px;
      left: calc(50% - 80px);
    }
  }
}

.cl-num {
  position: absolute;
  top: 15px;
  right: 15px;
  font-size: 28px;
  color: #111;

  .cl-total {
    font-size: 24px;
    color: #999999;
  }
}
</style>
