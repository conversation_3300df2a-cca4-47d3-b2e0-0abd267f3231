<template>
  <!-- 首页题库模块 -->
  <div class="w-[100%] px-[24px] pt-[24px] pb-[48px]">
    <div class="relative w-[702px] h-[305px]">
      <img
        class="w-[702px] h-[305px]"
        src="assets/images/home/<USER>/home_tiku_bg.png"
        loading="lazy"
        alt=""
      >
      <div
        class="flex items-center justify-center pl-[16px] pr-[13px] py-[8px] bg-[rgba(0,0,0,0.2)] rounded-[44px] absolute right-[20px] top-[30px]"
        @click="goTiKu()"
      >
        <span class="text-[24px] text-[#fff] pl-[5px] pt-[2px]">开始答题</span>
        <img class="w-[28px] h-[28px]" src="assets/images/home/<USER>/home_tiku_more.png" alt="">
      </div>
    </div>
    <div v-if="materialZBList.length" class="zb-content w-[702px] rounded-[24px] bg-[#fff] my-[26px] py-[30px]">
      <div class="flex items-center justify-between px-[22px]">
        <div class="zb-content__title">
          专升本资料下载
        </div>
        <div class="text-[24px] text-[#666] leading-[30px]" @click="moreHref(1)">
          <span>全部</span>
          <KKCIcon
            class="text-[#666]"
            name="icon-wode-youjiantou"
            :size="32"
          />
        </div>
      </div>
      <MaterialCell
        v-for="(item, index) in materialZBList"
        :key="index"
        :material="item"
        :page-type="1"
      />
    </div>
    <div v-if="materialGKList.length" class="gk-content w-[702px] rounded-[24px] bg-[#fff] py-[30px]">
      <div class="flex items-center justify-between px-[22px] ">
        <div class="zb-content__title">
          公考资料下载
        </div>
        <div class="text-[24px] text-[#666] leading-[30px]" @click="moreHref(2)">
          <span>全部</span>
          <KKCIcon
            class="text-[#666]"
            name="icon-wode-youjiantou"
            :size="32"
          />
        </div>
      </div>
      <MaterialCell
        v-for="(item, index) in materialGKList"
        :key="index"
        :material="item"
        :page-type="1"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { getHomeMaterialList } from '~/apis/material'

// [AI-GEN] 列表数据
const materialZBList = ref<any[]>([])
const materialGKList = ref<any[]>([])

// [AI-GEN] 获取资料列表数据
const getList = async (type: number) => {
  const { data, error } = await getHomeMaterialList({ type })
  console.log(data.value, error.value, type, 'M端--getList999999999999999')
  if (data.value) {
    const list = data.value.length > 5 ? data.value.slice(0, 5) : data.value
    if (type === 1) {
      materialZBList.value = list
    } else {
      materialGKList.value = list
    }
  } else if (type === 1) {
    materialZBList.value = []
  } else {
    materialGKList.value = []
  }
}
// 初始加载
await Promise.all([
  getList(1),
  getList(2),
])

// onMounted(async () => {
//   await getList(1)
//   await getList(2)
// })

const TIKU_URL = 'https://m.kuke99.com/cp/937282926268461057'
const DEFAULT_QUERY = {
  studyLevel1: 1,
  moduleManageId: 902421074161504256,
  subjectType: 8,
  directionType: -1,
  region: 10,
  academicSection: -1,
  examFormat: -1,
  page: 1,
  pageSize: 10
}

const goTiKu = () => {
  // Message('去题库~')
  navigateTo({
    path: TIKU_URL,
    query: DEFAULT_QUERY,
  }, { external: true })
}

const moreHref = (type: number) => {
  const params = {
    syncInfo: '0',
    type: String(type),
    cateId: type === 1 ? 1 : ''
  }
  const str = JSON.stringify(params)
  if (os?.isApp && os.isAndroid) {
    window.android.goDataDownloadPage(str)
  } else if (os?.isApp && (os?.isPhone || os?.isTablet)) {
    window.webkit.messageHandlers.goDataDownloadPage.postMessage(str)
  } else {
    navigateTo({
      path: '/material/category-list',
      query: params
      // replace: true
    })
  }
}

</script>

<style scoped lang="scss">
.zb-content{
  background-image: url('@/assets/images/home/<USER>/tiku_zhuanben.png');
  background-size: 100% auto;
  background-repeat: no-repeat;
}
.gk-content{
  background-image: url('@/assets/images/home/<USER>/tiku_zhuanben.png');
  background-size: 100% auto;
  background-repeat: no-repeat;
}
.zb-content__title{
    position: relative;
  // text-[34px] font-[500] text-[#111] leading-[44px]
  font-size: 34px;
  font-weight: 500;
  color: #111;
  line-height: 44px;
    &::after{
    content: '';
    width: 30px;
    height: 18px;
    background-image: url('@/assets/images/home/<USER>/tiku_zhuanben-sub.png');
    background-repeat: no-repeat;
    background-size: cover;
    position: absolute;
    // right: -15px;
    // bottom: -9px;
    right: -10px;
    bottom: 2px;
}
}
</style>
