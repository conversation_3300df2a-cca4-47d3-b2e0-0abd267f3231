export interface GoodsList {
    goodsMasterId:string
    specificationItemId?:string
    goodsTitle:string
    goodsImg:string
    goodsPresentPrice?:string
    stock?:number
    cateIds?:string
}

export interface BaseConfig {
    // 活动名称
    activityName:string
    // 活动说明
    activityExplain:string
    // 背景图
    pcBackgroundPictureUrl:string
    wapBackgroundPictureUrl:string
    // 活动按钮名称
    activityButtonName:string
    // 按钮背景色
    buttonBackgroundColor:string
    // 按钮文字颜色
    buttonWordColor:string
}

export interface ReceiveT {
    activityName:string
    userName:string
    photo:string
    activationCode:string
    activationCodeType?:1|2
    activationCodeBeginTime?:string
    activationCodeEndTime?:string
    couponList?:any[]
    goodsList?:GoodsList[]
}
