<template>
  <div>
    <div class="flex flex-col items-center" @click.stop="handleShareClick">
      <slot name="default">
        <KKCIcon
          :name="text ? 'icon-fenxiang1' : 'icon-xiangqing-fenxiang'"
          color="#111"
          :size="iconSize"
        />
        <span v-if="text" class="text-[22px] text-[#111] pt-[6px]">{{
          text
        }}</span>
      </slot>
    </div>
    <Teleport to="body">
      <Poster
        v-if="show"
        :id="id"
        :qr-width="320"
        :show="show"
        :title="title"
        :link="link"
        @close="show = false"
      />
      <!-- 小程序中，分享调用引导弹窗，引导使用微信分享 -->
      <QuestionBankShareGuide
        :is-show-guide="isShowGuide"
        :share-title="`这${type === 2 ? '套试卷' : '道题'}不错，快来看看吧~`"
        :link="link"
        @close="isShowGuide = false"
      />
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import type { SelectParams } from '../../pages/question-bank/types/basic'
import Poster from './Poster.vue'
const { track } = useTrackEvent()

const props = withDefaults(
  defineProps<{
    id: string;
    title: string;
    type: 1 | 2; // 1: 试题 2: 试卷
    cid?: string; // 试题材料题子题ID
    iconSize?: number;
    text?: string;
    shareParams?: SelectParams;
    shareStudyLevel1?: string | number;
    shareGoodsMasterId?: string | number;
  }>(),
  {
    iconSize: 28,
    text: '分享',
  }
)

const route = useRoute()
const link = computed(() => {
  const { type, id, cid } = props
  const {
    studyLevel1,
    subjectType,
    region,
    directionType,
    academicSection,
    examFormat,
    moduleManageId,
    moduleId,
    moduleType,
    testpaperId,
  } = route.query
  // 分享参数
  const query = {
    studyLevel1: String(studyLevel1),
    subjectType: String(subjectType || 0),
    region: String(region || 0),
    directionType: String(directionType || 0),
    academicSection: String(academicSection || 0),
    examFormat: String(examFormat || 0),
    moduleManageId: String(moduleManageId || moduleId),
    testpaperId: String(testpaperId) || '',
  }

  // 存在分享信息参数，则使用分享信息参数
  if (props.shareStudyLevel1) {
    Object.assign(
      query,
      {
        ...props.shareParams,
        subjectType: props.shareParams.subjectType || query.subjectType,
        region: props.shareParams.region || query.region,
        directionType: props.shareParams.directionType || query.directionType,
        academicSection: props.shareParams.academicSection || query.academicSection,
        examFormat: props.shareParams.examFormat || query.examFormat,
      } || {},
      {
        studyLevel1: String(props.shareStudyLevel1),
      }
    )
  }

  // 存在商品ID，则添加商品ID
  if (props.shareGoodsMasterId) {
    Object.assign(query, {
      goodsMasterId: String(props.shareGoodsMasterId),
    })
  }

  const questionQuery = {
    shareId: id,
    cid: cid || '',
    moduleType,
  }
  const paperQuery = {
    ...query,
    testpaperId: id,
  }
  const baseQuery = type === 1 ? questionQuery : paperQuery
  const baseUrl = `${window.location.origin}/question-bank/`
  const detailPath = type === 1 ? 'question/detail' : 'paper/detail'
  return `${baseUrl}${detailPath}?${new URLSearchParams(baseQuery).toString()}`
})

const show = ref(false)

/**
 * 分享引导
 */
const isShowGuide = ref(false)

const { isXcx } = useAppConfig()

const handleShareClick = () => {
  if (isXcx) {
    isShowGuide.value = true
  } else {
    Loading(true)
    setTimeout(() => {
      show.value = true
      Loading(false)
    }, 1000)
  }
  // 做题->试题-分享 埋点
  track({
    category: '题库',
    // action: props.shareGoodsMasterId ? '答题报告-邀请好友' : '试题-分享'
    action:
      props.type === 1
        ? '试题-分享'
        : props.shareGoodsMasterId
          ? '答题报告-邀请好友'
          : '试卷-分享',
  })
}
// 监听分享链接变化，设置分享信息
watch(
  link,
  async (newValue) => {
    if (!newValue) {
      return
    }
    try {
      // 根据类型生成分享标题
      const shareTitle = `这${
        props.type === 2 ? '套试卷' : '道题'
      }不错，快来看看吧~`
      // 设置分享信息
      const { setQuestionBankShareInfo } = await useSetQuestionBankShare({
        title: shareTitle,
        img: 'https://oss.kuke99.com/kukecloud/static/common/tiku-share-cover.png',
        url: newValue,
      })
      // 执行分享设置
      setQuestionBankShareInfo()
    } catch (error) {
      console.error('设置分享信息失败:', error)
    }
  },
  {
    immediate: true,
  }
)
</script>

<style lang="scss" scoped>
</style>
