export interface ResultType {
  // 相对路径
  name: string,
  // 绝对路径
  url: string,
  res: any
}

/**
 * 获取答疑列表参数
 */

export interface ListParams {
  // 页码
  page?: number;
  // 每页数量
  pageSize?: number;
}

/**
 * 答疑问题描述
 */

export interface DescriptionType {
  type: string,
  text: string,
  firstPic?: string,
  duration?: string
}

/*
* 答疑列表item
*/
export interface ListItem {

  // id
  id: string;
  // 问题描述
  problemDescription: DescriptionType | string;
  // 科目名称
  subjectName: string;
  // 专业名称
  cateName: string;
  // 发起时间
  createdAt: string;

}

/**
 *  答疑列表返回
 *
 */

export interface ListReturn {
  // 全部
  total?: number;
  // 列表
  list: ListItem[];
}

/**
 *
 * 拍照搜题参数
 *
 */

export interface PhotoParams {

  /**
   * 问题描述
   */
  problemDescription: string,
  /**
   * 产品线id
   */
  productId: number | null,
  /*
  * 专业分类id
  */
  cateId: string,
  /**
   * 专业分类名称
   */
  cateName: string
}

/**
 * 拍照搜题列表
 */
export interface QuestionList {
  /**
   * 试题id
   */
  id: string,
  /**
   * 是否来源网络0.否1.是
   */
  isSourceNetwork: number,
  /**
   * 题干
   */
  stem: string,
  /**
   * 题型 1-单选, 2-多选，3-不定项选择题，4-填空题, 5-判断题 6-问答题 7-材料题
   */
  ptypeValueId: string,
  /**
   * 题型名称
   */
  ptypeTitle: string,
  /**
   * 答案
   */
  answer: string[] | string,
  /**
   * 解析
   */
  analysis: string,
  /**
   * 选项
   */
  metas: string[] | string,
  /**
   * 知识点名称
   */
  knowledgePointList: string[] | string,
  /**
   * 试题分类标签项中文名
   */
  questionTagList: string[] | string,
  /**
   * 题干音频src
   */
  stemSrc?: string,
  /**
   * 解析音频src
   */
  analysisSrc?: string
  childQuestions?:any[] // 子题
}

/**
 * 拍照搜题返回
 */

export interface PhotoReturn {
  /**
   * 拍搜id
   */
  requestId: string,
  /**
   *
   */
  isSourceNetwork: number,
  /**
   * 列表
   */
  questionList: QuestionList[];
  aiOriginResponse:string

}

/**
 * 分类列表参数
 */

export interface CategoryParams {
  /**
   * 专业分类id
   */
  categoryId: string;
}

/**
 * 分类列表返回
 */

/**
 * 商品关联标签参数
 */

export interface GoodsTagParams {
  /**
   * 商品id
   */
  goodsMasterId: string;

  /**
   *  标签项：地区region、学段academicSection、方向direction、科目subject、考试形式examFormat
   */
  tags: string[]
}

/**
 * 地区信息
 * 非必须
 */
export interface Region {
  /**
   * 标签名称
   * 必须
   */
  dataName: string;

  /**
   * 状态
   * 必须
   */
  status: number;

  /**
   * 标签值
   * 必须
   */
  usedId: string;
}

/**
 * 学段信息
 * 非必须
 */
interface AcademicSection {
  /**
   * 标签名称
   * 必须
   */
  dataName: string;

  /**
   * 状态
   * 必须
   */
  status: number;

  /**
   * 标签值
   * 必须
   */
  usedId: string;
}

/**
 * 方向信息
 * 非必须
 */
interface Direction {
  /**
   * 标签名称
   * 必须
   */
  dataName: string;

  /**
   * 状态
   * 必须
   */
  status: number;

  /**
   * 标签值
   * 必须
   */
  usedId: string;
}

/**
 * 科目信息
 * 非必须
 */
interface Subject {
  /**
   * 标签名称
   * 必须
   */
  dataName: string;

  /**
   * 状态
   * 必须
   */
  status: number;

  /**
   * 标签值
   * 必须
   */
  usedId: string;
}

/**
 * 考试形式信息
 * 非必须
 */
interface ExamFormat {
  /**
   * 标签名称
   * 必须
   */
  dataName: string;

  /**
   * 状态
   * 必须
   */
  status: number;

  /**
   * 标签值
   * 必须
   */
  usedId: string;

}

/**
 * 商品关联标签返回
 */
export interface GoodsTagReturn {
  /**
 * 地区信息
 * 非必须
 */
  region?: Region[];

  /**
   * 学段信息
   * 非必须
   */
  academicSection?: AcademicSection[];

  /**
   * 方向信息
   * 非必须
   */
  direction?: Direction[];

  /**
   * 科目信息
   * 非必须
   */
  subject?: Subject[];

  /**
   * 考试形式信息
   * 非必须
   */
  examFormat?: ExamFormat[];
}

/**
 * 专业分类处理数据
 *
 */

export interface TagItem {

  /**
   *
   * 标签名称
   */
  title: string;

  /**
   *
   * 标签值
   */
  value: Region[];

  /**
   *
   * 标签类型
   */
  key: string;

  /**
   *
   * 展开收起
   */
  fold: boolean,

  showFoldButton?: boolean

}

/**
 * 全部标签
 */

export interface AllTagItem {

  /**
 * 标签键ID
 * @type {string}
 * @example "858568207585726464"
 */
  labelKeyId: string

  /**
   * 标签值
   * @type {string}
   * @example "试讲"
   */
  labelValue: string

  /**
   * 标签值ID
   * @type {string}
   * @example "861779485196271616"
   */
  labelValueId: string

  /**
   * 级别
   * @type {number}
   * @example 3
   */
  level: number

  /**
   * 使用ID
   * @type {number}
   * @example 1250
   */
  usedId: number
}

/**
 * 定义一个接口，用于描述科目类型相关的信息
 */
export interface AllTagList {
  /**
   * 别名，表示科目类型的标识符
   * @type {string}
   * @example "subject_type"
   */
  alias: string;

  /**
   * 别名，使用驼峰命名法的科目类型标识符
   * @type {string}
   * @example "subjectType"
   */
  aliasCamel: string;

  /**
   * 标签键，表示科目的名称
   * @type {string}
   * @example "科目"
   */
  labelKey: string;

  /**
   * 标签键ID，表示科目名称的唯一标识符
   * @type {string}
   * @example "858568207585726464"
   */
  labelKeyId: string;

  /**
   * 标签值
   * @type AllTagItem[]
   * @example
   */

  labelValueList: AllTagItem[];
}

/*
 * 渲染标签
 */

export interface RenderTagItem {
  /**
     * 项目的标签键，表示项目的名称或描述
     * @type {string}
     * @example "科目"
     */
  title: string;

  /**
   * 项目的别名
   * @type {string}
   * @example "subjectType"
   */
  key: string;

  /**
   * 项目的值，表示项目的具体内容或数据
   * @type {any}
   * @example 12345
   */
  value: Region[];

  /**
   *
   * 展开收起
   */
  fold: boolean,

  length?:number
}

/**
 *
 * 选择分类标签值
 */
export interface ChoiceTag {

  /**
   * 标签值
   */
  usedId: string;

  /**
   * 类型
   */
  key: string;

  /**
   * 标签名称
   */
  dataName: string;
}

/**
 *
 * 自定义类型
 */

export interface KeyStringType {
  [key:string]: string
}

/**
* 标签值列表中的单个项
*/
export interface LabelValueListItem {
  /**
   * 标签值
   */
  labelValue?: string;

  /**
   * 标签值id
   */
  labelValueId?: string;

  /**
   * 标签项id
   */
  labelKeyId?: string;

  /**
   * 使用Id
   */
  usedId?: number;
}

/**
 * 标签列表中的单个项
 */
export interface LabelListItem {
  /**
   * 标签项名
   */
  labelKey?: string;

  /**
   * 标签项id
   */
  labelKeyId?: string;

  /**
   * mock字段（注意：mock字段通常不直接放在接口定义中，这里仅作为说明）
   * （实际开发中，mock字段不会放在接口定义里）
   */
  // mock?: string; // 移除这一行，因为它不应该在接口定义中

  /**
   * 标签项别名
   */
  alias?: string;

  /**
   * 标签项别名驼峰
   */
  aliasCamel?: string;

  /**
   * 标签值列表
   */
  labelValueList?: LabelValueListItem[];
}

/**
* 分类对象
*/
export interface AllTagReturn {
  /**
   * 非必须
   */
  id?: string;

  /**
   * 分类id（mock: @string("number", 18)）
   */
  categoryId?: string;

  /**
   * 分类名称
   */
  categoryName?: string;

  /**
   * 状态 1 开启 0 禁用（mock: @natural(1,9)）
   */
  status?: number;

  /**
   * 标签列表
   */
  labelList?: LabelListItem[];

}

/**
 *
 * 分类请求参数
 *
 */

export interface AllTagParams {
  /**
   * 分类id
   */
  id?: string;

  /**
   * 使用id
   */
  usedId?: string;

}

/**
 * @params
 * 题库搜索
 */

export interface QKParams {

  /**
   * 搜索关键字
   */
  questionIds: string[]

}

/**
* 真题标签项接口
*/
export interface TrueQuestionTag {
  /**
   * 非必须
   * 年份数组
   */
  years?: string[];

  /**
   * 非必须
   * 区域ID数组
   */
  regionIds?: number[];

  /**
   * 非必须
   * 真题标签数组
   */
  trueQuestionLabel?: string[];
}

/**
* 试题分类标签接口
*/
export interface QuestionTag {
  /**
   * 非必须
   * 一级分类
   */
  studyLevel1?: string;

  /**
   * 非必须
   * 二级分类
   */
  studyLevel2?: string;

  /**
   * 非必须
   * 三级分类
   */
  studyLevel3?: string;

  /**
   * 非必须
   * 四级分类
   */
  studyLevel4?: string;

  /**
   * 非必须
   * 标签对象
   */
  tag?: string[];

  /**
   * 非必须
   * 试题分类标签项中文名数组
   */
  questionTagList?: string[];
}

/**
* 标签接口
*/
export interface Tag {
  // 这里可以根据需要添加具体的Tag属性，但当前描述中没有详细说明
}

/**
* 关联知识点接口
*/
export interface Knowledge {
  /**
   * 必须
   * 知识题系
   */
  knowledgeSystemId: string;

  /**
   * 必须
   * 一级知识点
   */
  knowledgeLevel1: string;

  /**
   * 非必须
   * 二级知识点
   */
  knowledgeLevel2?: string;

  /**
   * 非必须
   * 三级知识点
   */
  knowledgeLevel3?: string;

  /**
   * 非必须
   * 四级知识点
   */
  knowledgeLevel4?: string;

  /**
   * 非必须
   * 五级知识点
   */
  knowledgeLevel5?: string;

  /**
   * 非必须
   * 关联知识点名称数组
   */
  knowledgePointList?: string[];
}

/**
* 关联章节接口
*/
export interface TextbookChapter {
  /**
   * 必须
   * 教材id
   */
  textbookId: string;

  /**
   * 必须
   * 一级章节id
   */
  knowledgeLevel1: string;

  /**
   * 非必须
   * 二级章节id
   */
  knowledgeLevel2?: string;

  /**
   * 非必须
   * 三级章节id
   */
  knowledgeLevel3?: string;

  /**
   * 非必须
   * 四级章节id
   */
  knowledgeLevel4?: string;

  /**
   * 非必须
   * 五级章节id
   */
  knowledgeLevel5?: string;

  /**
   * 非必须
   * 关联章节名称数组
   */
  chapterList?: string[];
}

/**
* 子试题接口
*/
export interface ChildQuestion {
  /**
   * 非必须
   * 一级题型
   */
  ptypeValueId?: number;

  /**
   * 非必须
   * 答案
   */
  answer?: string;

  /**
   * 非必须
   * 解析
   */
  analysis?: string;

  /**
   * 非必须
   * 选项
   */
  metas?: string;

  /**
   * 非必须
   * 关联知识点
   */
  knowledges?: Knowledge[];

  /**
   * 非必须
   * 子试题关联知识点名称
   */
  knowledgePointList?: string;

  /**
   * 非必须
   * 试题内容更新时间
   */
  questionUpdatedAt?: string;
}

/**
* 试题项接口
*/
export interface QuestionItem {
  /**
   * 必须
   * 试题id
   */
  id: string;

  /**
   * 必须
   * 一级题型
   */
  ptypeValueId: number;

  /**
   * 非必须
   * 一级题型名称
   */
  ptypeTitle?: string;

  /**
   * 非必须
   * 二级题型
   */
  questionTypeValueId?: number;

  /**
   * 非必须
   * 二级题型名称
   */
  questionTypeTitle?: string;

  /**
   * 必须
   * 题干
   */
  stem: string;

  /**
   * 非必须
   * 原始题干
   */
  originalStem?: string;

  /**
   * 非必须
   * 是否存在双中括号，0，不存在，1，存在
   */
  hasBrackets?: number;

  /**
   * 非必须
   * 答案
   */
  answer?: string;

  /**
   * 非必须
   * 解析
   */
  analysis?: string;

  /**
   * 非必须
   * 选项
   */
  metas?: string;

  /**
   * 非必须
   * 试题难度
   */
  difficultValueId?: number;

  /**
   * 非必须
   * 试题难度（标题）
   */
  difficultValueTitle?: string;

  /**
   * 非必须
   * 是否真题
   */
  hasTrueQuestion?: number;

  /**
   * 必须
   * 引用次数
   */
  citeCount: number;

  /**
   * 必须
   * 作答次数
   */
  doCount: number;

  /**
   * 必须
   * 正确次数
   */
  rightCount: number;

  /**
   * 必须
   * 相似题个数
   */
  similarityCount: number;

  /**
   * 必须
   * 更新时间
   */
  updatedAt: string;

  /**
   * 非必须
   * 真题标签
   */
  trueQuestionTag?: TrueQuestionTag[];

  /**
   * 非必须
   * 是否易错题
   */
  easyWrongQuestion?: number;

  /**
   * 非必须
   * 试题的分类标签
   */
  questionTags?: QuestionTag[];

  /**
   * 非必须
   * 关联知识点
   */
  knowledges?: Knowledge[];

  /**
   * 非必须
   * 关联章节
   */
  textbookChapters?: TextbookChapter[];

  /**
   * 非必须
   * 子试题列表
   */
  childQuestions?: ChildQuestion[];
}

/**
 * 搜索题库返回
 */

export interface QKReturn {
  /**
   * 非必须
   */
  count?: number;

  /**
   * 必须
   * 总条数
   */
  totalCount: string;

  /**
   * 必须
   * 试题列表
   */
  list: QuestionItem[];
}

/**
 *
 * 人工答疑权益校验
 *
*/

export interface UserEquityVerifyPort {
  /**
   * 必须
   * 分类id
   */
  cateId: string;

  /**
   * 非必须
   * 科目id
   */
  subjectId?: string;

  /**
   * 非必须
   * 地区id
   */
  regionId?: string;

  /**
   * 非必须
   * 方向id
   */
  directionId?: string;

  /**
   * 非必须
   * 学段
   */
  academicSectionId?: string;

  /**
   * 非必须
   * 考试形式
   */
  examFormatId?: string;

  /**
   * 非必须
   * 所属班型
   */
  classTypeId?: string;

}

export interface LabelValueVO {
  /**
   * 非必须
   * 标签项id
   */
  id: string;

  /**
   * 非必须
   * 标签项名称
   */
  name: string;

/**
   * 非必须
   * usedId
   */
  usedId: string
}

export interface LabelEquity {

  /**
     * 非必须
     * key
     */
  alias: string;

  /**
   * 非必须
   * 标签id
   */
  labelId: string;

  /**
   * 非必须
   * 标签名称
   */
  labelName: string;

  /**
   * 非必须
   * 标签名称, labelValueVO
   */
  labelValueList: LabelValueVO[];
}
