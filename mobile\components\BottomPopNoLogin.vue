<template>
  <div
    v-show="visible"
    class="kukeAi__nologin"
  >
    <div class="self-end">
      <img
        src="assets/images/home/<USER>"
        class="w-[130px] h-[124px]"
        alt=""
      >
    </div>
    <div class="ml-[10px]">
      <div class="text-white text-[28px] font-medium leading-[34px]">
        登录开启学习之旅~
      </div>
      <!-- mt-[4px] -->
      <div class="kukeAi__nologin-deepseek text-white text-[22px] font-medium leading-[28px]">
        接入DeepSeek，登录后马上使用！
      </div>
    </div>
    <span
      class="kukeAi__nologin-login flex-shrink-0"
    >
      登录
    </span>
    <span
      class="kukeAi__nologin-close flex-shrink-0"
      @click.stop.prevent="closeKukeAi"
    >
      <KKCIcon
        name="icon-com_guangbi"
        :size="24"
        color="#fff"
      />
    </span>
  </div>
</template>
<script setup lang="ts">
// defineProps({

// })
const visible = ref(true)
// const emits = defineEmits(['close'])

const closeKukeAi = () => {
  // emits('close')
  visible.value = false
}

</script>

<style lang="scss">
.kukeAi {
  &__nologin {
    padding: 0 26px 0 24px;
    width: 702px;
    height: 88px;
    background: rgba(0,0,0,0.6);
    border-radius: 48px 48px 48px 48px;
    margin: 0 auto;

    display: flex;
    align-items: center;
    justify-content: center;
  &-login {
width: 104px;
height: 60px;
line-height: 60px;
background: var(--kkc-brand);
border-radius: 30px 30px 30px 30px;
text-align: center;
margin-left: auto;

// width: 56px;
// height: 28px;
font-family: PingFang SC;
font-weight: 500;
font-size: 28px;
color: #FFFFFF;
// line-height: 28px;
// text-align: left;
font-style: normal;
// text-transform: none;

}
&-close{
  margin-left: 26px;
}
&-deepseek{
  background: linear-gradient(0deg, #AFD4FF 0%, #EFABFF 100%);
  background-clip: text;
  /* 文本颜色设为透明以显示背景渐变 */
            -webkit-text-fill-color: transparent;
}
  }

}
</style>
