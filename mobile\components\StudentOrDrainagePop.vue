<template>
  <div class="student-drainage-pop">
    <slot name="button" :show-popby-type="showPopbyType">
      <div class="mb-[24px] w-[100%] h-[128px] rounded-[16px] bg-[#fff] learn-bottom" @click.prevent="showPopbyType">
        <img :src="type === 'student'? StudentImg :DrainageImg">
      </div>
    </slot>

    <Teleport to="body">
      <KKPopup
        ref="popupStudentRef"
        position="bottom"
        box-padding="0"
        :close-icon-size="32"
        :title="title"
        :closeable="true"
        class="h-[1000px]"
        @close="closePop"
      >
        <Swiper
          v-if="isShowSwiper"
          ref="swiperRef"
          :navigation="{ nextEl: '.swiper-button-next', prevEl: '.swiper-button-prev' }"
          :modules="[Navigation]"
          class="!mb-[94px]"
          @slideChange="changeSwiper"
        >
          <Swiper
            v-if="isShowSwiper"
            ref="swiperRef"
            :navigation="{ nextEl: '.swiper-button-next', prevEl: '.swiper-button-prev' }"
            :modules="[Navigation]"
            class="!mb-[94px]"
            @slideChange="changeSwiper"
          >
            <SwiperSlide v-for="(item,index) in qrCodeList" :key="index">
              <StudentOrDeainage
                page-type="pop"
                :type="type"
                :qr-code="item.qrCode"
                :content="item.content"
                :goods-title="item.goodsTitle"
                :lazy-img="item.img"
                :key-id="index"
              />
            </SwiperSlide>
            <template v-if="qrCodeList?.length > 1">
              <!-- 内置按钮 -->
              <div class="swiper-button-prev">
                <KKCIcon name="icon-wode-youjiantou" :size="32" class="prev-icon" :color="currentIndex === 0 ? '#ccc':'#111'" />
              </div>
              <div class="swiper-button-next">
                <KKCIcon name="icon-wode-youjiantou" :size="32" :color="currentIndex === qrCodeList?.length - 1 ? '#ccc':'#111'" />
              </div>
            </template>
          </Swiper>
        </swiper>
      </KKPopup>
    </Teleport>
  </div>
</template>
<script lang="ts" setup>
import { Navigation } from 'swiper'
import { Swiper, SwiperSlide, } from 'swiper/vue'
import StudentImg from '@/assets/images/common/student1.png'
import DrainageImg from '@/assets/images/common/drainage1.png'
import { KKPopup } from '@/components/KKPopup/types'
import type { QrList } from '~/apis/course/types'
const props = defineProps<{
  type: 'student' | 'drainage' | undefined,
  qrCodeList: QrList[],
  content?:string,
}>()
const title = computed(() => {
  const numberContent = props.qrCodeList?.length > 1 ? `(${currentIndex.value + 1}/${props.qrCodeList?.length})` : ''
  return (props.type === 'student' ? '添加学管师' : '进入专属社群') + numberContent
})
const swiperRef = ref(null)
const popupStudentRef = ref<InstanceType<typeof KKPopup>>()
const isShowSwiper = ref(false)
const showPopbyType = () => {
  popupStudentRef.value?.showPopup()
  isShowSwiper.value = true
}
const currentIndex = ref(0)
const changeSwiper = (val:any) => {
  currentIndex.value = val.realIndex
}
const closePop = () => {
  currentIndex.value = 0
  isShowSwiper.value = false
}
</script>
<style lang="scss">
.student-drainage-pop{

  .swiper-button-prev,
  .swiper-button-next {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background-size: cover;
    z-index: 10;
    width: 52px;
    height: 112px;
    border: 1px solid #ccc;
    text-align: center;
    line-height: 112px;

    background-color: #fff;
  }
  .swiper-button-prev{
    left: 0;
    border-left: none;
    border-radius: 0 26px 26px 0;
    .prev-icon{
      transform: rotate(180deg);
    }
  }

  .swiper-button-next {
    border-radius: 26px 0 0 26px;
    right: 0;
    border-right: none;
  }

}
</style>
