<template>
  <div>
    <div v-if="classifyList.length" class="flex items-center justify-between">
      <div v-if="showCateType === 1" class="classify-wrap h-[58px]">
        <div class="flex items-center" @click="handleClassify">
          <div v-if="checkedClass" class="classify-wrap-item" :class="[textClass]">
            {{ checkedClass.name }}
          </div>
          <KKCIcon
            v-if="defaultChecked && checkedClass"
            name="icon-bofang"
            :size="20"
            color="#333"
            class="rotate-90"
          />
        </div>
      </div>
      <div v-else class="relative w-[600px] h-[88px]">
        <KKTabsWap
          ref="tabsRef"
          class="classify-nav-tabs w-[600px]"
          :value="defaultChecked"
          show-line
          :line-width="24"
          :line-height="4"
          :active-color="'var(--kkc-brand-text)'"
        >
          <KKTabItemWap
            v-for="item in classifyList"
            :key="item.studyLevel1"
            :name="item.studyLevel1"
          >
            <span
              :class="['tab', item.studyLevel1 === defaultChecked ? 'tab-active' : '']"
              replace
              @click="tabClick(item.studyLevel1, item.name)"
            >
              {{ item.name }}
            </span>
          </KKTabItemWap>
        </KKTabsWap>
        <div class="classify-nav-btn-area">
          <KKCIcon name="icon-gengduo" :size="32" color="#333" @click="handleClassify" />
        </div>
      </div>
      <slot name="right">
        <NuxtLink
          :to="{
            path: '/question-bank/search',
            query: {
              studyLevel1: defaultChecked,
              ...selectTags,
            },
          }"
          title="点击搜索"
          class="mt-[4px]"
          @click="searchClick"
        >
          <KKCIcon name="icon-sousuo1" :size="33" color="#333" />
        </NuxtLink>
      </slot>
    </div>
    <div class="flex items-center justify-between">
      <slot name="bottom" />
      <img
        v-if="!isHideCapsule"
        class="w-[160px] h-[54px]"
        :src="CapsuleIcon"
        alt="capsule"
        @click.stop="handleCapsuleClick"
      >
    </div>
    <ClassSingle
      :is-show="isShow"
      :list="classifyList"
      :checked="defaultChecked"
      @change="handleChange"
      @close="handleClassify"
    />
    <ClassCapsule
      :is-show="isShowCapsule"
      :list="dataList"
      :show-num-type="showNumType"
      @close="handleCapsuleClick"
    />
  </div>
</template>

<script setup lang="ts">
import CapsuleIcon from '../assets/images/question-bank/capsule_bg.png'
import ClassSingle from '~/pages/question-bank/components/choosePopup/ClassSingle.vue'
import ClassCapsule from '~/pages/question-bank/components/choosePopup/ClassCapsule.vue'
import type { ClassBasicType, SelectParams } from '~/pages/question-bank/types/basic'
import { useQuestionSearchStore } from '~/stores/question.search.store'
import { useUserStore } from '~/stores/user.store'
const questionSearchStore = useQuestionSearchStore()
const { track } = useTrackEvent()
const userStore = useUserStore()
const props = withDefaults(
  defineProps<{
    classifyList: ClassBasicType[];
    defaultChecked: number;
    isHideCapsule: boolean;
    selectTags: SelectParams;
    showCateType: 1 | 2;
    showNumType?: number;
    dataList?: any[];
    pageName?: string;
    textClass?: string;
  }>(),
  {
    classifyList: () => [],
    showCateType: 1,
    showNumType: 5,
    dataList: () => [],
    pageName: '',
    textClass: ''
  }
)
const emit = defineEmits(['change'])
// 选中分类
const checkedClass = computed(() => {
  return props.classifyList.find(item => item.studyLevel1 === props.defaultChecked)
})

const searchClick = () => {
  questionSearchStore.setKeyword('')
  questionSearchStore.setActive(1)
  track({
    category: '题库',
    action: '搜索入口',
  })
}

/**
 * 选项点击
 * @param studyLevel1
 */
const tabClick = (studyLevel1: number, name: string) => {
  if (studyLevel1 === props.defaultChecked) {
    return false
  }
  track({
    category: '题库',
    action: '切换专业分类',
    label: name,
  })
  emit('change', studyLevel1)
}

const handleChange = (val: number, name: string) => {
  emit('change', val, name)
  handleClassify()
}
// 分类切换
const isShow = ref(false)
const handleClassify = () => {
  isShow.value = !isShow.value
  if (isShow.value) {
    track({
      category: '题库',
      action: '专业分类弹窗',
      label: props.pageName,
    })
  }
}
// 百宝箱
const isShowCapsule = ref(false)
const handleCapsuleClick = () => {
  if (userStore.isLoginFn(history.state.current)) {
    isShowCapsule.value = !isShowCapsule.value
  }
  if (!props.isHideCapsule && isShowCapsule.value) {
    track({
      category: '题库',
      action: '常用功能百宝箱',
    })
  }
}
</script>

<style lang="scss" scoped>
.classify {
  &-wrap {
    @apply flex items-center justify-between;

    &-item {
      @apply pr-[12px] text-[24px] text-[#666] font-[400];

      &:nth-of-type(1) {
        @apply text-[36px] text-[#333] font-[500];
      }
    }
  }

  &-nav {
    position: relative;
    height: 88px;

    &-tabs {
      line-height: 88px;

      &::-webkit-scrollbar {
        display: none;
      }

      .tab {
        // margin: 0 24px;
        font-weight: 400;
        font-size: 30px;
        color: #666666;
      }

      .tab-active {
        @apply text-brand text-[34px] font-[600];
      }
    }

    &-btn-area {
      position: absolute;
      right: -42px;
      top: 22px;
      z-index: 9;
    }
  }
}
</style>
