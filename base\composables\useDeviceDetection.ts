// composables/useDeviceDetection.ts

export default function useDeviceDetection () {
  // 服务端直接返回默认值
  if (process.server) {
    return {
      isMobile: false,
      os: 'Unknown'
    }
  }

  const userAgent = navigator.userAgent
  const platform = navigator.platform

  // 移动端检测（优先处理）
  const isAndroid = /Android/i.test(userAgent)
  const isiPhone = /iPhone|iPod/i.test(userAgent)
  const isiPad = /iPad/i.test(userAgent) || (platform === 'MacIntel' && navigator.maxTouchPoints > 1)

  // 操作系统判断
  let os = 'Unknown'
  let isMobile = false
  let usedId = null

  if (isAndroid) {
    os = 'android'
    isMobile = true
    usedId = 300
  } else if (isiPhone) {
    os = 'iphone'
    isMobile = true
    usedId = 400
  } else if (isiPad) {
    os = 'iPad'
    isMobile = true
    usedId = 600
  } else if (/Windows/i.test(userAgent)) {
    os = 'PC'
    usedId = 100
  } else if (/Macintosh|Mac OS X/i.test(userAgent)) {
    os = 'PC'
    usedId = 100
    // 兼容旧版 iPad 判断
    if (platform === 'MacIntel' && navigator.maxTouchPoints > 1) {
      os = 'iPad'
      isMobile = true
      usedId = 600
    }
  } else if (/Linux/i.test(userAgent)) {
    os = 'Linux'
  } else if (/harmonyos[/\s](\d+(\.\d+)*)/i.test(userAgent) || /(?:OpenHarmony)/.test(userAgent)) {
    os = 'Harmony OS'
    usedId = 800
    if (/(?:OpenHarmony)/.test(userAgent)) {
      isMobile = true
    }
  } else {
    os = '未知'
    usedId = 0
  }

  return {
    isMobile,
    os,
    usedId
  }
}
