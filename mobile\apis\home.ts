import { hash } from 'ohash'
import type { GetAdvertisementPhotoListRequestData, GetAdvertisementPhotoListResponseData } from './types'
export enum ApisHome {
  getReleaseInfoForWx = '/kukemarketing/wap/pageMaster/getReleaseInfoForWx',
  getAssemblyForWx = '/kukemarketing/wap/pageMaster/getAssemblyForWx',
  getReleasePersonCenterORNavigationDetails = '/kukemarketing/wap/pageMaster/getReleasePersonCenterORNavigationDetails',
  getCustomPageReleaseDetails = '/kukemarketing/wap/pageMaster/getCustomPageReleaseDetails',
  getCustomPageNavigationStatus = '/kukemarketing/wap/pageMaster/getCustomPageNavigationStatus'
}

export const apiGetHome = (body = {}) => useHttp(ApisHome.getReleaseInfoForWx, {
  // server: false,
  key: ApisHome.getReleaseInfoForWx + body?.pageMasterId + body?.productSide,
  body,
  transform: ({ data }) => {
    const { getSingleAssemblyInfoVOList = [], ...o } = data || {}
    return {
      ...o,
      components: getSingleAssemblyInfoVOList?.filter(Boolean)
    }
  },
  default () {
    return {
      components: []
    }
  }
})

export function getDefaultOfficialRecommend (body) {
  return useHttp<any>('/kukecoregoods/wap/index/officialRecommend', {
    body,
    transform: res => res.data,
  })
}

type IResult = {
  propName?: string,
  props: object,
}
const clearObject = (obj, custom = false) => {
  const result:IResult = {
    // propName: undefined,
    props: {},
  }
  for (const i in obj) {
    if (typeof obj[i] === 'object' && obj[i] !== null) {
      if (custom) {
        obj[i]._showTitle = obj[i].showTitle
        obj[i].showTitle = 0
        obj[i].seeMore = 0
        if (obj[i].homePageShowNum) {
          obj[i].homePageShowNum = 50
        }
      }
      result.propName = i
      result.props[`${i}`] = obj[i]
      break
    } else {
      // delete obj[i]
    }
  }
  return result
}
interface Ibody {
  pageMasterId:string
  assemblyType:number
  homeAssemblyId?:string
  sortKey?:number // 排序字段: 1.销量 2.价格 3.开课时间
  sortValue?:number // 方式：1.升序 2.降序
}
//
export async function getAssemblyForWx (body: Ibody) {
  const { pageMasterId, assemblyType, homeAssemblyId, sortKey, sortValue } = body
  return useHttp<{getSingleAssemblyInfoVOList:any[]}, any[], any[]>(ApisHome.getAssemblyForWx, {
    key: ApisHome.getAssemblyForWx + pageMasterId + assemblyType + homeAssemblyId + sortKey + sortValue,
    body,
    transform: (res) => {
      const { getSingleAssemblyInfoVOList = [], bothSideSpace = 12, componentSpace = 12 } = res.data || {}
      return getSingleAssemblyInfoVOList.map((v) => {
        const { props, propName } = clearObject(v.props, true)
        v.props = props
        v.propName = propName
        v.bothSideSpace = bothSideSpace
        v.componentSpace = componentSpace
        return v
      })
    },
    default () {
      return []
    }
  })
}
interface IHeaderORFooterORTabbarOrMeBody {
  /**
  首页 100
  个人中心 101
  导航 102
  商品详情页 103
  店铺风格 104
  自定义页面 105
  */
  pageId: '100' | '101' | '102' | '103' | '104' | '105'
  /**
  0 默认
  1 底部导航-移动端
  2 顶部导航-移动端
  3 顶部导航PC
  4 页脚设置PC
  */
  pageTag: 0 | 1 | 2 | 3 | 4,
  productSide?: 1 | 5 | 7 | 9,
}

export async function getReleasePersonCenterORNavigationDetails (body: IHeaderORFooterORTabbarOrMeBody) {
  const key = ApisHome.getReleasePersonCenterORNavigationDetails + body.pageId + body.pageTag
  return useHttp<{getSingleAssemblyInfoVOList:any[]}, any, any >(ApisHome.getReleasePersonCenterORNavigationDetails, {
    body,
    key,
    transform: (res) => {
      const { getSingleAssemblyInfoVOList = [], ...o } = res.data || {}
      const components = getSingleAssemblyInfoVOList?.filter(Boolean).map((v) => {
        const { props, propName } = clearObject(v.props)
        v.props = props
        v.propName = propName
        return v
      })
      return {
        components,
        ...o
      }
    },
    default () {
      return {
        components: []
      }
    }
  })
}
// 自定义页面列表
export async function getCustomPageReleaseDetails (body:any) {
  return useHttp<any>(ApisHome.getCustomPageReleaseDetails, {
    key: ApisHome.getCustomPageReleaseDetails + body?.customPageId,
    method: 'post',
    body,
    isShowLoading: true,
    transform: (res) => {
      const { getSingleAssemblyInfoVOList = [], ...o } = res.data || {}
      const components = getSingleAssemblyInfoVOList?.filter(Boolean).map((v) => {
        const { props, propName } = clearObject(v.props)
        v.props = props
        v.propName = propName
        return v
      })
      return {
        components,
        ...o
      }
    },
    default () {
      return {
        components: []
      }
    }
  })
}
// 自定义页面是否配置导航
interface CustomBody{
  customPageId:string
}
interface CustomeRes{
  isTopNavigationPc?:number
  isBottomNavigationApp?:number
  isFooterSettingPc?:number
}
export async function getCustomPageNavigationStatus (body:CustomBody) {
  return useHttp<CustomeRes>(ApisHome.getCustomPageNavigationStatus, {
    body,
    transform: res => res.data,
  })
}
export async function getLaunchAd (body:{id:number}) {
  return useHttp<any>('/kukemarketing/wap/pageMaster/getStartupAdvertisingListWX', {
    body,
    transform: res => res.data,
  })
}

interface PopupAd {
  productSide:number,
  pageId:number | string,
  popupNumber:number,
  pageMasterId?: string,
  cateIds?: string,
  regionIds?: string,
  academicSectionIds?: string,
  subjectIds?: string,
  examFormatIds?: string,
  directionIds?: string,
}
export async function getPopUpAd (body: PopupAd) {
  return useHttp<any>('/kukemarketing/wap/pageMaster/getPagePopUpWindowsAdvertisingListWX', {
    body,
    transform: res => res.data,
  })
}
//
/**
 * 公共广告列表
 * @param body
 * @returns
*/
export async function getAdvertisementPhotoList (body:GetAdvertisementPhotoListRequestData = {}) {
  const _api = '/kukemarketing/wap/pageMaster/getAdvertisementPhotoList'
  const key = hash({
    ...body,
    _api
  })
  return useHttp<{list:GetAdvertisementPhotoListResponseData[]}>(_api, {
    key,
    body,
    transform: res => res.data,
  })
}
/**
 * 合并接口
 * http://yapi.kukewang.com:9005/project/273/interface/api/66946
 * @param body
 * @returns
*/
export async function getKukeMarketingInfo (body:any = {}) {
  const _api = '/kukemarketing/wap/pageMaster/getCombineDetails'
  const key = hash([_api, ...Object.values(body)])
  return useHttp<any>(_api, {
    key,
    body,
    transform: res => res.data,
  })
}
/**
 * 获取产品线信息以及第三方详情
 * http://yapi.kukewang.com:9005/project/273/interface/api/66946
 * @param body
 * @returns
*/
export async function queryProductDetailThirdList (body:any = {}) {
  const _api = '/kukebasesystem/index/product/queryProductDetailThirdList'
  const key = hash([_api, ...Object.values(body)])
  return useHttp<any>(_api, {
    key,
    body,
    transform: res => res.data,
  })
}
/**
 * 进入课程详情后，移除红点标记
 * http://yapi.kukewang.com:9005/project/69/interface/api/69543
 * @returns
 */
export async function removeUpdateRecordProt (body:any = {}) {
  return useHttp<any>('/kukecoregoods/wap/userCourseUpdate/removeUpdateRecordProt', {
    method: 'post',
    body,
    transform: res => res.data,
    default: () => ({}),
  })
}
/**
 * 用户是否有未读课程更新
 * http://yapi.kukewang.com:9005/project/69/interface/api/69543
 * @returns
 */
export async function getCourseUpdateStatus (body:any = {}) {
  return useHttp<any>('/kukecoregoods/wap/userCourseUpdate/getStatusProt', {
    method: 'post',
    body,
    transform: res => res.data,
    default: () => ({}),
  })
}

/**
 * 获取用户学习目标和用户信息
 * http://yapi.kukewang.com:9005/project/69/interface/api/66898
 * @returns
 */
export async function userInfoProt () {
  return useHttp<any>('/kukeuser/wap/apiMerge/userInfoProt', {
    method: 'post',
    // transform: (input) => {
    //   return input.data || {}
    // },
    default: () => ({}),
  })
}
/**
 * 通过查询id以及指定的参数获取规则信息
 * @returns
 */
export async function getRuleListByIdParams (body: any = {}) {
  return useHttp<any>('/kukecallcenter/chatRule/getRuleListByIdParams', {
    method: 'post',
    body,
    transform: res => res.data,
    default: () => ({}),
  })
}
