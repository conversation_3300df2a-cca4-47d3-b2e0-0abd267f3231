//  转换时间格式为时分秒
export const useAudioDetail = () => {
  const formatTime = (seconds:number) => {
    const hours = Math.floor(seconds / 3600) // 计算小时
    const minutes = Math.floor((seconds % 3600) / 60) // 计算分钟
    const secs = seconds % 60 // 计算剩余的秒数

    let timeString = ''
    if (hours > 0) {
      timeString += `${hours}:` // 如果小时大于 0，显示小时
    }
    timeString += `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}` // 始终显示分钟和秒数
    return timeString
  }
  return {
    formatTime
  }
}
