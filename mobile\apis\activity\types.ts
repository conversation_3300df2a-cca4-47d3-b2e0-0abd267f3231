import { MarketingActivityTypeEnum } from '@/apis/cart/types'

export interface UseableCoupon {
  activityId: string
  activityType: number
  goodsSpecificationItemId?: string
  goodsMasterId: string
}

export interface ReceiveCoupon {
  title?: string
  content?: string
  status: number
}

export interface QueryCouponAmount {
  goodsMasterId: string
  goodsSpecificationItemId: string
  activityType: number
  activityId: string
  couponId: string
}

export interface QueryMyCoupon {
  couponStatus: number
  page: number
  pageSize: number
}

export interface QuerySeckillList {
  page: number
  pageSize: number
  choiceType: number
  pageMasterId: string
  seckillType?: string
  seckillTimeBucket?: string
}

export interface CouponCanReceive {
  goodsMasterId: string
  goodsSpecificationItemId?: string
}

export interface BuyGiveGoods {
  goodsMasterId?: string
  specificationItemId?: string
  goodsTitle?: string
  goodsPresentPrice?: string
  goodsImg: string
  activityPrice?: string
  goodsNum?: string
  goodsPrice?:number
  deliveryMethod?:number
}

export interface BuyGiveGoodsResData{
  goods: BuyGiveGoods[]
  coupons: any[]
  giveGoodsCount:number
  giveCouponCount:number
  buyGiveId:string | undefined
}
export interface BuyGiveGoodsRes {
  goods: BuyGiveGoods[]
  coupons: any[]
  giveGoodsCount:number
  giveCouponCount:number
  buyGiveId:string | undefined
}

/**
 * 请求购买加价购商品列表参数
 */
export interface FetchBuyAtMarkupGoodsListParams {
  /**
   * 组织ID
   */
  orgId: string;

  /**
   * 优惠券ID
   */
  couponId?:string;

  /**
   * 购买加价购商品列表
   */
  goodsList:{
    /**
     * 主商品ID
     */
    goodsMasterId: string; // 必须，主商品ID

    /**
     * 规格商品ID
     */
    specificationItemId?: string; // 非必须，规格商品ID

    /**
     * 商品数量
     */
    quantity:number;
  } [];
}

/**
 * 请求购买加价购商品列表DTO
 */
export interface FetchBuyAtMarkupGoodsListDTO {
  /**
   * 商品图片
   */
  goodsImg: string; // 必须，商品图片

  /**
   * 商品原价
   */
  goodsPrice: number; // 必须，商品原价

  /**
   * 标题
   */
  goodsTitle: string; // 必须，标题

  /**
   * 换购价
   */
  raisePrice: number; // 必须，换购价

  /**
   * 现价
   */
  goodsPresentPrice: number; // 必须，现价

  /**
   * 子规格商品ID
   */
  specificationItemId?: string; // 非必须，子规格商品ID

  /**
   * 换购商品ID
   */
  goodsMasterId: string; // 必须，换购商品ID

  /**
   * 加价购活动id
   */
  buyAtMarkupId:string // 必须 加价购活动id
}

/**
 * 请求购买买赠商品列表参数
 */
export interface FetchBuyGiveProductListParams {
  /**
   * 主商品ID
   */
  goodsMasterId: string; // 必须，主商品ID

  /**
   * 规格商品ID
   */
  specificationItemId?: string; // 非必须，规格商品ID
}

/**
 * 买赠商品DTO
 */
export interface BuyGiveGoodsDTO {
  goodsMasterId: string; // 必须，商品主表id
  specificationItemId?: string; // 非必须，子规格商品ID
  goodsTitle: string; // 必须，商品标题
  goodsImg: string; // 必须，商品图片
  goodsNum: number; // 必须，商品数量
}

/**
 * 买赠产品DTO
 */
export interface FetchBuyGiveProductListDTO {
  goodsList: BuyGiveGoodsDTO[]; // 必须，商品列表
  couponList: object[]; // 必须，优惠券列表
  buyGiveId: string; // 必须，买赠活动ID
  giveGoodsCount: number; // 必须，赠送商品可选择个数
  giveCouponCount: number; // 必须，赠送优惠券可选择个数
}

/**
 * 获取商品可用优惠券列表goods参数
 */
export interface FetchCouponGoodsParams {
  goodsMasterId: string; // 商品主 id，必须
  specificationItemId?: string; // 子规格 id，非必须
  quantity: number; // 数量，必须
}

/**
 * 获取商品可用优惠券详情传参
 */
export interface FetchGetGoodsDiscountsParams {
  orgId: string; // 组织id，必须
  goods: FetchCouponGoodsParams[] // 商品信息;
}

/**
 * 营销活动优惠对象
 */
export interface IActivityDiscount {
  goodsImg?: string[]; // 非必须，商品图片地址集合
  activityText: string[]; // 必须，优惠小计集合
  activityType: MarketingActivityTypeEnum; // 必须，营销活动类型
  activityDiscountAmount: number; // 必须，活动优惠金额
}

/**
 * 优惠券信息
 */
export interface ICouponDiscount {
  discountContent: string; // 必须，优惠内容
  goodsImg?: string[]; // 非必须，商品图片集合
  couponText: string; // 必须，优惠小计金额
  couponId: string; // 必须，优惠券ID
  discountAmount: number; // 必须，优惠券优惠金额
  selected: boolean; // 必须，是否选中
  getSource:number;// 领取来源（1优惠券 2大礼包 3买赠 4推广员）
  userCouponId:string;
}

/**
 * 获取商品可用优惠详情DTO
 */
export interface FetchGetGoodsDiscountDTO {
  activityDiscount?: IActivityDiscount; // 非必须，营销活动优惠
  couponList?: ICouponDiscount[]; // 非必须，优惠券信息
}
/**
 * 优惠券展示文本
 */
export interface CouponText{
  /**
  * 折扣金额/折
  * - 当优惠券类型为 2（折扣券）时，表示折扣金额。
  * - 类型：`number`
  */
  discountAmount: number;

  /**
   * 优惠券类型
   * - 1: 满减券
   * - 2: 折扣券
   * - 类型：`number`
   */
  couponType: number;
}

/**
 * 查询有价优惠券活动列表参数
 */
export interface PirceCouponListParam {

  /**
   * 页码
   */
  page?: number
  /**
   * 每页条数
   */
  pageSize?: number
}

/**
 * 查询有价优惠券活动列表返回数据单条
 */
export interface PirceCouponListOne {

  /**
   * 活动ID
   */
  id: number

  /**
   * 原价
   */
  goodsPrice: number

  /**
   * 现价
   */
  goodsPresentPrice: number

  /**
   * PC营销图
   */
  pcMarketingImageUrl: string

  /**
   * wap营销图
   */
  wapMarketingImageUrl: string

  /**
   * 包含的优惠券数量
   */
  couponSum: number

  /**
   * 优惠券展示文本
   */
  couponText: CouponText[]
}

/**
 * 查询有价优惠券活动列表返回数据
 */
export interface PirceCouponListData{

  /**
   * 总数量
   */
  count: number

  /**
   * 列表数据
   */
  list: PirceCouponListOne[]
}

/**
 * 查询有价优惠券活动详情参数
 */
export interface PirceCouponDetailParam{

  /**
   * 活动ID
   */
  id: string
}
/**
 * 优惠券数据接口
 */
export interface PriceCouponOne {
  /**
   * 优惠券ID
   */
  id: string;

  /**
   * 优惠券名称
   */
  couponName: string;

  /**
   * 优惠券类型；
   * 1:满减券，
   * 2:折扣券
   */
  couponType: number;

  /**
   * 满减抵扣金额，
   * 仅优惠券类型为1时使用
   */
  rewardAmount?: number;

  /**
   * 折扣值，
   * 仅优惠券类型为2时使用
   */
  discountRate?: number;

  /**
   * 最多折扣金额，
   * 仅优惠券类型为2时使用
   */
  discountAmount?: number;

  /**
   * 使用限制，满减条件阈值；
   * 0表示无条件，
   * 其他值为满减值
   */
  couponThreshold?: number;

  /**
   * 过期类型；
   * 1:固定时间,
   * 2:领取后
   */
  expiredType: number;

  /**
   * 固定有效期开始时间，
   * 仅过期类型为1时使用
   */
  expiredStart?: string;

  /**
   * 固定有效期结束时间，
   * 仅过期类型为1时使用
   */
  expiredEnd?: string;

  /**
   * 领取后有效期（过期天数），
   * 仅过期类型为2时使用
   */
  expiredDays?: number;

  /**
   * 优惠券使用说明
   */
  couponDesc: string;

  /**
   * 发行数量
   */
  couponCount?: number;

  /**
   * 追加发行量
   */
  additionalCount?: number;

  /**
   * 余量预警是否开启；
   * 1:是,
   * 0:否
   */
  allowanceWarning?: number;

  /**
   * 余量预警数量
   */
  warningCount?: number;

  /**
   * 发放结束时间
   */
  publishEnd?: string;

  /**
   * 发放开始时间
   */
  publishStart?: string;

  /**
   * 推广设置；
   * 1:商品详情页,
   * 2:链接, 3:海报，
   * 多选用逗号隔开
   */
  popularizeSetting?: string;

  /**
   * 领取人限制；
   * 1:全部用户可领,
   * 2:指定用户可领,
   * 3:导入用户可领
   */
  receiverSetting?: number;

  /**
   * 导入领取人文件名称，
   * 仅领取人限制为3时使用
   */
  couponUserName?: string;

  /**
   * 优惠券领取人导入文件路径
   */
  couponUserUrl?: string;

  /**
   * 单人领取设置；
   * 1:无限制,
   * 2:限制个数
   */
  receiveType?: number;

  /**
   * 单人领取个数，
   * 仅领取设置为2时使用
   */
  receiveCount?: number;

  /**
   * 余量
   */
  allowanceCount?: number;

  /**
   * 优惠叠加设置；
   * 0:不能叠加,
   * 1:可与其他活动叠加,
   * 2:优惠券可叠加，
   * 多选用逗号隔开
   */
  superpositionSetting?: string;

  /**
   * 商品设置；
   * 1:全部商品可用,
   * 2:部分类型可用,
   * 3:部分类型不可用,
   * 4:部分商品可用,
   * 5:部分商品不可用
   */
  goodsSetting?: number;

  /**
   * 商品设置文字说明
   */
  goodsSettingName?: string;

  /**
   * 产品线
   */
  productId?: number;

  /**
   * 模板是否启用状态；
   * 1:启用,
   * 0:禁用
   */
  templateStatus?: number;

  /**
   * 活动状态；
   * 1:启用,
   * 0:禁用
   */
  activityStatus?: number;

  /**
   * 创建时间
   */
  createdAt?: string;

  /**
   * 创建用户（
   * 默认0未知，
   * 非0为操作用户ID）
   */
  createAdminId?: string;

  /**
   * 更新时间
   */
  updatedAt?: string;

  /**
   * 更新用户
   * （默认0未知，
   * 非0为操作用户ID）
   */
  updateAdminId?: string;

  /**
   * 删除时间（
   * 默认1为未删除，
   * 时间戳为删除时间
   */
  deletedAt?: number;

  /**
   * 优惠券类型为2或3时的提示文案
  */
  categoryName?: string;
}

/**
 * 查询有价优惠券活动详情
 */
export interface PirceCouponDetailData{

  /**
   * 活动名称
   */
  activityName: string;

  /**
   * 活动说明
   */
  activityExplain: string;

  /**
   * 现价
   */
  goodsPresentPrice: number;

  /**
   *  原价
   */
  goodsPrice: number;

  /**
   * 活动ID
   */
  id: string;

  /**
   * PC营销图
  */
  pcMarketingImageUrl: string;

  /**
   *  WAP营销图
   */
  wapMarketingImageUrl: string;

  /**
   * 优惠券列表
   */
  coupons: PriceCouponOne[];
}

/**
 * 应付金额
 */
export interface PirceCouponShouldPay{

  /**
   * 应付金额
   */
  shouldPay: string

}

/**
 * 优惠券下单参数
 */
export interface PirceCouponOrderParam{

  /**
   * 优惠券活动id
   */
  activityId: string

  /**
   * 应付金额
   */
  payInfo: PirceCouponShouldPay

}

/**
 * 优惠券下单返回数据
 */
export interface PirceCouponOrderData{

  /**
   * 订单编号
   */
  orderSn: string

  /**
   * 订单id
   */
  orderId: string

}

/**
 * 有价优惠券订单退款校验接口参数
 */
export interface refundVerifyParam{

  /**
   * 订单编号
   */
  orderSn: string

  /**
   * 用户ID
   */
  userId?: string

}

/**
 * 有价优惠券订单退款校验接口返回
 */
export interface refundVerifyData{

  /**
   * 是否可以退款
   */
  refundable: boolean

  /**
   * 可以退款的金额
   */
  refundedAmount: number

}
