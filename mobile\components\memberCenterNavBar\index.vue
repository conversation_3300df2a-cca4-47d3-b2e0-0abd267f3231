<template>
  <div class="member-nav-bar">
    <!-- 导航栏容器 -->
    <div
      class="nav-container h-[80px] flex justify-center items-center w-[7.5rem]"
      :class="[
        props.isScrolled
          ? 'bg-white shadow-sm fixed z-[99] top-0 transition-all duration-300 member-nav__fixed'
          : 'bg-transparent',
        route.name === 'course-id' ? '!z-[101]' : '',
      ]"
    >
      <!-- 返回按钮 -->
      <KKCIcon
        v-if="(showBack && !isXcx && !isCrm) || awayShowBack"
        name="icon-com_return"
        :color="computedBackIconColor"
        :size="48"
        class="absolute left-[32px] member-nav-bar__back"
        @click="handleBack"
      />

      <!-- 标题插槽 -->
      <slot name="title">
        <h4
          class="max-w-[600px] text-[36px] font-medium leading-[52px] truncate my-title transition-colors duration-300"
          :class="props.isScrolled ? 'text-black' : 'text-brand'"
        >
          {{ title }}
        </h4>
      </slot>

      <!-- 右侧配置插槽 -->
      <div
        v-if="showConfig"
        class="absolute right-[32px] member-nav-bar__tips"
        :class="props.isScrolled ? 'text-black' : 'text-brand'"
      >
        <slot name="config">
          <div v-if="props.content" class="cursor-pointer text-[28px]" @click="showMenu">
            会员说明
          </div>
        </slot>
      </div>
    </div>

    <!-- 内容插槽 -->
    <slot />

    <!-- 关闭组件 -->
    <Close />
    <!-- 底部弹窗 -->
    <KKPopup
      ref="menuPopupRef"
      position="bottom"
      :height="800"
      :width="750"
      title="会员说明"
      class="home-header__popup"
      :closeable="true"
      box-padding="0px"
      @close="onMenuClose"
    >
      <!-- 内联原 AgreementContent 内容（移除接口获取，直接使用 props.content） -->
      <div
        class="ageree-inner h-[100%] w-[100%] overflow-x-hidden overscroll-x-none"
        v-html="props.content"
      />
    </KKPopup>
  </div>
</template>

<script setup lang="ts">
const { isXcx } = useAppConfig()
const route = useRoute()

// Props 定义
const props = withDefaults(
  defineProps<{
    title: string;
    customBack?: boolean;
    showBack?: boolean;
    awayShowBack?: boolean;
    backIconColor?: string;
    showConfig?: boolean;
    content?: string;
    isScrolled?: boolean; // 接收父组件传入的吸顶状态
  }>(),
  {
    customBack: false,
    showBack: true,
    awayShowBack: false,
    backIconColor: '',
    isScrolled: false,
  }
)

// Emits 定义
const emit = defineEmits<{
  (e: 'back'): void;
}>()

// 判断是否为CRM系统
const internalSystemId = isClient && sessionStorage.getItem('internalSystemId')
const isCrm = computed(
  () =>
    [26, 33].includes(Number(internalSystemId)) ||
    [26, 33].includes(Number(route.query.internalSystemId))
)

// 计算返回按钮颜色
const computedBackIconColor = computed(() => {
  if (props.backIconColor) {
    return props.backIconColor
  }
  // 未滚动时使用主题色，滚动后固定黑色
  return props.isScrolled ? '#000000' : 'var(--kkc-brand-text)'
})

// 返回处理函数
const handleBack = () => {
  const isLoginPage = route.name === 'login'
  const isReceivePage =
    route.fullPath.includes('/receive-page-h5/') || route.query['kk-system'] === '1'
  const isBrowserHistoryValid =
    window.history?.length > 2 && window.history?.state?.position > 0
  const isBrangin = route.fullPath.includes('/course/') && route.query.isBargain

  if (!props.customBack) {
    if (os?.isApp) {
      if (route.meta?.pageDeep) {
        if (os?.isAndroid) {
          // @ts-ignore
          window.android?.goBack?.()
        } else if (os?.isPhone || os?.isTablet) {
          // @ts-ignore
          window.webkit?.messageHandlers?.goBack?.postMessage?.(null)
        } else if (os?.isHarmonyOS) {
          // @ts-ignore
          window.harmony?.goBack?.()
        }
      } else {
        window.history.back()
      }
    } else if (sessionStorage.getItem('toLinkUrl')) {
      navigateTo('/')
      sessionStorage.removeItem('toLinkUrl')
    } else if (isBrowserHistoryValid || isLoginPage || isReceivePage || isBrangin) {
      window.history.back()
    } else {
      navigateTo('/')
    }
  } else {
    emit('back')
  }
}

// 原 AgreementContent 内联后使用 KKPopup 实例引用；缺少 PopupInstance 类型定义，使用 any 规避类型错误
const menuPopupRef = ref<any | null>(null)

/**
 * 显示菜单弹窗 [AI-GEN]
 */
const showMenu = () => {
  menuPopupRef.value?.showPopup()
}

/**
 * 关闭菜单弹窗后的回调 [AI-GEN]
 */
const onMenuClose = () => {}
</script>

<style lang="scss" scoped>
// .member-nav-bar {
//   .nav-container {
//     backdrop-filter: blur(10px);
//     -webkit-backdrop-filter: blur(10px);
//   }

//   .my-title {
//     color: var(--kkc-brand-text);
//   }
// }

/* 内联自 AgreementContent 的样式 */
.ageree-inner {
  padding: 0 32px 32px;
  word-break: break-all;
  .table,
  table,
  tbody {
    max-width: 100%;
    display: block;
  }
  th,
  td {
    border: 2px solid #bfbfbf;
    padding: 10px;
  }
  tr {
    width: 100%;
  }
  td {
    word-break: break-all;
  }
}
</style>
