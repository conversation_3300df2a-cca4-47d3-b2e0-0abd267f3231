import { createApp } from 'vue'
import { SetTextBookPopup } from '../pages/question-bank/components/index'

export const TextBookPopup = ({ list, confirm }: { list: any[], confirm: (item: any) => void }) => {
  if (process.client) {
    let AppInstance: any = null
    const container: HTMLDivElement = document.createElement('div')
    AppInstance = createApp(SetTextBookPopup, {
      show: true,
      list,
      close: () => {
        AppInstance && AppInstance.unmount()
        container && document.body.removeChild(container)
      },
      confirm
    })

    document.body.appendChild(container)
    AppInstance.mount(container)
  }
}
