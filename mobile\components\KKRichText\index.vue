<template>
  <div v-if="content" class="kk_rich_text_comp ck ck-editor" :style="{ height: richHeight, background: backgroundColor }">
    <div class="rich_text ck-content" v-html="content" />
  </div>
</template>

<script setup lang='ts'>
const richProps = defineProps({
  props: {
    type: Object,
    default: () => {}
  }
})
const { props } = toRefs(richProps)
const { richTextVO: { backgroundColor = '', content = '', showHigh = '' } } = props.value

const richHeight = computed(() => {
  return showHigh === 1 ? 'auto' : '1000px'
})
</script>

<style lang='scss'>
.kk_rich_text_comp {
  width: 7rem;
  margin: 0;
  margin: 36px  24px;
  overflow-y: auto;
  word-wrap:break-word;

  .rich_text {
    padding: 32px 24px;
  }

  .table,table,tbody{
    width: 100%;
    margin: 0 auto;
  }
  th,td{
    border: 1px solid #bfbfbf;
    padding: 10px;
  }
  td{
    word-break: break-all;
  }
  ul, ol, menu {
  list-style: revert;
  margin: revert;
  padding: revert;
}
}
</style>
