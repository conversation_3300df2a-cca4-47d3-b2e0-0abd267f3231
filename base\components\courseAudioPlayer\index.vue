<template>
  <div>
    <div v-if="!loadAudio" class="audio-buffer" :class="isPc&&'pc_audio'">
      <div v-if="isPc" class="audio-text mx-[12px]">
        {{ formatTime(lastTimePoint) }}
      </div>
      <div class="relative flex items-center">
        <input
          type="range"
          class="audio-buffer-process"
          :value="lastTimePoint"
          min="0"
          :max="audioDuration"
          step="1"
          disabled
        >
        <div class="inner-procecss" :style="{width:`${(lastTimePoint/audioDuration)*100}%`}" />
      </div>
      <div v-if="isPc" class="audio-text ml-[12px] mr-[12px]">
        {{ formatTime(audioDuration) }}
      </div>
      <div v-if="!isPc" class="flex items-center justify-between h-[44px]">
        <div class="audio-text">
          {{ formatTime(lastTimePoint) }}
        </div>
        <div class="audio-text ml-[12px]">
          {{ formatTime(audioDuration) }}
        </div>
      </div>
    </div>
    <AudioCom
      v-if="loadAudio"
      :id="audioId"
      ref="audioCom"
      v-bind="{...props}"
      @handleChanged="emits('handleChanged')"
      @playEnd="emits('playEnd')"
      @pauseFun="pauseFun"
    />
  </div>
</template>
<script setup lang="ts">
import { buildUUID } from '../../utils/uuid'
import AudioCom from './audioCom.vue'
import { useAudioDetail } from './useAudioDetail'
const { formatTime } = useAudioDetail()
const { isPc } = useAppConfig()
const audioId = computed(() => 'audio_' + buildUUID())
// 音频信息
const props = withDefaults(
  defineProps<{
    // 音频资源
    url: string;
    nodeId: string,
    goodsMasterId: string,
    tryListenId?: string,
    learnProgress?: number,
    lastTimePoint?: number,
    audioDuration?: number,
    loadAudio?: boolean,
    isNoLoginSeeCourseNode?: boolean,
  }>(),
  {
    learnProgress: 0,
    lastTimePoint: 0,
    audioDuration: 0,
    loadAudio: false
  }
)
const isPause = ref(true)
const pauseFun = (v:boolean) => {
  isPause.value = v
}
const emits = defineEmits<{
  (e: 'handleChanged'): void;
  (e: 'playEnd'): void;
}>()
const audioCom = ref<InstanceType<typeof AudioCom>>()
watch(() => props.loadAudio, () => {
  nextTick(() => {
    console.log(audioCom.value)
    // expose.value.isPause = false
    expose.value = defineExposeInfo(audioCom)
    expose.value.isPause = isPause
    expose.value.pause = pausePlay
  })
}, {
  deep: true
})

const pausePlay = () => {
  audioCom.value?.pause()
}
const expose = ref({
  onClickBtn: () => {},
  isPause,
  pause: pausePlay,
  isAudioEnd: false,
})
const defineExposeInfo = (aRef:any) => {
  aRef.value.onClickBtn()
  return aRef
}
defineExpose({ expose })
</script>
<style lang="scss" scoped>
.audio-buffer{
  @apply h-[34px] pr-[24px] mt-[6px] w-[100%] rounded-[6px];
  .audio-buffer-process {
    @apply w-full h-[4px] bg-[#dddddd] border-none rounded-[8px] cursor-pointer relative;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;

    &:focus {
      outline: none;
    }
    &::-webkit-slider-thumb {
      @apply w-[10px] h-[10px] bg-[#ffffff] rounded-[50%] cursor-pointer relative z-20 relative top-[-1px];
      box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.2);
      outline: none;
      -webkit-appearance: none;
      -moz-appearance: none;
      appearance: none;
    }
  }
  .inner-procecss{
    @apply h-[4px] bg-[#36D281] absolute pointer-events-none;
    border-radius: 8px 4px 4px 8px;
  }
}
.pc_audio{
  @apply flex items-center justify-between w-[100%];
  .audio-buffer-process{
    width:240px
  }
}
</style>
