import { emitter } from '../../base/utils/mitt'
import type { Item, SKUItem, Specs, DefaultSku, Key, Sku, ResultSku, valueItem, AllSku, SpecAttrs, } from '../pages/course/types'
import type { SpecificationItem, CourseDetailModel } from '@/apis/course/types'
import { useCourseStore } from '~/stores/course.store'

// TODO 内存泄漏可疑点: 全局变量
const { courseApi } = useApi()
// 用于显示的规格名规格值数据  [{name: xx, values: [name1,name2,...],itemIndex: x},...]
const key = ref<Key[] | []>([])
// 获取所有的 sku 组合的价格和库存数据  { 'XX;XX;XX;': {price:xx, count: xx}... }
const sku = ref<Sku>({})
// 计算出所有 sku/spu 组合的价格库存数据 { 'XX;XX;XX;': {price:xx, count: xx}... }
const resultSku = ref<ResultSku>({})
// 备份的属性集
const specsArr = ref<Specs[]>()

const isGetAllSku = ref(false)
export default function useSKU () {
  const courseStore = useCourseStore()

  /**
   * @name: 初始化 SKU 组合数据
   * @param {Specs[]}: specs [{id: xxx, name: xxx, value: [{id: xx, name:xxx}, ...]},...]
   * @param {string}: goodsMasterId 商品 id
   * @param {DefaultSku[]}: defaultSku [{name: xx, value: xxx},... ] 当前默认选中的规格
   * @param {string}: defaultInfo 商品详情返回数据
   * @description: 根据后端返回的多规格数据处理成包含所有组合的价格及库存的数据集
   * @return {*} -
   */
  const initSku = async ({ specs, goodsMasterId, defaultSku, defaultInfo }: {specs: Specs[], goodsMasterId: string, defaultSku: DefaultSku[], defaultInfo: CourseDetailModel}) => {
    // 存储一份原始属性集 用于计算 isSku 值
    specsArr.value = specs
    // 处理规格名和规格值数据 [{name: xx, values: [name1,name2,...],itemIndex: x},...]
    await handleSpecs(specs)
    // 把所有 sku 数据处理成 { 'XX;XX;XX;': {price:xx, count: xx}... }
    await getAllSKU(goodsMasterId, defaultSku, defaultInfo)
    // 回显默认选中 sku 组合
    await handleDefaultSelectSku(defaultSku)
    // 获取所有的 sku 的 key (所有的组合情况)
    const skuKeys: string[] = getObjKeys()
    // 处理所有 sku 组合（包含spu的所有组合情况）库存状况和价格
    handleAllSkuSpu(skuKeys)
  }

  /**
   * @name: 处理接口返回的属性集
   * @param {Specs[]} specs itemIndex: 当前选中的规格值索引值 -1 则是没有选中 默认为 -1
   * @description: 处理成需要使用的数据结构  [{name: xx,itemIndex: xx,  values: [xx,xx,xx]}]
   * @return {*}
   */
  const handleSpecs = (specs: Specs[]) => {
    key.value = specs.map((i: Specs) => {
      return {
        name: i.name,
        values: i.value.map((it: valueItem) => it.name),
        itemIndex: -1
      }
    })
  }

  /**
   * @name: 获取所有的SKU组合
   * @param {string} goodsMasterId 商品 id
   * @param {DefaultSku[]} defaultSku defaultSku [{name: xx, value: xxx},... ] 当前默认选中的规格
   * @param {string}: defaultInfo 商品详情接口返回的商品现价
   * @description: 处理处所有 sku 组合的价格和库存且获取当前选择的规格值的商品图片 价格 标题 详情内容
   * @return {*}
   */

  const route = useRoute()
  const getAllSKU = async (goodsMasterId?: string, defaultSku?: DefaultSku[], defaultInfo?: CourseDetailModel) => {
    try {
      const { data } = await courseApi.getAllMultiSpecs({ ...handleAllSKUParams(goodsMasterId, defaultSku) })
      const { goodsItemList, goodsTitle, goodsSubtitle, priceRange, goodsContent, wordageLabels, classTypeList } = data.value as any
      sku.value = handleSKU(unref(goodsItemList))
      // 获取当前选择的规格值的商品图片 价格 标题 详情内容

      courseStore.setSpuInfo({
        isSku: true,
        content: goodsContent?.content || '',
        goodsImg: goodsContent?.goodsImg || '',
        simpleContent: goodsContent?.simpleContent || [],
        goodsTitle,
        goodsSubtitle,
        priceRange,
        wordageLabels,
        classTypeList
      })
      isGetAllSku.value = true

      const { internalSystemId } = route?.query
      // 判断是否为crm 26, 或者scrm 37 的改价分享链接
      if (internalSystemId && (+internalSystemId === 26 || +internalSystemId === 37) && route?.query.sharePrice && defaultInfo?.goodsPresentPrice) {
        courseStore.setSpuInfo({ priceRange: defaultInfo.goodsPresentPrice })
      }
    } catch (e) {
      console.log(e)
    }
  }

  /**
   * @name: 处理组合的 SKU 数据集
   * @param {AllSku} allSku sku组合原始数据
   * @description: 处理成 sku组合 name 为 key  { xx:xx:xx: {price: xx, count: xx} } 的数据结构
   * @return {*}
   */
  interface AllSkuGoodsImgItem {id: string, goodsImg: string}
  // const allSkuGoodsImg = ref<AllSkuGoodsImgItem[]>()
  const handleSKU = (allSku: AllSku[]): SKUItem => {
    const skuData: SKUItem | {} = {}
    unref(allSku).forEach((item: AllSku) => {
      const key: string = item.specification.map((i:SpecificationItem) => i.value).join(';')
      skuData[key] = { price: item.goodsPresentPrice, count: item.status ? 1 : 0, }
    })
    // 获取所有启用状态的 sku 商品主图
    const allSkuGoodsImg = allSku.filter(i => i.status).map((it):AllSkuGoodsImgItem => {
      return {
        goodsImg: it.itemImg,
        id: it.specification.map(i => i.value).join('_')
      }
    })
    courseStore.setAllSkuGoodsImg(allSkuGoodsImg)
    return skuData
  }

  /**
   * @name: 处理所有 sku 组合
   * @param {string[]} skuKeys 所有 sku 组合数组
   * @description: 包含spu的所有组合的库存状况和价格
   * @return {*}
   */
  const handleAllSkuSpu = (skuKeys: string[]) => {
    const resultSKU:ResultSku = {}
    for (const i in skuKeys) {
      // 获取一条SKU的key
      const skuKey = skuKeys[i]
      // 获取当前 sku 组合的的数据（价格&库存状况）
      const skuData = sku.value[skuKey]
      // 获取当前sku的属性数组
      const skuKeyAttrs: string[] = skuKey.split(';')
      // 获取当前s ku拆分的组合数组 (包含 spu 组合的所有数据)
      const combArr = recombine(skuKeyAttrs)
      // 组合数据
      for (let j = 0; j < combArr.length; j++) {
        const key = combArr[j].join(';')
        // sku 数据价格和库存状况
        if (resultSKU[key]) {
          resultSKU[key].count += skuData.count
          resultSKU[key].price.push(skuData.price)
        } else {
        // spu 数据价格和库存状况
          resultSKU[key] = {
            count: skuData.count,
            price: [skuData.price as number],
          }
        }
      }
    }
    // 处理出来的数据结构 [:XX;: {status: xx, price: [X,X,X,X...]}...]
    resultSku.value = resultSKU
  }

  /**
   * @name: 处理出所有的 SKU/spu 组合
   * @param {string[]} arr
   * @description:
   * @return {*}
   */
  const recombine = (arr: string[]) => {
    const labelArr = arr
    // 例如获取重新组合的数组格式 例如[['黑', ''],['16G', ''],['xiaomi14']]
    // 进行横向遍历，填充组合数组
    const newLabelArr:string[][] = labelArr.map((item: string) => [item, ''])
    // 进行横向遍历，填充组合数组，从 0 开始递归
    const resultArr = getCombineArr(newLabelArr, 0)
    return resultArr
  }

  /**
   * @name: 递归遍历所有的 sku 和 spu 组合（从 m 中取出 n 所有取法）
   * @param {string[][]} arr 例如 [[a, ''], [b,''],...]
   * @param {number} index 递归的索引值
   * @description: 递归循环组合出所有的sku组合 例如处理成[[a,b],[a,''],['',b], ['','']]
   * @return {*}
   */
  const getCombineArr = (arr: string[][], index: number) => {
    const resultArr: any = []
    const newArr: any = []
    const recursion = (arr: string[][], index: number) => {
      for (let i = 0; i < arr[index].length; i++) {
        // 注意这里的 newArr 是和上次计算出来的数据基础上进行重新赋值
        newArr[index] = arr[index][i]
        // 循环到当前项的最后一层将最后的数据 push 到 resultArr
        if (index === (arr.length - 1)) {
          resultArr.push(JSON.parse(JSON.stringify(newArr)))
        } else {
          // 不满足继续递归调用
          recursion(arr, index + 1)
        }
      }
    }
    recursion(arr, index)
    return resultArr
  }

  /**
   * @name: 处理当前组合的请求参数
   * @param {string} goodsMasterId 商品 id
   * @param {DefaultSku[]} defaultSku 默认选中sku数据
   * @description: 处理默认选中规格值情况和切换改变规格值情况的请求参数
   * @return {*}
   */
  const handleAllSKUParams = (goodsMasterId?: string, defaultSku?: DefaultSku[]) => {
    const {
      agentCode = '',
      agentMType = '',
      sharePriceId = '',
    } = route?.query
    const specAttrs: SpecAttrs = {}
    const params = {
      goodsMasterId: goodsMasterId || route.params.id,
      specAttrs,
      promotion: {
        type: agentMType || 'CP',
        code: agentCode,
        sharePriceId
      }
    }

    // 默认选中sku
    if (defaultSku?.length) {
      defaultSku.forEach((i) => {
        params.specAttrs[i.name] = i.value
      })
    } else {
      // 切换选中sku/spu
      key.value.forEach((i: Item) => {
        if (i.itemIndex > -1) {
          params.specAttrs[i.name] = i.values[i.itemIndex]
        }
      })
    }
    return params
  }

  /**
   * @name: 处理默认选中的 sku
   * @param {DefaultSku[]} defaultSku
   * @description: itemIndex 选中则为当前规格值的索引值
   * @return {*}
   */
  const handleDefaultSelectSku = (defaultSku: DefaultSku[]) => {
    // 获取默认的 sku 且赋值
    const skuLabel = defaultSku.map(i => i.value).join('_')
    courseStore.setSpuInfo({ ...courseStore.spuInfo, skuLabel, })
    key.value = key.value.map((i: Item) => {
      return {
        ...i,
        itemIndex: handleItemIndex(i)
      }
    })
    function handleItemIndex (i: Item) {
      let valueIndex: number | null = null
      unref(defaultSku).forEach((it) => {
        if (it.name === i.name) {
          valueIndex = i.values.findIndex((item: string) => item === it.value)
        }
      })
      return valueIndex
    }
  }

  /**
   * @name: 获取所有的 sku 组合 name 数组
   * @description: 只包含 sku 组合  不包含 spu 组合情况
   * @return {string[]} keysArr
   */
  const getObjKeys = (): string[] => {
    if (sku.value !== Object(sku.value)) {
      throw new TypeError('Invalid object!')
    }
    const keysArr:string[] = []
    for (const key in sku.value) {
      if (Object.prototype.hasOwnProperty.call(sku.value, key)) {
        keysArr[keysArr.length] = key
      }
    }
    return keysArr
  }

  /**
   * @name: 切换规格值选中的状态
   * @param {number} index 规格名索引
   * @param {number} sort sort：规格值索引 -1为未选中状态
   * @param {string} goodsMasterId 多规格id
   * @description: 切换规格值需要重新触发当前规格组成的 sku/spu 的相关数据
   * @return {*}
   */
  //  选择、反选规格 index: 规格名索引 sort： 规格值索引 -1为未选中状态
  const switchItem = async (index:number, sort:number, goodsMasterId?: string) => {
    if (key.value[index].itemIndex === sort) {
      key.value[index].itemIndex = -1
    } else {
      key.value[index].itemIndex = sort
    }
    // 获取当前规格值组成的 sku/spu 需要展示的数据
    await getAllSKU(goodsMasterId)
    // 展示库存和价格
    showCountAndPrice()
  }

  /**
   * @name: 处理需要展示的sku/spu相关数据
   * @description: 价格/区间从接口获取（前端也可计算获取）
   * @return {*}
   */
  const showCountAndPrice = () => {
    const skuLabelArr: string[] = []
    // 获取当前选中的索引数组
    key.value.forEach((element: Item) => {
      const _index = element.itemIndex
      skuLabelArr.push(_index === -1 ? '' : element.values[_index])
    })
    const skuLabel:string = skuLabelArr.join(';')
    // 找到当前对应的sku/spu价格和库存状态
    if (resultSku.value[skuLabel]) {
      // 前端计算
      courseStore.setSpuInfo({
        ...courseStore?.spuInfo,
        count: resultSku.value[skuLabel].count,
        skuLabel: skuLabelArr.filter((i:string) => i).join('_'),
        isSku: specsArr.value?.length === skuLabelArr.filter((i:string) => i)?.length
      })
    } else {
      // 未找到对应的规格组合
      courseStore.setSpuInfo({})
    }
    emitter.emit('switchSpecification')
  }

  /**
   * @name: 根据库存是否禁用当前规格名
   * @param {string} label 当前规格名下的所有规格值
   * @param {number} index 规格名的索引
   * @description: 找出当前选中的sku在resultSku中找出是否有库存进行禁用启用
   * @return {*}
   */
  const isDisSpecValue = (label: string, index: number) => {
    const chooseLabelArr = []
    key.value.forEach((item: Item) => {
      chooseLabelArr.push(item.itemIndex > -1 ? item.values[item.itemIndex] : '')
    })
    // 假设当前已选中
    chooseLabelArr[index] = label
    const nowChooseStr = chooseLabelArr.join(';')

    // 遍历加工完的数据集，是否有库存可选，有就可点击，没有就禁用
    let canSelected = false
    for (const skuLey in resultSku.value) {
      if (skuLey === nowChooseStr) {
        // console.log(skuLey, resultSku.value[skuLey].count, 'isDisSpecValue11111')

        if (resultSku.value[skuLey].count) {
          canSelected = true
          break
        }
      }
    }
    return canSelected
  }
  return {
    initSku,
    isDisSpecValue,
    getAllSKU,
    sku,
    key,
    switchItem,
    isGetAllSku,
    // allSkuGoodsImg
  }
}
