import type {
  QuestionListParams,
  TargetResultId,
  submitQuestType,
  submitLeaveType,
  submitFinishType,
  FixedStartProtParams,
  FixedCatalogueParams,
  DoQuestionProtBodyType,
} from '../types'
import filterQuestionBankBody from '../../../../base/utils/filterBody'

enum Api {
  // 固定刷题
  getFixedQuestionList = '/kukecorequestion/wap/brush/list',
  getFixedQuestionType = '/kukecorequestion/wap/brush/getQuestionType',
  getFixedQuestionAnswerCard = '/kukecorequestion/wap/testpaper/answerCardProt',
  getFixedQuestionReport = '/kukecorequestion/wap/brushChapter/reportProt',
  getFixedQuestionReportStatics = '/kukecorequestion/wap/brush/reportStaticsProt',
  getFixedQuestionCatalogue = '/kukecorequestion/wap/brush/info',
  fixedQuestionStart = '/kukecorequestion/wap/brushChapter/startProt',
  fixedQuestionDoAgain = '/kukecorequestion/wap/brushChapter/doAgainProt',
  fixedQuestionDoContinue = '/kukecorequestion/wap/brushChapter/doContinueProt',
  fixedQuestionSubmit = '/kukecorequestion/wap/brushChapter/submitQuestionProt',
  fixedQuestionSubmitLeave = '/kukecorequestion/wap/brushChapter/submitLeaveProt',
  fixedQuestionSubmitFinish = '/kukecorequestion/wap/brushChapter/submitFinishProt',
  fixedQuestionDoClickCount = '/kukecorequestion/wap/brush/doClick',
  // 判断固定刷题目录是否存在
  getFixedQuestionExistBrush = '/kukecorequestion/wap/brush/existBrush',
}

/**
 * 获取固定刷题列表
 *
 * @param {QuestionListParams} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function getFixedQuestionListProt (body: QuestionListParams) {
  return useHttp<any>(Api.getFixedQuestionList, {
    method: 'post',
    body: filterQuestionBankBody(body),
    transform: res => res.data,
  })
}

/**
 * 固定刷题-获取刷题模式
 *
 * @param {{ moduleManageId: string }} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function getFixedQuestionTypeProt (body: { moduleManageId: string }) {
  return useHttp<any>(Api.getFixedQuestionType, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 固定刷题-获取答题卡数据
 *
 * @param {TargetResultId & { model: number }} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function getFixedQuestionAnswerCardProt (body: TargetResultId & { model: number }) {
  return useHttp<any>(Api.getFixedQuestionAnswerCard, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 固定刷题-开始做题
 *
 * @param {FixedStartProtParams} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function startFixedQuestionProt (body: FixedStartProtParams) {
  return useHttp<any>(Api.fixedQuestionStart, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 固定刷题-再次做题
 *
 * @param {TargetResultId} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function againFixedQuestionProt (body: TargetResultId) {
  return useHttp<any>(Api.fixedQuestionDoAgain, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 固定刷题-继续做题
 *
 * @param {TargetResultId} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function continueFixedQuestionProt (body: TargetResultId) {
  return useHttp<any>(Api.fixedQuestionDoContinue, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 固定刷题-一题一提交
 *
 * @param {submitQuestType} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function submitFixedQuestionProt (body: submitQuestType) {
  return useHttp<any>(Api.fixedQuestionSubmit, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 固定刷题-中途提交/暂存
 *
 * @param {submitLeaveType} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function fixedSubmitLeave (body: submitLeaveType) {
  return useHttp<any>(Api.fixedQuestionSubmitLeave, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 固定刷题-交卷
 *
 * @param {submitFinishType} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function fixedSubmitFinish (body: submitFinishType) {
  return useHttp<any>(Api.fixedQuestionSubmitFinish, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 固定刷题-点击人次统计
 *
 * @param {DoQuestionProtBodyType} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function fixedQuestionClickStatistics (body: DoQuestionProtBodyType) {
  return useHttp<any>(Api.fixedQuestionDoClickCount, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 固定刷题-报告
 *
 * @param {TargetResultId} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function getFixedQuestionReport (body: TargetResultId) {
  return useHttp<any>(Api.getFixedQuestionReport, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 固定刷题-报告统计
 *
 * @param {TargetResultId} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function getFixedQuestionReportStatics (body: TargetResultId) {
  return useHttp<any>(Api.getFixedQuestionReportStatics, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 固定刷题-目录
 *
 * @param {FixedCatalogueParams} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function getFixedQuestionCatalogue (body: FixedCatalogueParams) {
  return useHttp<any>(Api.getFixedQuestionCatalogue, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 判断固定刷题目录是否存在
 *
 * @param {{ brushChapterId: string, moduleManageId: string }} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function getFixedQuestionExistBrush (body: { brushChapterId: string, moduleManageId: string }) {
  return useHttp<any>(Api.getFixedQuestionExistBrush, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}
