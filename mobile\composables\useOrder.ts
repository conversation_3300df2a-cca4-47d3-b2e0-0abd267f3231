/* eslint-disable no-case-declarations */
// 订单卡片和订单详情  按钮区域展示和操作逻辑处理
import { memberGoodsDetailsProt } from '~/apis/memberUser'
import tipImg from '~/assets/images/order/icon_success.png'
const { activityApi } = useApi()
export const orderEffect = (order: any) => {
  console.log(order, 9956)
  const route = useRoute()
  const isOrderListOrOrderDetail = computed(() => {
    return ['user-order', 'user-order-id'].includes(route.name as string)
  })
  // 如果为有价优惠券付款成功跳转至有价优惠券成功页面
  const { toCashier } = buyEffect(
    {
      info: {},
      options: {},
    }
  )
  const { orderApi } = useApi()
  // http://yapi.kukewang.com:9005/project/705/interface/api/36090   用户端显示的订单状态字典
  // 1 待付款：取消订单、立即付款；
  // 2 待发货：申请售后；
  // 3 待收货：查看物流、申请售后、确认收货；
  // 4 已完成：查看物流、申请售后；
  // 5 交易关闭：删除订单、再次购买；
  // 6 退款审核中：撤销售后、查看物流；
  // 7 审核驳回：前端暂不展示；
  // 8 退款成功：查看物流；
  const statusMap = {
    1: [
      { label: '取消订单', id: 2, active: false, ignoreOrderType: [] },
      { label: '立即付款', id: 1, active: true, ignoreOrderType: [] },
    ],
    2: [{ label: '申请售后', id: 3, active: false, ignoreOrderType: [17, 21] }],
    3: [
      { label: '查看物流', id: 5, active: false, ignoreOrderType: [17] },
      { label: '申请售后', id: 3, active: false, ignoreOrderType: [17, 21] },
      { label: '确认收货', id: 6, active: true, ignoreOrderType: [17] },
    ],
    4: [
      { label: '申请售后', id: 3, active: false, ignoreOrderType: [17, 21] },
      { label: '查看物流', id: 5, active: false, ignoreOrderType: [17] }
    ],
    5: [
      { label: '删除订单', id: 7, active: false, ignoreOrderType: [] },
      { label: '再次购买', id: 8, active: true, ignoreOrderType: [17] },
    ],
    6: [{ label: '撤销售后', id: 4, active: false, ignoreOrderType: [17] }, { label: '查看物流', id: 5, active: false, ignoreOrderType: [17] }],
    7: [
      { label: '申请售后', id: 3, active: false, ignoreOrderType: [17, 21] },
      { label: '确认收货', id: 6, active: true, ignoreOrderType: [17] },
    ],
    8: [{ label: '查看物流', id: 5, active: false, ignoreOrderType: [17] }],
    10: [{ label: '查看物流', id: 5, active: false, ignoreOrderType: [17] }],
    11: [{ label: '查看物流', id: 5, active: false, ignoreOrderType: [17] }],
    13: [
      { label: '申请售后', id: 3, active: false, ignoreOrderType: [17, 21] },
      { label: '查看物流', id: 5, active: false, ignoreOrderType: [17] },
      { label: '立即付款', id: 1, active: true, ignoreOrderType: [] },
    ]
  }

  const statusList = computed(() => {
    const status = order.visibleOrderStatus
    const list = statusMap[status] || []
    // 不包含物流
    if (!order.hasExpress) {
      const index = list.findIndex((item: any) => item.id === 5)
      if (index !== -1) {
        list.splice(index, 1)
      }
    }
    // 不支持售后
    if (!order.availableForSale) {
      const index = list.findIndex((item: any) => item.id === 3)
      if (index !== -1) {
        list.splice(index, 1)
      }
    }
    // 转班成功的订单 不展示取消订单和申请售后
    if (order.orderType === 2) {
      const index = list.findIndex((item: any) => item.id === 2)
      if (index !== -1) {
        list.splice(index, 1)
      }
      const indexOne = list.findIndex((item: any) => item.id === 3)
      if (indexOne !== -1) {
        list.splice(indexOne, 1)
      }
    }
    // 交易关闭的购物车订单 若只包含一个主商品，则允许再次购买，若包含多个主商品，则不可再次购买
    // changeType  0：主商品；2：主商品；3：赠送商品；4：加购商品；
    if (status === 5) {
      const statusNum = order.orderGoodsVOList?.filter((f:any) => (f.changeType === 0 || f.changeType === 2))?.length
      if (statusNum > 1) {
        const index = list.findIndex((item: any) => item.id === 8)
        if (index !== -1) {
          list.splice(index, 1)
        }
      }
    }
    console.log('list', list)

    // return statusMap[status] || []
    const listFilter = list.filter((item: any) => !item.ignoreOrderType.includes(order.orderType))
    return listFilter || []
  })
  // 不同title处理
  const handleTitle = (id: number) => {
    let title: string
    switch (id) {
      case 4:
        title = '确定要撤销本次售后申请吗？'
        break
      case 6:
        title = '为了保证您的权益，请查阅商品确认无误后再确认收货哦~'
        break
      case 7:
        title = '删除之后不可找回，确定删除吗？'
        break
      default:
        title = ''
    }
    return title
  }
  // 不同按钮确认字段处理
  const handleConfirmText = (id: number) => {
    let title: string
    switch (id) {
      case 4:
        title = '撤回'
        break
      case 6:
        title = '确定'
        break
      case 7:
        title = '删除'
        break
      default:
        title = '确定'
    }
    return title
  }

  // const kkPopupOrderRef = ref(null)
  const popupTitle = ref<string>('')
  const confirmText = ref<string>('确定')
  const operateHandle = (id: number) => {
    popupTitle.value = handleTitle(id)
    confirmText.value = handleConfirmText(id)
    // kkPopupOrderRef.value?.showPopup()
  }
  const secondConfirmhandle = async () => {
    const func = selectType.value === 4 ? orderApi.cancelApplyRefund : selectType.value === 6 ? orderApi.receiptOrder : selectType.value === 7 ? orderApi.deleteOrder : ''
    const { data } = await func({ orderSn: order.orderSn })
    Message(data.value.msg, tipImg)

    // kkPopupOrderRef.value?.hide()
    // 调接口 刷新列表 页面中执行
  }
  // 售后判断
  const afterSale = async () => {
    console.log(order, 66)
    if (order?.orderType === 33) {
      // 如果为优惠券订单，则优先校验是否可以退款
      const { data } = await activityApi.refundVerify({ orderSn: order?.orderSn })
      if (data.value.refundable) {
        // 跳转
        navigateTo({
          path: '/user/order/afterSales',
          query: {
            orderSn: order.orderSn
          }
        })
      } else {
        Message(data.value.cause)
      }
    } else {
      // 跳转
      navigateTo({
        path: '/user/order/afterSales',
        query: {
          orderSn: order.orderSn
        }
      })
    }
  }

  const {
    toMemberCenterPage,
  } = useMemberUtils()

  const selectType = ref<number>(1)
  const btnStatusHandle = debounceFunc((id: number) => {
    // 1 立即付款| 2 取消订单| 3 申请售后| 4 撤销申请| 5 查看物流| 6 确认收货| 7 删除订单| 8 再次购买| 9 查看详情
    selectType.value = id

    switch (id) {
      case OrderAction.VIEW_DETAILS:
        navigateTo(`/user/order/${order.orderSn}`)
        break
      case OrderAction.PAY_NOW:
        /* let backRedictUrlsMapKey = ''
        if (order.orderType === 33) {
          backRedictUrlsMapKey = 'priceConpun'
        } else if (order.orderType === 21) {
          backRedictUrlsMapKey = 'memberCenter'
        } else {
          backRedictUrlsMapKey = 'default'
        }
        toCashier('', orderSns, backRedictUrlsMapKey) */
        if (order.orderType === 21) {
          // TODO 会员
          toCashier(order.orderSn, '', 'memberCenter')
        } else {
          const orderSns = `${encodeURIComponent(JSON.stringify([order.orderSn]))}`
          toCashier('', orderSns, order.orderType === 33 ? 'priceConpun' : 'default')
        // toCashier(order.orderSn)
        }
        break
      case OrderAction.CANCEL_ORDER:
        // 取消订单  页面中执行
        break
      case OrderAction.APPLY_AFTER_SALE:
        afterSale()
        break
      case OrderAction.VIEW_LOGISTICS:
        navigateTo({
          path: '/user/order/logistics',
          query: {
            orderSn: order.orderSn,
            fromPage: isOrderListOrOrderDetail.value ? 'order' : undefined
          }
        })
        break
      case OrderAction.REORDER:
        let masterGoods: any
        if (Array.isArray(order?.orderGoodsVOList)) {
          const orderGoodsVOList = order?.orderGoodsVOList || []
          masterGoods = orderGoodsVOList.find((item: any) => {
            return item.changeType === 0 || item.changeType === 2
          })
        } else {
          masterGoods = order
        }
        console.log(order, 667)
        if (order.orderType === 33) {
          navigateTo(`/activity/pricecoupon/${masterGoods.goodsMasterId}`)
        } else if (order.orderType === 21) {
          // TODO 跳到会员中心页面且选中此会员所属的专业分类值+两个标签的标签值
          // Message('购买逻辑待对接！！')
          const body = {
            type: 1,
            memberGoodsId: masterGoods.goodsMasterId,
            memberSpecId: masterGoods.specificationItemId || '',
          }
          // const { data, error } = await
          memberGoodsDetailsProt(body).then(({ data, error }) => {
            console.log('[ memberGoodsDetailsProt ] >', data.value, error.value)
            if (!error.value) {
              toMemberCenterPage(data.value)
            }
          })
        } else {
          navigateTo(`/course/${masterGoods.goodsMasterId}`)
        }
        break
      default:
        // 操作按钮
        operateHandle(id)
        break
    }
  }, 800, true)
  return {
    statusList,
    popupTitle,
    confirmText,
    selectType,
    btnStatusHandle,
    secondConfirmhandle,
  }
}
