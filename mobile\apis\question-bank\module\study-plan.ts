enum Api {
  // 做题
  studentStartProt = '/kukecorequestion/wap/student/startProt',
  // 查看解析
  studentCorrectProt = '/kukecorequestion/wap/student/getMyAnswerProt',
  // 答题
  studentSubmitProt = '/kukecorequestion/wap/student/submitQuestionProt',
  // 暂存
  studentLeaveProt = '/kukecorequestion/wap/student/submitLeaveProt',
  // 交卷
  studentFinishProt = '/kukecorequestion/wap/student/submitFinishProt',
  // 查看报告
  studentReportProt = '/kukecorequestion/wap/student/reportProt',
}

/**
 * 开始/继续/再次做题
 *
 * @param body
 */
export async function studentStartProt (body: {
  testpaperId: string
  studyPlanId: string
}) {
  return useHttp<any>(Api.studentStartProt, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 查看解析
 *
 * @param body
 */
export async function studentCorrectProt (body: any) {
  return useHttp<any>(Api.studentCorrectProt, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 答题
 *
 * @param body
 */
export async function studentSubmitProt (body: any) {
  return useHttp<any>(Api.studentSubmitProt, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 暂存
 *
 * @param body
 */
export async function studentLeaveProt (body: any) {
  return useHttp<any>(Api.studentLeaveProt, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 交卷
 *
 * @param body
 */
export async function studentFinishProt (body: any) {
  return useHttp<any>(Api.studentFinishProt, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 查看报告
 *
 * @param body
 */
export async function studentReportProt (body: {
  targetResultId: string
}) {
  return useHttp<any>(Api.studentReportProt, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}
