import type { SelectParams, TagListType, TagType } from '../pages/question-bank/types/basic'
import { useQuestionMultipleTagStore } from '~/stores/question.multiple.tag.store'
import { useQuestionBankStore } from '~/stores/question.bank.store'

export function useSelectExam (tagList: Ref<TagListType[]>) {
  const questionMultipleTagStore = useQuestionMultipleTagStore()
  const questionBankStore = useQuestionBankStore()

  const { purchasedTag } = useQuestionBankMultipleTag()

  const { moduleManageId, studyLevel1 } = purchasedTag.value
  const storageId = ref('')

  const isShowMultipleTag = ref(false)

  const openSelectExamPopup = () => {
    isShowMultipleTag.value = true
  }

  /**
   * 关闭标签弹窗
   */
  const handleTagClose = () => {
    isShowMultipleTag.value = false
  }

  /**
   * 选中标签项
   */
  const selectParams = ref<SelectParams>({
    subjectType: '',
    region: '',
    academicSection: '',
    directionType: '',
    examFormat: '',
  })

  const originSelectParams = ref<SelectParams>({
    subjectType: '',
    region: '',
    academicSection: '',
    directionType: '',
    examFormat: '',
  })

  /**
   * [AI GEN] 验证标签选择是否有效
   * @param tags - 标签列表
   * @param lastSelected - 上次选择的参数
   * @returns 是否为有效选择
   */
  const isValidSelection = (tags: TagListType[], lastSelected: SelectParams): boolean => {
    // 快速失败: 检查基本条件
    if (!tags?.length || !lastSelected) {
      return false
    }

    const selectedFields = Object.keys(lastSelected)
    // 确保选择的字段数量与标签数量一致
    if (selectedFields.length !== tags.length) {
      return false
    }

    // 验证每个标签是否都有有效选择
    return tags.every((tag) => {
      const selectedValue = lastSelected[tag.field]
      return selectedValue && tag.list.some(item => Number(item.id) === selectedValue)
    })
  }

  /**
   * 标签项判断是否多选展示
   *
   * @param id 唯一标识
   * @param needVerify 是否需要执行后续检查
   * @param selected 标签项
   *
   */
  const handleTag = ({ id, needVerify = true, selected }: {id?: string, needVerify?: boolean, selected?: SelectParams}) => {
    const tagArrValues = tagList.value.map(item => item.list)
    const hasMultipleTags = tagArrValues.some(item => item.length > 1)
    storageId.value = id || ''

    if (!needVerify) {
      // 打开标签弹窗
      openSelectExamPopup()
      return
    }

    // 判断是否有多个标签项
    if (hasMultipleTags) {
      if (!id) {
        // 打开标签弹窗
        openSelectExamPopup()
        return
      }
      const lastSelected = selected || questionMultipleTagStore.getSelectTag(moduleManageId, studyLevel1, id)
      // 判断是否有上次选中的标签项
      if (lastSelected && isValidSelection(tagList.value, lastSelected)) {
        Object.assign(selectParams.value, {
          subjectType: lastSelected.subjectType,
          region: lastSelected.region,
          directionType: lastSelected.directionType,
          academicSection: lastSelected.academicSection,
          examFormat: lastSelected.examFormat,
        })
        originSelectParams.value = { ...selectParams.value }
        // 标签title
        tagTitle.value = handleTagTitle(tagList.value, selectParams.value)
      } else {
        // 打开标签弹窗
        openSelectExamPopup()
      }
    }
  }

  const handleDefaultTag = (tagsList?:TagListType[]) => {
    const tList = tagsList || tagList.value
    // 根据tagList生成一个对象，key为field
    return tList.reduce((pre: any, { field, list }: TagListType) => {
      const defaultTag = list.find((item: TagType) => item.id)
      pre[field] = defaultTag?.id || ''
      return pre
    }, {})
  }

  const handleTagSelect = (item: { field: string, item: TagType }) => {
    return new Promise((resolve) => {
      Object.assign(selectParams.value, {
        [item.field]: item.item.id,
      })
      clearChild(item.field)
      resolve(selectParams.value)
    })
  }

  /**
   * 清除下级选项
   */
  const clearChild = (field: string) => {
    const tagKeyArr = tagList.value.map(item => item.field)
    const index = tagKeyArr.findIndex(item => item === field)
    if (index === tagKeyArr.length - 1) {
      return
    }
    for (let i = index + 1; i < tagKeyArr.length; i++) {
      const tagKey = tagKeyArr[i]
      selectParams.value[tagKey] = ''
    }
  }

  /**
   * 多选标签选择提交
   */
  const submitSelect = async ({
    subjectType,
    region,
    directionType,
    academicSection,
    examFormat,
  }: SelectParams): Promise<{ withTag: 1 | 0 } & SelectParams> => {
    return new Promise((resolve) => {
      const params: {withTag : 1 | 0} & SelectParams = {
        subjectType: Number(subjectType) || 0,
        region: Number(region) || 0,
        directionType: Number(directionType) || 0,
        academicSection: Number(academicSection) || 0,
        examFormat: Number(examFormat) || 0,
        withTag: 1, // 标签选中触发获取详情默认携带了标签
      }

      Object.assign(selectParams.value, {
        subjectType: params.subjectType,
        region: params.region,
        directionType: params.directionType,
        academicSection: params.academicSection,
        examFormat: params.examFormat,
      })
      originSelectParams.value = { ...selectParams.value }
      if (process.client && storageId.value) {
        // 存储标签
        questionMultipleTagStore.addSelectTag({
          id: storageId.value,
          moduleManageId,
          studyLevel1,
          ...selectParams.value
        })
      }
      // 标签title
      tagTitle.value = handleTagTitle(tagList.value, selectParams.value)
      questionBankStore.setQueryDataByObject(selectParams.value)
      isShowMultipleTag.value = false
      resolve(params)
    })
  }

  /**
   * 标签title
   */
  const tagTitle = ref('')

  /**
   * [AI-GEN]
   * 根据selectParams和tagList生成默认标签title
   */
  const handleTagTitle = (tagList: TagListType[], selectParams: SelectParams) => {
    const tagArrValues = tagList.map(item => item.list)
    const hasMultipleTags = tagArrValues.some(item => item.length > 1)
    if (!hasMultipleTags) {
      return ''
    }

    const selectedTags = tagList
      .map(({ field, list }: TagListType) => {
        const tag = list.find((item: TagType) => item.id === selectParams[field])
        return tag?.title || ''
      })
      .filter(title => title)

    return selectedTags.join(' | ')
  }

  /**
   * 重置数据
   */
  const resetData = () => {
    selectParams.value = { ...originSelectParams.value }
    tagTitle.value = handleTagTitle(tagList.value, selectParams.value)
  }

  return {
    tagTitle,
    selectParams,
    isShowMultipleTag,
    isValidSelection,
    openSelectExamPopup,
    handleTag,
    handleTagSelect,
    submitSelect,
    handleDefaultTag,
    handleTagClose,
    resetData
  }
}
