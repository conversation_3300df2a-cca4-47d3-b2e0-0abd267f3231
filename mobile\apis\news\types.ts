export interface NewsInfoDTO {
  cateId: number
  newsClassifyId: string
  province: string
  city: string
  area: string
  page: number
  pageSize: number
}

export interface SeoInfo {
  productId: string
  seoTitle: string
  seoKeyword: string
  seoDescription: string
}

export interface ClassLableInfo {
  id: string
  name: string
  uniqueIdentification?: string
  newsClassifySeo?: SeoInfo
}

export interface NewInfoDTO {
  id: string // 资讯id
  title: string // 资讯标题
  introduction: string // 资讯摘要
  picture: string // 新闻封面图
  isTop: number // 是否置顶：1是 0否
  publishTime: string // 发布时间
  cateId?: string // 项目（一级分类）
  cateName?: string // 项目（一级分类）名称
  province?: string
  provinceName?: string
  city?: string
  cityName?: string
  area?: string
  areaName?: string
  newsClassify: ClassLableInfo[]
  newsLabelsList: ClassLableInfo[]
}

export interface NewsListResVo {
  count: number
  name?: string
  list: NewInfoDTO[],
  newsLabelSeo: SeoInfo
}

export interface LabelListDTO {
  id: string
  page: number
  pageSize: number
}

export interface AreaInfoDTO {
  parentId: string
  isFilterNewsArea?: number
}

export interface AreaLabel {
  id: string
  areaName: string
  alias: string
  parentId: number
  level: number
  disabled: boolean
  status: number
  childrenList: AreaLabel[]
}

export interface AreaLabelResVo {
  list: AreaLabel[]
}

export interface RegionLabelDTO {
  usedId: number
}

export interface IdVO {
  id: string
}

export interface CateIdVO {
  cateId: string | number
}

export interface RegionLabel {
  usedId: number
  labelValue: string
  level: number // 地区级别,1省2市3县区4镇
}

export interface RegionLabelResVO {
  count: number
  list: RegionLabel[]
}

export interface CategoryInfo {
  id: string
  categoryName: string
  usedId: number
  level: string
}

// 专业分类
export interface CategoryResVO {
  count: number
  list: CategoryInfo[]
}

interface ContentInfo {
  id: string // 资讯id
  title: string // 资讯标题
}

export interface ClassLableInfoResVO {
  count: number
  list: ClassLableInfo[]
}

export interface NewsDetailInfoResVO {
  id: string
  title: string // 资讯标题
  introduction: string
  picture: string
  sumViewCount: number
  source: string
  isStudentQuestions: number
  publishTime: string
  newsClassifys?: string
  productIds?: string
  isUpload: number
  content: string
  previousPosts: ContentInfo
  nextChapter: ContentInfo
  newsLabelsList: ClassLableInfo[]
  newsSeo: SeoInfo
  cateId: string
}

export interface QuestionConfirmDTO {
  newsId: string
  content: string
  productSide: number
  userName: string
  userMobile: string
}

export interface HomeNewsDTO {
  productSide: number,
  code: number
}

/**
 * 资讯详情UV统计上报 请求参数
 */
export interface FetchNewsDetailVisitReportParams {
  id: string; // 资讯id（必填）
  uniqueId?: string; // 唯一标识id，未登录传唯一标识，使用浏览器指纹加密（可选）
  isLogin: boolean; // 是否登录（必填）
}
