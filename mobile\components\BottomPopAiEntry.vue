<template>
  <a
    v-show="visible"
    class="kukeAi__entry"
    :href="kukeAiUrl"
  >
    <span class="flex-shrink-0">
      <!-- 库AI -->
      <img
        class="w-[72px] h-[72px]"
        src="@/assets/images/home/<USER>"
        alt="库AI"
      >
    </span>
    <div
      class="text-[#6F44FF] px-[8px] text-[24px] leading-[32px] font-medium flex-1"
    >
      已接入DeepSeek，备<br>
      考、答疑都可以找我~
    </div>
    <span
      class="flex-shrink-0"
      @click.stop.prevent="closeKukeAi"
    >
      <KKCIcon
        name="icon-com_guangbi"
        :size="24"
        color="#111"
      />
    </span>
  </a>
</template>
<script setup lang="ts">
defineProps({
  kukeAiUrl: {
    type: String,
    default: ''
  }
})
const visible = ref(true)
// const emits = defineEmits(['close'])

const closeKukeAi = () => {
  // emits('close')
  visible.value = false
}

</script>

<style lang="scss">
.kukeAi {
&__entry {
    // min-width: 374px;
    min-width: 380px;
    // width: 80px;
    padding: 12px 16px 12px 8px;
  border-radius: 48px 48px 48px 48px;
    font-size: 28px;
    color: #111;
    // background-color: #fff;
    // box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.15);
    background: linear-gradient( 90deg, #ECF4FF 0%, #F1ECFF 100%);
    box-shadow: 0px 4px 16px 0px rgba(150,164,181,0.25);
    border: 2px  solid rgba(110, 68, 255, 1);
    // border-image: linear-gradient(90deg, rgba(74, 122, 255, 1), rgba(110, 68, 255, 1)) 2 2;

    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
