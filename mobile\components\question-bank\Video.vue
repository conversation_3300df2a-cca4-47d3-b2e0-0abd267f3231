<template>
  <div>
    <!-- 预览图，这里使用兜底图 -->

    <div class="mt-[24px]">
      <div v-if="type === 'answer'" class="relative w-[366px] h-[244px]" @click.stop.prevent="handlePlay">
        <div class="video-bg-cover w-[366px] h-[244px]" />
        <div class="video-bg-btn w-[60px] h-[60px] absolute top-[50%] left-[50%] translate-y-[-50%] translate-x-[-50%] z-2" />
      </div>

      <div v-else class="w-[702px] h-[468px] rounded-[8px] m-auto preview" @click.stop.prevent="handlePlay" />
    </div>
    <Teleport to="body">
      <KKPopup
        ref="kkVidPopupRef"
        title=""
        :is-close="false"
        close-icon-color="#fffff"
        bg-color="none"
        box-padding="0"
        position="center"
        :close-on-click-overlay="false"
        :width="720"
        :is-show-btn="false"
        :style="{ overflow: 'visible', top: '45%' }"
        @close="closePlayer"
        @touch-start="handleTouchStart"
        @touch-end="handleTouchEnd"
      >
        <div v-if="isPlaying">
          <KKCQuestionVideoPlayer
            ref="videoPlayer"
            class="overflow-hidden rounded-[24px]"
            width="100%"
            :height="px2rem(468).toString()"
            :video-detail="videoInfo"
            @play="handleVideoPlay"
          />
          <KKCIcon
            name="icon-com_guangbi"
            color="#fff"
            :size="48"
            class="absolute right-0 top-[-96px]"
            @click="handleClose"
          />
        </div>
      </KKPopup>
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import px2rem from '../../../base/utils/px2rem'
import { pauseAudio } from '../../../base/utils/audio'
import { useQuestionBankStore } from '~/stores/question.bank.store'
import { useQuestionVideoStore } from '~/stores/question.video.store'
import { useChatStore } from '~/stores/chat.store'
const { updateCurPlayingMedia } = useChatStore()
const questionBankStore = useQuestionBankStore()
const questionVideoStore = useQuestionVideoStore()
const { track } = useTrackEvent()

const props = withDefaults(defineProps<{
  questionId: string
  type?: string
  polyvRef?: string
}>(), {
  questionId: '',
  type: '',
  polyvRef: 'polyvRef'
})

const { videoInfo, getVideoInfo } = useQuestionVideoInfo()
const isPlaying = ref(false)
const kkVidPopupRef = ref()

const handlePlay = async (event: Event) => {
  event.preventDefault()
  if (!props.questionId) { return }
  try {
    // 做题->视频解析 埋点
    track({
      category: '题库',
      action: '视频解析'
    })
    if (props.type === 'answer') {
      updateCurPlayingMedia({
        playingRef: props.polyvRef,
        option: {
          closePlayer
        }
      })
    }

    getVideoInfo(props.questionId).then(() => {
      // 关闭其他音频播放
      pauseAudio()
      console.log('videoInfo', videoInfo)
      // AI生成的视频解析，在安卓app中播放存在无法拖动播放，改为调用原生播放
      // 鸿蒙没有播放页，暂不处理
      if (os?.isApp && os?.isAndroid) {
        window.android.goOnlinePlayPage(videoInfo.orgId, videoInfo.videoId)
      } else if (os?.isApp && (os?.isPhone || os?.isTablet)) {
        window.webkit?.messageHandlers?.goOnlinePlayPage?.postMessage(JSON.stringify(videoInfo))
      } else {
        isPlaying.value = true
        kkVidPopupRef.value?.showPopup()
      }
    })
  } catch (error) {
    isPlaying.value = false
  }
}

const videoPlayer = ref()

const closePlayer = () => {
  isPlaying.value = false
  questionVideoStore.pauseVideoForId(props.questionId)
  questionVideoStore.clearVideo()
  // 目前保利威广告期间无法暂停实例，只能销毁
  videoPlayer.value?.handleDestroyPlayer()
}

// 关闭弹窗
const handleClose = () => {
  kkVidPopupRef.value?.hide()
}

// 保存视频实例
const handleVideoPlay = (player: any) => {
  questionVideoStore.addVideo({
    videoId: props.questionId,
    videoType: videoInfo.videoType,
    _Video: player
  })
}

// 处理touch事件，防止swiper滑动
const handleTouchStart = () => {
  questionBankStore.setDisabledSwiperTouch(true)
}

// 恢复swiper滑动
const handleTouchEnd = () => {
  setTimeout(() => {
    questionBankStore.setDisabledSwiperTouch(false)
  }, 50)
}

</script>

<style scoped>

.video-bg-cover{
  background-image: url('../../assets/images/answer/answer-wap-bg.png');
  background-repeat: no-repeat;
  background-size: cover;
}
.video-bg-btn{
  background-image: url('../../assets/images/answer/video-play.png');
  background-repeat: no-repeat;
  background-size: cover;
}
.preview {
  background: url('../../../base/assets/video/question-video-bg.png') no-repeat center center;
  background-size: 100% 100%;
}
</style>
