<template>
  <div>
    <ul class="flex flex-wrap">
      <li
        v-for="item in list"
        :key="item.id"
        :class="['specs-item', { 'active': item.id === activeValue }]"
        @click="onSpecs(item)"
      >
        {{ item.departmentName }}
      </li>
    </ul>
  </div>
</template>

<script setup lang="ts">
import type { SchoolType } from '@/apis/course/types'
defineProps<{
  list: any,
  activeValue: string
}>()

const emits = defineEmits<{
  (e: 'click', event: SchoolType): void
}>()
const onSpecs = (item: SchoolType): void => {
  emits('click', item)
}

</script>

    <style scoped lang="scss">
    .specs-item {
      padding: 15px 16px;
      margin: 0 8px 16px 8px;
      margin-bottom: 24px;
      font-size: 26px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #111111;
      border-radius: 12px;
      background: #F7F7F7;
      border: 2px solid transparent;

      &.active {
        font-weight: 500;
        border-style: solid;
        border-width: 2px;
        @apply text-brand border-brand bg-brand/5;
      }

      &:active {
        background: #F2F2F2;
      }
    }
    </style>
