/**
 * 默认选中的标签
 */
export interface DefaultTagListModel {
  field: string
  name: string
  id: string
  title: string
}

export interface DefaultTagModel {
  count: number
  list: DefaultTagListModel[]
}

/**
 * 获取多个模块标签列表
 */
export interface getDefaultTagParams {
  subjectType: number
  region: number
  directionType: number
  academicSection: number
  examFormat: number
  moduleManageId: string
  studyLevel1: number
}
