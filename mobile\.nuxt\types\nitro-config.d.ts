// Generated by nitro

// App Config
import type { Defu } from 'defu'

import type { default as appConfig0 } from "../../app.config";

type UserAppConfig = Defu<{}, [typeof appConfig0]>

declare module "nitropack/types" {
  interface AppConfig extends UserAppConfig {}
  interface NitroRuntimeConfig {
   public: {
      TOKEN_KEY: string,

      BUILD_ENV: string,

      lastBuildTime: number,

      baseUrlClient: string,

      version: string,

      PLC_WX_APP_ID: string,

      USER_KEY: string,

      CALLBACK_URL: string,

      PAY_LOCALHOST: string,

      persistedState: {
         storage: string,

         debug: boolean,

         cookieOptions: any,
      },
   },

   baseUrlServer: string,
  }
}
export {}