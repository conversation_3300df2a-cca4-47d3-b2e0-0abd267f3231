import type {
  StartType,
  submitFinishType,
  TargetResultId,
  StartProtParams,
} from '../types'
import filterQuestionBankBody from '../../../../base/utils/filterBody'

enum Api {
  // 估分试卷
  subscribeEstimateQuestionProt = '/kukecorequestion/wap/gufen/subscribeProt',
  estimateStart = '/kukecorequestion/wap/gufen/startProt',
  estimateDoAgain = '/kukecorequestion/wap/gufen/doAgainProt',
  estimateDoContinue = '/kukecorequestion/wap/gufen/doContinueProt',
  estimateSubmitFinish = '/kukecorequestion/wap/gufen/submitFinishProt',
  estimateReport = '/kukecorequestion/wap/gufen/reportProt',
  // 估分邀请详情页
  estimateInviteDetail = '/kukecorequestion/wap/gufen/detail',
}

/**
 * 估分试卷---预约估分
 *
 * @param {StartType} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function estimateSubscribe (body: StartType) {
  return useHttp<any>(Api.subscribeEstimateQuestionProt, {
    method: 'post',
    body: filterQuestionBankBody(body),
    transform: res => res.data
  })
}

/**
 * 估分试卷---开始做题
 *
 * @param {StartType} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function estimateStart (body: StartType) {
  return useHttp<any>(Api.estimateStart, {
    method: 'post',
    body: filterQuestionBankBody(body),
    transform: res => res.data,
  })
}

/**
 * 估分试卷---再次练习
 *
 * @param {TargetResultId} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function estimateDoAgain (body: TargetResultId) {
  return useHttp<any>(Api.estimateDoAgain, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 估分试卷---继续做题
 *
 * @param {TargetResultId} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function estimateDoContinue (body: TargetResultId) {
  return useHttp<any>(Api.estimateDoContinue, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 估分试卷---完成提交
 *
 * @param {submitFinishType} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function estimateSubmitFinish (body: submitFinishType) {
  return useHttp<any>(Api.estimateSubmitFinish, {
    method: 'post',
    body,
    transform: res => res.data,
    watch: false
  })
}

/**
 * 估分试卷---查看报告
 *
 * @param {TargetResultId} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function estimateReport (body: TargetResultId) {
  return useHttp<any>(Api.estimateReport, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 估分试卷---好友邀请详情页
 *
 * @param {StartProtParams} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function getEstimateInviteDetail (body: StartProtParams) {
  return useHttp<any>(Api.estimateInviteDetail, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}
