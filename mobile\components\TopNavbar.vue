<template>
  <component
    v-bind="item.props"
    :is="item.componentName"
    v-for="item of componentsData.components"
    v-show="showTopNavbar"
    :key="item.id"
    class="kkc-top-navbar"
    :classify-list="[]"
  />
</template>

<script setup lang="ts">
import { useUserStore } from '~/stores/user.store'
const { data: kuke99DefaultHomeData } = useKuke99DefaultHomeData()
const localId = useCookie<string>('LT')
const route = useRoute()
const {
  setLearnTargetId,
} = useLearnTarget()
const {
  isXcx,
} = useAppConfig()
const userStore = useUserStore()
const { userApi } = useApi()
const { data: componentsData } = await getReleasePersonCenterORNavigationDetails({
  pageId: '102',
  productSide: isXcx ? 5 : 7,
  pageTag: 2,
})
if (userStore.isLogin) {
  const { data: messageData } = await userApi.getMessageCount()
  // 设置消息数量
  if (unref(componentsData)?.components?.length) {
    unref(componentsData).components[0].props.count = messageData.value?.unreadMsgNum || 0
  }
}
const isTopNavigationPc = ref(0)
const isCustomePage = ref(false)
const getBottomNavigateStatus = async (id:string) => {
  // 获取自定义页面 导航配置
  const { data: customPageNavigateData } = await getCustomPageNavigationStatus({
    customPageId: id as string
  })
  isTopNavigationPc.value = customPageNavigateData.value?.isTopNavigationPc as number
}
watch(() => [route.name, route.params.id], ([newV, newId]) => {
  if (newId && newV === 'cp-id') {
    isCustomePage.value = true
    getBottomNavigateStatus(newId as string)
  } else {
    isCustomePage.value = false
  }
}, {
  immediate: true
})

const showTopNavbar = computed(() => {
  if (!unref(componentsData)?.components?.length) { return false }
  //  自定义页面开启顶部导航展示  不开启就不展示
  if (isTopNavigationPc.value) { return true }
  if (isCustomePage.value && !isTopNavigationPc.value) { return false }
  const { applicationPage = '' } = unref(componentsData).components[0]?.props?.topNavigationApp || {}
  return applicationPage.split(',').includes(route.meta.pageid)
})
const logoClickHandle = (e) => {
  if (kuke99DefaultHomeData.value.cpId) {
    e.preventDefault()
    // 如果登录 -> 进入选过的学习目标
    // 否组进入首页
    console.log('TODO 确认清除 token 逻辑 小程序环境是否重启')
    if (userStore.isLogin) {
      navigateTo({
        path: '/',
        query: {
          lt: localId.value ? localId.value : undefined
        }
      }, { external: true })
    } else {
      setLearnTargetId('idOfCurrentUser', null)
      localId.value = null
      useCookie('LT_TAGS').value = null
      if (isXcx) {
        wx.miniProgram.reLaunch({
          url: '/pages/index/index',
        })
      } else {
        navigateTo('/', { external: true })
      }
    }
  }
}
onMounted(() => {
  const logoDom = document.querySelector('.kb-navbar-custom__logo')
  if (logoDom) {
    logoDom.addEventListener('click', logoClickHandle)
  }
})
onUnmounted(() => {
  const logoDom = document.querySelector('.kb-navbar-custom__logo')
  if (logoDom) {
    logoDom.removeEventListener('click', logoClickHandle)
  }
})
</script>

<style scoped>
</style>
