import { useUserStore } from '~/stores/user.store'

// 微信分享
interface ConfigInfo {
  appId: string
  timestamp: number
  nonceStr: string
  signature: string
  jsApiList: string[]
}
// 设置分享信息
interface ShareInfo {
  id?: string
  title?: string
  desc?: string
  shareImg?: string
  url?: string
}

export const useWxShare = () => {
  const userStore = useUserStore()
  const appConfig = useAppConfig()
  const { commonApi } = useApi()

  let initConfig: () => void = () => {}
  let setShareInfo: (val: ShareInfo, flag: boolean) => void = () => {}

  // 初始化
  initConfig = async () => {
    // appId 为1 是库课网校  库课网校没租户id
    // if (appConfig.appId !== 1 && (!appConfig.lesseeId)) {
    //   return Promise.resolve()
    // }
    const { data } = await commonApi.getWxShareConfig({
      url: location.href
    })
    const configInfo: ConfigInfo = data?.value || {}
    return new Promise((resolve) => {
      wx.config({
        debug: false,
        appId: configInfo.appId,
        timestamp: configInfo.timestamp,
        nonceStr: configInfo.nonceStr,
        signature: configInfo.signature,
        jsApiList: [
          'updateAppMessageShareData',
          'updateTimelineShareData',
        ],
        openTagList: [],
      })
      wx.ready(() => {
        resolve()
      })
    })
  }
  setShareInfo = async (shareData: ShareInfo, flag = true) => {
    if (!isWxBrowser || appConfig.isXcx) { return }
    await initConfig()
    // 除商品详情页和资讯详情页的 自定义页面>推广设置>兜底规则
    if (flag) {
      console.log('appConfig.appId', appConfig.appId, appConfig)
      if (!appConfig.appId) { return }
      const { data } = await userStore.wxGetShareDataInfo(shareData?.id as string)
      wx.ready(function () {
        // 自定义“分享给朋友”及“分享到QQ”
        wx.updateAppMessageShareData({
          title: data.value.shareTitle || `${appConfig.productName}-${document.title}`, // 分享标题
          desc: data.value.shareDescription || document.title, // 分享描述
          link: shareData?.url ? shareData.url : location.href, // 分享链接
          imgUrl: data.value.shareImg || appConfig?.logoImg, // 分享图标
        })
        // 自定义“分享到朋友圈”及“分享到QQ空间”
        wx.updateTimelineShareData({
          title: data.value.shareTitle || document.title, // 分享标题
          link: shareData?.url ? shareData.url : location.href, // 分享链接
          imgUrl: data.value.shareImg || appConfig?.logoImg, // 分享图标
        })
      })
    } else {
      console.log('setShareInfo flag: false', shareData)
      wx.ready(function () {
        // 自定义“分享给朋友”及“分享到QQ”
        wx.updateAppMessageShareData({
          title: shareData.title, // 分享标题
          desc: shareData.desc ? shareData.desc : '', // 分享描述
          link: shareData?.url ? shareData.url : location.href, // 分享链接
          imgUrl: shareData.shareImg, // 分享图标
          success: function () {
            console.log('updateAppMessageShareData success')
          },
          fail: function () {
            console.log('updateAppMessageShareData fail')
          },
        })
        // 自定义“分享到朋友圈”及“分享到QQ空间”
        wx.updateTimelineShareData({
          title: shareData.title, // 分享标题
          link: shareData?.url ? shareData.url : location.href, // 分享链接
          imgUrl: shareData.shareImg, // 分享图标
        })
      })
    }
  }
  return {
    setShareInfo
  }
}
