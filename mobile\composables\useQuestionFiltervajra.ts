// [AI GEN] 封装过滤金刚区数据
export const useQuestionFilter = () => {
  const { questionBankApi } = useApi()

  const moduleList = ref<Array<any>>([])

  const handleFilterModules = async (params: any) => {
    try {
      const { data, error } = await questionBankApi.getFixedModuleManage({ ...params })
      if (!error.value) {
        moduleList.value = data.value.list || []
      }
    } catch (error) {
      console.error('Error filtering modules:', error)
      throw error
    }
  }

  /**
   * 筛选数组中的模块
   * @param {Array} modules - 模块数组
   */
  const filterModules = (modules: Array<any>) => {
    return modules.filter(module => module.skipId === 1).map((module: any) => {
      return {
        moduleManageId: module.questionModuleId,
        ...module,
      }
    })
  }

  return {
    moduleList,
    filterModules,
    handleFilterModules,
  }
}
