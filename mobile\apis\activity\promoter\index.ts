import type {
  FetchActivityDetailDTO,
  FetchApplyActivityDetailDTO,
  FetchSubmitApplyParams,
  FetchUserPromoterInfoDTO,
  FetchIncomeListParams,
  FetchCouponListParams,
  FetchGoodsListParams
} from './types'

const serveName = 'kukecoupon'
const client = 'wap'

enum promoterApiEnum {
  fetchActivityDetail = `/${serveName}/${client}/extensionOfficer/detail`,
  fetchApplyActivityDetail = `/${serveName}/${client}/extensionOfficer/applyProt`,
  fetchSubmitApply = `/${serveName}/${client}/extensionOfficer/applySubmitProt`,
  fetchUserPromoterInfo= `/${serveName}/${client}/extensionOfficer/extensionDetailProt`,
  fetchIncomeList= `/${serveName}/${client}/extensionOfficer/myIncomeList`,
  fetchCouponList= `/${serveName}/${client}/extensionOfficer/extensionCouponList`,
  fetchGoodsList= `/${serveName}/${client}/extensionOfficer/extensionGoodsList`,
  wechatMiniProgramShareDetail= `/${serveName}/${client}/extensionOfficer/wechatMiniProgramShareDetail`,
}

/**
 * 请求活动详情
 * @param body
 * @returns
 */
export async function fetchActivityDetail (body: { id: string }) {
  return useHttp<FetchActivityDetailDTO>(promoterApiEnum.fetchActivityDetail, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 请求申请活动详情
 * @param body
 * @returns
 */
export async function fetchApplyActivityDetail (body: { id: string }) {
  return useHttp<FetchApplyActivityDetailDTO>(promoterApiEnum.fetchApplyActivityDetail, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 请求提交 申请
 * @param body
 * @returns
 */
export async function fetchSubmitApply (body: FetchSubmitApplyParams) {
  return useHttp<any>(promoterApiEnum.fetchSubmitApply, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 获取用户推广信息
 */
export async function fetchUserPromoterInfo () {
  return useHttp<FetchUserPromoterInfoDTO>(promoterApiEnum.fetchUserPromoterInfo, {
    method: 'post',
    transform: res => res.data,
  })
}

/**
 *
 * @returns 收益列表
 */
export async function fetchIncomeList (body: FetchIncomeListParams) {
  return useHttp<IResponsePage<any>>(promoterApiEnum.fetchIncomeList, {
    method: 'post',
    body,
    watch: false,
    // transform: res => res.data,
  })
}

/**
 * 推广优惠券列表
 * @param body
 * @returns
 */
export async function fetchCouponList (body: FetchCouponListParams) {
  return useHttp<any>(promoterApiEnum.fetchCouponList, {
    method: 'post',
    body,
    watch: false,
    transform: res => res.data,
  })
}

/**
 * 推广商品列表
 * @param body
 * @returns
 */

export async function fetchGoodsList (body: FetchGoodsListParams) {
  return useHttp<any>(promoterApiEnum.fetchGoodsList, {
    method: 'post',
    body,
    watch: false,
    transform: res => res.data,
  })
}

/**
 * 小程序分享详情
 * @param body
 * @returns
 */

export async function wechatMiniProgramShareDetail (body: {extensionOfficerId:string}) {
  return useHttp<any>(promoterApiEnum.wechatMiniProgramShareDetail, {
    method: 'post',
    body,
    watch: false,
    transform: res => res.data,
  })
}
