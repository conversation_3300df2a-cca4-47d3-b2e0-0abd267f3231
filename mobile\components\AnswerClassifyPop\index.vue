<template>
  <div class="answer-classify-wrap">
    <KKPopup
      ref="answerClassifyPopRef"
      :closeable="true"
      class="customer_popup"
      position="bottom"
      title="请选择要提问的分类"
      box-padding="0"
      :close-icon-size="32"
      :max-height="'1000px'"
      @close="onClose"
    >
      <div class="answer-classify-content">
        <div class="classify-list">
          <div class="top-title">
            <div class="title-left">
              专业分类
            </div>
          </div>
          <div class="list-wrap">
            <div
              v-for="item in cateList"
              :key="item.cateId"
              class="classify-item line-clamp-1"
              :class="info.cateId === item.cateId ? 'item-active':''"
              @click="handleClassify(item)"
            >
              {{ item.cateName }}
            </div>
          </div>
        </div>

        <!-- 标签 -->

        <Tags :classify-list="classifyList" :cate-id="propId" @change="handleActive" />
      </div>

      <!-- 底部按钮 -->
      <AnswerBtn :show-person="true" @showbtnPerson="handleManual" />
    </KKPopup>
  </div>
</template>
<script setup lang="ts">

// components
import Tags from './tags.vue'
import type { ChoiceTag, KeyStringType, RenderTagItem, LabelEquity, LabelValueVO, TagItem } from '~/apis/answer/types'

const { appId } = useAppConfig()

const { answerApi } = useApi()

const userInfo = useCookie<any | null>('user')
const info = ref<{
  cateId: string,
  cateName: string
}>({
  cateId: '',
  cateName: ''
})

/**
 * @params
 * 标签值转化
 */

const changeTag = (el: string) => {
  if (el === 'subjectType') {
    return 'subject'
  } else if (el === 'directionType') {
    return 'direction'
  } else {
    return el
  }
}

/**
 * 获取分类列表
 */
// 答疑分类标签
const tagList = ref<string[]>([])
// 是否已加载完分类列表数据
const isLoadCategory = ref(false)

const propId = ref('')
const getCateGoryList = async () => {
  const { error, data } = await answerApi.getEquityLabelList({
    id: info.value.cateId
  })
  if (!error.value) {
    classifyList.value = allTagReform(data.value)
    classifyList.value.forEach((item:TagItem) => {
      item.value.sort((a, b) => a.dataName.length - b.dataName.length)
    })
    propId.value = info.value.cateId
    tagList.value = classifyList.value.map(item => item.key)
    console.log(classifyList.value, 'data.valueclassifyList')
  }
  isLoadCategory.value = true
}
const allLengthOne = computed(() => {
  return classifyList.value?.every(item => item.length === 1)
})

const classifyList = ref<RenderTagItem[]>([])

// 数据重组为[ { title:'地区', key:'region', value:[] }]的形式

const allTagReform = (list: LabelEquity[]): RenderTagItem[] => {
  const arr = list.map((item: LabelEquity) => {
    return {
      title: item.labelName,
      key: item.alias,
      showFoldButton: false,
      value: item.labelValueList.map((el: LabelValueVO) => {
        return {
          status: 0,
          usedId: el.usedId,
          dataName: el.name
        }
      }),
      fold: false,
      length: item.labelValueList.length
    }
  })
  return arr
}

//  region:地区   academicSection:学段    direction:方向   subject:科目   examFormat:考试形式
const tagType:{[key: string]: string} = {
  region: '地区',
  academicSection: '学段',
  directionType: '方向',
  subjectType: '科目',
  examFormat: '考试形式',
  classroomType: '班型'
}

// 弹窗
const answerClassifyPopRef = ref(null)

const onClose = () => {
  console.log(classifyList.value, '关闭')
  if (classifyList.value.length) {
    classifyList.value.flatMap(el => el.value ?? []).forEach((item) => {
      item.status = 0
    })
  }
  choiceTag.value = []
  tagList.value = []
  isLoadCategory.value = false

  // answerClassifyPopRef.value?.hide()
}

/**
 * 获取标签值
 * 进行重组
 */

const choiceTag = ref<KeyStringType[]>([])

const handleActive = (val:ChoiceTag) => {
  const { usedId, dataName, key } = val

  const changeKey = changeTag(key)

  const obj = {
    changeKey,
    [`${changeKey}Id`]: usedId,
    [`${changeKey}Name`]: dataName
  }
  choiceTag.value.push(obj)
  console.log(choiceTag.value, 'choiceTag.value0000')

  // 判断choiceTag.value数组是否为空
  //  空 直接push obj
  // 不为空  查找changekey
  // 存在相同的 替换
  // 不存在  直接push
  if (choiceTag.value.length) {
    const map = new Map()

    // 将现有数组转换为 Map
    choiceTag.value.forEach((item) => {
      map.set(item.changeKey, item)
    })

    // 检查是否已存在相同的项
    if (map.has(changeKey)) {
      console.log(1111)
      map.set(changeKey, obj)
    } else {
      console.log(2222)
      map.set(changeKey, obj)
    }

    // 将 Map 转换回数组
    choiceTag.value = Array.from(map.values())
  } else {
    console.log(333)
    choiceTag.value.push(obj)
  }

  console.log(choiceTag.value, 'val', obj)
}

// 答疑没有勾选专业分类的标签  跳转人工答疑
// 勾选了专业分类的标签
//  1. 该商品 对应的标签项只有一个值  直接跳转人工答疑
//  2. 该商品 对应的标签项有多个值  弹窗
//  3. 该商品 没有关联标签  弹窗展示 默认组织对应的标签值
//  4. 该商品 已选择标签项 不满足 答疑专业分类的标签项  多余的展示默认组织的标签值
// 比如 答疑分类 专升本  勾选了  地区 科目  方向  考试形式
// 该商品  只选择了  地区  科目 ；则 方向 考试形式 使用默认组织的值
const isSingle = computed(() => {
  const cateSingle = cateList.value.length === 1
  if (classifyList.value.length) {
    return cateSingle && allLengthOne.value
  } else {
    return cateSingle
  }
})

// const isJump = computed(() => {
//   return isSingle.value
// })

/**
 * 整理只有单个选项值的标签
 */

/**
 * 处理标签值
 * 转化成 人工答疑参数
 */
const changeManualParams = (list: KeyStringType[]) => {
  const params:KeyStringType = {}
  params.cateId = info.value.cateId
  params.cateName = info.value.cateName
  list.forEach((el) => {
    // 删除changeKey字段
    delete el.changeKey
    for (const key in el) {
      if (Object.prototype.hasOwnProperty.call(el, key)) {
        let paramsKey = key
        switch (key) {
          case 'subjectTypeId':
            paramsKey = 'subjectId'
            break
          case 'subjectTypeName':
            paramsKey = 'subjectName'
            break
          case 'directionTypeId':
            paramsKey = 'directionId'
            break
          case 'directionTypeName':
            paramsKey = 'directionName'
            break
          case 'classroomTypeId':
            paramsKey = 'classTypeId'
            break
          case 'classroomTypeName':
            paramsKey = 'classTypeName'
            break
        }
        const element = el[key]
        params[paramsKey] = element
      }
    }
  })

  console.log(params)

  return params
}

// 拍照搜题id
const photoId = ref('')

const photoParams = ref('')

interface PhotoType {
  phontoRequestId: string,
  params: string
}

/**
 *
 * @param val
 * 是否是拍照搜题
 */

const setPhoto = (val: PhotoType) => {
  photoId.value = val.phontoRequestId
  photoParams.value = val.params
}

/**
 * 如果是拍搜
 * 调用 人工答疑上传接口
 * 否则直接跳转
 */

const handleManualAnswer = async (cateParams: KeyStringType) => {
  const body = {
    problemDescription: photoParams.value,
    // cateId: info.value.cateId,
    // cateName: info.value.cateName,
    productId: appId as any,
    userId: userInfo.value?.userId,
    phone: userInfo.value?.mobile,
    userName: userInfo.value.userName,
    phontoRequestId: photoId.value,
    requestWay: 3,
    // classTypeId: useCookie('classTypeId').value || '',
    // classTypeName: useCookie('classTypeName').value || ''
  }
  Object.assign(body, cateParams)
  const { data } = await answerApi.firstSendAnswer(body)
  return data.value.data
}
/**
 * 处理跳转逻辑
 */
const jumpParams = ref<KeyStringType>()
const handleJump = async () => {
  const { error } = await answerApi.getUserEquityVerifyPort({
    cateId: jumpParams.value?.cateId
  })
  if (error.value) {
    return
  }
  if (photoId.value) {
    const requestId = await handleManualAnswer(jumpParams.value as KeyStringType)
    navigateTo({
      path: '/answerQuestion/manual-answer',
      query: { requestId }
    })
  } else {
    navigateTo({
      path: '/answerQuestion/manual-answer',
      query: { params: encodeURIComponent(JSON.stringify(jumpParams.value)) }
    })
  }
}

/**
 * 显示弹窗
 * 符合条件直接跳转
 */

const show = async () => {
  await getEquityCateList()
  console.log(11111, 'show', isLoadCategory.value)

  // 如果分类列表未加载完，先禁用发起人工答疑按钮
  console.log(isSingle.value, 'isSingle.valueshow')
  if (isSingle.value) {
    if (!isLoadCategory.value) { return }
    let obj: KeyStringType
    classifyList.value.forEach((el) => {
      obj = {
        [`${el.key}Id`]: el.value[0].usedId,
        [`${el.key}Name`]: el.value[0].dataName
      }
      choiceTag.value.push(obj)
    })
    jumpParams.value = changeManualParams(choiceTag.value)
    handleJump()
  } else {
    answerClassifyPopRef.value?.showPopup()
  }
}

/**
 * 已选择的key
 */
const selectedKeys = (el: string) => {
  if (el === 'subject') {
    return 'subjectType'
  } else if (el === 'direction') {
    return 'directionType'
  } else {
    return el
  }
}

/**
 *
 * 发起人工答疑
 */

const handleManual = () => {
  if (!isLoadCategory.value) { return }
  const choiceArr = choiceTag.value.map(item => selectedKeys(item.changeKey))
  console.log(tagType, choiceArr, tagList.value, '点击按钮')
  const filterNoChoice = tagList.value.filter(item => !choiceArr.includes(item))
  const classifyName = tagType[filterNoChoice[0]]

  // 是否 选择所有分类标签
  if (tagList.value.length === choiceTag.value.length) {
    jumpParams.value = changeManualParams(choiceTag.value)

    handleJump()
  } else {
    Message(`请先选择${classifyName}哦`)
  }
}
interface CateType {
  cateId: string,
  cateName: string
}
/**
 * 选择专业分类 TODO 选择专业分类后展示其他标签值
 */

const handleClassify = async (item:CateType) => {
  if (cateList.value.length === 1) {
    return
  }
  info.value = item
  console.log(info.value)
  classifyList.value = []
  choiceTag.value = []
  tagList.value = []
  propId.value = ''
  await getCateGoryList()
}

const cateList = ref<CateType[]>([])
/**
 *
 * 获取专业分类
 *
 */

const getEquityCateList = async () => {
  const { data, error } = await answerApi.getEquityCateList({})
  if (!error.value) {
    console.log(data.value, 'data.value')
    if (data.value?.list.length) {
      cateList.value = data.value?.list
      info.value.cateId = data.value?.list[0].cateId
      info.value.cateName = data.value?.list[0].cateName
      await getCateGoryList()
    }
  }
}

onMounted(async () => {
  await nextTick()

  // await getEquityCateList()
})

defineExpose({
  show,
  setPhoto
})

</script>

<style scoped lang="scss">
.answer-classify-wrap {
  z-index: 110;

    .answer-classify-content{
        width: 100%;
        height: calc(1000px -  env(safe-area-inset-bottom) - 240px);
        overflow-y: auto;
        padding:0 32px;

        .classify-list{
            margin-bottom: 28px;

            .top-title{
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 24px;

                .title-left{
                    font-family: PingFang SC;
                    font-weight: 500;
                    font-size: 28px;
                    color: #111111;
                    line-height: 34px;
                    text-align: left;
                    font-style: normal;
                    text-transform: none;
                }

                .title-right{
                    display: flex;
                    align-items: center;
                    color: #666666;
                    font-size: 24px;
                }
            }

            .list-wrap{
                display: flex;
                flex-wrap: wrap;

                .classify-item{
                    padding: 10px 12px;
                    margin-bottom: 20px;
                    background: #F7F7F7;
                    border-radius: 12px;
                    margin-right: 20px;
                    border: 2px solid #f7f7f7;
                    color: #666666;
                    font-size: 26px;
                    font-weight: 400;
                    &:nth-child(4n){
                        margin-right: 0;
                    }
                }
                .item-active{
                    font-weight: 500;
                    @apply text-brand border-brand bg-brand/5;
                }
            }

            .height-fold{
                height: 176px;
                overflow-y: hidden;
            }

        }
    }
}
</style>
