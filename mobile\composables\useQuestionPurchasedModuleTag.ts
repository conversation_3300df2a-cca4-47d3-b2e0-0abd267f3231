export const useQuestionPurchasedModuleTag = () => {
  const { tagApi } = useApi()
  const { purchasedTag } = useQuestionBankMultipleTag()

  const getDefaultTag = async () => {
    try {
      const { data, error } = await tagApi.getModuleDefaultTag(purchasedTag.value)
      if (error.value) { return false }
      if (data.value) {
        const { list } = data.value
        return handleCreateTagBodyFromCheckList({}, list)
      }
    } catch (error) {
      console.warn(error)
    }
  }

  const handleCreateTagBodyFromCheckList = (body: any, list: any[]) => {
    const validFields = ['subjectType', 'region', 'directionType', 'academicSection', 'examFormat']

    list.forEach((item: any) => {
      if (validFields.includes(item.field)) {
        body[item.field] = item.id
      }
    })

    return body
  }

  return {
    purchasedTag,
    getDefaultTag,
    handleCreateTagBodyFromCheckList,
  }
}
