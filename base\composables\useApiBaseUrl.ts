import { createEncryptionByEncryptType } from '@kukefe/kkutils'
import { os } from './common'

/**
 *
 * @function useApiBaseUrl
 * @desc 根据不同环境抛出当前API请求 baseUrl
 */
export default () => {
  const {
    baseUrlServer,
    public: {
      version,
      baseUrlClient,
    },
  } = useRuntimeConfig()
  const baseURL = ref()
  baseURL.value = baseUrlServer || baseUrlClient
  if (process.server && process.env.KKC_BASE_URL_CUSTOM) {
    baseURL.value = process.env.KKC_BASE_URL_CUSTOM
  }
  // TODO state
  const {
    appId,
    requestAppid = '',
    requestAppkey = '',
    requestEncryptKey = '',
    lesseeId, isXcx, orgId, isKukeCloudAppWebview, isPc, isMobile
  } = useAppConfig()

  const headers = {
    'content-type': 'application/json',
    accept: 'application/json',
    // 'kk-os': '',
    // 'kk-modle': '',
    'kk-version': version,
    // TODO /utils 目录
    'kk-request-id': `${new Date().getTime()}${buildUUID()}`,
    'kk-platform': appId,
  } as unknown as Record<string, string>

  if (isKukeCloudAppWebview) {
    headers['kk-modle'] = 'kukeCloudAppWebview'
  }
  /**
   *
   * @param "kk-terminal-type" { string }  - 客户端的类型
   * @param "kk-from" { string }  - 请求来源（请求客户端类型）
   * @param "kk-platform" { string }  - 平台
   * @param "kk-os" { string? }  - 客户端系统版本 ios12.2/pc和手机站传浏览器的版本号
   * @param "kk-modle" { string? }  - 客户端型号 iphone11/huawei mate10 /（PC端传浏览器的版本号）/h5能获取到机型就传机型获取不到就传手机所属系统
   *
   */
  if (os?.isAndroid) {
    headers['kk-terminal-type'] = 'android'
  } else if (os?.isPhone) {
    headers['kk-terminal-type'] = 'iphone'
  } else if (os?.isTablet) {
    headers['kk-terminal-type'] = 'ipad'
  } else if (os?.isPc) {
    headers['kk-terminal-type'] = 'pc'
  } else if (os?.isHarmonyOS) {
    headers['kk-terminal-type'] = 'harmony'
  }

  if (isMobile) {
    if (isXcx) {
      headers['kk-from'] = 'wechat-app'
    } else {
      headers['kk-from'] = 'h5'
    }
  } else if (isPc) {
    headers['kk-from'] = 'web'
  } else {
    console.error('kk-from 配置错误')
  }
  if (lesseeId) {
    headers['tenant-id'] = lesseeId
  }
  if (orgId) {
    headers['org-id'] = orgId
  }
  if (appId) {
    headers['kk-platform'] = appId
    // headers['business-type'] = appId
  } else {
    headers['kk-platform'] = '1'
  }

  // 加密验签
  /**
 * @description: kkweb post 请求加签加密
 * @beta
 * @param appId appId
 * @param appKey appKey
 * @return {function} 按照指定规则生成的请求体
 */
  const { encrypt, decrypt, postBodyEncryption } = createEncryptionByEncryptType(requestAppid, requestAppkey, requestEncryptKey)
  return {
    baseURL,
    headers,
    encrypt,
    decrypt,
    postBodyEncryption
  }
}
