<template>
  <div class="kkc-empty-login">
    <img src="@/assets/icon__data-empty.png" class="h-[280px]" alt="">
    <div class="flex flex-col items-center mt-[24px]">
      <p class="text-[28px] text-[#999]">
        {{ text }}
      </p>
    </div>
    <div v-if="isShowLogin" class="mt-[48px]" @click.prevent="handleError">
      <a href="/pages/login" class="kkc-empty-login__login block text-[#fff] text-center h-[72px] leading-[72px] px-[24px] rounded-[8px] text-[26px]">{{ btnText }}</a>
    </div>
  </div>
</template>
<script setup lang="ts">
import { useUserStore } from '~/stores/user.store'

const userStore = useUserStore()

interface Props {
  text: string;
  btnText?: string;
}

withDefaults(defineProps<Props>(), {
  text: '资源不存在',
  btnText: '如果您已购买，请登录',
})

const isShowLogin = computed(() => {
  return !userStore.isLogin
})

const handleError = () => {
  userStore.isLoginFn()
}
</script>
<style lang="scss" scoped>
.kkc-empty-login {
  display: flex;
  min-height: 72vh;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  &__login {
    background-color: var(--kkc-brand, #eb2330);
  }
}
</style>
