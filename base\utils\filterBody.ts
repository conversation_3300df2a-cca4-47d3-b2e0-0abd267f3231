// 目前仅需特殊处理五个标签项目
const FILTER_KEYS: string[] = ['subjectType', 'academicSection', 'directionType', 'examFormat', 'region']

// 重新赋值值为-1的字段
function filterQuestionBankBody (body: {[key:string]:any}) {
  // 重新赋值对象为-1的字段为0
  FILTER_KEYS.forEach((key) => {
    if (
      body[key] === '-1' ||
      body[key] === -1 ||
      body[key] === null ||
      body[key] === undefined ||
      body[key] === '' ||
      body[key] === 'undefined' ||
      (typeof body[key] === 'number' && isNaN(body[key]))
    ) {
      body[key] = 0
    }
  })

  return body
}

export default filterQuestionBankBody
