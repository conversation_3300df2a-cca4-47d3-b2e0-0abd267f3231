<template>
  <div class="share_poster_comp">
    <ClientOnly>
      <KKPopup
        v-model:show="visible"
        title="分享图片"
        :closeable="true"
        position="bottom"
      >
        <Swiper
          ref="swiperRef"
          :initial-slide="0"
          :centered-slides="true"
          :slides-per-view="1.2"
          :effect="'coverflow'"
          :coverflow-effect="coverflowEffect"
          :pagination="pagination"
          @slideChange="onSwiperChange"
        >
          <swiper-slide
            v-for="(item,index) in sharePosterLists"
            :key="index"
            :class="[index+1 === activeDotInd && `swiper_slide_${activeDotInd}`]"
          >
            <!-- 小程序下载海报使用图片 -->
            <img
              v-if="xcxSavePoster[index]"
              :src="xcxSavePoster[index]"
              class="saveImg"
              alt="分享图片"
            >
            <!-- 模板主图 -->
            <img class="share_template" :src="item.template" alt="分享图片">
            <!-- 第一个模板内容 -->
            <div v-if="index === 0" class="template0_content">
              <img class="goods_img" :src="item.goodsImg" alt="商品主图">
              <div class="title">
                {{ handleCutText(item.title) }}
              </div>
            </div>

            <!-- 第二个模板内容 -->
            <div v-if="index === 1" class="template1_content">
              <div class="user_info_wrapper">
                <img class="user_avatar" :src="item.avatar" alt="用户头像">
                <div class="user_info">
                  <div class="user_name">
                    {{ item.userName }}
                  </div>
                  <div class="invite">
                    邀请你一起学习
                  </div>
                </div>
              </div>
              <img class="goods_img" :src="item.goodsImg" alt="商品主图">
              <div class="title">
                {{ handleCutText(item.title,31) }}
              </div>
            </div>

            <!-- 第三个模板内容 -->
            <div v-if="index === 2" class="template2_content">
              <div class="title">
                {{ handleCutText(item.title) }}
              </div>
              <img class="goods_img" :src="item.goodsImg" alt="商品主图">
            </div>

            <!-- 分享二维码 -->
            <!-- TODO 微信小程序分享商品/资讯详情页，应展示商品/资讯详情页的二维码，而不是小程序首页的二维码 -->
            <!--         // TODO 详情页小程序分享 /kukemarketing/xxx/pageMaster/getURLLink -->
            <img
              :class="['w-[210px]', 'h-[210px]', `qr_code${index}`]"
              :src="item.qrImgLink"
              alt="分享二维码"
            >
            <!-- :src="isXcx ? miniImage : item.qrImgLink" -->

            <!-- 产品线 logo -->
            <img
              :class="['logo_img', `logo_img${index}`]"
              :src="logo2"
              alt="产品线logo"
            >
          </swiper-slide>

          <!-- 分页器 -->
          <div class="swiper-pagination pagination_dots">
            <div
              v-for="i in [...Array(4).keys()].slice(1)"
              :key="i"
              class="dot_item"
              :class="{ 'dot_item_active': activeDotInd === i }"
            />
          </div>

          <!-- 保存按钮 -->
          <div
            v-if="isXcx || isWxBrowser"
            :class="['xcx_save_share_text',isIOS && isWxBrowser ? 'mt-[10px]' : 'mt-[34px]']"
          >
            长按图片保存至相册
          </div>
          <div v-else class="save_share_btn" @click="handleDownload">
            保存至相册
          </div>
        </swiper>
      </KKPopup>
    </ClientOnly>
  </div>
</template>

<script setup lang='ts'>
import { Swiper, SwiperSlide } from 'swiper/vue'
import { toDataURL } from 'qrcode'
import html2canvas from 'html2canvas'
import SwiperCore, { EffectCoverflow, Pagination } from 'swiper'
import { base64toBlob } from '../../../base/utils/file'
import CourseCover from '../../assets/images/empty/course-cover.png'
import ShareTemplate1 from '@/assets/images/course/share_template1.png'
import ShareTemplate2 from '@/assets/images/course/share_template2.png'
import ShareTemplate3 from '@/assets/images/course/share_template3.png'
import 'swiper/css'
import { useUserStore } from '~/stores/user.store'
import { useCourseStore } from '~/stores/course.store'
import { GoodsType } from '@/apis/course/types'
import { getURLLink } from '~/apis/course'

const { commonApi } = useApi()
const { isXcx, logo2, miniImage } = useAppConfig()
const userStore = useUserStore()
const courseStore = useCourseStore()
SwiperCore.use([EffectCoverflow, Pagination])

interface SkuTitleImg {
  goodsTitle: string,
  itemImg: string
}
interface Props {
  skuTitleImg: SkuTitleImg,
}
const props = withDefaults(defineProps<Props>(), {
  skuTitleImg: () => { return {} },
})
interface SharePosterListItem {
  template: string;
  qrImgLink: string;
  goodsImg: string;
  title: string;
  userName?: string;
  avatar?: string;
}
const defaultAvatar = 'https://oss.kuke99.com/kukecloud/static/common/default-avatar.png'
// 分享海报配置
const sharePosterLists = ref<SharePosterListItem[]>([
  {
    template: ShareTemplate1,
    qrImgLink: '',
    goodsImg: '',
    title: '',
  },
  {
    template: ShareTemplate2,
    qrImgLink: '',
    goodsImg: '',
    title: '',
    userName: userStore?.getUserInfo?.userName,
    avatar: userStore?.getUserInfo?.photo || defaultAvatar,

  },
  {
    template: ShareTemplate3,
    qrImgLink: '',
    goodsImg: '',
    title: '',
  },
])
// coverflow 流配置
const coverflowEffect = reactive({
  rotate: 0, // slide做3d旋转时Y轴的旋转角度。默认50。
  stretch: -40, // 每个slide之间的拉伸值，越大slide靠得越紧。
  depth: 100, // slide的位置深度。值越大z轴距离越远，看起来越小。
  modifier: 1, // depth和rotate和stretch的倍率，相当于depth*modifier、rotate*modifier、stretch*modifier，值越大这三个参数的效果越明显。默认1。
  slideShadows: false // 开启slide阴影。默认 true。
})
// 当前选中的分页索引
const activeDotInd = ref<number>(1)
// 分液器配置
const pagination = reactive({
  // el是分页器的类名，当然你也可以用这个类名在style中写样式这样就可以自定义分页器样式（这是个小技巧）
  el: '.pagination_dots',
  // 这个属性是将分页器样式设置为自定义
  type: 'custom',
  // 用于自定义分页器的函数，vue3在这里面写东西很麻烦，我只写了一个让分页器跟随轮播图切换的效果
  renderCustom: function (swiper: any, current: number) {
    console.log('🚀 ~ swiper:', swiper)
    activeDotInd.value = current
  }
})

// 初始化二维码 & 处理海报显示商品数据
const initPoster = async () => {
  if (isXcx) {
    await handleShareLinkByXcx()
  }
  sharePosterLists.value =
    await Promise.all(
      sharePosterLists.value.map(async (i): Promise<SharePosterListItem> => {
        return {
          ...i,
          goodsImg: props.skuTitleImg.itemImg || CourseCover,
          title: props.skuTitleImg.goodsTitle,
          qrImgLink: await createQrCode(),
        }
      })
    )
}

const swiper = ref()
const visible = ref<boolean>(false)
// 默认展示第一个海报
watch(() => visible.value, (v) => {
  if (v) {
    nextTick(async () => {
      if (!xcxSavePoster.value[0]) {
        Loading(true, '加载中', true)
      }
      await initPoster()
      swiper.value = document.querySelector('.swiper')?.swiper
      swiper.value.slideTo(0, 1000, false)
      if (isXcx || isWxBrowser) {
        setTimeout(() => {
          drawImg(0)
        }, 100)
      }
      if (!xcxSavePoster.value[0]) {
        setTimeout(() => {
          Loading(false)
        }, 800)
      }
    })
  }
})

// 生成二维码
const createQrCode = async (otherOptions?: any) => {
  try {
    const options = {
      errorCorrectionLevel: 'H',
      width: otherOptions?.width || 100,
      margin: otherOptions?.margin || 2,
      color: {
        dark: '#000',
        light: '#FAFAFA',
      },
    }
    let link = ''
    if (isXcx) {
      // link = await handleShareLinkByXcx()
      link = urlLink.value || (miniImage as string)
    } else {
      link = await handleShareLink()
    }
    const qrImgLin: Promise<string> = await toDataURL(link, options)
    return qrImgLin!
  } catch (err) {
    console.error(err)
  }
}

// 处理分享二维码链接
const handleShareLink = () => {
  const { goodsType } = courseStore.courseInfo
  let href = window.location.href
  const origin = href.split('?')[0]
  // 分享的非多规格商品或者多规格 spu 商品链接
  const flag = (goodsType !== GoodsType.MultiSpecification) ||
    (goodsType === GoodsType.MultiSpecification && !courseStore.isSku)
  if (flag) {
    href = `${origin}`
  } else if (courseStore.isSku && courseStore.courseSkuId) {
    // 分享的多规格 sku 商品
    href = `${origin}?courseSpecId=${courseStore.courseSkuId}`
  }
  return href
}
const urlLink = ref('')
const { public: { BUILD_ENV } } = useRuntimeConfig()

const handleShareLinkByXcx = async () => {
  const body = {
    path: '/pages/kkWebView/index',
    query: 'path=/',
    /** 小程序环境
   * 正式版为 release
   * 体验版为 trial
   * 开发版为 develop
   * */
    envVersion: 'release',
  }
  if (!['PROD', 'LOCAL_PROD'].includes(BUILD_ENV)) {
    body.envVersion = 'trial'
  } else {
    body.envVersion = 'release'
  }
  const { goodsType } = courseStore.courseInfo
  const pathname = window?.location?.pathname || '/'
  // 分享的非多规格商品或者多规格 spu 商品链接
  const flag = (goodsType !== GoodsType.MultiSpecification) ||
    (goodsType === GoodsType.MultiSpecification && !courseStore.isSku)
  if (flag) {
    body.query = `path=${pathname}`
  } else if (courseStore.isSku && courseStore.courseSkuId) {
    const _path = `${pathname}?courseSpecId=${courseStore.courseSkuId}`
    // 分享的多规格 sku 商品
    body.query = `path=${encodeURIComponent(_path)}`
  }
  const { data } = await getURLLink(body)
  urlLink.value = data.value?.urlLink
}

// 切换图片
const onSwiperChange = (swiper: any) => {
  setTimeout(() => {
    if (!xcxSavePoster.value[swiper.activeIndex]) {
      Loading(true, '加载中', true)
    }
    // 小程序重新生成下载的海报
    if (isXcx || isWxBrowser) {
      const activeIndex = swiper.activeIndex
      drawImg(activeIndex)
    }

    if (!xcxSavePoster.value[swiper.activeIndex]) {
      setTimeout(() => {
        Loading(false)
      }, 800)
    }
  })
}

// wap 下载海报到本地
const handleDownload = () => {
  drawImg()
}

// 生成二维码海报
const xcxSavePoster = ref<string[]>([])
const drawImg = (activeIndex?: number) => {
  const activeSlideClass = `.swiper_slide_${activeDotInd.value}`
  const activeSlideEl = document.querySelector(activeSlideClass)
  if (!activeSlideEl) { return false }
  html2canvas(activeSlideEl,
    { scale: 4, useCORS: true })
    .then((canvas:any) => {
      const base64 = canvas.toDataURL('image/jpg', 1)
      // 将生成的小程序海报赋值到对应的位置
      if (base64 && (isXcx || isWxBrowser)) {
        xcxSavePoster.value[activeIndex] = base64 as string
        return
      }
      // 兼容uc浏览器
      if (/UCBrowser/.test(navigator.userAgent)) {
        const file = base642File(base64, 'png')
        handleFileUpload(file)
      } else {
        downloadBase64(base64)
      }
    }).catch((error: any) => {
      Message('图片保存失败，请重试~')
      console.log(error)
    })
}
const ossInstance = ref()
// 上传海报图片
const handleFileUpload = async (file: File) => {
  try {
    const { data } = await commonApi.fetchOSSConfig()
    ossInstance.value = await initOSS(data || {})
    const result = await unref(ossInstance).put(
      generateFilename(file, 'img/kukecloud/wap/course/poster'),
      file
    ) // 将图片上传到阿里云OSS并返回上传结果给客户端
    const posterUrl = result.url
    downloadFile(posterUrl, '海报分享')
  } catch (e) {
    Message('图片保存失败，请重试~')
  }
}

// 下载 base64 图片
const downloadBase64 = async (str: string) => {
  // base64转blob格式
  const blob = base64toBlob(str)
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.download = '海报分享.png'
  a.href = url
  const e = new MouseEvent('click')
  a.dispatchEvent(e)
  URL.revokeObjectURL(url)
}

// 处理截取文字（canvas 截取的图片无法识别css超出隐藏显示...的相关css 需要用 js 代替）
const handleCutText = (title: string, num = 29) => {
  if (title.length > num) {
    return title.slice(0, num) + '...'
  }
  return title
}

onMounted(() => {
  initPoster()
})

defineExpose({
  visible
})
</script>

<style lang='scss' scoped>
.share_poster_comp {
  :deep() {
    .popup-box-title {
      margin-top: 11px !important;
    }
    .van-popup__close-icon--top-right {
      --van-popup-close-icon-margin: 43px;
    }
    .van-popup__close-icon {
      color: #666;
      --van-popup-close-icon-size: 40px;
    }
  }
  // 分页样式
  .pagination_dots {
    margin: 0 auto;
    @apply flex justify-between items-center px-[6px] w-[72px] h-[24px]
    bg-black/[.4] rounded-[12px] mt-[32px]
  }
  .dot_item {
    @apply w-[9px] h-[9px] rounded-[50%] bg-white/[.8]
  }
  .dot_item_active {
    @apply w-[18px] h-[9px] rounded-[8px] bg-[#fff]
  }
  // 分享模板样式
  .share_template {
    @apply w-[600px] h-[950px] relative
  }
  .title {
    @apply text-[32px] leading-[40px] w-[510px]
  }
  .template0_content {
    @apply absolute top-[167px] left-[42px] right-[42px];
    .goods_img {
      @apply w-[516px] h-[344px] mb-[24px] rounded-[24px]
    }
  }
  .template1_content {
    @apply absolute top-[41px] left-[20px] right-[20px];
    .user_info_wrapper {
      @apply flex mb-[10px]
    }
    .user_avatar {
      @apply w-[65px] h-[65px] rounded-[50%]  mr-[22px]
    }
    .user_info {
      @apply flex flex-col items-start
    }
    .user_name {
      @apply line-clamp-1 text-[26px]
    }
    .invite {
      @apply text-[#666] text-[22px]
    }
    .goods_img {
      @apply w-[560px] h-[373px] rounded-[11px] mb-[20px]
    }
    .title {
      @apply w-[540px]
    }
  }
  .template2_content {
    @apply absolute top-[139px] left-[42px] right-[42px] w-[488px];
    .goods_img {
      @apply w-[488px] h-[325px] mt-[24px] rounded-[9px]
    }
    .title {
      @apply text-[#fff] w-[500px]
    }
  }
  // 二维码样式
  .qr_code0 {
    @apply absolute right-[28px] bottom-[50px]
  }
  .qr_code1 {
    transform: translateX(-50%);
    @apply absolute left-[50%] bottom-[100px]
  }
  .qr_code2 {
    transform: translateX(-50%);
    @apply absolute left-[50%] bottom-[112px] rounded-[16px]
  }

  .saveImg {
    transform: translateX(-50%);
    z-index: 999;;
    @apply fixed top-0 left-[50%] w-[600px] h-[950px] opacity-0;
  }

  // logo
  .logo_img {
    @apply w-[140px] h-[35px] absolute
  }
  .logo_img0 {
    @apply left-[42px] top-[39px]
  }
  .logo_img1 {
    transform: translateX(-50%);
    @apply left-[50%] bottom-[37px]
  }
  .logo_img2 {
    @apply left-[32px] top-[42px]
  }

  // 小程序提示文案样式
  .xcx_save_share_text {
    height: calc(88px + constant(safe-area-inset-bottom));
    height: calc(88px + env(safe-area-inset-bottom));
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
    @apply flex items-center justify-center font-bold text-[32px]
  }

  // 保存分享海报按钮
  .save_share_btn {
    height: calc(88px + constant(safe-area-inset-bottom));
    height: calc(88px + env(safe-area-inset-bottom));
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
    @apply w-[6805x] h-[88px] bg-brand rounded-[16px] font-bold mx-auto
    text-[#fff] text-[28px]  text-center leading-[88px] mt-[34px]
  }
}
</style>
