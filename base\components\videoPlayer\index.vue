<template>
  <div :style="{ width, height }" class="relative">
    <div v-if="cantPlay" class="player-error">
      <div class="player-error__icon" />
      <div>暂不支持当前浏览器，建议更换谷歌浏览器</div>
    </div>
    <template v-else>
      <template v-if="isLoadVideo">
        <div class="video-loading flex items-center justify-center absolute top-0 left-0" :style="{ width, height }">
          <img :src="VideoLoading" class="w-[64px] h-[64px]" height="64" width="64">
        </div>
      </template>
      <div v-if="type === 1" id="player" :style="{ width, height }" />
      <CCPlayer
        v-else
        ref="ccPlayerRef"
        :node-id="nodeId"
        :goods-master-id="goodsMasterId"
        :width="width"
        :height="height"
        :vid="decodeInfo?.ccVideoId || decodeInfo?.videoId"
        :siteid="decodeInfo?.siteId"
        :is-buy="isPlayVideo"
        :last-time-point="decodeInfo?.lastTimePoint"
        :is-promote="isPromote"
        :is-show-video="isShowVideo"
        @ccPlayStart="handleCcStart"
        @ccPlayPause="handleCcPause"
        @ccPlayOver="handleCcEnd"
        @ccPlayTime="handleCcTime"
      />
    </template>
    <slot />
  </div>
  <!-- <button @click="onPauseVideo">
    暂停
  </button>
  <button @click="onResumeVideo">
    开始播放
  </button> -->
</template>

<script setup lang="ts">
import VideoLoading from '../../assets/video/loading.png'
import { isWAP } from '../../utils/is'
//  getCCNodeInfo, getPolyvInfo
import { getClassifiedInformation, uploadProgress as uploadProgressData, getCCNodeInfoProt, getPolyvInfo, getCCNodeInfo, getPolyvOrCCInfoByVideoId, uploadProgressByVideoId, getClassifiedInformationOfNoLogin, getCCNodeInfoProtOfNoLogin } from '../../apis/video'
import type { UploadProgressParams } from '../../apis/video/types'
import { decryptData, createDecipher } from '../../utils/video'
import useAuditionValidity from '../../composables/useAuditionValidity'
import CCPlayer from './ccplayer/index.vue'
import { useUserStore } from '~/stores/user.store'
import { useLiveDetailStore } from '~/stores/live-detail.store'
const route = useRoute()
const userStore = useUserStore()
const props = withDefaults(defineProps<{
  nodeId: string,
  goodsMasterId: string,
  type: number,
  preview?: boolean
  width?: string
  height?: string
  isPromote?: number
  liveOrgId: string
  tryListenId?: string
  // userSelectionMode?: string
  teaserShow?: number // 预告片展示
  isNoLoginSeeCourseNode?: boolean //
  playByVideoId?: string
}>(), {
  width: '100%',
  height: '100%',
  preview: false,
  playByVideoId: '',
  teaserShow: 1,
  isPromote: 0
})
const { getAuditonValidity } = useAuditionValidity()
const liveDetailStore = useLiveDetailStore()
// 赋值操作时，需要配置其参数 expires
const userInfo = useCookie<any | null>('user')

// TODO state
const { isXcx, clientType, lesseeId } = useAppConfig()

// 是否是试听商品跳转到详情标识
const kkIsAudition = useCookie<string>(IS_AUDITION)
const kkPoolId = useCookie<string>(POOL_ID)
const isDistory = ref(false) // 组件销毁前kkIsAudition已经重置了，所以需要存储下
interface DecodeInfoType {
  goodsTitle: string,
  nodeTitle: string,
  goodsImg: string,
  number: string,
  lastTimePoint: string,
  videoId: string, // pc | h5 使用
  playSafe: string, // pc 使用
  ts: string, // h5 使用
  sign: string, // h5 使用
  prevNodeId: string,
  nextNodeId: string,
  learningToken: string,
  nodeId: string,
  ccVideoId: string,
  siteId: string,
  ccFlag: number
}

const emits = defineEmits<{
  (e: 'playOver', value: string, type?: string): void // 播放结束
  (e: 'pauseVideo', value: string, type?: string): void // 暂停播放
  (e: 'resumeVideo', value: string): void // 开始播放
  (e: 'buy'): void // 购买
  (e: 'videoSeekEnd'): void // 拖拽结束
}>()
const ccPlayerRef = ref<InstanceType<typeof CCPlayer>>()
// 暂停
const onPauseVideo = (): void => {
  if (props.type === 1) {
    if (polyvPlayerInit.value) {
      playerInstance.j2s_pauseVideo()
    }
  } else {
    console.log(ccPlayerRef.value, '暂停')

    ccPlayerRef.value?.onCcPauseVideo()
  }
}
// 播放
const onResumeVideo = (): void => {
  if (props.type === 1) {
    if (polyvPlayerInit.value) {
      playerInstance.j2s_resumeVideo()
    }
  } else {
    console.log(ccPlayerRef.value, '播放')
    ccPlayerRef.value?.onCcResumeVideo()
  }
}

const { width, height, preview } = toRefs(props)

const courseNodeId = ref(props.nodeId)
const nextNodeId = ref('')
console.log(width, height, preview, 'kkcvideoplayer')
// vrm11加密

// const vodPlayerJs = '//player.polyv.net/resp/vod-player-drm/latest/player.js'
// const vodPlayerJs = '//player.polyv.net/resp/vod-player-drm/canary/player.js' // 可以在鸿蒙使用
const vodPlayerJs = '//player.polyv.net/resp/vod-player/latest/player.js' // viewid 异常暂时使用
// vrm13加密
/**
 *
 *https://player.polyv.net/resp/vod-player-drm/canary/player.js (pc)
https://player.polyv.net/resp/vod-player-drm/canary/mobile/player.js （mobile
 * **/
// const vodPlayerJs = '//player.polyv.net/resp/vod-player-drm/canary/player.js'

console.log(vodPlayerJs, 'vodPlayerJs')

let playerInstance: any = null
const loadPlayerScript = (callback: Function) => {
  if (!window.polyvPlayer) {
    const myScript = document.createElement('script')
    myScript.setAttribute('src', vodPlayerJs)
    myScript.onload = callback
    document.body.appendChild(myScript)
  } else {
    callback()
  }
}
const isVideoBuy = inject('isVideoBuy')
const isPlayVideo = ref(true)
// 是否展示视频
const isShowVideo = ref<boolean>(true)

// 获取播放加密信息
const decodeInfo = ref<DecodeInfoType>()
const { specificationItemId = '' } = useRoute()?.query
// 视频加载中
const isLoadVideo = ref(false)

const getVideoData = async (getInfoFunc: any) => {
  console.log(getInfoFunc, '111getInfoFunc=====')
  isLoadVideo.value = true
  const { error, data } = await getInfoFunc({
    nodeId: courseNodeId.value,
    goodsMasterId: props.goodsMasterId as string,
    orgId: props.liveOrgId,
    videoId: props.playByVideoId,
    tryListenId: props.tryListenId,
    poolId: Number(kkIsAudition.value) === 1 && kkPoolId.value ? kkPoolId.value : undefined,
    ...(specificationItemId && { specificationItemId })
  })
  console.log(error.value, 'error.value')
  let result
  if (!error.value) {
    const { kkAes, kkSdkString } = data.value.data
    const kkAesDecode = kkAes
    const kkSdkStringDecode = kkSdkString
    const ivLength = 16 // IV长度为16字节
    const encryptedData = decryptData(kkAesDecode) as string // key + iv
    const iv = encryptedData.slice(ivLength)
    const key = encryptedData.slice(0, ivLength)

    const resultStr = createDecipher(kkSdkStringDecode, key, iv)
    result = JSON.parse(resultStr)
    console.log('🚀 ~ file: index.vue:76 ~ result:', result)
    decodeInfo.value = result

    console.log(isVideoBuy, 'isVideoBuy')
    isPlayVideo.value = isVideoBuy || result.isFree
    // if (result.isBuy && props.playByVideoId) {
    //   isPlayVideo.value = true
    // }
    //
    if (!props.playByVideoId && !isVideoBuy && !result.isFree) {
      emits('buy')
      return
    }
    nextNodeId.value = result?.nextNodeId
    updateParams.learningToken = result.learningToken
  } else if (error.value?.data?.code === '10741') {
    isShowVideo.value = false
    Message('该视频已下架，请联系管理员')
    return
  }
  isLoadVideo.value = false
  return result
}

// 保利威视频信息
const getPolyvVideoData = async () => {
  if (props.isNoLoginSeeCourseNode) {
    return getVideoData(getClassifiedInformationOfNoLogin)
  }
  const url = props.isPromote === 1 && !userStore.isLogin ? getPolyvInfo : getClassifiedInformation
  if (props.playByVideoId) {
    return getVideoData(getPolyvOrCCInfoByVideoId)
  }
  return getVideoData(url)
}

// cc视频播放信息
const getCcVideoData = async () => {
  if (props.isNoLoginSeeCourseNode) {
    return getVideoData(getCCNodeInfoProtOfNoLogin)
  }
  const url = props.isPromote === 1 && !userStore.isLogin ? getCCNodeInfo : getCCNodeInfoProt
  if (props.playByVideoId) {
    return getVideoData(getPolyvOrCCInfoByVideoId)
  }
  return getVideoData(url)
}

// 学习进度参数
const updateParams = reactive<UploadProgressParams>({
  nodeId: courseNodeId.value,
  learningToken: undefined,
  realLearningTime: '0',
  timePoint: undefined,
  playOver: undefined,
  goodsMasterId: props.goodsMasterId,
  tryListenId: props.tryListenId,
})
const polyvPlayerInit = ref(false)
const polyvPlayerOption = ref({
  wrap: '#player',
  width: width.value,
  height: height.value,
  hideSwitchPlayer: true,
  forceH5: true,
  mutedAutoplay: true, // 静音自动播放
  // flashvars: { banVideoTitle: true },
  autoplay: true,
  keyboardSeekTime: 5000,
  // ban_history_time: 'on',
  vid: decodeInfo.value?.videoId,
  ts: isWAP(clientType) ? decodeInfo.value?.ts : undefined,
  sign: isWAP(clientType) ? decodeInfo.value?.sign : undefined,
  playsafe: decodeInfo.value?.playSafe,
  preview: props.isPromote === 1 && !userStore.isLogin, // 试听  boolean
  watchStartTime: decodeInfo.value?.lastTimePoint, // 播放开始时间
  preventKeyboardEvent: false,
  viewerInfo: {
    viewerId: userInfo.value?.userId
  },
  code: `${lesseeId}-${props.liveOrgId}-${userInfo.value?.userId}`,
  vrmVersion: 11,
  // 预告片不展示
  // 1展示,0不展示
  teaser_show: props.teaserShow !== 0 ? 1 : 0,
  hideRepeat: true // 播放结束后隐藏重播按钮
})

// 监视decodeInfo的变化
watch(() => decodeInfo.value, async (newValue) => {
  // 在decodeInfo变化时更新polyvPlayerOption
  polyvPlayerOption.value = {
    ...polyvPlayerOption.value,
    vid: newValue.videoId,
    ts: isWAP(clientType) ? newValue.ts : undefined,
    sign: isWAP(clientType) ? newValue.sign : undefined,
    playsafe: newValue.playSafe,
    watchStartTime: newValue.lastTimePoint,
  }
})

// 监听nodeid 变化
watch(() => props.nodeId, async (newVal, oldVal) => {
  console.log(isUpdateVideoData.value, newVal, 'isUpdateVideoData.value~~~~~~~~~~~')

  // 自动播放下一个视频得时候不需要二次走下面 得逻辑
  if (!isUpdateVideoData.value) {
    isUpdateVideoData.value = true
    return
  }
  if (oldVal) {
    updateParams.nodeId = oldVal
    upProgressByIsPromote()
  }
  if (newVal) {
    courseNodeId.value = newVal
    updateParams.nodeId = newVal
    ccPlayTime.value = 0
    ccDuration.value = 0
    props.type === 1 ? await getPolyvVideoData() : await getCcVideoData()
    if (playerInstance && props.type === 1) {
      playerInstance.changeVid(polyvPlayerOption.value)
    }
  }
})
watch(() => props.playByVideoId, async (newVal, oldVal) => {
  console.log(isUpdateVideoData.value, newVal, 'isUpdateVideoData.value~~~~~~~~~~~')

  // 自动播放下一个视频得时候不需要二次走下面 得逻辑
  if (!isUpdateVideoData.value) {
    isUpdateVideoData.value = true
    return
  }
  if (oldVal) {
    updateParams.goodsFileId = oldVal
    upProgressByIsPromote()
  }
  if (newVal) {
    // courseNodeId.value = newVal
    updateParams.goodsFileId = newVal
    ccPlayTime.value = 0
    ccDuration.value = 0
    props.type === 1 ? await getPolyvVideoData() : await getCcVideoData()
    if (playerInstance && props.type === 1) {
      playerInstance.changeVid(polyvPlayerOption.value)
    }
  }
})
const isUpdateVideoData = ref(true) // 是否更新视频信息，如果播放完成后播放下一个视频是不需要二次更新数据得
// 设置播放信息
const loadPlayer = () => {
  // 视频被删除不进行播放
  if (!isShowVideo.value) {
    Message('该视频已下架，请联系管理员')
    onPauseVideo()
    return false
  }
  const polyvPlayer = window.polyvPlayer
  console.log(polyvPlayer, 'window.polyvPlayer')
  playerInstance = polyvPlayer(polyvPlayerOption.value)

  if (playerInstance) { onResumeVideo() }

  // 加载结束
  playerInstance.on('s2j_onPlayerInitOver', () => {
    console.log('加载结束', updateParams)
    polyvPlayerInit.value = true
    // 加载结束后自动播放
    // playerInstance.value?.j2s_resumeVideo()
  })

  // 视频播放
  playerInstance.on('s2j_onVideoPlay', (vid: string) => {
    emits('resumeVideo', vid)
  })

  // 视频暂停
  playerInstance.on('s2j_onVideoPause', (vid: string) => {
    upProgressByIsPromote()
    emits('pauseVideo', vid)
  })

  // 播放结束
  playerInstance.on('s2j_onPlayOver', async (vid: string) => {
    // const type = props.isPromote === 1 && !userStore.isLogin ? 'isPromote' : ''
    if (props.isNoLoginSeeCourseNode) {
      emits('playOver', vid, 'isNoLoginSeeCourseNode')
    } else if (props.isPromote === 1 && !userStore.isLogin) {
      emits('playOver', vid, 'isPromote')
    } else {
      const validity = await getAuditonValidity()
      let isMode3NextNodeIdJieSuo = true
      if (route.name === 'learn-center-live-detail') {
        console.log('学习模式', liveDetailStore.userSelectionMode)
        if (liveDetailStore.userSelectionMode === 3) {
          const nodeInfo = liveDetailStore.getNodeIdInfoFn(nextNodeId.value)
          console.log('下个节点的解锁状态', nodeInfo)
          isMode3NextNodeIdJieSuo = !!nodeInfo?.unlockStatus
          if (!isMode3NextNodeIdJieSuo && nodeInfo?.unlockTime) {
            Message('暂未到解锁时间，敬请期待')
          }
        /**
         * 学习计划模式下
         * 上一个课时 id、下一个课时 id 均无意义，不能连续播放；
         */
        } else if (liveDetailStore.userSelectionMode === 4) {
          isMode3NextNodeIdJieSuo = false
        }
        // 日期模式 下，isVideoBuy未购买并且播放结束后触发emits('buy')
        if ([3].includes(liveDetailStore.userSelectionMode) && isVideoBuy === 0) {
          emits('buy')
        }
      }
      if (!validity) {
        emits('buy')
      } else if (nextNodeId.value && isMode3NextNodeIdJieSuo) {
        // && props.userSelectionMode !== 3
        courseNodeId.value = nextNodeId.value
        const nextId = nextNodeId.value
        const data = await getPolyvVideoData()
        console.log(data, 'bofangjieshu1id')
        if (data?.isBuy) {
          updateParams.nodeId = nextId
          console.log(polyvPlayerOption.value, '触发自动播放下一个视频 1')
          playerInstance.changeVid(polyvPlayerOption.value)
          console.log(nextId, 'nextNodeId.valuenextNodeId.valuenextNodeId.value')

          console.log(updateParams, 'updateParamsupdateParamsupdateParams',)
        }
      }
      isUpdateVideoData.value = !nextNodeId.value
      // 播放结束
      emits('playOver', vid, courseNodeId.value)
    }
  })

  // 拖拽
  playerInstance.on('s2j_onVideoSeek', () => {
    upProgressByIsPromote()
    emits('videoSeekEnd')
  })

  playerInstance.on('s2j_onPlayerError', () => {
    if (!isShowVideo.value) { return false }
    Message('请使用其他浏览器观看')
  })

  // /**
  //  * 更新清晰度
  //  */
  // playerInstance.switchBitrate(() => {
  //   alert('更换了清晰度')
  // })
}
// 获取保利威当前学习时间
const getCurrentTime = () => {
  // 向下取整
  if (!playerInstance) { return { currentTime: 0, playVideoTime: 0 } }
  return {
    currentTime: parseInt(playerInstance.j2s_getCurrentTime()),
    playVideoTime: parseInt(playerInstance.j2s_realPlayVideoTime()),
  }
}
// 获取cc学习时间
const ccDuration = ref(0)
const ccPlayTime = ref(0)
const handleCcTime = (duration: number, playTime: number) => {
  ccDuration.value = Math.floor(duration) || 0
  ccPlayTime.value = Math.floor(playTime) || 0
}
// 上传学习进度

// if (props.isNoLoginSeeCourseNode) {
const updateProgress = async () => {
  if (!userStore.isLogin) { // 接口有Prot，需要登录才能调用
    return
  }
  updateParams.realLearningTime = props.type === 1 ? getCurrentTime().playVideoTime : ccPlayTime.value
  updateParams.timePoint = props.type === 1 ? getCurrentTime().currentTime : ccDuration.value
  if ((Number(kkIsAudition.value) === 1 || isDistory.value) && kkPoolId.value) {
    updateParams.poolId = kkPoolId.value
    updateParams.specificationItemId = specificationItemId ?? undefined
  }
  // 通过videoId上传学习进度
  if (props.playByVideoId) {
    await uploadProgressByVideoId({
      ...updateParams,
      orgId: props.liveOrgId,
      goodsFileId: props.playByVideoId
    })
  } else {
    const { data, error } = await uploadProgressData({ ...updateParams })
    console.log(data, error.value)
  }
  sessionStorage.setItem('nodeLastLearnTime', updateParams.timePoint.toString())
}

// 关闭 返回 结束 暂停 切换浏览器tab 操作需要上传学习进度
// 监听浏览器关闭操作方法
const handleWindowClose = () => {
  // 处理窗口关闭的逻辑
  upProgressByIsPromote()
}
//
const handleVisibilityChange = () => {
  if (document.hidden) {
    // 执行相应的操作，如暂停视频、音频等
    upProgressByIsPromote()
  } else {
    // 切回 执行相应的操作，如恢复视频、音频等
  }
}
// cc视频操作
// 暂停
const handleCcPause = (vid: string, type?: string) => {
  console.log(vid, 'cc视频播放暂停', type)
  upProgressByIsPromote()
  emits('pauseVideo', vid, type)
}
// 开始ccVideoId
const handleCcStart = (vid: string) => {
  console.log(vid, 'cc视频播放开始')
  emits('resumeVideo', vid)
  upProgressByIsPromote()
}
const upProgressByIsPromote = () => {
  if (!props.isPromote || userStore.isLogin) {
    if (updateParams.learningToken) {
      updateProgress()
    }
  }
}

// 播放结束
const handleCcEnd = async (vid: string) => {
  const validity = await getAuditonValidity()
  console.log(vid, nextNodeId.value, getAuditonValidity, validity, 'cc视频播放结束')
  let isMode3NextNodeIdJieSuo = true
  if (route.name === 'learn-center-live-detail') {
    console.log('学习模式', liveDetailStore.userSelectionMode)
    /**
     * 学习计划模式下
     * 上一个课时 id、下一个课时 id 均无意义，不能连续播放；
     */
    if (liveDetailStore.userSelectionMode === 4) {
      isMode3NextNodeIdJieSuo = false
    }
    // 日期模式 下，isVideoBuy未购买并且播放结束后触发emits('buy')
    if ([3].includes(liveDetailStore.userSelectionMode) && isVideoBuy === 0) {
      emits('buy')
    }
  }
  if (!validity) {
    emits('buy')
  } else if (nextNodeId.value && isMode3NextNodeIdJieSuo) {
    console.log('下一个视频开始播放', nextNodeId.value)

    courseNodeId.value = nextNodeId.value
    updateParams.nodeId = nextNodeId.value
    ccDuration.value = 0
    ccPlayTime.value = 0
    await getCcVideoData()
  }
  if (decodeInfo.value?.isBuy) {
    updateProgress()
  }
  isUpdateVideoData.value = !nextNodeId.value
  emits('playOver', vid, courseNodeId.value)
}
const getVideoDataByType = async () => {
  // 获取直播平台类型
  // 1 保利威  2 cc
  if (props.type === 1) {
    await getPolyvVideoData()
    loadPlayerScript(loadPlayer)
  } else {
    await getCcVideoData()
  }
}
let timer: any

// 视频播放浏览器黑名单
const blackBrowserList = ref(['vivo'])
if (isXcx) {
  blackBrowserList.value = []
}
const cantPlay = typeof window !== 'undefined' && blackBrowserList.value.some(item => navigator.userAgent.toLowerCase().includes(item))
onMounted(async () => {
  if (!cantPlay) {
    await getVideoDataByType()
    if (isXcx && decodeInfo.value?.isBuy) {
      // TODO 内存泄漏可疑点: 确保客户端执行并及时清除
      timer = setInterval(function () {
        upProgressByIsPromote()
      }, 60000)
    }
    // 监听浏览器关闭操作
    window.addEventListener('beforeunload', handleWindowClose)
    document.addEventListener('visibilitychange', handleVisibilityChange)
  }
})

// // 获取视频播放模式
onBeforeUnmount(() => {
  clearInterval(timer)
  if (decodeInfo.value?.isBuy) {
    isDistory.value = true
    upProgressByIsPromote()
  }
  if (playerInstance) {
    // 保存上一次的配置 给下一个弹窗使用
    localStorage.setItem('polyvPlayerOption', JSON.stringify(polyvPlayerOption.value))
    playerInstance.destroy()
  }
  // 监听用户切换浏览器标签页操作
  document.removeEventListener('visibilitychange', handleVisibilityChange)
})
defineExpose({
  onPauseVideo,
  onResumeVideo,
  isShowVideo,
})

</script>
<style lang="scss" scoped>
.player-error {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #000;
  color: #fff;
  font-size: 14px;

  .player-error__icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 10px auto;
    background: url(data:image/png;base64,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) center / 100% no-repeat;
  }
}
</style>
