<template>
  <div :style="{ width, height }">
    <div id="PolyvPlayer" :style="{ width, height }" />
  </div>
</template>

<script setup lang="ts">
const props = withDefaults(defineProps<{
  width?: string
  height?: string
  videoId: string
  liveOrgId: string
  ts: string
  sign: string
  playsafe: string
  hideRepeat?: boolean
  autoplay?: boolean
  mutedAutoplay?: boolean
}>(), {
  width: '100%',
  height: '100%',
  hideRepeat: false,
  autoplay: true,
  mutedAutoplay: true
})

const emits = defineEmits<{
  (e: 'on-play', data: any): void
  (e: 'on-pause', data: any): void
  (e: 'on-ended', data: any): void
  (e: 'on-ready', data: any): void
  (e: 'on-seek', data: any): void
}>()

const playerInstance = ref<any>()

onUnmounted(() => {
  onPauseVideo()
})

const { lesseeId } = useAppConfig()
// 赋值操作时，需要配置其参数 expires
const userInfo = useCookie<any | null>('user')

const polyvPlayerOption = ref({
  wrap: '#PolyvPlayer',
  width: '100%',
  height: '100%',
  hideSwitchPlayer: true,
  forceH5: true,
  mutedAutoplay: props.mutedAutoplay, // 静音自动播放
  // flashvars: { banVideoTitle: true },
  autoplay: props.autoplay,
  keyboardSeekTime: 5000,
  // ban_history_time: 'on',
  vid: props.videoId,
  // vid: '88083abbf5bcf1356e05d39666be527a_8',
  ts: props.ts,
  sign: props.sign,
  playsafe: props.playsafe,
  // watchStartTime: decodeInfo.value?.lastTimePoint, // 播放开始时间
  preventKeyboardEvent: false,
  // viewerInfo: {
  //   viewerId: userInfo.value?.userId
  // },
  code: `${lesseeId}-${props.liveOrgId}-${userInfo.value?.userId}`,
  vrmVersion: 11,
  hideRepeat: props.hideRepeat // 播放结束后隐藏重播按钮
})

// 暂停
const onPauseVideo = (): void => {
  if (playerInstance.value) {
    playerInstance.value.j2s_pauseVideo()
  }
}
// 播放
const onResumeVideo = (): void => {
  if (playerInstance.value) {
    playerInstance.value.j2s_resumeVideo()
  }
}

// 销毁播放器
const destroyPlayer = (): void => {
  if (playerInstance.value) {
    playerInstance.value.destroy()
    playerInstance.value = null
  }
}

// 加载播放器

useHead({
  script: [
    {
      src: '//player.polyv.net/resp/vod-player/latest/player.js', // viewid 异常暂时使用
      onload: () => {
        loadPlayer()
      }
    }
  ]
})

// 设置播放信息
const loadPlayer = () => {
  playerInstance.value = (window as any).polyvPlayer(polyvPlayerOption.value)

  if (playerInstance.value) {
    // 加载结束
    playerInstance.value.on('s2j_onReadyPlay', () => {
      // 加载结束后自动播放
      // playerInstance.value?.j2s_resumeVideo()
      emits('on-ready', playerInstance.value)
    })

    // 视频播放
    playerInstance.value.on('s2j_onVideoPlay', () => {
      emits('on-play', playerInstance.value)
    })

    // 视频暂停
    playerInstance.value.on('s2j_onVideoPause', (vid: string) => {
      onPauseVideo()
      emits('on-pause', vid)
    })

    // 播放结束
    playerInstance.value.on('s2j_onPlayOver', async (vid: string) => {
      onPauseVideo()
      emits('on-ended', vid)
    })

    // 播放时长
    playerInstance.value.on('s2j_onVideoSeek', (vid: string, time: number) => {
      emits('on-seek', { vid, time })
    })
  }
}

defineExpose({
  onPauseVideo,
  onResumeVideo,
  destroyPlayer
})

</script>

<style scoped>
</style>
