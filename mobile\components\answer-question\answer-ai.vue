<template>
  <div>
    <template v-if="list.length">
      <div class="answer-ai-wrap">
        <!-- tabs -->
        <div class="top-title">
          <div class="result-number">
            <div
              v-for="(item, index) in arr.length"
              :key="index"
              class="result-item "
              :class="index === activeIndex ? 'item-active' : ''"
              @click="handleResult(index)"
            >
              试题{{ index + 1 }}
            </div>
          </div>
        </div>

        <!-- 题目答案 start -->

        <div class="answer-ai-main">
          <div class="flex items-center justify-between top-box">
            <div class="text-[28px] leading-[28px]">
              {{ showMessage(resultObj.ptypeValueId) }}
            </div>
            <!-- <div class="title-right">
              来源于AI，内容仅供参考
            </div> -->
          </div>

          <SearchAI :result-obj="resultObj" class="p-[12px]" />
        </div>

        <!-- 题目答案 end -->
      </div>
    </template>
    <template v-else>
      <div class="ai-no-data">
        <img :src="AIEmpty" alt="">
        无拍搜结果～
      </div>
    </template>
  </div>
</template>
<script lang="ts" setup>

import { SearchAI } from './components/index'
import AIEmpty from '~/assets/images/answer/ai-no.png'

import type { QuestionList } from '~/apis/answer/types'

const props = defineProps<{
  list: QuestionList[] | Object
}>()

/**
* @description: 切换结果
* @param {*}
* @return {*}
*/
const activeIndex = ref(0)

const handleResult = (index: number) => {
  activeIndex.value = index
}
const arr = ref<QuestionList[]>(props.list as QuestionList[])
// arr.value = JSON.parse(props.list)
// 试题结果
const resultObj = computed(() => {
  return arr.value[activeIndex.value]
})

const showMessage = (ptypeValueId:string) => {
  switch (Number(ptypeValueId)) {
    case 1:
      return '选择题'
    case 2:
      return '选择题'
    case 3:
      return '选择题'
    case 4:
      return '填空题'
    case 5:
      return '判断题'
    case 6:
      return '问答题'
    case 7:
      return '材料题'
    default:
      return ''
  }
}

</script>
<style lang="scss" scoped>
.answer-ai-wrap {
  width: 478px;
  padding: 12px;
  background: #FFFFFF;
  border-radius: 4px 20px 20px 20px;

  .top-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24px;

    .result-number {
      display: flex;
      align-items: center;

      .result-item {
        width: 81px;
        height: 36px;
        background: #fff;
        color: #111111;
        font-size: 20px;
        text-align: center;
        line-height: 36px;
        border-radius: 18px;
        margin-right: 16px;
        cursor: pointer;
        border: 1px solid #E5E7EB;
      }

      .item-active {
        @apply text-brand border-brand bg-brand/5;
      }
    }

  }

  .title-right {
    font-weight: 400;
    text-align: right;
    color: #999999;
    font-size: 22px;
  }

  .answer-ai-main {
    background: #F7F7F7;
    border-radius: 8px 8px 24px 24px;
    border: 2px solid #E5E7EB;

    .top-box{
      padding: 9px 12px;
      border-bottom: 1px solid #F0F0F0;
    }

  }

}
.ai-no-data{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 270px;
    height: 180px;
    background: rgba(255,255,255,0.5);
    border-radius: 8px 8px 8px 8px;
    font-size: 21px;
    color: #999999;

    img{
      width: 120px;
      height: 95px;
      margin-bottom: 16px;
    }
  }
</style>
