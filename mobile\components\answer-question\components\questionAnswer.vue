<template>
  <div class="point-wrap" :style="'fontSize:'+(size/2)+'px'">
    <div v-if="showLabel" class="label" :class="size === 32?'!text-brand':''">
      答案
    </div>
    <!-- 选择题 -->
    <div :class="isChoice?'flex-1 flex flex-wrap':''">
      <div v-for="(item, index) in answerCom" :key="index" class="point-item !text-brand" :class="size === 32?'!text-brand':''">
        <KKCHtmlMathJax :content="item" :is-answer-questions="true" />
        {{ index === answerCom.length - 1 ? '' : ',' }}
        <KKCAudioPlayer
          v-if="item.answerSrc"
          class="bg-[#f1f2f3] !w-[100%] audio-wrap"
          :icon-name="'icon-yinpinbofang'"
          :playing-icon="'icon-yinpinzanting'"
          :icon-size="48"
          :url="item.answerSrc"
        />
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>

const props = withDefaults(defineProps<{
  answer: string | undefined,
  size?: number,
  showLabel?: boolean,
  ptypeValueId?: string
}>(), {
  size: 28,
  showLabel: true,
  ptypeValueId: ''
})
const isChoice = computed(() => {
  return ['1', '2', '3'].includes(props.ptypeValueId.toString())
})

const answerCom = computed(() => {
  if (typeof props.answer === 'string') {
    try {
      // 尝试将字符串解析为 JSON 对象
      return JSON.parse(props.answer)
    } catch (error) {
      // 如果解析失败，返回一个默认值或者处理错误
      console.error('Invalid JSON:', error)
      return {} // 返回一个空对象作为示例，默认值可以根据实际情况调整
    }
  } else {
    // 如果 props.answer 不是字符串，则直接返回
    return props.answer
  }
})

</script>
<style lang="scss" scoped>
  .point-wrap{

    margin-bottom: 24px;
    font-size: 24px;
    display: flex;
    align-items: start;
    flex-wrap: wrap;

    .label{
      text-align: left;
      font-weight: 500;
      color: #111;
      width: 112px;
      // margin-right: 48px;
    }

    .point-item{
      // flex: 1;
      color: #333;
      display: flex;
      flex-wrap: wrap;
      word-break: break-all;
    }

    .audio-wrap{
        border-radius: 8px 40px 40px 40px!important;
    }
  }
</style>
