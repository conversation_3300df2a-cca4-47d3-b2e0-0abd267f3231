<template>
  <div>
    <div class="exam-quest-title">
      <span class="quest-title">
        {{ questionTitle }}
      </span>
      <span v-if="(moduleType === 2 || moduleType === 0)" class="explain" @click="onDescriptionClick">
        <span v-if="question.description">（{{ question.description }}）</span>&nbsp;
        <KKCIcon
          v-if="question.description?.length > 15"
          class="quest-title-right-icon"
          name="icon-wode-youjiantou"
          :size="24"
        />
      </span>
    </div>
    <KKCAudioPlayer
      v-if="question.stemSrc"
      class="bg-[#f8f8f8] mb-[10px]"
      :icon-name="'icon-yinpinbofang'"
      :playing-icon="'icon-yinpinzanting'"
      :icon-size="48"
      :url="question.stemSrc"
    />
    <div
      v-img-preview="question.stem"
      class="material-stem exam-html overflow-y-scroll overflow-x-hidden"
      :style="{
        height: `${materialHeight}px`,
        paddingBottom: `${140}px`
      }"
    >
      <KKCHtmlMathJax :content="question.stem" />
    </div>

    <van-floating-panel v-model:height="height" :anchors="anchors" @height-change="onHeightChange">
      <div class="w-[700px] m-auto py-[40px] pb-[180px]">
        <van-swipe
          ref="examSwiperRef"
          :loop="true"
          :show-indicators="false"
          :duration="50"
          stop-propagation
          @change="onChange"
        >
          <van-swipe-item
            v-for="value in childQuestions"
            :key="value.id"
          >
            <template v-if="value.id === childQuestions[currentIndex].id">
              <div class="cl-num">
                <span>{{ value.sonSort }}</span><span class="cl-total">/{{ childQuestions.length }}</span>
              </div>
              <!-- 单选 || 判断 -->
              <QuestionBankRadioChoice
                v-if="value.ptypeValueId && isRadioChoice(value.ptypeValueId)"
                :question="value"
                :is-edit="isEdit"
                :model="model"
                :is-material="true"
                @change="(val, logStatus) => changeAnswer(val, logStatus, value, cindex)"
              />
              <!-- 多选 || 不定项 -->
              <QuestionBankMultipleChoice
                v-if="value.ptypeValueId && isMultipleChoice(value?.ptypeValueId)"
                :question="value"
                :is-edit="isEdit"
                :model="model"
                :is-material="true"
                @change="(val, logStatus) => changeAnswer(val, logStatus, value, cindex)"
              />
              <!-- 填空 || 简答 -->
              <QuestionBankFill
                v-if="value.ptypeValueId && isFillQuestion(value?.ptypeValueId)"
                :question="value"
                :is-edit="isEdit"
                :model="4"
                :exam-mode-number="examModeNumber"
                :is-material="true"
                @getIsFocus="getIsFocus"
                @change="(val, logStatus) => changeAnswer(val, logStatus, value, cindex)"
              />

              <!-- 解析 -->
              <QuestionBankShowAnswer
                v-if="value"
                class="showAnswer"
                :is-expand="value.isExpand"
                :is-detail="true"
                @changeIsShow="(val) => switchAnalyze(val, value.id)"
              />
              <QuestionBankAnalyze
                v-if="value && value.isExpand"
                ref="analyzeRef"
                :info="value"
              />
              <slot />
            </template>
          </van-swipe-item>
        </van-swipe>
      </div>
    </van-floating-panel>
    <Teleport to="body">
      <QuestionBankPopupDescription
        v-if="description"
        :show="isShowDescription"
        :description="description"
        @ok="onDescriptionClick"
      />
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import {
  isFillQuestion,
  isMultipleChoice,
  isRadioChoice
} from '../../../base/utils/tools'
import type { questType } from './type'
import { useUserStore } from '~/stores/user.store'

const userStore = useUserStore()
const route = useRoute()
// 1每日一练,3 章节练习,2 试卷，处理数据逻辑不同
const moduleType = Number(route.query.moduleType)
interface Props {
  question: questType; // 试题信息
  model?: number; // 1联系模式，2考试模式，3快答模式，4背题模式
  // current: number; // 当前题目位于全部题目的位置
  isEdit?: boolean; // 1可以操作，2纯展示
  examModeNumber?:number,
}
const props = withDefaults(defineProps<Props>(), { isEdit: true })
const emit = defineEmits<{
  (
    e: 'change',
    val: any[],
    status: number,
    item: questType,
    index: number
  ): void;
  (e: 'getIsFocus', bool: boolean): void;
}>()
// 填空和简答的是否聚焦
const getIsFocus = (val: boolean) => {
  // isInputFocus.value=true
  emit('getIsFocus', val)
}

// 选择 、 判断题 、 填空 、 简答题的答案
const changeAnswer = (
  logAnswer: any,
  logStatus: number,
  cItem: any,
  cIndex: number
) => {
  emit('change', logAnswer, logStatus, cItem, cIndex)
}

// 题型说明
const isShowDescription = ref(false)
const description = computed(() => props.question.description)
const onDescriptionClick = () => {
  if ((moduleType === 2 || moduleType === 0) && description) {
    isShowDescription.value = !isShowDescription.value
  }
}

const anchors = [
  400,
  Math.round(0.4 * window.innerHeight),
  Math.round(0.7 * window.innerHeight),
]
const height = ref(anchors[0])
const materialHeight = ref(window.innerHeight - height.value)

const onHeightChange = (data: {height: number}) => {
  materialHeight.value = window.innerHeight - data.height
}

const childQuestions = ref<any[]>([])

const switchAnalyze = (val: boolean, id: string) => {
  if (userStore.isLoginFn()) {
    childQuestions.value.forEach((item: any) => {
      if (item.id === id) {
        item.isExpand = val
        item.isAnalyze = 1
      }
    })
  }
}

// 当前题目索引
const currentIndex = ref(0)
const onChange = (index: number) => {
  currentIndex.value = index
}

const examSwiperRef = ref()
const next = (sort: number) => {
  console.log('next', sort)
  examSwiperRef.value?.swipeTo(sort)
}

/**
 * 题型title
 */
const questionTitle = computed(() => {
  const { ptypeValueIdTitle, questionTypeValueIdTitle } = props.question
  return handleQuestionTitle(ptypeValueIdTitle || '', questionTypeValueIdTitle, moduleType)
})

onMounted(() => {
  childQuestions.value = props.question.childQuestions
  // document.body.classList.remove('van-overflow-hidden')
})

defineExpose({
  next
})

</script>

<style scoped lang="scss">
.material-stem {
  font-size: 32px;
  word-wrap: break-word !important;
  color: #111;
}

:deep(.van-floating-panel) {
  touch-action: pan-y;
  .van-floating-panel__header {
    box-shadow: #ccc 0px 5px 10px 5px;
  }

  .van-floating-panel__content {
    .drag-up {
      width: 160px;
      height: 43px;
      position: absolute;
      top: -42px;
      left: calc(50% - 80px);
    }
  }
}

.cl-num {
  position: absolute;
  top: 15px;
  right: 15px;
  font-size: 28px;
  color: #111;

  .cl-total {
    font-size: 24px;
    color: #999999;
  }
}

// .van-swipe {
//   width: 750px;
//   overflow-y: auto;
//   overflow-x: hidden;
//   :deep(.van-swipe__track) {
//     height: 0;
//     .van-swipe-item {
//       overflow-y: auto;
//       overflow-x: hidden;
//     }
//   }
// }
</style>
