<template>
  <!-- 答题卡--题号内容(TODO组装答题卡-----) -->
  <div class="exam-card" :class="isAnalysis ? 'exam-card-analyze' : ''">
    <div class="exam-card-head mt-[24px] p-[31px] flex justify-between items-center">
      <div class="text-[32px] text-[#111] font-[500]">
        答题卡
      </div>
      <span class="flex">
        <template v-for="item in statusList" :key="item.type">
          <span
            v-if="handleIsShow(item.type)"
            :key="item.type"
            class="ml-[20px] text-[24px] flex items-center"
          >
            <span
              :class="[
                'relative',
                'w-[24px]',
                'h-[24px]',
                'mr-[8px]',
                'rounded-[50%]',
                'inline-block',
                item.type === 6 ? 'right-half' : '',
              ]"
              :style="{ backgroundColor: item.color }"
            />
            <span>{{ item.label }}</span>
          </span>
        </template>
      </span>
    </div>
    <!-- 试卷类型部分 -->
    <div
      v-if="isExamPaper && quesCardList[0]?.part !== undefined"
      class="exam-card-content px-[31px]"
      :class="heightClass"
      @scroll="handleScroll"
    >
      <template v-for="(part, index) in quesCardList" :key="index">
        <div
          v-if="part.part"
          class="flex justify-center items-center my-[20px] text-[32px] font-[500] text-[#111111] leading-[40px]"
        >
          <KKCIcon name="icon-bufenshuomingyidongduan" :size="36" />
          <div class="max-w-[523px] mx-[24px] line-clamp-2 break-words">
            {{ part.part }}
          </div>
          <KKCIcon name="icon-bufenshuomingyidongduan" :size="36" />
        </div>
        <template v-for="(item, subIndex) in part.questionTypeList" :key="subIndex">
          <div class="mt-[20px] mb-[32px]">
            <span v-if="!item?.chineseNum" class="exam-card-bar" />
            <span
              v-if="item?.chineseNum"
              class="text-[28px] text-[#111] font-[500]"
            >{{ item?.chineseNum }}、</span>
            <span class="exam-card-tip">
              {{ item?.questionTypeValueIdTitle || item.ptypeValueIdTitle }}
            </span>
          </div>
          <div class="flex flex-wrap exam-card-items">
            <template v-for="it in item.childQuestions" :key="it.id">
              <span
                class="exam-card-item"
                :class="[
                  handleClass(it.logStatus, it.isAnalyze),
                  { 'click-status': currentSelectNum === it.sort },
                ]"
                @click="selectOption(it)"
              >
                <!-- <span class="absolute z-10 font-gilroy-bold">{{ it.logStatus }}-{{ it.isAnalyze }}</span> -->
                <span class="absolute z-10 font-gilroy-bold">{{ it.sort }}</span>
                <span
                  v-if="
                    showCorrected(
                      it.ptypeValueId,
                      it.awarding,
                      it.isSelect,
                      item.selectMark
                    )
                  "
                  class="exam-card-corrected"
                >
                  <span
                    class="inline-block w-[86px] leading-[33px] scale-83 origin-top-left"
                    style="transform: scale(0.833)"
                  >已批改</span>
                </span>
              </span>
            </template>
          </div>
        </template>
      </template>
    </div>
    <div
      v-else
      class="exam-card-content px-[31px]"
      :class="heightClass"
      @scroll="handleScroll"
    >
      <template v-for="(item, index) in quesCardList" :key="index">
        <div v-if="!item.isExercise" class="mt-[20px] mb-[32px]">
          <span v-if="!item?.chineseNum" class="exam-card-bar" />
          <span v-if="item?.chineseNum">{{ item?.chineseNum }}、</span>
          <span class="exam-card-tip">{{
            item?.questionTypeValueIdTitle || item.ptypeValueIdTitle
          }}</span>
        </div>
        <div
          :class="[
            item.isExercise
              ? 'inline-block exercise-items'
              : 'flex flex-wrap exam-card-items',
            '',
          ]"
        >
          <template v-for="it in item.questionList" :key="it.id">
            <span
              class="exam-card-item"
              :class="[
                handleClass(it.logStatus, it.isAnalyze),
                { 'click-status': currentSelectNum === it.sort },
              ]"
              @click="selectOption(it)"
            >
              <!-- <span class="absolute z-10 font-gilroy-bold">{{ it.logStatus }}-{{ it.isAnalyze }}</span> -->
              <span class="absolute z-10 font-gilroy-bold">{{ it.sort }}</span>
              <span
                v-if="
                  showCorrected(it.ptypeValueId, it.awarding, it.isSelect, it.selectMark)
                "
                class="exam-card-corrected"
              >
                <span
                  class="inline-block w-[86px] leading-[33px] scale-83 origin-top-left"
                  style="transform: scale(0.833)"
                >已批改</span>
              </span>
            </span>
          </template>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { PropType } from 'vue'
const { track } = useTrackEvent()

interface StatusItem {
  label: string;
  color: string;
  type: number;
  key?: number | string;
}
const props = defineProps({
  quesList: {
    type: Array as PropType<any[]>,
    default: () => [],
  },
  // 是否是解析状态 不显示底部栏
  isAnalysis: {
    type: Boolean,
    default: false,
  },
  // 练习模式 一题一页1  考试模式 通栏2
  model: {
    type: Number,
    default: 1,
  },
  // 用户当前切换的题
  sortNum: {
    type: Number,
    default: 1,
  },
  // 是否固定内容高度
  isFixedHeight: {
    type: Boolean,
    default: true,
  },
  // 是否是试卷类型，默认不是，处理数据逻辑不同
  isExamPaper: {
    type: Boolean,
    default: false,
  },
  showType: {
    type: Array,
    default: () => [],
  },
  status1Text: {
    type: String,
    default: '未答',
  },
  status2Text: {
    type: String,
    default: '已答',
  },
  status3Text: {
    type: String,
    default: '正确',
  },
  status4Text: {
    type: String,
    default: '错误',
  },
  status6Text: {
    type: String,
    default: '半对',
  },
  moduleType: {
    type: Number,
    default: 0,
  },
  // 是否显示已批改标识
  isShowCorrected: {
    type: Boolean,
    default: false,
  },
  // 是否是报告页面
  isReport: {
    type: Boolean,
    default: false,
  },
})
/**
 * [AI-GEN] 答题卡滚动到底部时触发加载更多
 */
function debounce (fn: Function, delay: number) {
  let timer: ReturnType<typeof setTimeout> | null = null
  return function (this: any, ...args: any[]) {
    if (timer) {
      clearTimeout(timer)
    }
    timer = setTimeout(() => {
      fn.apply(this, args)
    }, delay)
  }
}

/**
 * [AI-GEN] 答题卡滚动到底部时触发加载更多
 */
const handleScroll = debounce((e: Event) => {
  const target = e.target as HTMLElement
  if (target.scrollTop + target.clientHeight >= target.scrollHeight - 2) {
    console.log('滚动到底部')
    // 滚动到底部，触发加载下一页
    emit('loadMore')
  }
}, 500)

// 处理后的答题卡
const quesCardList = ref()
const quesTypeList = ref<any>([])

/**
 * [AI-GEN]降低函数复杂度
 */
// 定义一个函数，用于判断是否使用特殊状态
const shouldUseSpecialStatus = (model: number, isAnalyze: number): boolean => {
  // 根据做题模式判断展示样式，非考试模式单选判断直接展示试题对错状态
  return (
    props.isAnalysis || model === 4 || (isAnalyze === 1 && (model === 1 || model === 3))
  )
}

const changeOriginLogStatus = (ques: any) => {
  // 解析模式 | 练习模式已解析
  if (shouldUseSpecialStatus(props.model, ques.isAnalyze)) {
    return ques.logStatus
  }
  // 有答案 练习模式未解析
  if (ques.logAnswer) {
    if ([4, 6].includes(ques.ptypeValueId)) {
      if (ques.logAnswer && typeof ques.logAnswer === 'string') {
        ques.logAnswer = JSON.parse(ques.logAnswer)
      }

      return ques.logAnswer.length &&
        ques.logAnswer.some(
          (item: any) => item.html || item.imgs.length !== 0 || item.htmlSrc
        )
        ? 2
        : 1
    } else {
      return 2
    }
  }
  // 非解析 练习模式未解析无答案 试卷无答案
  return ques.logStatus
}

const assembleQuesCardList = () => {
  const groupedItems = []
  for (const item of quesTypeList.value) {
    const key = `${item.ptypeValueId}-${item.questionTypeValueId}`
    let existingGroup: any = groupedItems.find(group => group.key === key)
    // 三个本数据不区分题型，走else处理 item.isExercise
    if (existingGroup && !item.isExercise) {
      if (item.childQuestions?.length) {
        item.childQuestions.forEach((itemc: any) => {
          const childs = {
            id: itemc.id,
            sort: itemc.sort,
            logStatus: changeOriginLogStatus(itemc),
            isAnalyze: itemc.isAnalyze,
            ptypeValueId: itemc.ptypeValueId,
          }
          existingGroup.questionList.push(toRaw(childs))
        })
      } else {
        existingGroup.questionList.push(item)
      }
    } else {
      existingGroup = {
        key,
        isExercise: item.isExercise,
        questionList: item.childQuestions?.length ? item.childQuestions : [item],
        questionTypeValueIdTitle: item.questionTypeValueIdTitle,
        ptypeValueIdTitle: item.ptypeValueIdTitle,
        ptypeValueId: item.ptypeValueId,
        questionTypeValueId: item.questionTypeValueId,
      }
      existingGroup.questionList.forEach((que: any) => {
        que.logStatus = changeOriginLogStatus(que)
      })
      groupedItems.push(existingGroup)
    }
  }
  quesCardList.value = groupedItems
}
// 监听传递至答题卡的数据
watch(
  () => props.quesList,
  (newVal) => {
    if (newVal) {
      if (props.isExamPaper || props.moduleType === 3) {
        console.log('quesCardList.value', newVal)
        quesCardList.value = newVal
      } else {
        console.log('quesCardList.value', newVal)
        quesTypeList.value = newVal
        assembleQuesCardList()
      }
    }
  },
  {
    immediate: true,
    deep: props.isExamPaper,
  }
)

watch(
  () => props.quesList.length,
  (newVal) => {
    if (newVal) {
      if (props.isExamPaper || props.moduleType === 3) {
        quesCardList.value = props.quesList
      } else {
        quesTypeList.value = props.quesList
        assembleQuesCardList()
      }
    }
  }
)

// 监听当前题号
watch(
  () => props.sortNum,
  (newVal) => {
    currentSelectNum.value = newVal
  }
)
// 选择题目
const currentSelectNum = ref<number>(props.sortNum || 1)
const selectOption = (obj: any) => {
  console.log('🚀 ~ selectOption ~ obj:11111111111', obj)
  currentSelectNum.value = obj.sort
  emit('select', obj.sort, obj.bigSort)
  // 做题->答题卡切换试题 埋点
  track({
    category: '题库',
    action: props.isReport ? '答题报告-查看试题' : '答题卡切换试题',
  })
}
// 处理更改答题卡的状态
// 1未做 2做了没评分 3对 4错
const statusList = reactive<StatusItem[]>([
  {
    label: props.status3Text,
    color: '#15CCC0',
    type: 3,
  },
  {
    label: props.status4Text,
    color: '#EB2330',
    type: 4,
  },
  {
    label: props.status6Text,
    color: '#15CCC0',
    type: 6,
  },
  {
    label: props.status2Text,
    color: '#F5890C',
    type: 2,
  },
  {
    label: props.status1Text,
    color: '#CCCCCC',
    type: 1,
  },
])

const isExamMode = () => props.isExamPaper
// 用于查找匹配的题目
const findMatchingQuestion = (
  cardItem: any,
  questionItem: any,
  isMaterialQuestion: any
) => {
  // 创建一个匹配条件
  const matchCondition = isMaterialQuestion
    ? (item: any) => item.id === questionItem.childQuestions[0].id
    : (item: any) => item.id === questionItem.id
  // 在cardItem的题目列表中查找匹配的题目
  return cardItem.questionList.find(matchCondition)
}
// 用于更新题目状态
const updateQuestionStatus = (
  matchedQuestion: any,
  logStatus: number,
  isAnalyze?: number
) => {
  // 更新matchedQuestion的logStatus
  matchedQuestion.logStatus = logStatus
  // 如果当前模式为1，且isAnalyze不为空，则更新matchedQuestion的isAnalyze
  if (props.model === 1 && isAnalyze !== undefined) {
    matchedQuestion.isAnalyze = isAnalyze
  }
}

/**
 * 更改答题卡中材料题的做题状态
 *
 * @param {number} logStatus - 新的做题状态
 * @param {object} questionItem - 待更新的题目信息
 * @param {number} [isAnalyze] - 是否分析（可选）
 */
const changeStatus = (logStatus: number, questionItem: any, isAnalyze?: number) => {
  // 如果处于考试模式，直接返回不做处理
  if (isExamMode()) {
    return
  }

  quesCardList.value.forEach((cardItem: any) => {
    // 如果卡项和题目对应的类型值ID相同
    if (cardItem.ptypeValueId === questionItem.ptypeValueId) {
      // 判断问题是否为材料题
      const isMaterialQuestion = questionItem.ptypeValueId === 7
      // 查找匹配的题目
      const matchedQuestion = findMatchingQuestion(
        cardItem,
        questionItem,
        isMaterialQuestion
      )
      // 如果找到了匹配的题目
      if (matchedQuestion) {
        // 更新题目状态
        updateQuestionStatus(matchedQuestion, logStatus, isAnalyze)
      }
    }
  })
}
// 做题不同状态的类名处理
// 答题卡状态映射
const statusToClassNameMapping: { [key: number]: string } = {
  1: 'unfinished-status',
  2: 'finished-status',
  3: 'finished-status',
  4: 'finished-status',
  6: 'finished-status',
}

const handleClass = (status: number, isAnalyze: number) => {
  // 从props中获取model
  const { model } = props

  // 根据status从statusToClassNameMapping中获取基础的className，如果获取不到，则使用unfinished-status
  const baseClassName = statusToClassNameMapping[status] || 'unfinished-status'
  // 如果status为3且shouldUseSpecialStatus为true，则返回correct-status或wrong-status
  if ([3, 4, 6].includes(status) && shouldUseSpecialStatus(model, isAnalyze)) {
    switch (status) {
      case 3:
        return 'correct-status'
      case 4:
        return 'wrong-status'
      case 6:
        return 'correct-half-status'
    }
  }

  return baseClassName
}
const heightClass = computed(() => {
  const { isFixedHeight, isAnalysis } = props
  if (isFixedHeight) {
    if (!isAnalysis) {
      return 'fixcardheight'
    } else {
      return 'h-full'
    }
  } else {
    return ''
  }
})

const emit = defineEmits<{
  (e: 'select', sort: number, bigSort: number): void;
  (e: 'loadMore'): void;
}>()

/**
 * [AI-GEN]
 * 是否显示已批改标识
 */
const showCorrected = computed(
  () => (
    ptypeValueId: number,
    awarding: number,
    isSelect: number,
    selectMark: number
  ) => {
    /**
     * 题型定义:
     * 1 - 单选题
     * 2 - 多选题
     * 3 - 不定项选择题
     * 4 - 填空题
     * 5 - 判断题
     * 6 - 问答题
     * 7 - 材料题
     */

    // 常规题型(单选、多选、不定项、判断)不显示批改状态
    const normalQuestionTypes = [1, 2, 3, 5]
    if (normalQuestionTypes.includes(ptypeValueId)) {
      return false
    }

    // 需要AI批改的填空题判断
    const isAICorrectedFillBlank = ptypeValueId === 4 && awarding === 2

    // 已选中的情况
    if (selectMark === 1) {
      // 填空题且需要AI批改
      if (ptypeValueId === 4 && isAICorrectedFillBlank && isSelect === 1) {
        return props.isShowCorrected
      }

      // 问答题且已选中
      if (ptypeValueId === 6 && isSelect === 1) {
        return props.isShowCorrected
      }
    }

    // 未选中且为问答题或需AI批改的填空题
    if ((ptypeValueId === 6 || isAICorrectedFillBlank) && selectMark === 0) {
      return props.isShowCorrected
    }

    return false
  }
)
const handleIsShow = (type: number) => {
  const { showType, model, isAnalysis } = props
  const isExamMode = model === 2 || model === 5 // 考试模式
  return showType.length
    ? showType.includes(type)
    : !(isExamMode && (type === 3 || type === 4 || type === 6) && !isAnalysis)
}
defineExpose({ changeStatus })
</script>

<style scoped lang="scss">
.exam-card {
  height: calc(100% + 24px - 120px);
  box-sizing: border-box;

  overflow: hidden;

  // 128-31
  // border-bottom: 97px solid #fff;
  &.exam-card-analyze {
    border-bottom: none;
    overflow: visible;
  }

  &-bar {
    display: inline-block;
    width: 8px;
    height: 21px;
    background: #ff9a62;
    border-radius: 8px 8px 8px 8px;
  }

  &-tip {
    font-size: 28px;
    font-weight: 500;
    color: #111111;
    padding-left: 8px;
  }

  &-items {
    span:nth-child(5n) {
      margin-right: 0px !important;
    }
  }

  &-item {
    position: relative;
    // display: inline-block;
    margin-right: 74px;
    margin-bottom: 40px;
    width: 78px;
    height: 78px;
    background: #f7f7f7;
    border-radius: 50%;
    font-size: 30px;
    font-weight: bold;
    color: #999999;
    display: flex;
    justify-content: center;
    align-items: center;
    font-family: DINPro-Bold;
  }

  &-border {
    border: 1px solid #dddddd;
  }

  .cardHeight {
    max-height: calc(100% - 200px);
  }

  &-content {
    // margin-right: -25px;
    // min-height: 600px;
    /* 设置最大高度 */
    overflow-y: auto;
    /* 当内容超出时显示滚动条 */
    // padding-bottom: constant(safe-area-inset-bottom);
    // padding-bottom: env(safe-area-inset-bottom);
  }

  &-corrected {
    @apply absolute w-[72px] h-[28px] bottom-[-14px] bg-[#00C555] rounded-[4px] text-[24px] text-[#fff] text-center z-10;
  }
}

/* iPad上的特定样式 */
@media only screen and (min-device-width: 768px) and (max-device-width: 1024px) and (-webkit-min-device-pixel-ratio: 2) {
  .exam-card-content {
    padding-bottom: 80px;
  }
}

.correct-status {
  color: #15ccc0;
  background-color: #d1fbee;
  border: 1px solid #d1fbee;
}

.correct-half-status {
  @apply text-[#15CCC0] bg-transparent z-10;
  border: 1px solid #15ccc0;

  &::after {
    @apply content-[''] absolute top-0 right-0 w-[50%] h-full bg-[#d1fbee] rounded-e-[100px];
  }
}

.wrong-status {
  background: #ffe5e6;
  color: #f0484a;
  border: 1px solid #ffe5e6;
}

.finished-status {
  background: #fff3da;
  color: #f5890c;
  border: 1px solid #fff3da;
}

.unfinished-status {
  border: 1px solid #dddddd;
  background: #f7f7f7;
  color: #999999;
}

.fixcardheight {
  height: calc(100% - 160px);
}

// @media (min-width: 750PX) {
//   .exam-card-items span:nth-child(6n) {
//     margin-right: 44px !important;
//   }
// }

.exercise-items:nth-of-type(5n) span {
  margin-right: 0 !important;
}

.right-half {
  @apply overflow-hidden;
  border: 1px solid #15ccc0;

  &::before {
    @apply content-[''] absolute left-0 w-[50%] h-full bg-[#ffffff] rounded-s-[50%];
    border: 1px solid #15ccc0;
    border-right: none;
  }
}
</style>
