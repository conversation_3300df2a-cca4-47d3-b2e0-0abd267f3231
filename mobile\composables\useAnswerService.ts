// store
import { useAnswerStore } from '~/stores/answer.store'
const { answerApi } = useApi()

export const useAnswerService = async (type?: string, childType?: string) => {
  const { state } = useAnswerStore()
  if (type !== 'picture') {
    //  查看人工答疑服务设置
    const { data, error } = await answerApi.getManualService({})
    if (!error.value) {
      if (type === 'manual') {
        if (!data.value.manualServiceFlag) {
          Message('人工答疑功能已关闭，如有疑问请联系客服')
          return true
        }
      }
      if (childType === 'voice') {
        if (!data.value.voiceToTextFlag) {
          Message('语音转文字功能已关闭，如有疑问请联系客服')
          return true
        }
      }
      state.manualServiceFlag = data.value.manualServiceFlag
      state.ocrFlag = data.value.ocrFlag
      state.aiReplyFlag = data.value.aiReplyFlag
      state.voiceToTextFlag = data.value.voiceToTextFlag
    }
  }
  if (type !== 'manual') {
    //  查看拍搜服务设置
    const { data: aiData, error: aiError } = await answerApi.getSearchService({})
    if (!aiError.value) {
      if (type === 'picture') {
        if (!aiData.value.searchFlag) {
          Message('拍搜功能已关闭，如有疑问请联系客服')
          return true
        }
      }
      state.searchFlag = aiData.value.searchFlag
      state.searchOcrFlag = aiData.value.searchOcrFlag
      state.searchAiReplyFlag = aiData.value.searchAiReplyFlag
    }
  }
  return false
}
