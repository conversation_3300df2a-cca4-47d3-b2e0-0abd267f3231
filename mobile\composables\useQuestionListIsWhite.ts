export const useQuestionListIsWhite = () => {
  const headerIsWhite = ref<boolean>(false)

  const handleScroll = (event: Event) => {
    const el = event.target as HTMLElement
    const scrollTop = el.scrollTop || window.pageYOffset || document.documentElement.scrollTop
    headerIsWhite.value = scrollTop > 0
  }

  onMounted(async () => {
    document.addEventListener('scroll', handleScroll)
  })

  onUnmounted(() => {
    document.removeEventListener('scroll', handleScroll)
  })

  return {
    headerIsWhite
  }
}
