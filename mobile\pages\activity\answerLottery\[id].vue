<template>
  <div class="relative">
    <NavBar v-if="!isXcx && !isKukeCloudAppWebview" :title="activityInfo?.activityName" custom-back @back="navigateTo('/')" />
    <img class="w-[100%] mb-[140px]" :src="activityInfo?.wapBackgroundPictureUrl" alt="">
    <!--  :class="(!isXcx && !isKukeCloudAppWebview)?'top-[1323px]':'top-[1243px]'" -->
    <div class="fixed w-[100%] left-[0] py-[16px] flex justify-center items-center bg-[#fff] activity_icon">
      <div class="flex w-[702px] items-center h-[108px] gap-[8px]">
        <div
          v-for="(item,index) in buttonConfig.buttonArr"
          :key="index"
          class="scan-style relative overflow-hidden h-[100%] text-[36px] flex flex-col flex-1 justify-center rounded-[24px] text-center cursor-pointer"
          :class="[buttonConfig.buttonArr.length>1 && 'scan-style-slow']"
          :style="{
            background:item.bg||activityInfo?.buttonBackgroundColor,
            color:item.color||activityInfo?.buttonWordColor
          }"
          @click="clickBtn(item)"
        >
          <span class="font-medium">{{ item.name || activityInfo?.activityButtonName }}</span>
          <span v-if="item.tips" class="text-[24px] font-normal">{{ item.tips }}</span>
        </div>
      </div>
    </div>
    <div class="absolute w-[48px] h-[137px] right-[0] top-[96px]" @click="activityRule">
      <img class="w-[100%] h-[100%]" :src="ActRule" alt="">
    </div>
    <!-- 活动规则 -->
    <MaskDialog
      v-model="model"
      custom-class="activity-rule"
      title="活动规则"
      minheight="300px"
      maxheight="600px"
      maxheight-content="200px"
    >
      <div class="scrollbar-thumb">
        <span class="whitespace-pre-wrap">{{ activityInfo?.activityExplain }}</span>
      </div>
    </MaskDialog>
  </div>
</template>
<script setup lang="ts">
import type { ActivityInfoType, ButtonConfig } from './types/type'
import { useAnswerLottery } from './hooks/useAnswerLottery'
import { useUserStore } from '~/stores/user.store'
import ActRule from '@/assets/images/answerLottery/act_rules.png'
const userStore = useUserStore()
const { isXcx, isKukeCloudAppWebview } = useAppConfig()
const { answerLotteryApi } = useApi()
const route = useRoute()
const activityInfo = ref<ActivityInfoType>()
/**
   * 答题抽奖活动id
   */
const questionLotteryId = ref<string>(route.params.id as string)
const {
  buttonConfig,
  getButtonConfig
} = useAnswerLottery(questionLotteryId.value)
const getQuesDetail = async () => {
  const { data } = await answerLotteryApi.questionLotteryDetail({ questionLotteryId: questionLotteryId.value })
  console.log(data.value, '===========')
  activityInfo.value = data.value
  getButtonConfig(data.value?.status)
  Loading(false)
}
useHead({
  titleTemplate: () => {
    return activityInfo.value?.activityName || '答题抽奖'
  }
})
/**
   * 活动规则
   */
const model = ref<boolean>(false)
const activityRule = () => {
  model.value = true
}

const clickBtn = (item:ButtonConfig) => {
  if (!userStore.isLogin) {
    userStore.isLoginFn()
    return
  }
  item?.click?.(activityInfo.value)
}
Loading(true)
setTimeout(() => {
  getQuesDetail()
}, 500)
// onMounted(async () => {
//   await getQuesDetail()
// })
</script>
  <style lang="scss" scoped>
  .scan-style::before {
    will-change: transform; /* 提前告知浏览器动画属性 */
    backface-visibility: hidden; /* 避免闪烁 */
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 30%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255,255,255,0.6)
    );
    transform: skewX(-20deg);
    animation: scan 3s linear infinite;
  }
  .scan-style-slow::before{
      animation: scan 2s linear infinite;
  }
  @keyframes scan {
    0% {
      left: -30%;
    }
    100% {
      left: 200%;
    }
  }
  :deep(.activity-rule) {
  .mask {
    background: url("~/assets/images/answerLottery/bg.png") no-repeat;
    background-size: 100% 600px;
    padding-bottom: 0 !important;
    .mask-content{
      margin-bottom: 50px !important;
    }
  }
}
.activity_icon{
    padding-bottom: calc(constant(safe-area-inset-bottom) + 16px) !important;
    padding-bottom: calc(env(safe-area-inset-bottom) + 16px) !important;
    bottom: 0;
  }
  </style>
