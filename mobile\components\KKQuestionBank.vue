<template>
  <div :style="{ padding: `${styleConfig.bothSideSpace}px 0` }">
    <!-- 分类标签 -->
    <ClassifySwitch
      :style="style"
      class="!pb-0 classify-switch"
      :classify-list="classList"
      :default-checked="currentClassify"
      :is-hide-capsule="isHideCapsule"
      :select-tags="select"
      :show-cate-type="showCateType"
      :data-list="selectDataList"
      :show-num-type="showNumber"
      :page-name="item.props.questionSet.pageName"
      @change="handleClassifyChange"
    >
      <template #bottom>
        <ChooseTag
          ref="chooseTag"
          :list="tagList"
          :selected="select"
          :title="tagTitle"
          :study-level1="currentClassify"
          :module-manage-id="currentModule.examinationPaperModuleId"
          :module-type="currentModule.moduleType"
          :is-hide-capsule="isHideCapsule"
          :scene="1"
          is-show-hot-tag
          is-show-collect-tag
          :page-name="item.props.questionSet.pageName"
          class=" mt-[12px]"
          @select="handleSelect"
          @confirm="handleTagConfirm"
          @close="resetData"
        />
      </template>
    </ClassifySwitch>
    <KKVajraDistrictWap
      v-if="
        item.props.questionSet.isShowVajraAdvertisement && vajraDistrictList.length > 0
      "
      id="tk-king-kong-district"
      :style-config="styleConfig"
      label-key="name"
      :list="vajraDistrictList"
      :custom-columns-num="vajraShowNumber"
    />
    <div
      v-if="isShowDataStatistics || isShowCommonFunction"
      class="w-[702px] mt-[32px] m-auto p-[8px] rounded-[24px]"
      style="background: linear-gradient(180deg, #e3f5ff 1%, #caefff 100%)"
    >
      <!-- 数据统计 -->
      <KKQuestionBankDataStatisticsWap
        v-if="isShowDataStatistics"
        :is-login="isLogin"
        :data="questionDataStatistics"
      />
      <!-- 快捷入口 -->
      <KKQuestionBankCommonFunctionWap
        v-if="isShowCommonFunction"
        id="tk-KKQuestionBankCommonFunction"
        :is-login="isLogin"
        :style-config="styleConfig"
        :data-list="selectDataList"
        :show-num-type="showNumber"
        :page-master-id="pageMasterId"
      />
    </div>
    <!-- 模块以及列表 -->
    <KKExamModelWap
      v-if="isShowExamModel && examinationPaperModuleInfo.length > 0"
      class="mt-[24px]"
      :examination-paper-module-info="examinationPaperModuleInfo"
      :module-manage-id="currentModule.examinationPaperModuleId"
      :exam-paper-list="questionList"
      :show-switch-material="showSwitchMaterial"
      :show-switch-mode="showSwitchMode"
      :is-login="isLogin"
      :query-prams="{
        ...queryData,
        labelName: textBookInfo.title, // 教材标题
        modeName: labelName, // 模式名称
        rightType: textBookInfo.rightType,
        questionType, // 做题模式
      }"
      :custom-style="currentModule.moduleType !== 3 ? style : ''"
      :check-fn="checkModuleManageProt"
      :default-active="defaultChapterActiveId"
      :module-right-type="modulePrivilege.rightType"
      @tabClick="handleTabChange"
      @mode="handleModel"
      @material="handleMaterial"
      @note="(item: any) => handleNoteClick(item)"
      @more="handleMore"
      @login="handleLogin"
      @tips="handleTips"
      @not-started="(item: any) => handleWait(item)"
      @chapter-reset="(item: any) => handleReset(item, getList)"
      @reservation-score="(item: any) => handleSubscribeClick(item)"
      @jump-click="(url: string, item?: any, replace?: boolean, moduleManageId?: string) => jumpClickHandler(url, item, replace, moduleManageId)"
      @chapter-rights="handleTextBookRight"
      @module-purchase="handleModulePurchase"
      @on-exam-type-title-to-detail="(url: string) => jumpClickTitleHandler(url)"
      @on-start-practice="handleStartPractice"
    >
      <template v-if="isEmpty" #exam-empty>
        <KKCEmpty />
      </template>
    </KKExamModelWap>
  </div>
</template>

<script setup lang="ts">
import ChooseTag from '../pages/question-bank/components/choosePopup/Tag.vue'
import ClassifySwitch from './question-bank/ClassifySwitch.vue'
import { useUserStore } from '~/stores/user.store'
import { useQuestionBankStore } from '~/stores/question.bank.store'
import { useQuestionExerciseBookStore } from '~/stores/question.exercise.book.store'
import MODULE_DEFAULT_BG from '@/assets/images/question-bank/module_default_bg.png'
import { useTagTree } from '~/pages/question-bank/hooks/useTagTree'
import type { ClassifyCheckListType, KgModuleType, SelectParams, } from '~/pages/question-bank/types/basic'
const { track } = useTrackEvent()
const getUrlParam = (url: string, paramName: string) => {
  try {
    // 分割URL获取查询字符串部分
    const queryString = url.split('?')[1]
    if (!queryString) {
      return null
    }

    // 使用URLSearchParams解析查询参数
    const urlParams = new URLSearchParams(queryString)
    return urlParams.get(paramName)
  } catch (error) {
    console.error('解析URL参数出错:', error)
    return null
  }
}

const jumpClickHandler = (url: string, item?: any, replace?: boolean, moduleManageId?: string) => {
  const examStatus = getUrlParam(url, 'examStatus')
  const actionMap = new Map([
    ['start', '模块-开始答题/估分/考试'],
    ['continue', '模块-继续答题/估分/考试'],
    ['again', '模块-再次答题/估分/补考练习'],
  ])
  const action = actionMap.get(examStatus || '')
  if (action) {
    track({
      category: '题库',
      action,
      label: currentModule.value.examinationPaperModuleName,
    })
  }
  // 是否是点击的查看报告按钮
  const isViewReport = url.includes('report')
  // 是否是点击的查看解析按钮
  const isAnalysis = url.includes('analysis')
  if (isViewReport || isAnalysis) {
    track({
      category: '题库',
      action: '模块-查看报告/解析',
      label: currentModule.value.examinationPaperModuleName,
    })
  }
  if (item.moduleType === 4) {
    track({
      category: '题库',
      action: '固定刷题-查看详情',
      label: currentModule.value.examinationPaperModuleName,
    })
  }
  handleJumpClick(url, item, replace, moduleManageId)
}

const jumpClickTitleHandler = (url: string) => {
  handleXcxJump(url)
}

const { questionBankApi } = useApi()
const userStore = useUserStore()
const questionBankStore = useQuestionBankStore()
const questionExerciseBookStore = useQuestionExerciseBookStore()
const isLogin = computed(() => !!userStore.isLogin)
const { isXcx } = useAppConfig()
const {
  getTagTreeList,
  tagList,
  handleSelect,
  select,
  tagTitle,
  generateCheckFromSelect,
  resetData,
} = useTagTree({ pageType: 1 })
const {
  getIngList,
  clearSetInterVal,
  handleNote,
  handleSwitchMaterial,
  handleSwitchModel,
  getExerciseModel,
  getTextBookList,
  handleLogin,
  handleTips,
  handleWait,
  handleReset,
  handleSubscribe,
  checkModuleManageProt,
  handleJumpClick,
  handleXcxJump,
  handleTextBookRight,
  objectToQueryString,
} = useQuestionBankHandleEvents()
const { setHistoryState, transformObject } = useHistoryState()
const route = useRoute()

const handleNoteClick = (item: any) => {
  track({
    category: '题库',
    action: '模块-试卷说明',
    label: currentModule.value.examinationPaperModuleName,
  })
  handleNote(item, currentModule.value.examinationPaperModuleId)
}

const { scrollViewBack, scrollViewRecord } = useScrollViewBack()
interface IStyle {
  bothSideSpace?: String;
  componentSpace?: String;
}
interface Props {
  styleConfig?: IStyle;
  item: any;
  title?: string;
}
const props = withDefaults(defineProps<Props>(), {
  styleConfig: () => ({
    bothSideSpace: '12',
    componentSpace: '12',
  }),
  title: '',
})

const isIpad = () => {
  const ua = navigator.userAgent
  return /iPad|PlayBook/i.test(ua)
}

const handleModulePurchase = (data: any) => {
  if (userStore.isLoginFn() && modulePrivilege.value.rightType === 2) {
    if (isXcx && (isIpad() || os?.isPhone)) {
      Message('iOS系统用户如需购买解锁，可联系客服老师解决。')
      return false
    }
    const jumpData = {
      ...select.value,
      moduleManageId: data.examinationPaperModuleId,
      studyLevel1: currentClassify.value,
      targetType: 99,
      targetId: data.examinationPaperModuleId,
      expiry: route.query.expiry || '',
    }
    const url = `/question-bank/order-place?${objectToQueryString(jumpData)}`
    // 跳转权益开通页面
    navigateTo(url)
    track({
      category: '题库',
      action: '立即开通入口',
      label: data.examinationPaperModuleName,
    })
  }
}

const { modulePrivilege, getModulePrivilegeAsync } = useQuestionBankModulePrivilege()

const style = computed(() => {
  const { bothSideSpace } = props.styleConfig
  return {
    padding: `0 ${bothSideSpace}px`,
  }
})
// 默认展开章节
const defaultChapterActiveId = computed(
  () =>
    questionBankStore.getDefaultChapterActiveId() || handleInitDefaultChapterActiveId()
)
const handleInitDefaultChapterActiveId = () => {
  if (questionList.value.length && currentModule.value.moduleType === 3) {
    // 递归找到数组第一个中的第一个children为0的元素
    return findFirst(questionList.value)
  }

  return ''
}

const findFirst = (arr: any[]) => {
  if (!arr.length || !arr[0].children) {
    return ''
  }
  if (arr[0].children.length === 0) {
    return arr[0].id
  } else {
    return findFirst(arr[0].children)
  }
}

/**
 * 做题模式
 */
const questionType = computed(() => questionBankStore.queryData.questionType)

// labelName
const labelName = computed(() => questionBankStore.state.labelName)
// 教材权益信息
const textBookInfo = computed(() => questionBankStore.state.textBookInfo)
// 筛选条件
const initQueryData = computed(() =>
  route.query.studyLevel1
    ? transformObject(route.query)
    : questionBankStore.getQueryData()
)
const queryData = computed(() => questionBankStore.getQueryData())
// 当前分类
const currentClassify = ref(0)
// 题库分类列表
const classList = computed(() => {
  return props.item.props.questionSet.list.map((item: any) => ({
    studyLevel1: item.studyLevel1,
    name: item.name,
  }))
})

// 当前模块信息
const currentModule = computed<KgModuleType>(() => {
  const moduleList = examinationPaperModuleInfo.value
  if (!moduleList || moduleList.length === 0) {
    return {} as KgModuleType
  }
  const module = moduleList.find(
    (item: KgModuleType) =>
      Number(item.examinationPaperModuleId) ===
      Number(questionBankStore.queryData.moduleManageId)
  )
  if (module) {
    setExamModelValue(module)
    return module
  } else {
    moduleList[0] && setExamModelValue(moduleList[0])
    return moduleList[0]
  }
})
const questionDataStatistics = computed(
  () => props.item.props.questionSet.questionDataStatistics
)
const selectDataList = computed(() => props.item.props.questionSet.selectDataList)
const showNumber = computed(() => props.item.props.questionSet.showNumber)
const pageMasterId = computed(() => props.item.props.questionSet.pageMasterId)
const isShowCommonFunction = computed(
  () =>
    props.item.props.questionSet.isShowCommonFunction === 1 &&
    props.item.props.questionSet.showStyle === 1
)
const isShowExamModel = computed(
  () => props.item.props.questionSet.isShowExaminationPaperModule === 1
)
const isShowDataStatistics = computed(
  () => props.item.props.questionSet.isShowDataStatistics === 1
)
const showSwitchMaterial = computed(() => questionBankStore.state.showSwitchMaterial)
const showSwitchMode = computed(() => questionBankStore.state.showSwitchMode)
const isHideCapsule = computed(
  () =>
    props.item.props.questionSet.isShowCommonFunction === 2 ||
    props.item.props.questionSet.showStyle === 1
)
// 选中的标签数据
const tagCheckList = ref<ClassifyCheckListType[]>([])
const tagCheckTitle = ref('')
const lastFetchCheckList = ref<ClassifyCheckListType[]>([])
const vajraShowNumber = computed(() => {
  switch (props.item.props.questionSet.vajraShowNumber) {
    case 1:
      return 5
    case 2:
      return 4
    case 3:
      return 3
    default:
      return 5
  }
})

const moduleManage = computed(() => {
  const currentClassifyData = props.item.props.questionSet.list.find(
    (item: any) => item.studyLevel1 === currentClassify.value
  )
  const KgModuleManage = currentClassifyData
    ? filterModules(currentClassifyData.vajraModuleManage)
    : []
  const classModuleManage = currentClassifyData ? currentClassifyData.moduleManage : []
  return [
    ...KgModuleManage.map((item: any) => ({
      ...item,
      examinationPaperModuleId: item.moduleManageId,
    })),
    ...classModuleManage,
  ]
})

/**
 * 专业分类展示形式
 */
const showCateType = computed(() => props.item.props.questionSet.showCateType)
const vajraDistrictList = ref<any[]>([])
const { moduleList, filterModules, handleFilterModules } = useQuestionFilter()

const getKgModuleList = async () => {
  try {
    // list无数据，金刚区直接展示vajraDistrictList
    if (props.item.props.questionSet.list.length === 0) {
      vajraDistrictList.value = props.item.props.questionSet.vajraDataList
      Loading(false)
      return
    }
    const list = props.item.props.questionSet.list.find(
      (item: any) => item.studyLevel1 === currentClassify.value
    )
    const vajraDataList = list?.vajraModuleManage || []
    const modules = filterModules(vajraDataList)
    if (!modules.length) {
      vajraDistrictList.value = vajraDataList
      Loading(false)
      return
    }
    await handleFilterModules({
      studyLevel1: currentClassify.value,
      moduleManageInfoList: modules,
      tags: select.value,
    })

    vajraDistrictList.value = vajraDataList
      .map((item: any) => {
        const currentModule = moduleList.value.find(
          (module: any) =>
            module.moduleManageId === item.questionModuleId && item.skipId === 1
        )
        item.isShow = currentModule ? currentModule.isShow : 0
        return item
      })
      .filter(
        (item: any) => (item.skipId === 1 && item.isShow === 1) || item.skipId !== 1
      )
  } catch (error) {
    Loading(false)
    console.warn(error)
  }
}

const init = async () => {
  if (route.query.studyLevel1) {
    const customTagSelect = !!questionBankStore.getLastCustomTagHistory(
      Number(route.query.studyLevel1)
    )
    questionBankStore.setQuestionQuery({
      ...transformObject(route.query),
      customTagSelect,
    }, true)
  }
  // 初始化模式
  questionBankStore.setIsShowSwitchMaterial(false)
  questionBankStore.setIsShowSwitchMode(false)
  // 初始化分类
  const initCurrent = classList.value.find(
    (item: any) => item.studyLevel1 === initQueryData.value.studyLevel1
  )
  if (!initCurrent && classList.value.length === 0) {
    // 不存在分类等数据，直接展示金刚区数据
    vajraDistrictList.value = props.item.props.questionSet.vajraDataList
    Loading(false)
    return
  }
  const initClassify: number = initCurrent
    ? initQueryData.value.studyLevel1
    : classList.value[0].studyLevel1
  // 根据存储获取当前分类
  currentClassify.value = initClassify

  await getTagTreeList({
    lastCheckTags: {
      studyLevel1: currentClassify.value,
      subjectType: initQueryData.value.subjectType,
      directionType: initQueryData.value.directionType,
      region: initQueryData.value.region,
      academicSection: initQueryData.value.academicSection,
      examFormat: initQueryData.value.examFormat,
    },
    studyLevel1: currentClassify.value,
    moduleManage: moduleManage.value,
  })
  questionExerciseBookStore.setState({
    studyLevel1: currentClassify.value,
    ...select.value,
  })
  // 获取金刚区数据
  getKgModuleList()
  await getModuleList()
  await getList()
  questionBankStore.setStudyLevel1Name(initCurrent?.name || classList.value[0]?.name)
  if (initQueryData.value.studyLevel1) {
    questionBankStore.setQueryData('moduleManageId', initQueryData.value.moduleManageId)
  }
}

const handleCreateBody = () => {
  return {
    studyLevel1: currentClassify.value,
    moduleManageId: currentModule.value.examinationPaperModuleId,
    ...select.value,
  }
}
const UNDEFINED_TAG_VALUE = -1
const NULL_TAG_VALUE = null
const handelTagParams = (tagValue: any) => {
  if (tagValue === 0) {
    return 0
  } else if (Object.prototype.toString.call(tagValue) === '[object Null]') {
    return NULL_TAG_VALUE
  } else if (typeof tagValue === 'undefined') {
    return UNDEFINED_TAG_VALUE
  } else {
    return tagValue
  }
}

const chooseTag = ref()

// 模块列表
const examinationPaperModuleInfo = ref([])
/**
 * 根据分类标签，获取对应模块列表
 */
const getModuleList = async () => {
  try {
    const body = {
      studyLevel1: currentClassify.value,
      tags: {
        ...select.value,
      },
      moduleManage: moduleManage.value.filter((item: KgModuleType) => item.skipId !== 1),
    }
    const { data, error } = await questionBankApi.getModuleList(body)
    if (!error.value) {
      const { list } = data.value
      if (!list.length) {
        // 更改url
        setHistoryState({ studyLevel1: currentClassify.value, ...select.value })
        Loading(false)
      }
      const hasImg = list?.some((item: any) => item.imgUrl)
      // 设置默认图片
      const hasImgList = list.map((item: any) => {
        return {
          ...item,
          entranceStyle: 2,
          imgUrl: item.imgUrl ? item.imgUrl : MODULE_DEFAULT_BG,
        }
      })
      examinationPaperModuleInfo.value = hasImg ? hasImgList : list
      // 设置模块默认选中
      setDefaultModule()
    }
  } catch (error) {
    Loading(false)
    console.warn(error)
  }
}

/**
 * 处理默认模块
 */
const setDefaultModule = () => {
  // 先查找历史记录
  const history = questionBankStore.findHistory(currentClassify.value)
  if (history) {
    const historyModule = examinationPaperModuleInfo.value.find(
      (item: KgModuleType) => item.examinationPaperModuleId === history.moduleManageId
    )
    if (historyModule) {
      setExamModelValue(historyModule)
      return
    }
  }

  // 无历史记录则选中第一个
  const defaultModule = examinationPaperModuleInfo.value[0]
  if (defaultModule) {
    setExamModelValue(defaultModule)
  }
}

const customTagFlag = ref(false)
// 分类标签提交
const handleTagConfirm = async (data: SelectParams) => {
  const { subjectType = 0, region = 0, academicSection = 0, directionType = 0, examFormat = 0, } = data
  Object.assign(select.value, {
    subjectType,
    region,
    academicSection,
    directionType,
    examFormat,
  })
  questionBankStore.setIsShowSwitchMaterial(false)
  questionBankStore.setIsShowSwitchMode(false)
  questionExerciseBookStore.setState({
    studyLevel1: currentClassify.value,
    ...select.value,
  })
  // 获取金刚区数据
  getKgModuleList()
  // 重置历史记录中标签标识
  questionBankStore.resetHistoryCustomTagSelect(currentClassify.value)
  customTagFlag.value = true
  await getModuleList()
  generateCheckFromSelect()
  getList()
}

const handleGetOthersBodyParams = async (checkFlag = true) => {
  const moduleType = questionBankStore.state.moduleType || currentModule.value.moduleType

  if (checkFlag) {
    if ([1, 3, 4].includes(moduleType)) {
      // 获取做题模式
      await getExerciseModel(currentModule.value.examinationPaperModuleId)
    }
    // 获取教材
    if (moduleType === 3) {
      await getTextBookList(handleCreateBody())
    }
  }

  if (moduleType !== 1) {
    const { studyLevel1, moduleManageId, subjectType, region, directionType, academicSection, examFormat, } = handleCreateBody()
    await getModulePrivilegeAsync({
      studyLevel1,
      moduleManageId,
      subjectType: handelTagParams(subjectType),
      region: handelTagParams(region),
      directionType: handelTagParams(directionType),
      academicSection: handelTagParams(academicSection),
      examFormat: handelTagParams(examFormat),
      moduleType,
    })
  }
}

const classifySwitch = ref()

// 分类切换
const handleClassifyChange = async (studyLevel1: number, studyLevel1Name: string) => {
  Loading(true)
  currentClassify.value = studyLevel1
  questionBankStore.setIsShowSwitchMaterial(false)
  questionBankStore.setIsShowSwitchMode(false)
  questionBankStore.setLastModuleToCookie(studyLevel1)
  questionBankStore.setStudyLevel1Name(studyLevel1Name)
  scrollViewRecord()
  await getTagTreeList({
    studyLevel1: currentClassify.value,
    moduleManage: moduleManage.value,
  })
  questionExerciseBookStore.setState({
    studyLevel1: currentClassify.value,
    ...select.value,
  })
  examinationPaperModuleInfo.value = []
  await getModuleList()
  // 获取金刚区数据
  getKgModuleList()
  await getList()
  // nextTick(() => {
  //   // 重置CustomClassSelect，使二级页面重新选中默认分类
  //   questionBankStore.resetHistoryCustomClassSelect()
  // })
}
// 做题模式切换
const handleTabChange = async (item: KgModuleType) => {
  Loading(true)
  questionBankStore.setIsShowSwitchMaterial(false)
  questionBankStore.setIsShowSwitchMode(false)
  questionBankStore.setQueryData('moduleManageId', item.examinationPaperModuleId)
  scrollViewRecord()
  track({
    category: '题库',
    action: '滑动导航',
    label: item.examinationPaperModuleName,
  })
  await handleClassModuleChange(item)
  await getList()
}
const handleClassModuleChange = async (item: KgModuleType) => {
  questionBankStore.initTextbookAndQuestionTypePrams(item.moduleType)
  await setExamModelValue(item)
}

const setExamModelValue = async (item: KgModuleType) => {
  return new Promise((resolve) => {
    if (item) {
      questionBankStore.setQueryData('moduleManageId', item.examinationPaperModuleId)
      questionBankStore.setModuleType(item.moduleType)
      questionBankStore.setModuleName(item.examinationPaperModuleName)
      questionBankStore.setExamModel(item.examMode)
      questionBankStore.setIsOpenPopular(item.isOpenPopular === 2 ? 2 : 1)
    }
    resolve(true)
  })
}
const getPromise = async () => {
  const { moduleType } = questionBankStore.state.moduleType
    ? questionBankStore.state
    : currentModule.value
  const { studyLevel1, moduleManageId, subjectType, directionType, region, academicSection, examFormat, } = handleCreateBody()
  const baseBody = {
    studyLevel1,
    moduleManageId,
    subjectType: handelTagParams(subjectType),
    directionType: handelTagParams(directionType),
    region: handelTagParams(region),
    academicSection: handelTagParams(academicSection),
    examFormat: handelTagParams(examFormat),
    page: 1,
    pageSize: 10,
  }
  // 兼容老版本queryData处理方式
  handleQueryData(baseBody)
  // 获取列表
  const options = {
    getDailyPracticeList: {
      key: 'getDailyPracticeList',
      body: { ...baseBody, questionType: questionBankStore.queryData.questionType },
    },
    getTestpaperList: { key: 'getTestpaperList', body: baseBody },
    getChapterList: {
      key: 'getChapterList',
      body: { ...baseBody, textbookId: questionBankStore.queryData.textbookId },
    },
    getFixedExamList: {
      key: 'getTestpaperList',
      body: baseBody,
      questionType: questionBankStore.queryData.questionType,
    },
    getAiExerciseList: {
      key: 'getAiExerciseList',
      body: baseBody,
    }
  }
  let optionsKey = ''
  switch (moduleType) {
    case 1:
      clearSetInterVal()
      optionsKey = 'getDailyPracticeList'
      break
    case 2:
      optionsKey = 'getTestpaperList'
      break
    case 3:
      clearSetInterVal()
      optionsKey = 'getChapterList'
      break
    case 4:
      optionsKey = 'getFixedExamList'
      break
    case 5:
      optionsKey = 'getAiExerciseList'
      break
  }
  if (optionsKey) {
    return options[optionsKey as keyof typeof options]
  }
}
const handleQueryData = (baseBody: any) => {
  questionBankStore.initTagFilterQueryPrams()
  questionBankStore.initLearnTargetPrams()
  for (const key in baseBody) {
    questionBankStore.setQueryData(key, baseBody[key])
  }
}
// 列表数据
const questionList = ref([])
const hasTimerModel = [2, 3, 4]
// 是否空数据
const isEmpty = ref(false)
// 获取列表数据
const getList = async (flag = true) => {
  try {
    const { moduleType, examModel } = questionBankStore.state.moduleType
      ? questionBankStore.state
      : currentModule.value
    if (!moduleType && moduleType !== 0) {
      // 更改url
      setHistoryState({ studyLevel1: currentClassify.value, ...select.value })
      Loading(false)
      return
    }
    await handleGetOthersBodyParams(flag)
    const { key, body } = (await getPromise()) as { key: string; body: any }
    if (moduleType === 5) {
      setHistoryState(body)
      return
    }
    // 更新列表数据
    const { data, error } = await questionBankApi[key as keyof typeof questionBankApi]({
      ...body,
    })
    // 存储筛选条件
    questionBankStore.setQuestionQuery({ ...body, customTagSelect: customTagFlag.value }, true)
    // 当前分类标签标识重置，只在主动提交标签为true
    customTagFlag.value = false
    setHistoryState(body)
    lastFetchCheckList.value = deepClone(tagCheckList.value)
    // 标签title
    tagCheckTitle.value = tagCheckList.value
      .map((item: ClassifyCheckListType) => item.title)
      .join(' | ')
    if (!error.value) {
      questionList.value = data.value.list
      isEmpty.value = !data.value.list.length
      if (moduleType === 2 || hasTimerModel.includes(examModel)) {
        getIngList(questionList.value, getList)
      }
      if (moduleType === 3) {
        // 章节练习，根据接口返回的上次选中数据，进行标红等处理
        questionBankStore.setDefaultChapterActiveId(
          data.value.lastTargetResultId,
          questionList.value
        )
      }
    }
    nextTick(() => {
      if (moduleType === 3 && defaultChapterActiveId.value) {
        setTimeout(() => {
          scrollViewBack()
        }, 300)
      } else {
        scrollViewBack()
      }
    })
  } catch (error) {
    console.error(error)
    Loading(false)
  } finally {
    Loading(false)
  }
}

/**
 * 是否点击生效
 */
const isAllowClick = computed(() => {
  return questionBankStore.state.isAllowClick
})

/**
 * 教材练习模式切换
 */
const handleMaterial = () => {
  if (!isAllowClick.value) {
    return
  }
  track({
    category: '题库',
    action: '教材弹窗',
    label: currentModule.value.examinationPaperModuleName,
  })
  questionBankStore.setIsAllowClick(false)
  handleSwitchMaterial({
    ...handleCreateBody(),
    cb: () => {
      getList(false)
    },
  })
}

/**
 * 做题模式切换
 */
const handleModel = async () => {
  if (!isAllowClick.value) {
    return
  }
  track({
    category: '题库',
    action: '答题模式弹窗',
    label: currentModule.value.examinationPaperModuleName,
  })
  questionBankStore.setIsAllowClick(false)
  handleSwitchModel({
    moduleType: currentModule.value.moduleType,
    moduleManageId: currentModule.value.examinationPaperModuleId,
  })
}

const handleMore = () => {
  const { examMode, examinationPaperModuleId, examinationPaperModuleName, isOpenPopular, moduleType, } = currentModule.value
  track({
    category: '题库',
    action: '模块详情',
    label: examinationPaperModuleName,
  })
  const {
    studyLevel1,
    subjectType,
    directionType,
    region,
    academicSection,
    examFormat,
    questionType,
    textbookId,
  } = queryData.value
  if (moduleType === 5) {
    // Ai练习
    handleStartPractice(currentModule.value, 'more')
    return
  }
  const url = `/question-bank/list?${objectToQueryString({
    examModel: examMode,
    moduleName: examinationPaperModuleName,
    moduleType,
    moduleManageId: examinationPaperModuleId,
    studyLevel1,
    subjectType,
    academicSection,
    directionType,
    region,
    examFormat,
    isOpenPopular,
    questionType:
      moduleType === 4 ? questionBankStore.getFixedQuestionType() : questionType,
    textbookId,
  })}`
  navigateTo(url)
}

// AI练习开始答题
const handleStartPractice = (item: any, type: string) => {
  if (!type) {
    track({
      category: '题库',
      action: '模块详情',
      label: item.examinationPaperModuleName,
    })
  }
  navigateTo(`/question-bank/ai-exercise?${objectToQueryString({
    moduleManageId: item.examinationPaperModuleId,
    studyLevel1: currentClassify.value,
    subjectType: select.value.subjectType,
    region: select.value.region,
    directionType: select.value.directionType,
    academicSection: select.value.academicSection,
    examFormat: select.value.examFormat,
  })}`)
}

// 预约估分
const handleSubscribeClick = (item: any) => {
  const { studyLevel1, moduleManageId, subjectType, directionType, region, academicSection, examFormat, } = handleCreateBody()
  handleSubscribe(
    {
      testpaperId: item.id,
      moduleManageId,
      studyLevel1,
      subjectType,
      region,
      directionType,
      academicSection,
      examFormat,
    },
    item
  ).then((flag) => {
    if (!flag) {
      return false
    }
    // 更新当前数据
    const current: any = questionList.value.find(
      (question: any) => question.id === item.id
    )
    if (current) {
      current.btnStatus = 3
      current.clickCount && current.clickCount++
    }
  })
}

// fix: 小程序中打开新webview，返回无法更新页面数据
if (isClient) {
  const { isHidden } = useVisibilityChange('')
  watch(isHidden, (hidden) => {
    if (!hidden && isXcx) {
      Loading(true)
      nextTick(() => {
        init()
      })
    }
  })

  if (route.path.includes('/cp/')) {
    useHead({
      title: props.title || '题库',
    })
  }
}

/**
 * 监听页面隐藏
 */
const handleVisibilityChange = async () => {
  if (document.hidden) {
    scrollViewRecord()
  }
}

let tkKingKongDistrictDom: any = null

let ulDom: any = null

const findLi = (event: any) => {
  let target = event.target
  // 向上查找最近的 li 元素
  while (
    target &&
    target.className !== 'kb-question-bank-common-function-wap-wrapper-layout__item'
  ) {
    target = target.parentNode
    if (target === ulDom) {
      return
    } // 防止无限循环
  }
  if (target) {
    const text = target.textContent
    track({
      category: '题库',
      action: '常用功能',
      label: text,
    })
  }
}

const findKingKongDistrictIem = (event: any) => {
  let target = event.target
  // 向上查找最近的 li 元素
  while (target && target.tagName !== 'LI') {
    target = target.parentNode
    if (target === tkKingKongDistrictDom) {
      return
    } // 防止无限循环
  }
  if (target) {
    const text = target.firstChild.childNodes[1].textContent
    track({
      category: '题库',
      action: '金刚区',
      label: text,
    })
  }
}

const trackDomHandler = () => {
  ulDom = document.querySelector('#tk-KKQuestionBankCommonFunction')
  ulDom?.addEventListener('click', findLi)

  tkKingKongDistrictDom = document.querySelector('.kb-vajra-district')
  tkKingKongDistrictDom?.addEventListener('click', findKingKongDistrictIem)
}

onMounted(() => {
  Loading(true)
  nextTick(() => {
    questionBankStore.setIsAllowClick(true)
    classifySwitch.value = document.querySelector('.classify-switch')
    init()
    document?.addEventListener('visibilitychange', handleVisibilityChange)
    trackDomHandler()
  })
})

onUnmounted(() => {
  clearSetInterVal()
  ulDom?.removeEventListener('click', findLi)
  tkKingKongDistrictDom?.removeEventListener('click', findKingKongDistrictIem)
  document.removeEventListener('visibilitychange', handleVisibilityChange)
})
</script>
