<template>
  <div class="options-wrap">
    <div v-for="(item, index) in optionsCom" :key="index" class="options-item">
      {{ item.key }}.
      <div class="break-all">
        <KKCHtmlMathJax :content="item.value" :is-answer-questions="true" />
      </div>

      <KKCAudioPlayer
        v-if="item.valueSrc"
        class="bg-[#f1f2f3] !w-[100%] audio-wrap"
        :icon-name="'icon-yinpinbofang'"
        :playing-icon="'icon-yinpinzanting'"
        :icon-size="48"
        :url="item.valueSrc"
      />
    </div>
  </div>
</template>
<script lang="ts" setup>

const props = defineProps<{
    options: string | undefined
}>()

const optionsCom = computed(() => {
  if (typeof props.options === 'string') {
    try {
      // 尝试将字符串解析为 JSON 对象
      return JSON.parse(props.options)
    } catch (error) {
      // 如果解析失败，返回一个默认值或者处理错误
      console.error('Invalid JSON:', error)
      return {} // 返回一个空对象作为示例，默认值可以根据实际情况调整
    }
  } else {
    // 如果 props.options 不是字符串，则直接返回
    return props.options
  }
})

</script>
<style lang="scss" scoped>

.options-wrap{
  font-size: 24px;
  color: #000000;

  .options-item{
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 24px;
    word-break: break-all;
  }
  .audio-wrap{
      border-radius: 8px 40px 40px 40px!important;
  }
}

</style>
