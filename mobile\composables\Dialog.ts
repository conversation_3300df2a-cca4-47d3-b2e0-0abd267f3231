import { createApp, reactive } from 'vue'
import KKDialog from '~/components/KKDialog/index.vue'

interface Props {
  title?: string;
  message?: string | Element;
  confirmText?: string;
  cancelText?: string;
  showCancelButton?: boolean;
  showConfirmButton?: boolean;
  teleport?: string | Element;
  onConfirm?: () => void;
  onCancel?: () => void;
}

export const Dialog = (options: Props) => {
  let container: HTMLDivElement
  let AppInstance: any = null

  const state = reactive<{
    show: boolean;
    [key: string]: any
  }>({
    show: true
  })

  const toggle = (show: boolean) => {
    state.show = show
    AppInstance && AppInstance.unmount()
    container && document.body.removeChild(container)
  }

  const handleConfirm = () => {
    toggle(false)
    options.onConfirm && options.onConfirm()
  }

  const handleCancel = () => {
    toggle(false)
    console.log('handleCancel', state)
    options.onCancel && options.onCancel()
  }
  if (process.client) {
    container = document.createElement('div')
    AppInstance = createApp(KKDialog, {
      ...options,
      ...state,
      onConfirm: handleConfirm,
      onCancel: handleCancel
    })
    document.body.appendChild(container)
    AppInstance.mount(container)
  }
}
