import { useQuestionBankStore } from '~/stores/question.bank.store'

import type { KgModuleType } from '~/pages/question-bank/types/basic'

export const useQuestionBankModuleInfo = () => {
  const { questionBankApi } = useApi()
  const questionBankStore = useQuestionBankStore()

  // 当前模块信息
  const currentModule = reactive({
    state: 1,
    moduleType: 0,
    examMode: 0,
    examinationPaperModuleName: '',
    examinationPaperModuleId: ''
  })

  /**
   * 模块名称
   */
  const moduleTitle = ref('')

  const examinationPaperModuleList = ref<any[]>([])

  const setExamModelValue = (item: KgModuleType) => {
    if (item) {
      questionBankStore.setQueryData('moduleManageId', item.examinationPaperModuleId)
      questionBankStore.setModuleType(item.moduleType)
      questionBankStore.setModuleName(item.examinationPaperModuleName)
      questionBankStore.setExamModel(item.examMode)
      questionBankStore.setIsOpenPopular(item.isOpenPopular === 2 ? 2 : 1)
    }
  }

  /**
   * 获取模块相关信息
   */
  const getModuleInfo = async (moduleManageId: string) => {
    try {
      const { data, error } = await questionBankApi.getModuleDetail({
        moduleManageId
      })

      if (!error.value) {
        const { id, moduleType, examMode, name, state } = data.value
        currentModule.moduleType = moduleType
        currentModule.examMode = examMode
        currentModule.examinationPaperModuleName = name
        currentModule.examinationPaperModuleId = id || moduleManageId
        currentModule.state = state

        moduleTitle.value = name

        examinationPaperModuleList.value = [{
          id,
          examMode,
          name,
          moduleType,
          examinationPaperModuleName: name,
          examinationPaperModuleId: id,
        }]
      }
    } catch (error) {
      console.log(error)
    }
  }

  return {
    moduleTitle,
    currentModule,
    examinationPaperModuleList,
    setExamModelValue,
    getModuleInfo
  }
}
