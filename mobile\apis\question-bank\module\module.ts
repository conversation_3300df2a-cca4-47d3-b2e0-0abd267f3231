import type {
  SaveUserCategoryModuleTagBodyType,
  GetModuleStudyLevelCategoryTagsListBodyType,
  GetModuleManageTagsListBodyType,
  GetModuleManagePrivilegeType,
  DoQuestionProtBodyType,
  ModuleManageTagsType,
} from '../types'

enum Api {
  // 题库组件化
  moduleCategoryListTree = '/kukecorequestion/wap/moduleManageTags/moduleStudyLevelListTree',
  // 刷题数据统计
  getQuestionCountProt = '/kukecorequestion/wap/questionRank/getQuestionCountProt',
  // 保存用户分类模块标签
  saveUserCategoryProt = '/kukecorequestion/wap/moduleManageTags/saveUserGoalsProt',
  // 获取用户分类模块标签
  getUserCategoryProt = '/kukecorequestion/wap/moduleManageTags/getUserGoalsProt',
  // 获取产品线专业分类下关联的标签
  getModuleStudyLevelCategoryTagsList = '/kukebasesystem/wap/productCategory/productCategoryLabelValueList',
  // 获取模块标签列表（新分类改造需求 v3.0.3）
  getModuleManageTagsList = '/kukecorequestion/wap/moduleManageTagsV1/getTagLists',
  // 获取用户分类模块标签（新分类改造需求 v3.0.3）
  getUserCategoryV3Prot = '/kukecorequestion/wap/moduleManageTagsV1/getUserGoalsProt',
  // 保存用户分类模块标签（新分类改造需求 v3.0.3）
  saveUserCategoryV3Prot = '/kukecorequestion/wap/moduleManageTagsV1/addUserGoalsProt',
  // 人气统计埋点
  buriedPopularForQuestion = '/kukecorequestion/wap/testpaper/doClick',
  // 判断模块权益
  getModulePrivilege = '/kukecorequestion/kqQuestionRight/judgeModuleRight',
  // 获取教材权益
  getTextBookPrivilege = '/kukecorequestion/wap/chapter/textbookRightType',
  // 获取模块管理标签列表  题库首页使用
  getHomeTagLists = '/kukecorequestion/wap/moduleManageTagsV1/getTagListsV1',
  // 获取模块详情
  getModuleDetail = '/kukecorequestion/wap/moduleManageTagsV1/getModuleInfo',
  // 获取状态分类
  getModuleInfoAndStudyLevelName = '/kukecorequestion/wap/moduleManageTagsV1/getModuleInfoAndStudyLevelName',
}

/**
 * 题库组件化---专业分类
 *
 * @param {any} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function getModuleCategoryListTree (body: any) {
  return useHttp<any>(Api.moduleCategoryListTree, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 题库刷题统计
 *
 * @param {any} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function getQuestionCountProt (body: any) {
  return useHttp<any>(Api.getQuestionCountProt, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 保存用户分类模块标签
 *
 * @param {SaveUserCategoryModuleTagBodyType} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function saveUserCategoryModuleTag (body: SaveUserCategoryModuleTagBodyType) {
  return useHttp<any>(Api.saveUserCategoryProt, {
    method: 'post',
    body,
    transform: res => res.data,
    watch: false
  })
}

/**
 * 获取用户分类模块标签
 *
 * @returns {Promise<any>} - 返回数据
 */
export async function getUserCategoryModuleTag () {
  return useHttp<any>(Api.getUserCategoryProt, {
    method: 'post',
    transform: res => res.data,
    watch: false
  })
}

/**
 * 获取产品线分类下标签
 *
 * @param {GetModuleStudyLevelCategoryTagsListBodyType} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function getModuleStudyLevelCategoryTagsList (body: GetModuleStudyLevelCategoryTagsListBodyType) {
  return useHttp<any>(Api.getModuleStudyLevelCategoryTagsList, {
    method: 'post',
    body,
    transform: res => res.data,
    watch: false
  })
}

/**
 * 获取模块标签列表
 *
 * @param {GetModuleManageTagsListBodyType} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function getModuleManageTagsList (body: GetModuleManageTagsListBodyType) {
  return useHttp<any>(Api.getModuleManageTagsList, {
    method: 'post',
    body,
    transform: res => res.data,
    watch: false
  })
}

/**
 * 获取用户最后一次记录
 *
 * @returns {Promise<any>} - 返回数据
 */
export async function getUserCategoryV3Prot () {
  return useHttp<any>(Api.getUserCategoryV3Prot, {
    method: 'post',
    transform: res => res.data,
    watch: false
  })
}

/**
 * 保存用户分类模块标签 v3
 *
 * @param {GetModuleManageTagsListBodyType} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function saveUserCategoryV3Prot (body: GetModuleManageTagsListBodyType) {
  return useHttp<any>(Api.saveUserCategoryV3Prot, {
    method: 'post',
    body,
    transform: res => res.data,
    watch: false
  })
}

/**
 * 试卷点击埋点
 *
 * @param {DoQuestionProtBodyType & {examMode: number}} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function buriedPopularForQuestion (body: DoQuestionProtBodyType & {examMode: number}) {
  return useHttp<any>(Api.buriedPopularForQuestion, {
    method: 'post',
    body,
    transform: res => res.data,
    watch: false
  })
}

/**
 * 获取模块权益
 *
 * @param {GetModuleManagePrivilegeType} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function getModulePrivilege (body: GetModuleManagePrivilegeType) {
  return useHttp<any>(Api.getModulePrivilege, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 获取教材权益
 *
 * @param {GetBookPrivilegeType} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function getTextBookPrivilege (body: any) {
  return useHttp<any>(Api.getTextBookPrivilege, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 题库首页标签列表
 *
 * @param {ModuleManageTagsType} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function getModuleManageHomeTags (body: ModuleManageTagsType) {
  return useHttp<any>(Api.getHomeTagLists, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 获取模块详情
 *
 * @param {{ moduleManageId: string }} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function getModuleDetail (body: { moduleManageId: string }) {
  return useHttp<any>(Api.getModuleDetail, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 获取状态分类
 *
 * @param {any} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function getModuleInfoAndStudyLevelName (body: any) {
  return useHttp<any>(Api.getModuleInfoAndStudyLevelName, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}
