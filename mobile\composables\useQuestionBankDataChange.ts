import type { ClassifyCheckListType } from '~/pages/question-bank/types/basic'

export const useQuestionBankDataChange = () => {
  const getTagCheckFieldObj = (arr: ClassifyCheckListType[]) => {
    return Array.from(arr, ({ field, id }: { field: string; id: any }) => [field, id]).reduce((pre: any, [field, id]) => {
      pre[field] = id
      return pre
    }, {})
  }

  const handleCreateBody = (tagCheckList: ClassifyCheckListType[]) => {
    const { subjectType, directionType, region, academicSection, examFormat } = getTagCheckFieldObj(tagCheckList)
    // null 值为取消，需要传null。其他不存在传0
    return {
      subjectType: handelTagParams(subjectType),
      directionType: handelTagParams(directionType),
      region: handelTagParams(region),
      academicSection: handelTagParams(academicSection),
      examFormat: handelTagParams(examFormat)
    }
  }

  const UNDEFINED_TAG_VALUE = -1
  const NULL_TAG_VALUE = null
  const handelTagParams = (tagValue: any) => {
    if (tagValue === 0) {
      return 0
    } else if (Object.prototype.toString.call(tagValue) === '[object Null]') {
      return NULL_TAG_VALUE
    } else if (typeof tagValue === 'undefined') {
      return UNDEFINED_TAG_VALUE
    } else {
      return tagValue
    }
  }

  return {
    UNDEFINED_TAG_VALUE,
    NULL_TAG_VALUE,
    getTagCheckFieldObj,
    handleCreateBody,
    handelTagParams
  }
}
