import process from 'node:process';globalThis._importMeta_={url:import.meta.url,env:process.env};import { tmpdir } from 'node:os';
import { Server } from 'node:http';
import { resolve, dirname, join } from 'node:path';
import nodeCrypto from 'node:crypto';
import { parentPort, threadId } from 'node:worker_threads';
import { defineEventHandler, handleCacheHeaders, splitCookiesString, createEvent, fetchWithEvent, isEvent, eventHandler, setHeaders, sendRedirect, proxyRequest, getRequestHeader, setResponseStatus, setResponseHeader, send, getRequestHeaders, getRequestURL, getResponseHeader, setResponseHeaders, getRequestHost, appendResponseHeader, removeResponseHeader, createError, getRequestProtocol, getResponseStatus, getResponseHeaders, createApp, createRouter as createRouter$1, to<PERSON><PERSON><PERSON><PERSON><PERSON>, lazy<PERSON><PERSON><PERSON>and<PERSON>, getRouterParam, readBody, getQuery as getQuery$1, useBase, getCookie, setCookie, getResponseStatusText } from 'file://D:/workspace/0.nuxt3/migration/kuke-cloud---next/node_modules/h3/dist/index.mjs';
import { getRequestDependencies, getPreloadLinks, getPrefetchLinks, createRenderer } from 'file://D:/workspace/0.nuxt3/migration/kuke-cloud---next/node_modules/vue-bundle-renderer/dist/runtime.mjs';
import { stringify, uneval } from 'file://D:/workspace/0.nuxt3/migration/kuke-cloud---next/node_modules/devalue/index.js';
import { renderToString } from 'file://D:/workspace/0.nuxt3/migration/kuke-cloud---next/node_modules/vue/server-renderer/index.mjs';
import { renderSSRHead } from 'file://D:/workspace/0.nuxt3/migration/kuke-cloud---next/node_modules/@unhead/ssr/dist/index.mjs';
import { parseURL, withoutBase, joinURL, getQuery, withQuery, decodePath, withLeadingSlash, withoutTrailingSlash } from 'file://D:/workspace/0.nuxt3/migration/kuke-cloud---next/node_modules/ufo/dist/index.mjs';
import { klona } from 'file://D:/workspace/0.nuxt3/migration/kuke-cloud---next/node_modules/klona/dist/index.mjs';
import defu, { defuFn } from 'file://D:/workspace/0.nuxt3/migration/kuke-cloud---next/node_modules/defu/dist/defu.mjs';
import destr from 'file://D:/workspace/0.nuxt3/migration/kuke-cloud---next/node_modules/destr/dist/index.mjs';
import { snakeCase } from 'file://D:/workspace/0.nuxt3/migration/kuke-cloud---next/node_modules/scule/dist/index.mjs';
import { createHooks } from 'file://D:/workspace/0.nuxt3/migration/kuke-cloud---next/node_modules/hookable/dist/index.mjs';
import { createFetch, Headers as Headers$1 } from 'file://D:/workspace/0.nuxt3/migration/kuke-cloud---next/node_modules/ofetch/dist/node.mjs';
import { fetchNodeRequestHandler, callNodeRequestHandler } from 'file://D:/workspace/0.nuxt3/migration/kuke-cloud---next/node_modules/node-mock-http/dist/index.mjs';
import { createStorage, prefixStorage } from 'file://D:/workspace/0.nuxt3/migration/kuke-cloud---next/node_modules/unstorage/dist/index.mjs';
import unstorage_47drivers_47fs from 'file://D:/workspace/0.nuxt3/migration/kuke-cloud---next/node_modules/unstorage/drivers/fs.mjs';
import { digest } from 'file://D:/workspace/0.nuxt3/migration/kuke-cloud---next/node_modules/ohash/dist/index.mjs';
import { toRouteMatcher, createRouter } from 'file://D:/workspace/0.nuxt3/migration/kuke-cloud---next/node_modules/radix3/dist/index.mjs';
import { readFile } from 'node:fs/promises';
import consola from 'file://D:/workspace/0.nuxt3/migration/kuke-cloud---next/node_modules/consola/dist/index.mjs';
import { ErrorParser } from 'file://D:/workspace/0.nuxt3/migration/kuke-cloud---next/node_modules/youch-core/build/index.js';
import { Youch } from 'file://D:/workspace/0.nuxt3/migration/kuke-cloud---next/node_modules/youch/build/index.js';
import { SourceMapConsumer } from 'file://D:/workspace/0.nuxt3/migration/kuke-cloud---next/node_modules/source-map/source-map.js';
import { defineNitroPlugin as defineNitroPlugin$1 } from 'file://D:/workspace/0.nuxt3/migration/kuke-cloud---next/node_modules/nitropack/dist/runtime/plugin.mjs';
import { useCompression } from 'file://D:/workspace/0.nuxt3/migration/kuke-cloud---next/node_modules/h3-compression/dist/index.mjs';
import { promises } from 'node:fs';
import { fileURLToPath } from 'node:url';
import { dirname as dirname$1, resolve as resolve$1 } from 'file://D:/workspace/0.nuxt3/migration/kuke-cloud---next/node_modules/pathe/dist/index.mjs';
import { createServerHead as createServerHead$1 } from 'file://D:/workspace/0.nuxt3/migration/kuke-cloud---next/node_modules/unhead/dist/index.mjs';
import { unref, version } from 'file://D:/workspace/0.nuxt3/migration/kuke-cloud---next/node_modules/vue/index.mjs';
import { defineHeadPlugin } from 'file://D:/workspace/0.nuxt3/migration/kuke-cloud---next/node_modules/@unhead/shared/dist/index.mjs';

const serverAssets = [{"baseName":"server","dir":"D:/workspace/0.nuxt3/migration/kuke-cloud---next/mobile/server/assets"}];

const assets$1 = createStorage();

for (const asset of serverAssets) {
  assets$1.mount(asset.baseName, unstorage_47drivers_47fs({ base: asset.dir, ignore: (asset?.ignore || []) }));
}

const storage = createStorage({});

storage.mount('/assets', assets$1);

storage.mount('root', unstorage_47drivers_47fs({"driver":"fs","readOnly":true,"base":"D:/workspace/0.nuxt3/migration/kuke-cloud---next/mobile"}));
storage.mount('src', unstorage_47drivers_47fs({"driver":"fs","readOnly":true,"base":"D:/workspace/0.nuxt3/migration/kuke-cloud---next/mobile/server"}));
storage.mount('build', unstorage_47drivers_47fs({"driver":"fs","readOnly":false,"base":"D:/workspace/0.nuxt3/migration/kuke-cloud---next/mobile/.nuxt"}));
storage.mount('cache', unstorage_47drivers_47fs({"driver":"fs","readOnly":false,"base":"D:/workspace/0.nuxt3/migration/kuke-cloud---next/mobile/.nuxt/cache"}));
storage.mount('data', unstorage_47drivers_47fs({"driver":"fs","base":"D:/workspace/0.nuxt3/migration/kuke-cloud---next/mobile/.data/kv"}));

function useStorage(base = "") {
  return base ? prefixStorage(storage, base) : storage;
}

const Hasher = /* @__PURE__ */ (() => {
  class Hasher2 {
    buff = "";
    #context = /* @__PURE__ */ new Map();
    write(str) {
      this.buff += str;
    }
    dispatch(value) {
      const type = value === null ? "null" : typeof value;
      return this[type](value);
    }
    object(object) {
      if (object && typeof object.toJSON === "function") {
        return this.object(object.toJSON());
      }
      const objString = Object.prototype.toString.call(object);
      let objType = "";
      const objectLength = objString.length;
      objType = objectLength < 10 ? "unknown:[" + objString + "]" : objString.slice(8, objectLength - 1);
      objType = objType.toLowerCase();
      let objectNumber = null;
      if ((objectNumber = this.#context.get(object)) === void 0) {
        this.#context.set(object, this.#context.size);
      } else {
        return this.dispatch("[CIRCULAR:" + objectNumber + "]");
      }
      if (typeof Buffer !== "undefined" && Buffer.isBuffer && Buffer.isBuffer(object)) {
        this.write("buffer:");
        return this.write(object.toString("utf8"));
      }
      if (objType !== "object" && objType !== "function" && objType !== "asyncfunction") {
        if (this[objType]) {
          this[objType](object);
        } else {
          this.unknown(object, objType);
        }
      } else {
        const keys = Object.keys(object).sort();
        const extraKeys = [];
        this.write("object:" + (keys.length + extraKeys.length) + ":");
        const dispatchForKey = (key) => {
          this.dispatch(key);
          this.write(":");
          this.dispatch(object[key]);
          this.write(",");
        };
        for (const key of keys) {
          dispatchForKey(key);
        }
        for (const key of extraKeys) {
          dispatchForKey(key);
        }
      }
    }
    array(arr, unordered) {
      unordered = unordered === void 0 ? false : unordered;
      this.write("array:" + arr.length + ":");
      if (!unordered || arr.length <= 1) {
        for (const entry of arr) {
          this.dispatch(entry);
        }
        return;
      }
      const contextAdditions = /* @__PURE__ */ new Map();
      const entries = arr.map((entry) => {
        const hasher = new Hasher2();
        hasher.dispatch(entry);
        for (const [key, value] of hasher.#context) {
          contextAdditions.set(key, value);
        }
        return hasher.toString();
      });
      this.#context = contextAdditions;
      entries.sort();
      return this.array(entries, false);
    }
    date(date) {
      return this.write("date:" + date.toJSON());
    }
    symbol(sym) {
      return this.write("symbol:" + sym.toString());
    }
    unknown(value, type) {
      this.write(type);
      if (!value) {
        return;
      }
      this.write(":");
      if (value && typeof value.entries === "function") {
        return this.array(
          [...value.entries()],
          true
          /* ordered */
        );
      }
    }
    error(err) {
      return this.write("error:" + err.toString());
    }
    boolean(bool) {
      return this.write("bool:" + bool);
    }
    string(string) {
      this.write("string:" + string.length + ":");
      this.write(string);
    }
    function(fn) {
      this.write("fn:");
      if (isNativeFunction(fn)) {
        this.dispatch("[native]");
      } else {
        this.dispatch(fn.toString());
      }
    }
    number(number) {
      return this.write("number:" + number);
    }
    null() {
      return this.write("Null");
    }
    undefined() {
      return this.write("Undefined");
    }
    regexp(regex) {
      return this.write("regex:" + regex.toString());
    }
    arraybuffer(arr) {
      this.write("arraybuffer:");
      return this.dispatch(new Uint8Array(arr));
    }
    url(url) {
      return this.write("url:" + url.toString());
    }
    map(map) {
      this.write("map:");
      const arr = [...map];
      return this.array(arr, false);
    }
    set(set) {
      this.write("set:");
      const arr = [...set];
      return this.array(arr, false);
    }
    bigint(number) {
      return this.write("bigint:" + number.toString());
    }
  }
  for (const type of [
    "uint8array",
    "uint8clampedarray",
    "unt8array",
    "uint16array",
    "unt16array",
    "uint32array",
    "unt32array",
    "float32array",
    "float64array"
  ]) {
    Hasher2.prototype[type] = function(arr) {
      this.write(type + ":");
      return this.array([...arr], false);
    };
  }
  function isNativeFunction(f) {
    if (typeof f !== "function") {
      return false;
    }
    return Function.prototype.toString.call(f).slice(
      -15
      /* "[native code] }".length */
    ) === "[native code] }";
  }
  return Hasher2;
})();
function serialize(object) {
  const hasher = new Hasher();
  hasher.dispatch(object);
  return hasher.buff;
}
function hash(value) {
  return digest(typeof value === "string" ? value : serialize(value)).replace(/[-_]/g, "").slice(0, 10);
}

function defaultCacheOptions() {
  return {
    name: "_",
    base: "/cache",
    swr: true,
    maxAge: 1
  };
}
function defineCachedFunction(fn, opts = {}) {
  opts = { ...defaultCacheOptions(), ...opts };
  const pending = {};
  const group = opts.group || "nitro/functions";
  const name = opts.name || fn.name || "_";
  const integrity = opts.integrity || hash([fn, opts]);
  const validate = opts.validate || ((entry) => entry.value !== void 0);
  async function get(key, resolver, shouldInvalidateCache, event) {
    const cacheKey = [opts.base, group, name, key + ".json"].filter(Boolean).join(":").replace(/:\/$/, ":index");
    let entry = await useStorage().getItem(cacheKey).catch((error) => {
      console.error(`[cache] Cache read error.`, error);
      useNitroApp().captureError(error, { event, tags: ["cache"] });
    }) || {};
    if (typeof entry !== "object") {
      entry = {};
      const error = new Error("Malformed data read from cache.");
      console.error("[cache]", error);
      useNitroApp().captureError(error, { event, tags: ["cache"] });
    }
    const ttl = (opts.maxAge ?? 0) * 1e3;
    if (ttl) {
      entry.expires = Date.now() + ttl;
    }
    const expired = shouldInvalidateCache || entry.integrity !== integrity || ttl && Date.now() - (entry.mtime || 0) > ttl || validate(entry) === false;
    const _resolve = async () => {
      const isPending = pending[key];
      if (!isPending) {
        if (entry.value !== void 0 && (opts.staleMaxAge || 0) >= 0 && opts.swr === false) {
          entry.value = void 0;
          entry.integrity = void 0;
          entry.mtime = void 0;
          entry.expires = void 0;
        }
        pending[key] = Promise.resolve(resolver());
      }
      try {
        entry.value = await pending[key];
      } catch (error) {
        if (!isPending) {
          delete pending[key];
        }
        throw error;
      }
      if (!isPending) {
        entry.mtime = Date.now();
        entry.integrity = integrity;
        delete pending[key];
        if (validate(entry) !== false) {
          let setOpts;
          if (opts.maxAge && !opts.swr) {
            setOpts = { ttl: opts.maxAge };
          }
          const promise = useStorage().setItem(cacheKey, entry, setOpts).catch((error) => {
            console.error(`[cache] Cache write error.`, error);
            useNitroApp().captureError(error, { event, tags: ["cache"] });
          });
          if (event?.waitUntil) {
            event.waitUntil(promise);
          }
        }
      }
    };
    const _resolvePromise = expired ? _resolve() : Promise.resolve();
    if (entry.value === void 0) {
      await _resolvePromise;
    } else if (expired && event && event.waitUntil) {
      event.waitUntil(_resolvePromise);
    }
    if (opts.swr && validate(entry) !== false) {
      _resolvePromise.catch((error) => {
        console.error(`[cache] SWR handler error.`, error);
        useNitroApp().captureError(error, { event, tags: ["cache"] });
      });
      return entry;
    }
    return _resolvePromise.then(() => entry);
  }
  return async (...args) => {
    const shouldBypassCache = await opts.shouldBypassCache?.(...args);
    if (shouldBypassCache) {
      return fn(...args);
    }
    const key = await (opts.getKey || getKey)(...args);
    const shouldInvalidateCache = await opts.shouldInvalidateCache?.(...args);
    const entry = await get(
      key,
      () => fn(...args),
      shouldInvalidateCache,
      args[0] && isEvent(args[0]) ? args[0] : void 0
    );
    let value = entry.value;
    if (opts.transform) {
      value = await opts.transform(entry, ...args) || value;
    }
    return value;
  };
}
function cachedFunction(fn, opts = {}) {
  return defineCachedFunction(fn, opts);
}
function getKey(...args) {
  return args.length > 0 ? hash(args) : "";
}
function escapeKey(key) {
  return String(key).replace(/\W/g, "");
}
function defineCachedEventHandler(handler, opts = defaultCacheOptions()) {
  const variableHeaderNames = (opts.varies || []).filter(Boolean).map((h) => h.toLowerCase()).sort();
  const _opts = {
    ...opts,
    getKey: async (event) => {
      const customKey = await opts.getKey?.(event);
      if (customKey) {
        return escapeKey(customKey);
      }
      const _path = event.node.req.originalUrl || event.node.req.url || event.path;
      let _pathname;
      try {
        _pathname = escapeKey(decodeURI(parseURL(_path).pathname)).slice(0, 16) || "index";
      } catch {
        _pathname = "-";
      }
      const _hashedPath = `${_pathname}.${hash(_path)}`;
      const _headers = variableHeaderNames.map((header) => [header, event.node.req.headers[header]]).map(([name, value]) => `${escapeKey(name)}.${hash(value)}`);
      return [_hashedPath, ..._headers].join(":");
    },
    validate: (entry) => {
      if (!entry.value) {
        return false;
      }
      if (entry.value.code >= 400) {
        return false;
      }
      if (entry.value.body === void 0) {
        return false;
      }
      if (entry.value.headers.etag === "undefined" || entry.value.headers["last-modified"] === "undefined") {
        return false;
      }
      return true;
    },
    group: opts.group || "nitro/handlers",
    integrity: opts.integrity || hash([handler, opts])
  };
  const _cachedHandler = cachedFunction(
    async (incomingEvent) => {
      const variableHeaders = {};
      for (const header of variableHeaderNames) {
        const value = incomingEvent.node.req.headers[header];
        if (value !== void 0) {
          variableHeaders[header] = value;
        }
      }
      const reqProxy = cloneWithProxy(incomingEvent.node.req, {
        headers: variableHeaders
      });
      const resHeaders = {};
      let _resSendBody;
      const resProxy = cloneWithProxy(incomingEvent.node.res, {
        statusCode: 200,
        writableEnded: false,
        writableFinished: false,
        headersSent: false,
        closed: false,
        getHeader(name) {
          return resHeaders[name];
        },
        setHeader(name, value) {
          resHeaders[name] = value;
          return this;
        },
        getHeaderNames() {
          return Object.keys(resHeaders);
        },
        hasHeader(name) {
          return name in resHeaders;
        },
        removeHeader(name) {
          delete resHeaders[name];
        },
        getHeaders() {
          return resHeaders;
        },
        end(chunk, arg2, arg3) {
          if (typeof chunk === "string") {
            _resSendBody = chunk;
          }
          if (typeof arg2 === "function") {
            arg2();
          }
          if (typeof arg3 === "function") {
            arg3();
          }
          return this;
        },
        write(chunk, arg2, arg3) {
          if (typeof chunk === "string") {
            _resSendBody = chunk;
          }
          if (typeof arg2 === "function") {
            arg2(void 0);
          }
          if (typeof arg3 === "function") {
            arg3();
          }
          return true;
        },
        writeHead(statusCode, headers2) {
          this.statusCode = statusCode;
          if (headers2) {
            if (Array.isArray(headers2) || typeof headers2 === "string") {
              throw new TypeError("Raw headers  is not supported.");
            }
            for (const header in headers2) {
              const value = headers2[header];
              if (value !== void 0) {
                this.setHeader(
                  header,
                  value
                );
              }
            }
          }
          return this;
        }
      });
      const event = createEvent(reqProxy, resProxy);
      event.fetch = (url, fetchOptions) => fetchWithEvent(event, url, fetchOptions, {
        fetch: useNitroApp().localFetch
      });
      event.$fetch = (url, fetchOptions) => fetchWithEvent(event, url, fetchOptions, {
        fetch: globalThis.$fetch
      });
      event.waitUntil = incomingEvent.waitUntil;
      event.context = incomingEvent.context;
      event.context.cache = {
        options: _opts
      };
      const body = await handler(event) || _resSendBody;
      const headers = event.node.res.getHeaders();
      headers.etag = String(
        headers.Etag || headers.etag || `W/"${hash(body)}"`
      );
      headers["last-modified"] = String(
        headers["Last-Modified"] || headers["last-modified"] || (/* @__PURE__ */ new Date()).toUTCString()
      );
      const cacheControl = [];
      if (opts.swr) {
        if (opts.maxAge) {
          cacheControl.push(`s-maxage=${opts.maxAge}`);
        }
        if (opts.staleMaxAge) {
          cacheControl.push(`stale-while-revalidate=${opts.staleMaxAge}`);
        } else {
          cacheControl.push("stale-while-revalidate");
        }
      } else if (opts.maxAge) {
        cacheControl.push(`max-age=${opts.maxAge}`);
      }
      if (cacheControl.length > 0) {
        headers["cache-control"] = cacheControl.join(", ");
      }
      const cacheEntry = {
        code: event.node.res.statusCode,
        headers,
        body
      };
      return cacheEntry;
    },
    _opts
  );
  return defineEventHandler(async (event) => {
    if (opts.headersOnly) {
      if (handleCacheHeaders(event, { maxAge: opts.maxAge })) {
        return;
      }
      return handler(event);
    }
    const response = await _cachedHandler(
      event
    );
    if (event.node.res.headersSent || event.node.res.writableEnded) {
      return response.body;
    }
    if (handleCacheHeaders(event, {
      modifiedTime: new Date(response.headers["last-modified"]),
      etag: response.headers.etag,
      maxAge: opts.maxAge
    })) {
      return;
    }
    event.node.res.statusCode = response.code;
    for (const name in response.headers) {
      const value = response.headers[name];
      if (name === "set-cookie") {
        event.node.res.appendHeader(
          name,
          splitCookiesString(value)
        );
      } else {
        if (value !== void 0) {
          event.node.res.setHeader(name, value);
        }
      }
    }
    return response.body;
  });
}
function cloneWithProxy(obj, overrides) {
  return new Proxy(obj, {
    get(target, property, receiver) {
      if (property in overrides) {
        return overrides[property];
      }
      return Reflect.get(target, property, receiver);
    },
    set(target, property, value, receiver) {
      if (property in overrides) {
        overrides[property] = value;
        return true;
      }
      return Reflect.set(target, property, value, receiver);
    }
  });
}
const cachedEventHandler = defineCachedEventHandler;

const defineAppConfig = (config) => config;

const REM_ROOT_VALUE = 100;

const appConfig0 = defineAppConfig({
  defaultTitle: "",
  defaultKeywords: "",
  defaultDescription: "",
  lesseeId: null,
  appId: null,
  isPc: false,
  isMobile: true,
  isXcx: false,
  theme: {
    // currentTheme: 'light' as ITheme,
  },
  rem: {
    // designWidth: REM_DESIGN_WIDTH,
    rootValue: REM_ROOT_VALUE
  },
  wechatAppId: "",
  qqAppId: "",
  logoImg: "",
  defaultSeo: {
    seoTitle: "",
    seoKeyword: "",
    seoDescription: ""
  }
});

const inlineAppConfig = {
  "requestAppid": "c4881275944",
  "requestAppkey": "awo6ureum8bn",
  "clientType": "7",
  "WX_APP_ID": "wx32638bab45b67226",
  "QQ_APP_ID": "102079805",
  "PRODUCT_TYPE_DOMAIN": "m-kukeyun-devc.tyyk99.com",
  "AUTH_CENTER": "https://auth-center-dev.kukewang.com",
  "USER_AGREEMENT_ID": "815463590374293504",
  "PRIVACY_POLICY_AGREEMENT_ID": "815463680002375680",
  "PRIVACY_POLICY_THIRD_ID": "897273773977350144",
  "FILE_VIEW_DOMAIN": "https://kkfileviewdev.kukewang.com",
  "KUKE_CLOUD_ORG_ID_LIST": "['1','2']",
  "requestEncryptKey": "vmHbx4bnI91ytL389b52nGBSScAQ50oIbYsNV4AfVgw=",
  "NUXT_KK_STUDENT_LOCALHOST": "*",
  "NUXT_KK_STUDENT_LOCALHOST_TY": "*",
  "nuxt": {}
};

const appConfig = defuFn(appConfig0, inlineAppConfig);

function getEnv(key, opts) {
  const envKey = snakeCase(key).toUpperCase();
  return destr(
    process.env[opts.prefix + envKey] ?? process.env[opts.altPrefix + envKey]
  );
}
function _isObject(input) {
  return typeof input === "object" && !Array.isArray(input);
}
function applyEnv(obj, opts, parentKey = "") {
  for (const key in obj) {
    const subKey = parentKey ? `${parentKey}_${key}` : key;
    const envValue = getEnv(subKey, opts);
    if (_isObject(obj[key])) {
      if (_isObject(envValue)) {
        obj[key] = { ...obj[key], ...envValue };
        applyEnv(obj[key], opts, subKey);
      } else if (envValue === void 0) {
        applyEnv(obj[key], opts, subKey);
      } else {
        obj[key] = envValue ?? obj[key];
      }
    } else {
      obj[key] = envValue ?? obj[key];
    }
    if (opts.envExpansion && typeof obj[key] === "string") {
      obj[key] = _expandFromEnv(obj[key]);
    }
  }
  return obj;
}
const envExpandRx = /\{\{([^{}]*)\}\}/g;
function _expandFromEnv(value) {
  return value.replace(envExpandRx, (match, key) => {
    return process.env[key] || match;
  });
}

const _inlineRuntimeConfig = {
  "app": {
    "baseURL": "/",
    "buildAssetsDir": "/_nuxt/",
    "cdnURL": ""
  },
  "nitro": {
    "envPrefix": "NUXT_",
    "routeRules": {
      "/__nuxt_error": {
        "cache": false
      },
      "/user/**": {
        "ssr": false
      },
      "/shopping/**": {
        "ssr": false
      },
      "/question-bank/**": {
        "ssr": false
      },
      "/learn-center/**": {
        "ssr": false
      },
      "/cashier/**": {
        "ssr": false
      },
      "/me": {
        "ssr": false
      },
      "/cart": {
        "ssr": false
      },
      "/confirm-order": {
        "ssr": false
      },
      "/answerQuestion/**": {
        "ssr": false
      },
      "/points/**": {
        "ssr": false
      },
      "/member-center/order/**": {
        "ssr": false
      }
    }
  },
  "public": {
    "TOKEN_KEY": "TOKEN",
    "BUILD_ENV": "LOCAL",
    "lastBuildTime": *************,
    "baseUrlClient": "/dev",
    "version": "3.8.1",
    "PLC_WX_APP_ID": "wxb9e70d4c53049a9d",
    "USER_KEY": "user",
    "CALLBACK_URL": "https://icloud.kukewang.com/auth/callback/",
    "PAY_LOCALHOST": "https://mpay-dev.kukewang.com",
    "persistedState": {
      "storage": "cookies",
      "debug": false,
      "cookieOptions": {}
    }
  },
  "baseUrlServer": "https://m-kukeyun-devc.tyyk99.com/dev"
};
const envOptions = {
  prefix: "NITRO_",
  altPrefix: _inlineRuntimeConfig.nitro.envPrefix ?? process.env.NITRO_ENV_PREFIX ?? "_",
  envExpansion: _inlineRuntimeConfig.nitro.envExpansion ?? process.env.NITRO_ENV_EXPANSION ?? false
};
const _sharedRuntimeConfig = _deepFreeze(
  applyEnv(klona(_inlineRuntimeConfig), envOptions)
);
function useRuntimeConfig(event) {
  {
    return _sharedRuntimeConfig;
  }
}
_deepFreeze(klona(appConfig));
function _deepFreeze(object) {
  const propNames = Object.getOwnPropertyNames(object);
  for (const name of propNames) {
    const value = object[name];
    if (value && typeof value === "object") {
      _deepFreeze(value);
    }
  }
  return Object.freeze(object);
}
new Proxy(/* @__PURE__ */ Object.create(null), {
  get: (_, prop) => {
    console.warn(
      "Please use `useRuntimeConfig()` instead of accessing config directly."
    );
    const runtimeConfig = useRuntimeConfig();
    if (prop in runtimeConfig) {
      return runtimeConfig[prop];
    }
    return void 0;
  }
});

const config = useRuntimeConfig();
const _routeRulesMatcher = toRouteMatcher(
  createRouter({ routes: config.nitro.routeRules })
);
function createRouteRulesHandler(ctx) {
  return eventHandler((event) => {
    const routeRules = getRouteRules(event);
    if (routeRules.headers) {
      setHeaders(event, routeRules.headers);
    }
    if (routeRules.redirect) {
      let target = routeRules.redirect.to;
      if (target.endsWith("/**")) {
        let targetPath = event.path;
        const strpBase = routeRules.redirect._redirectStripBase;
        if (strpBase) {
          targetPath = withoutBase(targetPath, strpBase);
        }
        target = joinURL(target.slice(0, -3), targetPath);
      } else if (event.path.includes("?")) {
        const query = getQuery(event.path);
        target = withQuery(target, query);
      }
      return sendRedirect(event, target, routeRules.redirect.statusCode);
    }
    if (routeRules.proxy) {
      let target = routeRules.proxy.to;
      if (target.endsWith("/**")) {
        let targetPath = event.path;
        const strpBase = routeRules.proxy._proxyStripBase;
        if (strpBase) {
          targetPath = withoutBase(targetPath, strpBase);
        }
        target = joinURL(target.slice(0, -3), targetPath);
      } else if (event.path.includes("?")) {
        const query = getQuery(event.path);
        target = withQuery(target, query);
      }
      return proxyRequest(event, target, {
        fetch: ctx.localFetch,
        ...routeRules.proxy
      });
    }
  });
}
function getRouteRules(event) {
  event.context._nitro = event.context._nitro || {};
  if (!event.context._nitro.routeRules) {
    event.context._nitro.routeRules = getRouteRulesForPath(
      withoutBase(event.path.split("?")[0], useRuntimeConfig().app.baseURL)
    );
  }
  return event.context._nitro.routeRules;
}
function getRouteRulesForPath(path) {
  return defu({}, ..._routeRulesMatcher.matchAll(path).reverse());
}

function _captureError(error, type) {
  console.error(`[${type}]`, error);
  useNitroApp().captureError(error, { tags: [type] });
}
function trapUnhandledNodeErrors() {
  process.on(
    "unhandledRejection",
    (error) => _captureError(error, "unhandledRejection")
  );
  process.on(
    "uncaughtException",
    (error) => _captureError(error, "uncaughtException")
  );
}
function joinHeaders(value) {
  return Array.isArray(value) ? value.join(", ") : String(value);
}
function normalizeFetchResponse(response) {
  if (!response.headers.has("set-cookie")) {
    return response;
  }
  return new Response(response.body, {
    status: response.status,
    statusText: response.statusText,
    headers: normalizeCookieHeaders(response.headers)
  });
}
function normalizeCookieHeader(header = "") {
  return splitCookiesString(joinHeaders(header));
}
function normalizeCookieHeaders(headers) {
  const outgoingHeaders = new Headers();
  for (const [name, header] of headers) {
    if (name === "set-cookie") {
      for (const cookie of normalizeCookieHeader(header)) {
        outgoingHeaders.append("set-cookie", cookie);
      }
    } else {
      outgoingHeaders.set(name, joinHeaders(header));
    }
  }
  return outgoingHeaders;
}

function isJsonRequest(event) {
  if (hasReqHeader(event, "accept", "text/html")) {
    return false;
  }
  return hasReqHeader(event, "accept", "application/json") || hasReqHeader(event, "user-agent", "curl/") || hasReqHeader(event, "user-agent", "httpie/") || hasReqHeader(event, "sec-fetch-mode", "cors") || event.path.startsWith("/api/") || event.path.endsWith(".json");
}
function hasReqHeader(event, name, includes) {
  const value = getRequestHeader(event, name);
  return value && typeof value === "string" && value.toLowerCase().includes(includes);
}
function normalizeError(error, isDev) {
  const cwd = typeof process.cwd === "function" ? process.cwd() : "/";
  const stack = (error.unhandled || error.fatal) ? [] : (error.stack || "").split("\n").splice(1).filter((line) => line.includes("at ")).map((line) => {
    const text = line.replace(cwd + "/", "./").replace("webpack:/", "").replace("file://", "").trim();
    return {
      text,
      internal: line.includes("node_modules") && !line.includes(".cache") || line.includes("internal") || line.includes("new Promise")
    };
  });
  const statusCode = error.statusCode || 500;
  const statusMessage = error.statusMessage ?? (statusCode === 404 ? "Not Found" : "");
  const message = error.unhandled ? "internal server error" : error.message || error.toString();
  return {
    stack,
    statusCode,
    statusMessage,
    message
  };
}

const errorHandler$0 = (async function errorhandler(error, event) {
  const { stack, statusCode, statusMessage, message } = normalizeError(error);
  const errorObject = {
    url: event.path,
    statusCode,
    statusMessage,
    message,
    stack: statusCode !== 404 ? `<pre>${stack.map((i) => `<span class="stack${i.internal ? " internal" : ""}">${i.text}</span>`).join("\n")}</pre>` : "",
    data: error.data
  };
  if (error.unhandled || error.fatal) {
    const tags = [
      "[nuxt]",
      "[request error]",
      error.unhandled && "[unhandled]",
      error.fatal && "[fatal]",
      Number(errorObject.statusCode) !== 200 && `[${errorObject.statusCode}]`
    ].filter(Boolean).join(" ");
    console.error(tags, errorObject.message + "\n" + stack.map((l) => "  " + l.text).join("  \n"));
  }
  if (event.handled) {
    return;
  }
  setResponseStatus(event, errorObject.statusCode !== 200 && errorObject.statusCode || 500, errorObject.statusMessage);
  if (isJsonRequest(event)) {
    setResponseHeader(event, "Content-Type", "application/json");
    return send(event, JSON.stringify(errorObject));
  }
  const isErrorPage = event.path.startsWith("/__nuxt_error");
  const res = !isErrorPage ? await useNitroApp().localFetch(withQuery(joinURL(useRuntimeConfig().app.baseURL, "/__nuxt_error"), errorObject), {
    headers: getRequestHeaders(event),
    redirect: "manual"
  }).catch(() => null) : null;
  if (!res) {
    const { template } = await Promise.resolve().then(function () { return errorDev; }) ;
    {
      errorObject.description = errorObject.message;
    }
    if (event.handled) {
      return;
    }
    setResponseHeader(event, "Content-Type", "text/html;charset=UTF-8");
    return send(event, template(errorObject));
  }
  const html = await res.text();
  if (event.handled) {
    return;
  }
  for (const [header, value] of res.headers.entries()) {
    setResponseHeader(event, header, value);
  }
  setResponseStatus(event, res.status && res.status !== 200 ? res.status : void 0, res.statusText);
  return send(event, html);
});

function defineNitroErrorHandler(handler) {
  return handler;
}

const errorHandler$1 = defineNitroErrorHandler(
  async function defaultNitroErrorHandler(error, event) {
    const res = await defaultHandler(error, event);
    if (!event.node?.res.headersSent) {
      setResponseHeaders(event, res.headers);
    }
    setResponseStatus(event, res.status, res.statusText);
    return send(
      event,
      typeof res.body === "string" ? res.body : JSON.stringify(res.body, null, 2)
    );
  }
);
async function defaultHandler(error, event, opts) {
  const isSensitive = error.unhandled || error.fatal;
  const statusCode = error.statusCode || 500;
  const statusMessage = error.statusMessage || "Server Error";
  const url = getRequestURL(event, { xForwardedHost: true, xForwardedProto: true });
  if (statusCode === 404) {
    const baseURL = "/";
    if (/^\/[^/]/.test(baseURL) && !url.pathname.startsWith(baseURL)) {
      const redirectTo = `${baseURL}${url.pathname.slice(1)}${url.search}`;
      return {
        status: 302,
        statusText: "Found",
        headers: { location: redirectTo },
        body: `Redirecting...`
      };
    }
  }
  await loadStackTrace(error).catch(consola.error);
  const youch = new Youch();
  if (isSensitive && !opts?.silent) {
    const tags = [error.unhandled && "[unhandled]", error.fatal && "[fatal]"].filter(Boolean).join(" ");
    const ansiError = await (await youch.toANSI(error)).replaceAll(process.cwd(), ".");
    consola.error(
      `[request error] ${tags} [${event.method}] ${url}

`,
      ansiError
    );
  }
  const useJSON = opts?.json || !getRequestHeader(event, "accept")?.includes("text/html");
  const headers = {
    "content-type": useJSON ? "application/json" : "text/html",
    // Prevent browser from guessing the MIME types of resources.
    "x-content-type-options": "nosniff",
    // Prevent error page from being embedded in an iframe
    "x-frame-options": "DENY",
    // Prevent browsers from sending the Referer header
    "referrer-policy": "no-referrer",
    // Disable the execution of any js
    "content-security-policy": "script-src 'self' 'unsafe-inline'; object-src 'none'; base-uri 'self';"
  };
  if (statusCode === 404 || !getResponseHeader(event, "cache-control")) {
    headers["cache-control"] = "no-cache";
  }
  const body = useJSON ? {
    error: true,
    url,
    statusCode,
    statusMessage,
    message: error.message,
    data: error.data,
    stack: error.stack?.split("\n").map((line) => line.trim())
  } : await youch.toHTML(error, {
    request: {
      url: url.href,
      method: event.method,
      headers: getRequestHeaders(event)
    }
  });
  return {
    status: statusCode,
    statusText: statusMessage,
    headers,
    body
  };
}
async function loadStackTrace(error) {
  if (!(error instanceof Error)) {
    return;
  }
  const parsed = await new ErrorParser().defineSourceLoader(sourceLoader).parse(error);
  const stack = error.message + "\n" + parsed.frames.map((frame) => fmtFrame(frame)).join("\n");
  Object.defineProperty(error, "stack", { value: stack });
  if (error.cause) {
    await loadStackTrace(error.cause).catch(consola.error);
  }
}
async function sourceLoader(frame) {
  if (!frame.fileName || frame.fileType !== "fs" || frame.type === "native") {
    return;
  }
  if (frame.type === "app") {
    const rawSourceMap = await readFile(`${frame.fileName}.map`, "utf8").catch(() => {
    });
    if (rawSourceMap) {
      const consumer = await new SourceMapConsumer(rawSourceMap);
      const originalPosition = consumer.originalPositionFor({ line: frame.lineNumber, column: frame.columnNumber });
      if (originalPosition.source && originalPosition.line) {
        frame.fileName = resolve(dirname(frame.fileName), originalPosition.source);
        frame.lineNumber = originalPosition.line;
        frame.columnNumber = originalPosition.column || 0;
      }
    }
  }
  const contents = await readFile(frame.fileName, "utf8").catch(() => {
  });
  return contents ? { contents } : void 0;
}
function fmtFrame(frame) {
  if (frame.type === "native") {
    return frame.raw;
  }
  const src = `${frame.fileName || ""}:${frame.lineNumber}:${frame.columnNumber})`;
  return frame.functionName ? `at ${frame.functionName} (${src}` : `at ${src}`;
}

const errorHandlers = [errorHandler$0, errorHandler$1];

async function errorHandler(error, event) {
  for (const handler of errorHandlers) {
    try {
      await handler(error, event, { defaultHandler });
      if (event.handled) {
        return; // Response handled
      }
    } catch(error) {
      // Handler itself thrown, log and continue
      console.error(error);
    }
  }
  // H3 will handle fallback
}

const _5cb8x2GEUriNiiXl8bG6fdL5tptEnbgkpqCFRaLnA0A = defineNitroPlugin$1((nitroApp) => {
  nitroApp.hooks.hook("render:response", (response) => {
    response.body = response.body.replace(
      /(<script src="[^"]+-legacy\.[^>]+") defer/g,
      "$1 nomodule"
    );
    response.body = response.body.replace(
      /<link rel="preload"[^>]* href="[^"]+-legacy(\.\w+)?\.js".*?>\n/g,
      ""
    );
  });
});

function defineNitroPlugin(def) {
  return def;
}

const _dT8gGO10zjlSoJFuzlJvd9cSbmWAr1aNqD395WZdjw = defineNitroPlugin((nitro) => {
  nitro.hooks.hook("render:response", async (response, { event }) => {
    var _a, _b;
    try {
      if (!((_b = (_a = response.headers) == null ? void 0 : _a["content-type"]) == null ? void 0 : _b.startsWith("text/html"))) {
        return;
      }
      const { pathname } = getRequestURL(event);
      const compressiblePaths = ["/news", "/cp", "/courseList"];
      const shouldCompress = pathname === "/" || compressiblePaths.some((path) => pathname.startsWith(path));
      if (shouldCompress) {
        await useCompression(event, response);
      }
    } catch (error) {
      console.log(error);
    }
  });
});

const DEFAULT_M_LIST = [
  "m-icloud-testc.tyyk99.com",
  "m-kkceshi-c.pt.kukewang.com",
  "m-kuke99-c.pre.kukewang.com",
  "localhost:3006"
];
const DEFAULT_W_LIST = [
  "w-icloud-testc.tyyk99.com",
  "w-kkceshi-c.pt.kukewang.com",
  "w-kuke99-c.pre.kukewang.com",
  "localhost:3006"
];
const kuKeWangXiaoHosts = [
  ...DEFAULT_M_LIST,
  "m.kuke99.com"
];
const kuKeWangXiao = '<script src="https://s4.cnzz.com/z.js?id=1281226412&async=1" defer><\/script>';
const tianYiXinLinXcxHosts = [
  ...DEFAULT_W_LIST,
  "w-tyxl.kukecloud.cn"
];
const tianYiXinLinXcx = '<script src="https://v1.cnzz.com/z.js?id=1281393223&async=1" defer><\/script>';
const tianYiXinLinHosts = [
  ...DEFAULT_M_LIST,
  "m.tianyixinglin.com"
];
const tianYiXinLin = '<script src="https://v1.cnzz.com/z.js?id=1281393222&async=1" defer><\/script>';
const kuKeTiKuHosts = [
  ...DEFAULT_M_LIST,
  "m-kuketiku.kuke99.com"
];
const kuKeTiKu = '<script src="https://s9.cnzz.com/z.js?id=1281393215&async=1" defer><\/script>';
const kuKeTiKuXcxHosts = [
  ...DEFAULT_W_LIST,
  "w-kuketiku.kuke99.com"
];
const kuKeTiKuXcx = '<script src="https://v1.cnzz.com/z.js?id=1281393216&async=1" defer><\/script>';
const tianYiZhaoJiaoGongZhiHosts = [
  ...DEFAULT_W_LIST,
  "m.tyedu99.com"
];
const tianYiZhaoJiaoGongZhi = '<script src="https://s9.cnzz.com/z.js?id=1281402232&async=1" defer><\/script>';
const tianYiGuFenHosts = [
  ...DEFAULT_M_LIST,
  "m-tygufen.kukecloud.cn"
];
const tianYiGuFen = '<script src="https://s4.cnzz.com/z.js?id=1281402235&async=1" defer><\/script>';
const tianYiXueTangHosts = [
  ...DEFAULT_M_LIST,
  "h5.kst365.com"
];
const tianYiXueTang = '<script src="https://s9.cnzz.com/z.js?id=1281412266&async=1" defer><\/script>';
const kuKeGongZhiHosts = [
  ...DEFAULT_M_LIST,
  "bookgongzhih5.kuke99.com"
];
const kuKeGongZhi = '<script src="https://s9.cnzz.com/z.js?id=1281412269&async=1" defer><\/script>';
const tianYiZhaoJiaoGongZhiXcxHosts = [
  ...DEFAULT_W_LIST,
  "w.tyedu99.com"
];
const tianYiZhaoJiaoGongZhiXcx = '<script src="https://s4.cnzz.com/z.js?id=1281402233&async=1" defer><\/script>';
const tianYiGuFenXcxHosts = [
  ...DEFAULT_W_LIST,
  "w-tygufen.kukecloud.cn"
];
const tianYiGuFenXcx = '<script src="https://s4.cnzz.com/z.js?id=1281402236&async=1" defer><\/script>';
const tianYiXueTangXcxHosts = [
  ...DEFAULT_W_LIST,
  "w.kst365.com"
];
const tianYiXueTangXcx = '<script src="https://s4.cnzz.com/z.js?id=1281412267&async=1" defer><\/script>';
const kuKeGongZhiXcxHosts = [
  ...DEFAULT_W_LIST,
  "w-bookgongzhi.kuke99.com"
];
const kuKeGongZhiXcx = '<script src="https://v1.cnzz.com/z.js?id=1281412270&async=1" defer><\/script>';
const _XHrRbzP9HkDxk9fFxhtkL97wSHL7iDzJ_n6GnZyAIkI = defineNitroPlugin((nitroApp) => {
  nitroApp.hooks.hook("render:html", (html, { event }) => {
    const reqUrl = getRequestURL(event);
    let canonicalLinkStr = "";
    if (reqUrl.pathname === "/") {
      const lt = reqUrl.searchParams.get("lt") || "";
      if (lt) {
        canonicalLinkStr = `<link rel="canonical" href="${reqUrl.origin}/?lt=${lt}">`;
      } else {
        canonicalLinkStr = `<link rel="canonical" href="${reqUrl.origin}/">`;
      }
    } else {
      canonicalLinkStr = `<link rel="canonical" href="${reqUrl.href}">`;
    }
    const headStr = html.head[0] || "";
    if (!headStr.includes('rel="canonical"')) {
      if (headStr.includes("</title>")) {
        html.head[0] = headStr.replace("</title>", "</title>\n" + canonicalLinkStr);
      } else {
        html.head.push(canonicalLinkStr);
      }
    }
    const host = getRequestHost(event);
    if (kuKeWangXiaoHosts.includes(host)) {
      html.bodyAppend.push(kuKeWangXiao);
    } else if (tianYiXinLinXcxHosts.includes(host)) {
      html.bodyAppend.push(tianYiXinLinXcx);
    } else if (tianYiXinLinHosts.includes(host)) {
      html.bodyAppend.push(tianYiXinLin);
    } else if (kuKeTiKuHosts.includes(host)) {
      html.bodyAppend.push(kuKeTiKu);
    } else if (kuKeTiKuXcxHosts.includes(host)) {
      html.bodyAppend.push(kuKeTiKuXcx);
    } else if (tianYiZhaoJiaoGongZhiHosts.includes(host)) {
      html.bodyAppend.push(tianYiZhaoJiaoGongZhi);
    } else if (tianYiGuFenHosts.includes(host)) {
      html.bodyAppend.push(tianYiGuFen);
    } else if (tianYiXueTangHosts.includes(host)) {
      html.bodyAppend.push(tianYiXueTang);
    } else if (kuKeGongZhiHosts.includes(host)) {
      html.bodyAppend.push(kuKeGongZhi);
    } else if (tianYiZhaoJiaoGongZhiXcxHosts.includes(host)) {
      html.bodyAppend.push(tianYiZhaoJiaoGongZhiXcx);
    } else if (tianYiGuFenXcxHosts.includes(host)) {
      html.bodyAppend.push(tianYiGuFenXcx);
    } else if (tianYiXueTangXcxHosts.includes(host)) {
      html.bodyAppend.push(tianYiXueTangXcx);
    } else if (kuKeGongZhiXcxHosts.includes(host)) {
      html.bodyAppend.push(kuKeGongZhiXcx);
    }
  });
});

const _jqtGHxJeMBxpB8v7Q0Pv9GJOgaXdqBfRbozoebza7A = defineNitroPlugin((nitroApp) => {
  const { public: { BUILD_ENV } } = useRuntimeConfig();
  if ([
    "LOCAL",
    "LOCAL_STAGING",
    "DEV",
    "STAGING",
    "PRESSURE",
    "LOCAL_PRESSURE",
    "LOCAL_PRE",
    "PRE"
  ].includes(BUILD_ENV)) {
    nitroApp.hooks.hook("render:html", (html, { event }) => {
      const host = getRequestHost(event);
      if (!host.startsWith("localhost") && !host.startsWith("127.0.0.1")) {
        html.head.push('<script src="/libs/vconsole.min.js"><\/script>');
        html.bodyAppend.push(`<script>
      window.vConsole = new window.VConsole()
      <\/script>`);
      }
    });
  }
});

const _ytvpZ8aPXCeEk_B7g3SjXlBE5bu2yNShUlzSpiLHQs = defineNitroPlugin((nitroApp) => {
  nitroApp.hooks.hook("render:html", (html) => {
    html.head.push('<script> window.addEventListener("unhandledrejection", function (event) { console.warn("WARNING: Unhandled promise rejection. Reason: " + event.reason, event); }); <\/script>');
  });
});

const plugins = [
  _5cb8x2GEUriNiiXl8bG6fdL5tptEnbgkpqCFRaLnA0A,
_dT8gGO10zjlSoJFuzlJvd9cSbmWAr1aNqD395WZdjw,
_XHrRbzP9HkDxk9fFxhtkL97wSHL7iDzJ_n6GnZyAIkI,
_jqtGHxJeMBxpB8v7Q0Pv9GJOgaXdqBfRbozoebza7A,
_ytvpZ8aPXCeEk_B7g3SjXlBE5bu2yNShUlzSpiLHQs
];

const assets = {
  "/index.mjs": {
    "type": "text/javascript; charset=utf-8",
    "etag": "\"32b21-/cJsjQSZrBZRMFSJjfiGAkY1Lto\"",
    "mtime": "2025-09-26T01:44:23.063Z",
    "size": 207649,
    "path": "index.mjs"
  },
  "/index.mjs.map": {
    "type": "application/json",
    "etag": "\"d4249-SNig39pUZFrTxAzR2F0tU+d09oE\"",
    "mtime": "2025-09-26T01:44:23.063Z",
    "size": 868937,
    "path": "index.mjs.map"
  }
};

function readAsset (id) {
  const serverDir = dirname$1(fileURLToPath(globalThis._importMeta_.url));
  return promises.readFile(resolve$1(serverDir, assets[id].path))
}

const publicAssetBases = {};

function isPublicAssetURL(id = '') {
  if (assets[id]) {
    return true
  }
  for (const base in publicAssetBases) {
    if (id.startsWith(base)) { return true }
  }
  return false
}

function getAsset (id) {
  return assets[id]
}

const METHODS = /* @__PURE__ */ new Set(["HEAD", "GET"]);
const EncodingMap = { gzip: ".gz", br: ".br" };
const _Ui4b7p = eventHandler((event) => {
  if (event.method && !METHODS.has(event.method)) {
    return;
  }
  let id = decodePath(
    withLeadingSlash(withoutTrailingSlash(parseURL(event.path).pathname))
  );
  let asset;
  const encodingHeader = String(
    getRequestHeader(event, "accept-encoding") || ""
  );
  const encodings = [
    ...encodingHeader.split(",").map((e) => EncodingMap[e.trim()]).filter(Boolean).sort(),
    ""
  ];
  if (encodings.length > 1) {
    appendResponseHeader(event, "Vary", "Accept-Encoding");
  }
  for (const encoding of encodings) {
    for (const _id of [id + encoding, joinURL(id, "index.html" + encoding)]) {
      const _asset = getAsset(_id);
      if (_asset) {
        asset = _asset;
        id = _id;
        break;
      }
    }
  }
  if (!asset) {
    if (isPublicAssetURL(id)) {
      removeResponseHeader(event, "Cache-Control");
      throw createError({ statusCode: 404 });
    }
    return;
  }
  const ifNotMatch = getRequestHeader(event, "if-none-match") === asset.etag;
  if (ifNotMatch) {
    setResponseStatus(event, 304, "Not Modified");
    return "";
  }
  const ifModifiedSinceH = getRequestHeader(event, "if-modified-since");
  const mtimeDate = new Date(asset.mtime);
  if (ifModifiedSinceH && asset.mtime && new Date(ifModifiedSinceH) >= mtimeDate) {
    setResponseStatus(event, 304, "Not Modified");
    return "";
  }
  if (asset.type && !getResponseHeader(event, "Content-Type")) {
    setResponseHeader(event, "Content-Type", asset.type);
  }
  if (asset.etag && !getResponseHeader(event, "ETag")) {
    setResponseHeader(event, "ETag", asset.etag);
  }
  if (asset.mtime && !getResponseHeader(event, "Last-Modified")) {
    setResponseHeader(event, "Last-Modified", mtimeDate.toUTCString());
  }
  if (asset.encoding && !getResponseHeader(event, "Content-Encoding")) {
    setResponseHeader(event, "Content-Encoding", asset.encoding);
  }
  if (asset.size > 0 && !getResponseHeader(event, "Content-Length")) {
    setResponseHeader(event, "Content-Length", asset.size);
  }
  return readAsset(id);
});

const informationLabelMap = {
  nmghdwlxy: "714714829849477120_wx",
  bozhouxy: "708928540688199680_wx",
  sqgxy: "698747875076124672_wx",
  nxlgxy: "694710095463362560_wx",
  xjgcxy: "703777655431573504_wx",
  qdnydxhdxy: "715100121362186240_wx",
  gzcjdx: "697685792769269760_wx",
  hnhbzj: "889362789656358912_wx",
  ncdxkxjsxy: "716555244508291072_wx",
  zxjzks: "882848513399648256_wx",
  hnlhzj: "899145223075729408_wx",
  lnlgzydx: "716517893120049152_wx",
  sxxysfxy: "696600275269017600_wx",
  scwhysxy: "714706251672268800_wx",
  xnykdx: "693723731510996992_wx",
  bdlgxy: "708908356453634048_wx",
  nbcjxy: "699112719696662528_wx",
  gzcslgxy: "714728553947340800_wx",
  shgcjsdx: "716548483148005376_wx",
  hncjxy: "771266106036076544_wx",
  sxykdx: "706679715983126528_wx",
  zsb_cjcxlc: "48_wx",
  ccsfdx: "708899235832737792_wx",
  yuwen: "708930272321179648_wx",
  zsb_jyll: "63_wx",
  lnsfdx: "707095676611588096_wx",
  hnsfdxsdxy: "715452315919458304_wx",
  jzrdgg: "863555564254072832_wx",
  whdhxy: "701296695856513024_wx",
  hljdfxy: "703823867476127744_wx",
  gzmsxy: "703835489766682624_wx",
  hbwlxylgxy: "716546057573613568_wx",
  hnlyzj: "894454138088910848_wx",
  njyddxtdxy: "716526090388844544_wx",
  sxlgdx: "696597596841009152_wx",
  qlyyxy: "692257755510648832_wx",
  tjtyxy: "699832780961316864_wx",
  zsb: "1_wx",
  lzgsxy: "694714652947951616_wx",
  zjlgdx: "699123334778146816_wx",
  hnswjjxy: "715451506633064448_wx",
  hkjjxy: "704200771741782016_wx",
  zzgcjsxy: "716537866245451776_wx",
  xxgcxy: "698749016415494144_wx",
  lssfxy: "694087146500874240_wx",
  pthbm: "121_wx",
  xizangdx: "697255892568596480_wx",
  shsfdxthxy: "716549532718055424_wx",
  wuyixy: "706721063083126784_wx",
  shyyjsdx: "716548754100043776_wx",
  ankangxy: "696600957177966592_wx",
  hankouxy: "701297136529670144_wx",
  sdqnzzxy: "715100511861026816_wx",
  gsmzsfxy: "714715953860034560_wx",
  xynlxy: "698750121188831232_wx",
  yndxdcxy: "714697595694166016_wx",
  zsb_bmlc: "42_wx",
  hubesxy: "701301346672754688_wx",
  shldxy: "702055947290198016_wx",
  zzxysxy: "698752576031076352_wx",
  fzlgxy: "706722053796499456_wx",
  hhhtmzxy: "714714298846027776_wx",
  hungsdx: "704188700898488320_wx",
  qddyxy: "691911281287409664_wx",
  hnzsb: "4_wx",
  tjdxzjxy: "715448177273872384_wx",
  shaoyangxy: "704191384602226688_wx",
  shjqxy: "702055673795305472_wx",
  ynjjglxy: "714697820893618176_wx",
  nxzsb: "127_wx",
  hnszyfbkbz: "879239598162243584_wx",
  czgxy: "771267853362151424_wx",
  hljwgyxy: "716533852904697856_wx",
  jzxxxy: "706679494913241088_wx",
  zxjszp: "872355566929645568_wx",
  fskxjsxy: "714727203062685696_wx",
  hbgcdxkxxy: "716551063534157824_wx",
  tjzsb: "32_wx",
  ycgxy: "704913038643687424_wx",
  tgjmfsx: "862133231681343488_wx",
  hdzfdx: "702052781193265152_wx",
  herbxy: "703817532903387136_wx",
  herbsydx: "703818513744867328_wx",
  nylgxy: "698746854436331520_wx",
  scdydsxy: "714706002051342336_wx",
  huncsxy: "704190890110562304_wx",
  qqheyxy: "703821739992911872_wx",
  pthgg: "122_wx",
  zsb_bmrk: "43_wx",
  sdycxy: "691902308819709952_wx",
  anyangxy: "699505760989380608_wx",
  wuhanxy: "701299587454722048_wx",
  yibinxy: "693728115025391616_wx",
  zjkq: "138_wx",
  cqrwkjxy: "714720212219445248_wx",
  xijingxy: "696533889812410368_wx",
  cqzsb: "18_wx",
  zhanjiangkjxy: "703841311550705664_wx",
  ahslxy: "708929211097362432_wx",
  hngydxkjxy: "715453841949859840_wx",
  jszgkskm: "885404621804859392_wx",
  qhzsb: "132_wx",
  hbszyf: "859600848952299520_wx",
  hnwlxyfrxy: "715454874144026624_wx",
  ahzyydx: "708921371625123840_wx",
  hndysfxy: "715449708970778624_wx",
  cqsfdx: "696884314583478272_wx",
  fjzsb: "29_wx",
  sxzyydx: "706674583813066752_wx",
  zsb_jsj: "66_wx",
  xjtszyjsdx: "714694274005606400_wx",
  sanyaxy: "704200988468572160_wx",
  qdgxy: "691901487753289728_wx",
  gzhlxy: "703833875060305920_wx",
  shdldx: "702053000199135232_wx",
  hebykdx: "708913886130466816_wx",
  qdbhxy: "691911908954562560_wx",
  cslgdxcnxy: "715452798747881472_wx",
  gzmzdx: "697685499587420160_wx",
  hnayzj: "887547190919753728_wx",
  ahwdxxgcxy: "716553054445187072_wx",
  yinyue: "709639962141339648_wx",
  zjszyf: "861021572590014464_wx",
  xajtdxcsxy: "715441854994300928_wx",
  henkjdx: "699507206614974464_wx",
  jinsfxy: "698688440165249024_wx",
  xxyxy: "698749886676905984_wx",
  nmgnydx: "698689664638922752_wx",
  xmyxy: "706719470342320128_wx",
  dlysxy: "707102483829854208_wx",
  xtk: "112_wx",
  gyxxkjxy: "714701559692009472_wx",
  sxgcjsxy: "716535046594478080_wx",
  ynysxy: "694438924329246720_wx",
  hnzksj: "705293749175316480_wx",
  qjsfxy: "694438656974925824_wx",
  jclgxy: "701295426102280192_wx",
  szdxyyjsxy: "716527171738386432_wx",
  ynszyf: "857423570891636736_wx",
  zzkjxy: "698751347431354368_wx",
  gzdy: "706676821524582400_wx",
  gdykdx: "703833187127341056_wx",
  hengshuixy: "708914477404991488_wx",
  nantongdx: "704912831079460864_wx",
  cqwywsxy: "714719977099345920_wx",
  shlgdx: "702051632159490048_wx",
  yangzhoudx: "704916580424323072_wx",
  huaibsfdx: "708921111550201856_wx",
  xxyxysqxy: "716537633864232960_wx",
  whgckjxy: "716543727944675328_wx",
  pthcst: "123_wx",
  zjslsdxy: "715447381471797248_wx",
  mdjyxy: "703822015009230848_wx",
  zsb_zsjz: "37_wx",
  xagsxy: "696572115860205568_wx",
  jishoudx: "704187867615408128_wx",
  hungxy: "704193531462914048_wx",
  njcmxy: "704917536515919872_wx",
  jxkjsfdx: "716554309719932928_wx",
  hnsfxy: "708923353539907584_wx",
  zzb: "24_wx",
  tongrenxy: "697691747925975040_wx",
  nmgzsb: "131_wx",
  xzsfxy: "706675709633572864_wx",
  sanjiangxy: "704916836247506944_wx",
  njsjdx: "704909230127575040_wx",
  hbsldlxy: "716552246517379072_wx",
  dlhydx: "707094118888321024_wx",
  lnzyydx: "707094778049064960_wx",
  xnkjdx: "693723402545487872_wx",
  pthks: "754562065440280576_wx",
  lzgyxy: "694716592877613056_wx",
  maotaixy: "697692388557631488_wx",
  hbjszp: "873714672785494016_wx",
  yngsxy: "694429311991111680_wx",
  zjnldxjyxy: "715447945837490176_wx",
  xdxxgxdx: "714704232332247040_wx",
  xinyangxy: "771267226310676480_wx",
  xihuadx: "693726725044482048_wx",
  xianyxy: "696601442808061952_wx",
  dhlgdx: "707446733662687232_wx",
  hbhjgcxy: "716551409165778944_wx",
  jsdxjjxy: "716526964783038464_wx",
  nmggydx: "698689208190590976_wx",
  sdxdxy: "691912246445039616_wx",
  cxsfxy: "694429904712380416_wx",
  jshydx: "704913505653567488_wx",
  ncgcxy: "707450689226903552_wx",
  cqkjxy: "696887606629773312_wx",
  nchkdx: "707447609873764352_wx",
  hbsfdxwlxy: "716545833145544704_wx",
  zsb_xqjy: "58_wx",
  sqsfxy: "698748102776500224_wx",
  fjjssfxy: "715091777343463424_wx",
  cslgxy: "704912605153275904_wx",
  zsb_cjcxrk: "50_wx",
  kmlgdxjqxy: "714698113799012352_wx",
  zhaotongxy: "694435443018182656_wx",
  zsb_bljpx: "74_wx",
  xiaoxue: "715331506609963008_wx",
  zsb_zkzdysj: "44_wx",
  xajtgcxy: "715440190472925184_wx",
  jlyyxy: "708899651799347200_wx",
  xuchangxy: "698750342381789184_wx",
  hdjtdx: "707446447841841152_wx",
  xzgcxy: "704914267415375872_wx",
  zsb_zkzdyrk: "45_wx",
  hysfxy: "704189727878017024_wx",
  zzhkgyglxy: "716538388314664960_wx",
  yxsfxy: "694438410464092160_wx",
  sczsb: "11_wx",
  szcsxy: "704911732272476160_wx",
  zsb_cjcxsj: "49_wx",
  hntgtx: "885022346076418048_wx",
  zzsxy: "698751900590891008_wx",
  scdxjjxy: "714707271700717568_wx",
  gwy: "910402764987756544_wx",
  zjtx: "136_wx",
  zjtl: "137_wx",
  scsfdx: "691522240183128064_wx",
  zdnblgxy: "715444148998320128_wx",
  hunzyydx: "704185739037478912_wx",
  hainyxy: "704200041382146048_wx",
  bjwlxy: "696600513740365824_wx",
  fysfdx: "708922565802192896_wx",
  guiyangxy: "697690246562275328_wx",
  jljzkjxy: "716864906269040640_wx",
  jssfdx: "704910614914789376_wx",
  zjtj: "125_wx",
  zjst: "119_wx",
  scjcxy: "693729457914556416_wx",
  jxzyydx: "707448415924727808_wx",
  xtlgzy: "704199243394134016_wx",
  zjsz: "130_wx",
  fjsfdxxhxy: "715092756918968320_wx",
  jiangsudx: "704910058565529600_wx",
  kmykdxhyxy: "714697287395295232_wx",
  sczj: "83_wx",
  hubjgxy: "701293236121944064_wx",
  jinzhongxy: "706676259125850112_wx",
  ahsfdx: "708920537683537920_wx",
  hebkjdx: "708912176432779264_wx",
  henlgdx: "699508113618374656_wx",
  sycsxy: "707102274632757248_wx",
  sjzxy: "708915327586922496_wx",
  xingtaixy: "708916951327125504_wx",
  jxrjzyjsdx: "716554781733953536_wx",
  binzhouxy: "692254583514869760_wx",
  zsb_jjx: "62_wx",
  xtdxxxxy: "715452570573549568_wx",
  zxjzms: "115_wx",
  shjkyxy: "702055131082833920_wx",
  srsfxy: "707450058351259648_wx",
  zjkskm: "126_wx",
  ljwhlyxy: "714696346558382080_wx",
  zsb_bmfs: "41_wx",
  hubkjxy: "701294510090346496_wx",
  hunyyxy: "704198700043022336_wx",
  lndwjmxy: "716517659539259392_wx",
  herbjrxy: "703822263026814976_wx",
  nbgcxy: "699111654606196736_wx",
  qinghaidx: "704145798667300864_wx",
  hunkjdx: "704186718078316544_wx",
  zzjmxy: "698751094821007360_wx",
  hnsz: "871904555656089600_wx",
  gzhhxy: "703833657237516288_wx",
  dlkjxy: "707100504884637696_wx",
  qzzyjsdx: "715092479513014272_wx",
  herbsfdx: "703817246784040960_wx",
  zzqgydx: "698751615150731264_wx",
  zjkjxy: "699116064707137536_wx",
  wcgxy: "701298777601728512_wx",
  jianghandx: "701289580138692608_wx",
  wflgxy: "691903724556693504_wx",
  huablgdx: "708914907318947840_wx",
  jslgxy: "704911220288360448_wx",
  ahxhxy: "708928994576797696_wx",
  cdzyydx: "693727774779256832_wx",
  zyykdx: "697687097313112064_wx",
  hysdxy: "704914590055108608_wx",
  hunxxxy: "704199003721011200_wx",
  gszfdx: "694806752474574848_wx",
  qzsfxy: "706720351945068544_wx",
  bbgsxy: "708930856015310848_wx",
  ynsfdx: "694435174255570944_wx",
  xaqczydx: "790101951691665408_wx",
  szyftj: "861386271349346304_wx",
  jzbktj: "882849953304211456_wx",
  hljgsxy: "703824074352488448_wx",
  dgcsxy: "703829666443137024_wx",
  sxdtdx: "706674833005056000_wx",
  heihexy: "703820669373853696_wx",
  pdsxy: "698747568571113472_wx",
  ahsfdxwjxy: "716553593378578432_wx",
  gzhhsxy: "703834380344619008_wx",
  tjcjdxzjxy: "716540316121640960_wx",
  hbzjb: "5_wx",
  jxcjdx: "707445991591849984_wx",
  jsdxzjjxy: "715453589939298304_wx",
  ccjzxy: "708896932899794944_wx",
  cczyydx: "708899453869436928_wx",
  szyfsj: "861392931723743232_wx",
  nczydx: "707456491165683712_wx",
  kailixy: "697691166465417216_wx",
  hfcsxy: "708931534051708928_wx",
  jmsdx: "703819665476284416_wx",
  syhgdx: "707092349152079872_wx",
  hnbklc: "879238809465319424_wx",
  btyxy: "698695837040599040_wx",
  hungcxy: "704190593431367680_wx",
  btsfxy: "698694841591705600_wx",
  kunmingxy: "694436271081697280_wx",
  ahkjxy: "708923667100594176_wx",
  hefeisfxy: "708925593921204224_wx",
  hbsyzyjsdx: "788617921376858112_wx",
  zsb_zytbgz: "54_wx",
  szkjdx: "704912226187317248_wx",
  wclgxy: "701298504019574784_wx",
  bfgydx: "702056433337335808_wx",
  whfzdx: "701290236358811648_wx",
  jlnykjxy: "716864684092563456_wx",
  njtsjysfxy: "716524842700845056_wx",
  qqhegcxy: "716534608788832256_wx",
  henzyydx: "699508826486476800_wx",
  puerxy: "694438139268784128_wx",
  zsb_zkzdylc: "46_wx",
  czsfxy: "708909254997389312_wx",
  hubyyxy: "701292242230173696_wx",
  shlxkjjrxy: "716548999231578112_wx",
  jszpksjq: "895571317960953856_wx",
  scmzxy: "693728957048696832_wx",
  jxnydx: "707445281896632320_wx",
  jzbkrs: "894450110915342336_wx",
  huangshanxy: "708924348095475712_wx",
  nmgsfdx: "698693802826182656_wx",
  hunwlxy: "704190221028790272_wx",
  zzjszp: "887545961980293120_wx",
  zjwgyxy: "699119727446020096_wx",
  tgjsms: "871609940210159616_wx",
  hnsydwmsnr: "870878723462467584_wx",
  hnbzks: "900268237439832064_wx",
  xakjdxgxxy: "715440449231372288_wx",
  zgxqxy: "714372100825149440_wx",
  baodingxy: "708908693138804736_wx",
  qhsfdx: "704145550183444480_wx",
  sclyxy: "693729225642389504_wx",
  hnlgxynhxy: "715454436648009728_wx",
  xichangxy: "693728406726651904_wx",
  cdtyxy: "694081611271548928_wx",
  hnzj: "28_wx",
  bjcsxy: "714372570348122112_wx",
  qdcsxy: "692255129361592320_wx",
  gdbyxy: "703830244487540736_wx",
  yangguangxy: "706721601809911808_wx",
  ccghxy: "708900104405987328_wx",
  hbdzdxhxxy: "716550585065484288_wx",
  gzqnkjxy: "714701325260144640_wx",
  bjzsb: "133_wx",
  tzzsb: "27_wx",
  wzykdxrjxy: "715447629356281856_wx",
  sdcjdxdfxy: "715103775255281664_wx",
  hainzsb: "129_wx",
  hntgmsxs: "886839168102756352_wx",
  hntgkq: "886844193591980032_wx",
  hezexy: "692253676568928256_wx",
  tgjsks: "113_wx",
  hnsydwksnr: "870821564007649280_wx",
  xzmzdx: "697256261646376960_wx",
  zsb_zytblc: "51_wx",
  lnszyf: "862128647961382912_wx",
  tgbktj: "862141495601074176_wx",
  shsxy: "702054566834593792_wx",
  fzdxzcxy: "715096030758158336_wx",
  ycsfxy: "704913265903661056_wx",
  hhkjxy: "698746059877822464_wx",
  cdyszydx: "714706718322143232_wx",
  xjsfdx: "703777206418747392_wx",
  szyfksnr: "876324242703847424_wx",
  cqsxxy: "696884891710173184_wx",
  gzgsxy: "703833399698862080_wx",
  shsdxy: "702055429134270464_wx",
  zhaojiaozhenti: "727772496986247168_wx",
  lzcsxy: "694715748677464064_wx",
  herblgdx: "703818235110854656_wx",
  xjzsb: "128_wx",
  sdzsb: "10_wx",
  tlmdx: "703772549919223808_wx",
  jszgmsxs: "885407521914417152_wx",
  hebjmdx: "788617275460489216_wx",
  pth: "108_wx",
  sydwkssj: "871619345056075776_wx",
  herbykdx: "703821483876126720_wx",
  lzlgdx: "694716907661123584_wx",
  thsfxy: "708892046896345088_wx",
  hungydx: "704188432367849472_wx",
  nxdxxhxy: "714703225200914432_wx",
  scxmxy: "694088279906672640_wx",
  tangssfxy: "708916548449439744_wx",
  hebhdxy: "703823326731669504_wx",
  jzbmsj: "898114496108367872_wx",
  hnsydwmsjq: "870876933253828608_wx",
  hbsfdxhhxy: "716552033433403392_wx",
  sxnyxy: "706678540071546880_wx",
  ccrwxy: "708898386312007680_wx",
  sxgjsmxy: "715439509833105408_wx",
  quzhouxy: "699111949188943872_wx",
  nmgcjdx: "698693567481733120_wx",
  yangendx: "706723241243914240_wx",
  hbyyxyyhxy: "716545389894574080_wx",
  szyfmsjq: "858867819101224960_wx",
  scwlxy: "694080527538892800_wx",
  hexixy: "694769624169406464_wx",
  henkjxy: "699507834401619968_wx",
  lfsfxy: "708915102994526208_wx",
  jsszyf: "858478155534106624_wx",
  xawsxy: "696529701712781312_wx",
  absfxy: "694084573752549376_wx",
  xncjdxtfxy: "714707548776808448_wx",
  jzbklc: "900265694995353600_wx",
  ylsfdx: "703776392036610048_wx",
  xjdeyxy: "703777444692963328_wx",
  changzhixy: "706676793517928448_wx",
  hzyxy: "699121276645310464_wx",
  tjraxy: "699833060358152192_wx",
  gddrxy: "703831023949312000_wx",
  njlydx: "704909012711632896_wx",
  linyidx: "691901128174612480_wx",
  hainsfdx: "704200544198799360_wx",
  gzsfdx: "697684951763038208_wx",
  scgykjxy: "714706466286415872_wx",
  ynmzdx: "694433900549156864_wx",
  lzshzyjsdx: "714716738631090176_wx",
  hngwy: "910404059421274112_wx",
  hntgfy: "706654469455818752_wx",
  hnmyjjxy: "716537163601944576_wx",
  xaphxy: "696529202573828096_wx",
  lzbwkjxy: "714716216108892160_wx",
  sdwszydx: "715104123034017792_wx",
  ahysxy: "708928754439024640_wx",
  nbdxkxjsxy: "715445298506051584_wx",
  szyfkm: "861395239366889472_wx",
  njlgdxzjxy: "716525421392412672_wx",
  hnrwkjxy: "715451248424071168_wx",
  chizhouxy: "708926201361281024_wx",
  nmgmzdx: "714715408122363904_wx",
  hljszyf: "858842162619154432_wx",
  dzkjdxcdxy: "714708057977528320_wx",
  zsb_bmsj: "40_wx",
  ahzsb: "13_wx",
  bjlgdxzhxy: "714726693844819968_wx",
  jszpms: "871547257590976512_wx",
  wjgxy: "708929991708372992_wx",
  qqhedx: "703819929100169216_wx",
  sddyykdx: "715101432803606528_wx",
  shzsb: "36_wx",
  hunnydx: "704185248269070336_wx",
  sygcxy: "707100054738644992_wx",
  zjyxwgyxy: "715444474962849792_wx",
  qhmzdx: "704144941067636736_wx",
  zjjszp: "865102397900132352_wx",
  hebdzdx: "790100403005886464_wx",
  zysfxy: "697690004622237696_wx",
  xjnydx: "703776992417673216_wx",
  hebnydx: "708912777437507584_wx",
  xaxxzydx: "715442711928356864_wx",
  hntgfsx: "881783985505234944_wx",
  hljzyydx: "716533621920182272_wx",
  hnzhaojiao: "697241116240130048_wx",
  gnsfdxkjxy: "716555977597132800_wx",
  yncjdx: "694433595279323136_wx",
  fjzyydx: "706719006125846528_wx",
  zsb_bmtj: "39_wx",
  sxwlxy: "699117195012898816_wx",
  zjyyxy: "699123555420999680_wx",
  bjjzdx: "702056666259619840_wx",
  jnxy: "691897457979252736_wx",
  hljbynkdx: "716533402205761536_wx",
  szyfms: "864038526225551360_wx",
  cdjcxy: "714709338608709632_wx",
  sygydx: "707091166496763904_wx",
  tgbmrk: "862181124328984576_wx",
  kexue: "711808846147219456_wx",
  cjsfxy: "696885136426840064_wx",
  hljgyxy: "703822503842779136_wx",
  zknygcxy: "715088690164121600_wx",
  xamdlgxy: "715441253073559552_wx",
  gzlgxy: "703835114184241152_wx",
  zjgsdx: "699108051199492096_wx",
  zjmspfbz: "895569903591313408_wx",
  gdkjxy: "703831525935493120_wx",
  cqdwjmxy: "714719488576925696_wx",
  zsb_lqcx: "55_wx",
  cckjxy: "708894486158741504_wx",
  cqjtdx: "696884089478230016_wx",
  syyxy: "707094974912917504_wx",
  hubsmxy: "701299941206228992_wx",
  xhsfdx: "693727499105427456_wx",
  whhxlgxy: "716544214432858112_wx",
  jlwgydx: "708897704111370240_wx",
  lnkjxy: "707099490531250176_wx",
  eedsyyjsxy: "714714563254951936_wx",
  shdwjmdx: "716547291458158592_wx",
  ahxxgcxy: "716553331440099328_wx",
  sxgxy: "706679290038972416_wx",
  dezhouxy: "692257227259031552_wx",
  scqhgdx: "693722124163571712_wx",
  dqsfxy: "703823088259035136_wx",
  ycxy: "707449643182272512_wx",
  fjgcxy: "706718039025487872_wx",
  jszpsk: "900263996516143104_wx",
  herbtyxy: "703822838992834560_wx",
  gzjcxy: "697689373192794112_wx",
  zjcjdxdfxy: "715446432783515648_wx",
  ynzsb: "20_wx",
  minjiangxy: "706718281006182400_wx",
  hntgbktj: "884238268242784256_wx",
  chaohuxy: "708924597472718848_wx",
  ahwgyxy: "708929460647858176_wx",
  youer: "709195503165345792_wx",
  hbkjsfxy: "716551789321465856_wx",
  njgyzyjsdx: "716521862701629440_wx",
  qlsfxy: "692254208889020416_wx",
  zsb_zytbsj: "52_wx",
  cqcskjxy: "714718260458430464_wx",
  gzxhxy: "703837707530088448_wx",
  assfxy: "707098362246696960_wx",
  hntgjsks: "884236597211099136_wx",
  xahkxy: "696528312601239552_wx",
  xjcjdx: "703775006862815232_wx",
  lncmxy: "707102923250307072_wx",
  whwlxy: "701300858345324544_wx",
  jxjcxy: "707452602397962240_wx",
  tskjxy: "691900284520718336_wx",
  cqlgdx: "696889618117308416_wx",
  changjixy: "703773202624933888_wx",
  huaiyingxy: "704914890302369792_wx",
  nanhuadx: "704186259001151488_wx",
  anygxy: "699505205317341184_wx",
  zsb_zytbrk: "53_wx",
  cqykdx: "696883618948624384_wx",
  hbslsddx: "716537395875229696_wx",
  zzcjxy: "698750588364140544_wx",
  lcdxdcxy: "715100974199156736_wx",
  hnczjjxy: "715450112740765696_wx",
  njgydxpjxy: "716525855658815488_wx",
  yejzks: "882846401081044992_wx",
  xasyxy: "696571829591564288_wx",
  gzrjxy: "703836075946536960_wx",
  gnkjxy: "707456951421448192_wx",
  szyfcs: "706726579133952000_wx",
  wcsyxy: "701299327529508864_wx",
  jsdesfxy: "716521274605314048_wx",
  sanmingxy: "706719846193901568_wx",
  zgjldx: "699109451583066112_wx",
  hebzsb: "145_wx",
  hnsydwms: "870931459650424832_wx",
  hnzjtc: "706662211815837696_wx",
  xacjdxxzxy: "715440720263102464_wx",
  hssfxy: "703837977112580096_wx",
  sdnygcxy: "715102566432452608_wx",
  lzwlxy: "694717417989484544_wx",
  zhanjkjxy: "703840313380564992_wx",
  synydx: "707093758941540352_wx",
  changchundx: "708898850338451456_wx",
  gxjszp: "866854989227429888_wx",
  jilixy: "694088018502922240_wx",
  jssfdxkwxy: "716527399299981312_wx",
  wuxixy: "704910348810661888_wx",
  jzcjcxsj: "872344997300342784_wx",
  gzykdx: "697686432375463936_wx",
  dldrxxxy: "716518424269930496_wx",
  huaihuaxy: "704191852502003712_wx",
  ytnsxy: "691904264884858880_wx",
  zsb_slxbljpx: "64_wx",
  huzhouxy: "699112965099704320_wx",
  lishi: "711809258468274176_wx",
  cdeyxy: "708909558237179904_wx",
  kmwlxy: "714696747558641664_wx",
  ycnyxy: "694710404467273728_wx",
  wenzhoudx: "699111176408956928_wx",
  xxjzksnr: "897753337249337344_wx",
  yejzms: "116_wx",
  zjnldx: "699115331523317760_wx",
  dglgxy: "703829973900406784_wx",
  ccdzkjxy: "716865976492212224_wx",
  lnkjdx: "707091493851598848_wx",
  shanxizsb: "15_wx",
  cqcjxy: "696885883587571712_wx",
  qdkjdx: "691906597257621504_wx",
  huanghuaixy: "698746288763133952_wx",
  xxjsbzksnr: "895570936308649984_wx",
  zdcsxy: "699116654442610688_wx",
  sydwmsxs: "871612837287235584_wx",
  wanxixy: "708923918368059392_wx",
  tysfxy: "706675156511723520_wx",
  hnxyzj: "899144348936638464_wx",
  xxjsbztj: "895570423345270784_wx",
  kexwcs: "698828200390307840_wx",
  zjcjdx: "699110055783649280_wx",
  ncjtxy: "707456719119921152_wx",
  hnkfkjcmxy: "716536953492480000_wx",
  gxzsb: "134_wx",
  xmgxy: "706722278938349568_wx",
  yqt: "707833792801902592_wx",
  xjkjxy: "703777926842781696_wx",
  jsjszp: "861815420991901696_wx",
  bjxxkjdx: "714372863719243776_wx",
  qrzzsb: "75_wx",
  zsb_yw: "65_wx",
  tiyu: "709940262725062656_wx",
  zgmyhkfxxy: "714704673706876928_wx",
  hnkfzj: "894454953277059072_wx",
  shzqzyjsdx: "716549731758751744_wx",
  kkxw: "12_wx",
  zjykzydx: "715448385047109632_wx",
  jiayingxy: "703838924294189056_wx",
  sxjzlgxy: "716535517845504000_wx",
  xxjs: "711813309978021888_wx",
  hnjtgcxy: "715451813304545280_wx",
  sdszyf: "858479821222248448_wx",
  hubjjxy: "701292622015725568_wx",
  hntggzdy: "886841955955961856_wx",
  hebxxgcxy: "716534059771965440_wx",
  zjzyydx: "699115843352297472_wx",
  hebzyxy: "708914199491112960_wx",
  njcjdxhsxy: "716526532497096704_wx",
  gznfxy: "703835785994301440_wx",
  jszgzp: "9_wx",
  ahlgdx: "708918112982265856_wx",
  dxkjsfxy: "714695507554975744_wx",
  hbdxxzxy: "716544456246697984_wx",
  jstaizhouxy: "704916162037018624_wx",
  ynnydx: "694434176313208832_wx",
  jzgghz: "143_wx",
  lysfxy: "771266640491069440_wx",
  hzhsfxy: "699110651123159040_wx",
  hntgksnr: "885027688936042496_wx",
  shhsdx: "702052469225414656_wx",
  tangshanxy: "708916730048933888_wx",
  zsb_zybl: "70_wx",
  cccjxy: "708894807302025216_wx",
  cdyxy: "694087463589261312_wx",
  hensfdx: "699508555952377856_wx",
  cqdesfxy: "714719284992679936_wx",
  mysfxy: "694082641641222144_wx",
  dbdldx: "708895042481549312_wx",
  sydwbmtj: "871615987125653504_wx",
  pgzsb: "77_wx",
  zsb_sx: "67_wx",
  henjcxy: "699506890205069312_wx",
  xawlxy: "696597295543181312_wx",
  qllgxy: "691899105185148928_wx",
  xznmxy: "697256998727200768_wx",
  whsjgcxy: "716543954616696832_wx",
  shangluoxy: "696600752122638336_wx",
  ahykdx: "708919285978099712_wx",
  cqgsdx: "696886589158731776_wx",
  bcsfxy: "708892335351214080_wx",
  zsb_glx: "68_wx",
  gsnydx: "694771413626269696_wx",
  suqianxy: "704915197317738496_wx",
  hbjzgcxy: "716551611289288704_wx",
  hljkjdx: "703819220989112320_wx",
  hnjsbzks: "898111213436342272_wx",
  handanxy: "708909753024851968_wx",
  zzsfxy: "698752292739395584_wx",
  cslgdx: "704184865363955712_wx",
  xmlgxy: "706719210096136192_wx",
  jiujiangxy: "707450405402546176_wx",
  gzkjzyjsdx: "715087659070730240_wx",
  yeksdg: "897343431056756736_wx",
  hebwgyxy: "708913598614417408_wx",
  ncdxgqxy: "716555477930815488_wx",
  czyxy: "706674193872818176_wx",
  yjlgxy: "708917156630622208_wx",
  chddx: "693722451498643456_wx",
  xzzsb: "80_wx",
  gzyykjxy: "715087889601511424_wx",
  hebmsxy: "708912576523243520_wx",
  sxwlxyypxy: "715446712719753216_wx",
  hntgmsjq: "885409565781979136_wx",
  hntgbskm: "885018609740083200_wx",
  dalidx: "694439219845181440_wx",
  jzkstk: "856387617045544960_wx",
  jlnydx: "708900316139352064_wx",
  hbzsb: "16_wx",
  gdcjdx: "703830794046926848_wx",
  qzxxgcxy: "715092252857020416_wx",
  wuli: "711810342037352448_wx",
  tjzdyyjsdx: "716540051904536576_wx",
  dili: "711807982436069376_wx",
  shcjdxzjxy: "715448635497390080_wx",
  anshunxy: "697690506935283712_wx",
  nmgysxy: "698695557464076288_wx",
  kashidx: "703773438781026304_wx",
  sxxqsfxy: "715440974731526144_wx",
  szyfcjcx: "895564050304794624_wx",
  cqjszp: "861089427784273920_wx",
  hnzjksjq: "885402953193287680_wx",
  njxzxy: "704909502782500864_wx",
  hebdfxy: "708911677029584896_wx",
  whsxy: "701295673384468480_wx",
  sdykdx: "714371499488313344_wx",
  chuzhouxy: "708925061303316480_wx",
  njykdxkdxy: "716526321373360128_wx",
  tgjsjmmd: "857422003098550272_wx",
  gzsxy: "703836312773013504_wx",
  hnkqfx: "706753361203638272_wx",
  changzhoudx: "704910914157408256_wx",
  suzhouxy: "708925306057732096_wx",
  sxyykjxy: "716535262126809088_wx",
  zksfxy: "771266976068505600_wx",
  hntgkssj: "884247712720678912_wx",
  sdjjmydx: "714371082779488256_wx",
  cqgcxy: "696889372290146304_wx",
  hunjcxy: "704197870084526080_wx",
  czjtxy: "708908994174595072_wx",
  qdhxkjxy: "715103221121363968_wx",
  xayddx: "696601199251075072_wx",
  hnzmdzj: "889363246067937280_wx",
  hnnydxzjxy: "715088144077955072_wx",
  xjjcxy: "703772960940748800_wx",
  szyfbkjq: "861102800823455744_wx",
  hnzjks: "876720337242099712_wx",
  jzmslc: "885406208396816384_wx",
  njgcxy: "704908741855358976_wx",
  jdztcdx: "707448843941572608_wx",
  tggghz: "144_wx",
  zsb_zkzdyzysx: "47_wx",
  yulinxy: "696572413785812992_wx",
  henanzhenti: "740793572049534976_wx",
  cqyddx: "696883837117489152_wx",
  shzfxy: "702054817095843840_wx",
  gsyxy: "694807482701754368_wx",
  cdyxjdglxy: "714707044345393152_wx",
  lngcjsdx: "716517371504570368_wx",
  jszzb: "7_wx",
  jszgms: "871250139852640256_wx",
  hljzsb: "21_wx",
  jljcxy: "708892585939906560_wx",
  whcsxy: "701301109542191104_wx",
  jmdxcyxy: "715096658168066048_wx",
  hnjzzj: "899145789545844736_wx",
  hunnzxy: "704198180482707456_wx",
  jxkjxy: "707450980064137216_wx",
  yjzsb: "78_wx",
  bengbuxy: "708925811150393344_wx",
  sxzsb: "14_wx",
  dlgydx: "707092992027627520_wx",
  hgsfxy: "701294794331045888_wx",
  xymzsfxy: "714701056719831040_wx",
  cddrxy: "694089343068319744_wx",
  anysfxy: "699505502289350656_wx",
  hunlgxy: "704189333323329536_wx",
  cqjdzyjsdx: "714719747562614784_wx",
  njyddx: "704908392614432768_wx",
  hljcjxy: "703823550781009920_wx",
  cqwlxy: "696884567501004800_wx",
  ynysxywhxy: "714697026949988352_wx",
  sxfzgcxy: "715442201255067648_wx",
  jlsfdx: "708898162116784128_wx",
  xjlgxy: "703775904250933248_wx",
  qdlgdx: "692255475992453120_wx",
  xinyuxy: "707453078435393536_wx",
  tjmsxy: "699832462124572672_wx",
  dxyyjsdx: "714695127332929536_wx",
  bbyxy: "708922775287992320_wx",
  sxgckjzydx: "783236636243992576_wx",
  gdlgxy: "703831876247957504_wx",
  hbjjxyfsxy: "716545601891844096_wx",
  jszgks: "8_wx",
  jzmsjq: "887871668564455424_wx",
  gdgszyjsdx: "714727712720953344_wx",
  suihuaxy: "703820383433363456_wx",
  sdzj: "81_wx",
  hnkjzydx: "716519208358064128_wx",
  nxykdx: "694709052199493632_wx",
  hfjjxy: "708931197763710976_wx",
  hebydlgxy: "716534280722956288_wx",
  hnrjzyjsdx: "788620179193925632_wx",
  njgydx: "704908063297368064_wx",
  xxksdg: "897344186782257152_wx",
  pzhxy: "693728687460802560_wx",
  whswgcxy: "716543470741565440_wx",
  taiyuanxy: "706678263213928448_wx",
  tgkssj: "856313678978023424_wx",
  nchkdxkjxy: "716555707332960256_wx",
  mnkjxy: "706722815117201408_wx",
  hebgcdx: "788616940297850880_wx",
  bohaidx: "707098052969312256_wx",
  gdzcb: "6_wx",
  hnczjrxy: "716536721836875776_wx",
  hubzyydx: "701290496700366848_wx",
  nhdxcsxy: "715453027416023040_wx",
  gnsfdx: "707449372939776000_wx",
  putianxy: "706720610572955648_wx",
  njsfxy: "694082889584304128_wx",
  shsyxy: "704193241882685440_wx",
  gdpzxy: "703832201902108672_wx",
  cdlgdx: "693722735714066432_wx",
  zjkxy: "708917528765345792_wx",
  hubgcxy: "701294213885509632_wx",
  scyyxy: "694080869911539712_wx",
  sdglxy: "691906918877847552_wx",
  hbzj: "82_wx",
  herbjqxy: "703824603198992384_wx",
  hzdzkjdx: "715447011165085696_wx",
  jszpkstx: "890136837298515968_wx",
  shengwu: "711809550086549504_wx",
  njsjdxjsxy: "716525632374292480_wx",
  lnzsb: "31_wx",
  szjsdx: "703839720566026240_wx",
  aqsfdx: "708922079497809920_wx",
  ptzsb: "76_wx",
  jszpsjjq: "900264513149538304_wx",
  nclgxy: "707451441800491008_wx",
  jxszyf: "858477914395181056_wx",
  wenhuaxy: "701299093025685504_wx",
  hnzjbskm: "874153503833788416_wx",
  zsb_lqsj: "56_wx",
  hebjrxy: "790101009075392512_wx",
  taishanxy: "692253017017823232_wx",
  ahzj: "84_wx",
  hebbfxy: "708909997120761856_wx",
  hubwlxy: "701293477301706752_wx",
  shenyangdx: "707099268189204480_wx",
  wnyxy: "708923086051078144_wx",
  jldhxy: "708899870946832384_wx",
  sxkjdxgjxy: "715439138348544000_wx",
  jzbzksnr: "900263207534006272_wx",
  jlzsb: "35_wx",
  gdjrxy: "703831283307589632_wx",
  huizhouxy: "703838646710956032_wx",
  hebsfdx: "708912973715443712_wx",
  yingyu: "709544994567565312_wx",
  tgksnr: "856744006221758464_wx",
  xnlydx: "694433327603036160_wx",
  cbyxy: "694087734567460864_wx",
  gzzyydx: "697687577507540992_wx",
  shdrgydx: "716549314735882240_wx",
  zjsfdxxzxy: "715445033082105856_wx",
  taizhouxy: "699110943956881408_wx",
  ncgxy: "707453930356551680_wx",
  zjzsb: "30_wx",
  gzksdg: "897345724397326336_wx",
  cdgyxy: "694081886988316672_wx",
  czdxhdxy: "716526741985435648_wx",
  xalgdxgkxy: "715441488319856640_wx",
  zjwlxy: "699109762522107904_wx",
  ahjzdx: "708921819346104320_wx",
  zzsdjmglxy: "716538670452912128_wx",
  hebcmxy: "708911442863796224_wx",
  hnszyf: "859204675187838976_wx",
  nmgjszp: "873715286537998336_wx",
  gszyydx: "694806182456078336_wx",
  hnyyjsxy: "715452063624052736_wx",
  xiangnanxy: "704192391956606976_wx",
  sycsjsxy: "716518165309407232_wx",
  hlbexy: "698688141335814144_wx",
  qdhhxy: "691904628360044544_wx",
  zsb_gs: "69_wx",
  gzhsxy: "697689077747167232_wx",
  znlykjdx: "715449421677731840_wx",
  hljgcxy: "703819001246617600_wx",
  sdsyhgxy: "715102127670505472_wx",
  whqcxy: "701300575758286848_wx",
  bjlhdx: "702057615518695424_wx",
  whqgydx: "701290789394284544_wx",
  qnmzsfxy: "714698997090824192_wx",
  hnzjbmlc: "890139085558706176_wx",
  ndsfxy: "706720858283651072_wx",
  wfxy: "692246980087820288_wx",
  cqgsdxpsxy: "714717310177062912_wx",
  xxjzks: "882848031247626240_wx",
  jlgsxy: "708897251536875520_wx",
  shzyydx: "702053304243945472_wx",
  xinxiangxy: "698749292324204544_wx",
  jxlgdx: "707446962959806464_wx",
  sdxhxy: "692246382350315520_wx",
  jzksjq: "887870730000855040_wx",
  jxzsb: "19_wx",
  wzsxy: "699113422286782464_wx",
  wuyidx: "703839961935638528_wx",
  zsb_fx: "61_wx",
  gszsb: "33_wx",
  sdcjdxysxy: "715103490004492288_wx",
  gddesfxy: "714727431359021056_wx",
  yzdsglxy: "716527623079051264_wx",
  hnzjmsxs: "876687104722931712_wx",
  lpssfxy: "697692063940620288_wx",
  hnbzbmbz: "900269080159391744_wx",
  nysfxy: "698747315189014528_wx",
  fjnldxjsxy: "715096419578159104_wx",
  ynzyydx: "694437094507458560_wx",
  hunkjxy: "704192127010811904_wx",
  yzsfxy: "707456275089928192_wx",
  hntgzpjh: "895107756626472960_wx",
  hubsfdx: "701291609835094016_wx",
  zcb: "22_wx",
  ytlgxy: "691909597739921408_wx",
  qtsfxy: "704200297091379200_wx",
  gdsyhgxy: "714728348394008576_wx",
  hebkjxy: "708912372386467840_wx",
  sxdxkjxy: "716544949313417216_wx",
  zjgydxzjxy: "715444765887045632_wx",
  fjszyf: "859933837439602688_wx",
  xxjzms: "114_wx",
  shuxue: "709640479505248256_wx",
  gyrwkjxy: "714702089916067840_wx",
  changjiangdx: "701289153080246272_wx",
  zjksnr: "876323463695765504_wx",
  sdjtxy: "691899937077157888_wx",
  sdnzxy: "691907246293606400_wx",
  ynjszp: "882842887491612672_wx",
  nmgkjdx: "698688962704224256_wx",
  mdjsfxy: "703820188496900096_wx",
  xmhxxy: "706722512951152640_wx",
  xbsfdx: "694807124455809024_wx",
  tgkskm: "882076676889702400_wx",
  dzkjdxzsxy: "714726986716659712_wx",
  sdzyydx: "692262211025461248_wx",
  cssfxy: "704198462255345664_wx",
  hefeixy: "708924114053312512_wx",
  zzgyyyjsxy: "716538118120677376_wx",
  ncyyjssfxy: "716555019103318016_wx",
  xaoyxy: "696529460015017984_wx",
  wzykdx: "699113671038369792_wx",
  cfxy: "698694587819536384_wx",
  masxy: "708930596229214208_wx",
  jxfzxy: "707453383039619072_wx",
  syhkhtdx: "716517140532637696_wx",
  sysfdx: "707097079295897600_wx",
  gzhuasxy: "703834852804890624_wx",
  ncsfxy: "707455487714914304_wx",
  xzykdx: "704913773799616512_wx",
  mnlgxy: "706723035632734208_wx",
  hnkjdxxxxy: "715453321709731840_wx",
  zjhydx: "699108348141375488_wx",
  xxjszp: "871901680343257088_wx",
  zhaoqingxy: "703841585982730240_wx",
  cjdxwlxy: "716545166884548608_wx",
  scwgydx: "696885355885408256_wx",
  yckjxy: "694710781213237248_wx",
  lvliangxy: "706677354962563072_wx",
  gzsfxy: "697687922680836096_wx",
  hblgxy: "708931744981970944_wx",
  ptgxzsb: "79_wx",
  wnsfxy: "696528671610548224_wx",
  fjsxy: "706718561835806720_wx",
  jzms: "109_wx",
  xizzsb: "135_wx",
  dbsydx: "703819424783241216_wx",
  tjtsxy: "699833328519315456_wx",
  ntlgxy: "704917062033289216_wx",
  lnhsyxy: "707102713035984896_wx",
  ccdxlyxy: "716865713384271872_wx",
  zjksst: "120_wx",
  cdsfxy: "694082361538207744_wx",
  zhkjxy: "703841790699606016_wx",
  sxjcxy: "706678833381216256_wx",
  zjkssj: "124_wx",
  gzgcyyjsxy: "714699238983606272_wx",
  scgsxy: "694089086802681856_wx",
  ahgydx: "708918334437322752_wx",
  cdwlxy: "694089564169003008_wx",
  hnxczj: "899146282481422336_wx",
  wzlgxy: "699113208800903168_wx",
  lzxxkjxy: "714716466912243712_wx",
  zjsrxy: "699112220060844032_wx",
  shangqiuxy: "698748729778810880_wx",
  jxyykjxy: "716554565060403200_wx",
  jzksdg: "897336298529558528_wx",
  zjfsx: "141_wx",
  hnzjfsx: "876696315792003072_wx",
  dlcjxy: "707100710910136320_wx",
  gzqnjjxy: "714701827625267200_wx",
  hublgxy: "701295078841876480_wx",
  lzcjdx: "694716161175674880_wx",
  gdjssfdx: "714728069148712960_wx",
  lngydx: "707093222582333440_wx",
  hysfxynyxy: "715454654115033088_wx",
  hnjzks: "882844948723593216_wx",
  hntggwb: "895108850916192256_wx",
  yunchengxy: "706675990402273280_wx",
  xljk: "711810895702257664_wx",
  gdjszhp: "755663296351453184_wx",
  wfkjxy: "691911571018493952_wx",
  sygxy: "707100299694387200_wx",
  hjsfxy: "701296306896625664_wx",
  kmcsxy: "714696072670330880_wx",
  zjgghz: "142_wx",
  hzhsfdx: "699110397434429440_wx",
  gzjszp: "866854084587360256_wx",
  hbdrsfxy: "716542238853509120_wx",
  zjmsnr: "140_wx",
  herbsyxy: "703824356013871104_wx",
  hnrdhyxy: "716518885056167936_wx",
  gnyxy: "707449157429334016_wx",
  tjnxy: "699832195587473408_wx",
  hnzjbmtj: "874150005117358080_wx",
  dljtdx: "707092607886815232_wx",
  jlgcjssfxy: "716864343439581184_wx",
  xafyxy: "696528947811803136_wx",
  bjnxy: "702056904626110464_wx",
  zjb: "23_wx",
  ytkjxy: "691898541980344320_wx",
  wxthxy: "704917293267259392_wx",
  ahnydx: "708918672851832832_wx",
  zjbsnr: "139_wx",
  hetaoxy: "698689987331895296_wx",
  jingzhouxy: "701301636025204736_wx",
  fzgsxy: "706721837049655296_wx",
  jxnhxy: "699122862113640448_wx",
  zzgsxy: "698750831616995328_wx",
  czksdg: "897345298771939328_wx",
  zsb_ksdg: "38_wx",
  sdhygxy: "692256742168944640_wx",
  xnjtdxxwxy: "714707828658151424_wx",
  hnsydwtj: "870819224726867968_wx",
  hncjzfdx: "716536510549929984_wx",
  zykjxy: "698752880777768960_wx",
  njsfdxtzxy: "716525134414688256_wx",
  xjysxy: "703776143847059456_wx",
  zaozhuangxy: "692247967714336768_wx",
  zhengzhi: "711807143872163840_wx",
  hbgcjsxy: "716551242236305408_wx",
  jsbzkskm: "898113523537350656_wx",
  yczyjsdx: "716535730102452224_wx",
  fzwywmxy: "715092014426497024_wx",
  bjsyhgxy: "714370741481111552_wx",
  hengcxy: "699506125721862144_wx",
  henkjzydx: "716539126151458816_wx",
  tonglingxy: "708924829400952832_wx",
  kmlgdx: "694432657292484608_wx",
  hblgdxqgxy: "788619163484491776_wx",
  jnyxy: "692257463530512384_wx",
  nmgykdx: "698695060732653568_wx",
  szyf: "146_wx",
  huaxue: "711808464947900416_wx",
  bhhtgyxy: "716550274883371008_wx",
  jdzxy: "707456037979525120_wx",
  fjjxxy: "706718779822174208_wx",
  jlhgxy: "708898615600738304_wx",
  hnsydwzp: "870801061586997248_wx",
  hnxxzj: "887546732184530944_wx",
  pingxiangxy: "707455789123080192_wx",
  njxxgcdx: "716520999551614976_wx",
  shaoguanxy: "703839517848211456_wx",
  gzzsb: "17_wx",
  gdzsb: "110_wx",
  liaodongxy: "707098561734897664_wx",
  lnsfxy: "703839255707119616_wx",
  jzsk: "117_wx",
  wenshanxy: "694433016605925376_wx",
  zsbzz: "26_wx",
  jdzyszydx: "716556179452575744_wx",
  jzykdx: "707094383411195904_wx",
  cqytxy: "696889103326208000_wx",
  gzhlgxy: "697688681312989184_wx",
  ccgcxy: "708895319804030976_wx",
  baoshanxy: "694431407456006144_wx",
  hubmzdx: "701291936298745856_wx",
  tygyxy: "706677122526818304_wx",
  njsfdxzbxy: "771268139603595264_wx",
  hunzsb: "34_wx",
  sydwks: "870883006341779456_wx",
  hebmzsfxy: "788617710221070336_wx",
  hebtyxy: "708913384872685568_wx",
  hengydx: "699506631928217600_wx",
  lishuixy: "699112464016084992_wx",
  jiaxingxy: "699116376395747328_wx",
  changshaxy: "704191164489347072_wx",
  whgsxy: "701297691248738304_wx",
  whcmxy: "701300291166371840_wx",
  zsbyy: "25_wx",
  mycsxy: "714708864711077888_wx",
  sydwms: "871610919383011328_wx",
  xjykdx: "703768378147024896_wx",
  xbdxxdxy: "790101661067366400_wx",
  hengxy: "699506392653500416_wx",
  zsb_slx: "60_wx",
  shsfdx: "702054024569520128_wx",
  sxcmxy: "706678009691213824_wx",
  sxgsxy: "706679082013372416_wx",
  nxsfxy: "694709693978337280_wx",
  longyanxy: "706720148215140352_wx",
  jlkjxy: "704909851542368256_wx",
  beihuadx: "708897940908486656_wx",
  yadxxacxxy: "715442451076202496_wx",
  tjsdxrjxy: "716540562247593984_wx",
  hbqcgyxy: "716541920157708288_wx",
  jgsdx: "707447867204907008_wx",
  sdgczyjsdx: "715102864244813824_wx",
  jsbzks: "871259109392715776_wx",
  lylgxy: "698746536770187264_wx",
  tssfxy: "694803164142645248_wx",
  ccgydx: "708897484551434240_wx",
  hnnyzj: "899144756451020800_wx",
  shdjxy: "702054326920118272_wx",
  hnjszp: "865103274035712000_wx",
  zsb_mf: "59_wx",
  longdongxy: "694805443752058880_wx"
};

const CATE_ID_MAP = {
  qb: "",
  zsb: "1",
  szyf: "19",
  xmt: "",
  kkgw: "",
  sydw: "1003",
  gwy: "17",
  jszg: "2",
  kknews: "",
  jszp: "3",
  cjkj: "",
  zjkj: "",
  tgjs: "9"
};
const CITY_TREE_MAP = {
  weidong: "2189",
  wulateqianqi: "1146",
  yiliang: "3190",
  yongshou: "3388",
  jiangkou: "3101",
  mashanxian: "2703",
  gulangxian: "3485",
  dongxihu: "2337",
  qumalaixian: "3584",
  wanzai: "1965",
  zhencheng: "2145",
  shangyuqu: "1652",
  jinchuan: "3015",
  guiyang: "622",
  gongshu: "1603",
  longchuan: "2664",
  mianxian: "3424",
  jiuquan: "672",
  kulun: "1115",
  hnshengzhixiaxian: "598",
  wutongqiao: "2944",
  songshan: "1101",
  yixiu: "1746",
  linying: "2261",
  meixian: "2651",
  wuxiang: "990",
  yilihasake: "702",
  syyunxi: "2352",
  hami: "694",
  shuozhou: "382",
  xinglong: "903",
  huaihua: "556",
  dingzhou: "877",
  chongli: "885",
  gongjue: "3306",
  shenqiu: "2312",
  longsha: "1371",
  songpan: "3013",
  pidu: "2881",
  mengjin: "2178",
  guanyun: "1553",
  dongming: "2146",
  qingzhou: "2061",
  langxi: "1805",
  fenyang: "1075",
  linzhi: "650",
  queshan: "2323",
  dongxing: "2937",
  gaguangan: "2978",
  yazhou: "2813",
  tieshan: "2346",
  baohe: "1696",
  dunhua: "1347",
  baisha: "2827",
  langya: "1762",
  xunhuanhuagong: "784",
  fufeng: "3374",
  wanzhou: "2833",
  eryuan: "3259",
  dongyuan: "2667",
  shanyin: "1002",
  shifang: "2913",
  nanxian: "2515",
  chancheng: "2611",
  kenli: "2036",
  leqing: "1636",
  fangcheng: "2273",
  yalushan: "2998",
  shenhequ: "1185",
  npjianyang: "1871",
  dongchangfu: "2123",
  guangfeng: "1985",
  dhlongchuan: "3266",
  tongnan: "2854",
  baoji: "656",
  leizhou: "2630",
  hetian: "701",
  ljgucheng: "3193",
  wuping: "1884",
  yingquan: "1774",
  jiyuan: "2329",
  jingyan: "2947",
  jiaojiang: "1675",
  yuanyang: "2222",
  hecheng: "2545",
  ankang: "662",
  minhe: "3556",
  chongshan: "1542",
  bijiang: "3099",
  yingjishaxian: "3669",
  baode: "1042",
  ansai: "3407",
  changting: "1882",
  hgxingan: "1399",
  wanzhi: "1708",
  gongzhuling: "1298",
  fuan: "1894",
  qinzhouqu: "3476",
  qixingguan: "3091",
  zhecheng: "2290",
  luoding: "2694",
  linxiashi: "3531",
  cangxian: "913",
  diaobingshan: "1269",
  lianjiang: "2629",
  laoshan: "2012",
  weihjingkaiqu: "2089",
  fengnan: "792",
  ningshan: "3445",
  jingyuequ: "1293",
  dingan: "2823",
  yubei: "2844",
  sangri: "3325",
  hepingqu: "1184",
  gutian: "1889",
  nanming: "3057",
  hsgaoxin: "951",
  anda: "1470",
  yunyan: "3058",
  erlianhaote: "1167",
  hdquzhou: "831",
  huocheng: "3691",
  hangzhou: "450",
  manasi: "3635",
  fengyang: "1767",
  wafangdian: "1205",
  sxqita: "112",
  chuanshan: "2931",
  hbqita: "108",
  pucheng: "3401",
  dianjun: "2359",
  neixiang: "2276",
  minle: "3489",
  xuzhou: "439",
  wuwei: "669",
  anci: "930",
  xiaonan: "2387",
  ningjin: "843",
  langzhong: "2961",
  xinluo: "1880",
  tianquan: "2997",
  kashi: "700",
  anfu: "1960",
  yingshouying: "901",
  xiaoting: "2360",
  jpmiaozuyaozudaizu: "3236",
  yongshun: "2569",
  luonan: "3453",
  mdjxian: "1449",
  nandan: "2784",
  qiezihe: "1444",
  baoan: "2595",
  fzcangshanqu: "1814",
  qingbaijiang: "2877",
  ntjk: "1545",
  luohu: "2592",
  zhangdian: "2021",
  luliang: "3162",
  binhu: "1509",
  huanggang: "540",
  bayannaoer: "395",
  qufu: "2077",
  yangchun: "2671",
  jianzha: "3565",
  fugu: "3432",
  wugong: "3392",
  hdhanshan: "815",
  chuanying: "1301",
  gongqingcheng: "1925",
  hhhtwuchuan: "1084",
  mayang: "2551",
  jiangcheng: "2668",
  xiufeng: "2717",
  congjiang: "3129",
  weixian: "848",
  shuangta: "1271",
  lanling: "2103",
  meilan: "2809",
  shangrao: "496",
  lnlixian: "3528",
  xiongxian: "873",
  yygaoxinqu: "2519",
  dianbai: "2633",
  ganluo: "3054",
  sjzqiaoxi: "765",
  shangzhi: "1369",
  qindu: "3381",
  xianju: "1680",
  dinghu: "2638",
  shangsi: "2747",
  anju: "2932",
  rangtang: "3018",
  baoqing: "1410",
  qixian: "2214",
  kaizhou: "2856",
  lingchuan: "997",
  xuanwei: "3167",
  dadukou: "2836",
  nanzheng: "3420",
  guangyuan: "607",
  fujin: "1440",
  lfxixian: "1057",
  pianguan: "1043",
  gaoyao: "2639",
  pingchuanqu: "3472",
  lushui: "3267",
  fengshun: "2653",
  jianou: "1879",
  gulou: "2166",
  bincheng: "2131",
  huangzhou: "2403",
  zhuanghequ: "1206",
  yingxian: "1003",
  yongning: "3595",
  baijiantan: "3624",
  fuzhou: "477",
  anlu: "2392",
  dztongchuan: "2984",
  bowang: "1732",
  ezhuarong: "2380",
  whgaoxinqu: "2087",
  sihong: "1600",
  mentougou: "738",
  qingfeng: "2243",
  szkaifa: "1005",
  wulingyuan: "2510",
  shuangqing: "2477",
  cuoqin: "3351",
  yichuan: "2186",
  jixian: "1054",
  ypxuzhou: "2970",
  dongwuzhumuqin: "1172",
  heshanshi: "2621",
  lijiang: "636",
  xunyang: "3451",
  jieyang: "578",
  nangong: "852",
  binhuxin: "952",
  lantian: "3363",
  anshun: "625",
  huangchuan: "2303",
  shiguai: "1089",
  yingdong: "1773",
  taianxian: "1211",
  shanzhou: "2264",
  cixian: "825",
  taihequ: "1235",
  lclinxiang: "3208",
  xicheng: "733",
  xcjingkai: "2254",
  hunnanqu: "1190",
  maerkang: "3009",
  guangming: "2600",
  chaisangqu: "1916",
  quzhou: "457",
  yushushi: "1296",
  suiyangxian: "3075",
  alashanzuo: "1180",
  wenling: "1681",
  weiyuanxian: "3518",
  laifeng: "2429",
  haiyan: "1641",
  youhao: "1424",
  xilinxian: "2773",
  jingyangxian: "3385",
  chongyi: "1938",
  ruoqiang: "3646",
  yppingshan: "2977",
  leanxian: "1979",
  xian: "654",
  fuchuanxian: "2781",
  shanhaiguan: "807",
  rongshui: "2715",
  jinfeng: "3594",
  gangkou: "2745",
  xiuzhou: "1639",
  jianli: "2402",
  gulin: "2908",
  huilai: "2688",
  enping: "2622",
  xide: "3051",
  xiuyu: "1834",
  zznanjing: "1867",
  zichang: "3418",
  zhuozhou: "876",
  shehong: "2935",
  chengxiang: "1831",
  shiyan: "533",
  midu: "3254",
  wongan: "3137",
  zhaohua: "2925",
  dandong: "405",
  chuxiong: "639",
  qingdao: "498",
  dingri: "3289",
  shukan: "1309",
  putuoqu: "1672",
  weixilisuzu: "3273",
  xianqu: "1317",
  yingcheng: "2391",
  lnwenxian: "3524",
  dazigongyeyuan: "3285",
  qianxi: "797",
  antu: "1352",
  jishishan: "3538",
  jieshou: "1781",
  jianxi: "2176",
  jiashan: "1640",
  xiangzhouxian: "2795",
  xiuwu: "2235",
  qidong: "2471",
  shangyi: "889",
  mashan: "1392",
  hsyangxin: "2347",
  longkou: "2048",
  taipusi: "1174",
  karuo: "3304",
  hanchuan: "2393",
  gaoyang: "863",
  luodian: "3140",
  longtan: "1300",
  zhangqiu: "2002",
  tianzhu: "3123",
  jianye: "1498",
  wlcbjining: "1150",
  yiling: "2361",
  yakeshi: "1139",
  zaozhuang: "500",
  weiyuan: "2938",
  yulongnaxizu: "3194",
  zhaoling: "2259",
  yingde: "2678",
  lyyongding: "1881",
  ablixian: "3011",
  mianchi: "2265",
  shouguang: "2063",
  cflinxi: "1105",
  youyang: "2869",
  xihu: "1604",
  jingzhou: "539",
  xixian: "2305",
  mudanjiang: "432",
  houma: "1061",
  shilinyizu: "3154",
  shuangtaizi: "1260",
  mingshui: "1468",
  shouxian: "1729",
  jidong: "1393",
  jinpingxian: "3124",
  yanchengjk: "1573",
  beizhen: "1239",
  anyang: "517",
  puxian: "1059",
  yuganxian: "1991",
  cdchengde: "902",
  liannan: "2677",
  erqi: "2150",
  longgangqu: "1279",
  jinzhai: "1794",
  dawukou: "3598",
  qixingqu: "2720",
  shunping: "871",
  bcjingkaiqu: "1342",
  nanping: "483",
  czjingkai: "923",
  bjfangshan: "739",
  lianzhou: "2679",
  qingzhen: "3066",
  lhjingkaiqu: "2262",
  gaoxinqu: "2157",
  xiji: "3607",
  quwo: "1047",
  yongshan: "3187",
  wengyuan: "2587",
  suoxian: "3340",
  yuqing: "3081",
  tjhepingqu: "748",
  gangcha: "3563",
  lvyuan: "1288",
  gongchengxian: "2732",
  tiandong: "2767",
  dongchang: "1320",
  gaize: "3350",
  ruijin: "1948",
  shuangqiao: "899",
  gongyeyuan: "2248",
  jingtaixian: "3475",
  dengfeng: "2163",
  fucheng: "950",
  meitan: "3080",
  qijiang: "2842",
  pingding: "979",
  anxi: "1852",
  wuerhe: "3625",
  dqdatong: "1416",
  sjzpingshan: "780",
  congzuo: "593",
  huaiyin: "1998",
  baiquan: "1385",
  jinlin: "1430",
  lishu: "1313",
  njqixia: "1501",
  qingyunpu: "1898",
  yangling: "3382",
  tianya: "2812",
  duolun: "1178",
  poyangxian: "1992",
  hangjin: "1126",
  zoucheng: "2078",
  dushan: "3138",
  linhai: "1682",
  ziyangxian: "3446",
  bdgaoxin: "874",
  fuyu: "1382",
  dqzhaoyuan: "1418",
  mile: "3229",
  qujing: "632",
  siyang: "1599",
  xichuan: "2277",
  huairen: "1006",
  yizhouqu: "2783",
  yanshan: "917",
  lishi: "1063",
  anze: "1052",
  hongqiaoqu: "753",
  hulunbeier: "394",
  zengdu: "2420",
  dalian: "401",
  pingnan: "2756",
  meigu: "3055",
  junlian: "2975",
  ewenke: "1134",
  maanshan: "465",
  taijiang: "3126",
  qiongshan: "2808",
  chikan: "2623",
  zhungeer: "1123",
  yuan: "1790",
  banma: "3574",
  macheng: "2412",
  xiashan: "2624",
  fuyun: "3706",
  guchengxian: "2374",
  kaili: "3117",
  pingchang: "3004",
  hefei: "461",
  guanling: "3089",
  chunan: "1613",
  xinchang: "1653",
  kailu: "1114",
  qingjiangpu: "1559",
  xiaoyi: "1074",
  qingjian: "3439",
  huining: "3474",
  fuxian: "3413",
  xuyong: "2907",
  fanyu: "2577",
  whhainan: "1097",
  fengqing: "3209",
  daxiang: "2478",
  shunchang: "1872",
  kaifeng: "514",
  tongde: "3569",
  xihexian: "3527",
  szxiangcheng: "1533",
  lushan: "2194",
  yuci: "1007",
  huatingshi: "3499",
  bange: "3341",
  anren: "2530",
  sishui: "2074",
  xilin: "398",
  wslichuan: "2424",
  fzpingtanxian: "1823",
  anping: "947",
  lunan: "788",
  yizhou: "3629",
  dongzhouqu: "1215",
  tongan: "1829",
  balikunhasake: "3630",
  tljingkai: "1118",
  shijiang: "2765",
  biru: "3336",
  gushi: "2302",
  beita: "2479",
  tianyuan: "2447",
  xinzhouqu: "1984",
  xiqu: "2898",
  wenxi: "1021",
  dingxi: "674",
  liangshan: "621",
  wuzhishan: "2818",
  baigouxincheng: "875",
  meijiangqu: "2650",
  aershan: "1162",
  zhaoqing: "568",
  yichuanxian: "3415",
  fengxiang: "3372",
  hutubi: "3634",
  fuxing: "817",
  yuhu: "2454",
  alsgaoxinqu: "1183",
  tongjiang: "3002",
  quanjiao: "1765",
  xinghua: "1593",
  tonggu: "1969",
  meilisidawoerzu: "1377",
  ziliujing: "2891",
  xiangyun: "3252",
  xianfeng: "2428",
  fuyuan: "3165",
  tonghe: "1367",
  nanpiao: "1280",
  hebeiqu: "752",
  luyang: "1694",
  gaotang: "2129",
  mdjningan: "1454",
  yzhuilongyu: "2543",
  xingping: "3393",
  lengshuijiang: "2561",
  zyyizuhanizulahuzu: "3203",
  hhhtjingkai: "1085",
  eyang: "3001",
  hannan: "2338",
  taojiang: "2516",
  batang: "3036",
  wangcang: "2927",
  jinnan: "756",
  loudi: "557",
  geermu: "3585",
  huairou: "744",
  jiuyuan: "1091",
  shangzhou: "3452",
  wudalianchi: "1461",
  cdluolong: "3313",
  hechuan: "2849",
  beihu: "2521",
  huaianjk: "1564",
  daiyue: "2080",
  xinhuang: "2552",
  guangzong: "846",
  dingxing: "861",
  laohekou: "2376",
  lyghaizhou: "1550",
  jian: "493",
  wuyuan: "1994",
  duodao: "2383",
  qianjiang: "2846",
  miyi: "2900",
  yuli: "3645",
  yongchuan: "2850",
  huangling: "3417",
  hegang: "426",
  linyou: "3378",
  litang: "3035",
  qinglong: "810",
  ziyun: "3090",
  xinxiang: "519",
  yancheng: "445",
  jizhou: "942",
  wuqiang: "945",
  wensheng: "1254",
  dingyuan: "1766",
  tonglu: "1612",
  ruzhgongyiou: "2159",
  yuzhong: "2835",
  tianchang: "1770",
  shenmu: "3441",
  fengfengkuang: "818",
  baqiao: "3355",
  qiemo: "3647",
  honggang: "1415",
  longanxian: "2702",
  zongyang: "1743",
  longshan: "2570",
  shengzhou: "1655",
  qitaihe: "431",
  sheyang: "1571",
  pingxiang: "488",
  dachang: "937",
  lhyancheng: "2258",
  jinzhou: "406",
  xiangxiang: "2460",
  banan: "2845",
  wuxing: "1645",
  fengman: "1302",
  mingshan: "2993",
  guanghe: "3535",
  qianan: "804",
  tunliu: "983",
  yaoan: "3221",
  qinyang: "2240",
  muding: "3219",
  wuzhai: "1039",
  huozhou: "1062",
  dingjie: "3297",
  yczhijiang: "2369",
  rongcheng: "864",
  nierong: "3337",
  lincang: "638",
  zhongjiang: "2911",
  whrongcheng: "2090",
  zhijiang: "2553",
  jiangling: "2397",
  chishui: "3083",
  taiqian: "2246",
  qiantang: "1611",
  keerqin: "1111",
  tianhe: "2574",
  jinzhouqu: "1202",
  bama: "2790",
  qionglai: "2888",
  wangcheng: "2440",
  qingxian: "914",
  jiaoqu: "1742",
  yiixng: "1513",
  lijin: "2037",
  baqing: "3342",
  heshanqu: "2514",
  huichang: "1945",
  jinhu: "1563",
  sptiexi: "1311",
  gaoqing: "2026",
  gaochun: "1506",
  tongguan: "1740",
  jiexi: "2687",
  honghuagang: "3071",
  jinchengjiangqu: "2782",
  yzjiangdu: "1577",
  chongyang: "2417",
  fangshan: "1071",
  wenshanshi: "3239",
  jinzhong: "383",
  fuhsun: "2896",
  meizhou: "570",
  yuhua: "768",
  fenglin: "1427",
  maqu: "3544",
  mangshi: "3263",
  jiangyong: "2537",
  dzjingkai: "2990",
  tuokexun: "3628",
  czgulou: "1526",
  tongwei: "3516",
  yucheng: "2291",
  qianwei: "2946",
  huolinguole: "1119",
  hljyichun: "429",
  qingtongxia: "3605",
  xingren: "3110",
  enshi: "543",
  baoshan: "634",
  maguan: "3243",
  hengfengxian: "1989",
  liulin: "1068",
  biyang: "2324",
  yindu: "2202",
  ruyang: "2183",
  xiangyin: "2494",
  ycxixia: "3593",
  beibei: "2841",
  jinmen: "1855",
  mdjdongshan: "1446",
  yuhui: "1716",
  zhangjiakou: "372",
  bata: "3406",
  chengyang: "2014",
  songyang: "1688",
  jiuzhi: "3577",
  taoyuan: "2505",
  suichuan: "1958",
  chabuchaerxibo: "3690",
  huangpu: "2576",
  yaohai: "1693",
  basu: "3310",
  zhuxi: "2354",
  yinan: "2100",
  nygaoxin: "2282",
  shenzha: "3339",
  xinfengxian: "1935",
  jiangmen: "565",
  qimen: "1761",
  deanxian: "1920",
  shaoguan: "560",
  huangpi: "2341",
  ncxihuqu: "1897",
  jilong: "3300",
  tonghuaxian: "1322",
  fuyushi: "1338",
  tianjun: "3590",
  qinshui: "995",
  shangjie: "2153",
  huaiian: "1557",
  yanshou: "1368",
  tailai: "1380",
  dunhuang: "3506",
  yizheng: "1580",
  fsqingyuan: "1220",
  bazhong: "617",
  haidong: "679",
  jimo: "2015",
  lingwu: "3597",
  nianzishan: "1376",
  akesai: "3504",
  daozhen: "3077",
  mishan: "1395",
  baoying: "1578",
  beidaihexin: "814",
  tunxi: "1755",
  mdjdongning: "1456",
  rushan: "2091",
  linshui: "2982",
  cqxian: "600",
  tongxu: "2170",
  zkjingkai: "2316",
  bishan: "2852",
  tongguanxian: "3397",
  yuxi: "633",
  xzqita: "360",
  penglai: "2045",
  jaxingan: "1955",
  changjiang: "2828",
  ntqidong: "1546",
  dingbian: "3434",
  shuanghu: "3344",
  langao: "3447",
  fengning: "906",
  jiajiang: "2948",
  xuchang: "522",
  fzyongtaixian: "1822",
  hezhou: "590",
  ziyuanxian: "2730",
  xinmin: "1196",
  longyang: "3177",
  gzqita: "114",
  fanshi: "1035",
  kaiyuan: "3227",
  aykaifa: "2208",
  yutian: "798",
  hongsibao: "3602",
  pingshanqu: "1221",
  linjiang: "1332",
  changshanxian: "1667",
  jxhengshan: "1388",
  wuchang: "1370",
  heishui: "3017",
  geermuzangqing: "3282",
  sajia: "3290",
  diqing: "646",
  yangjiang: "573",
  gzxiangcheng: "3037",
  laiyuan: "865",
  yzguangling: "1575",
  wensu: "3655",
  boertala: "696",
  jianshi: "1326",
  guangze: "1874",
  huaiyuan: "1718",
  yongkang: "1664",
  baotou: "389",
  suixixian: "2627",
  puyangxian: "2247",
  xifeng: "1267",
  qqhelongjiang: "1378",
  nankang: "1933",
  lingshui: "2830",
  lipushi: "2733",
  huangshi: "532",
  huichun: "1348",
  bayi: "3315",
  mingshanqu: "1223",
  hengyangqu: "2472",
  ynqita: "118",
  pishan: "3681",
  dongping: "2082",
  zhalantun: "1140",
  huanan: "1436",
  xtgaoxinqu: "2457",
  jiulong: "3025",
  yinzhou: "1619",
  longyouxian: "1669",
  hnzhixia: "530",
  qingyuanxian: "1690",
  jinta: "3501",
  huazhouqu: "3396",
  kfqixian: "2169",
  zixixian: "1982",
  nmgqita: "342",
  chengbuxian: "2486",
  huimin: "2133",
  binchuan: "3253",
  tongyu: "1341",
  hgxiangyang: "1396",
  mengcun: "922",
  haiyuan: "3613",
  dangtu: "1733",
  fengshan: "2786",
  zhushanqu: "1906",
  xingxian: "1066",
  ruicheng: "1028",
  guangping: "828",
  qhqita: "341",
  kangbao: "887",
  tongzi: "3074",
  bayuquan: "1242",
  qingchengxian: "3508",
  xinzhou: "385",
  yaodu: "1046",
  wulong: "2858",
  pengzhou: "2887",
  jingningxian: "1691",
  kaifu: "2438",
  qingyuan: "574",
  haixing: "916",
  changzhouqu: "2735",
  shizhu: "2867",
  suiningxian: "2484",
  mengshanxian: "2739",
  zhengzhou: "513",
  bzjingkai: "3005",
  wuxixian: "2866",
  liangyuan: "2285",
  shuangliu: "2880",
  gaobeidian: "879",
  zunyi: "624",
  xilinhaote: "1168",
  mianyang: "606",
  shuifu: "3192",
  nanfengxian: "1977",
  xishiqu: "1241",
  pykaifa: "2249",
  lianchi: "855",
  zhangxian: "3520",
  fengquan: "2218",
  shahe: "853",
  sxgaoxin: "993",
  zhijin: "3094",
  donghuqu: "1896",
  jiayuguan: "665",
  wuhai: "390",
  xixia: "2274",
  wuhan: "531",
  jinghaiqu: "762",
  fztaijiangqu: "1813",
  yichengshi: "2378",
  yihuangxian: "1980",
  binhaixinqu: "760",
  fangchengqu: "2746",
  yongfuxian: "2727",
  jixi: "425",
  shanting: "2032",
  baiyun: "2575",
  nanchang: "486",
  hongguqu: "3463",
  huanghua: "928",
  lanxi: "1465",
  dawaqu: "1262",
  linan: "1609",
  shenbeixinqu: "1191",
  guangdeshi: "1811",
  xinwen: "2976",
  laoling: "2121",
  chengmai: "2825",
  kangping: "1194",
  hunqita: "127",
  nppingnan: "1890",
  jinxixian: "1981",
  szsuixian: "2421",
  heshan: "2210",
  yangpu: "1486",
  deqin: "3272",
  gaolanxian: "3465",
  dzpingyuan: "2116",
  dalate: "1122",
  susong: "1749",
  luolong: "2179",
  dechang: "3044",
  jize: "827",
  changtu: "1268",
  jiayuguanshi: "3468",
  yangqu: "961",
  yuanlingxian: "2547",
  pingbianmiaozu: "3230",
  wanan: "1959",
  tangyin: "2205",
  shanwei: "571",
  anqing: "468",
  yaobei: "1339",
  kunming: "631",
  liangdangxian: "3530",
  yichun: "494",
  abaga: "1169",
  sanshui: "2614",
  ruichang: "1924",
  zhuzhou: "546",
  nehe: "1386",
  wuzhou: "583",
  lancanglahuzu: "3206",
  donggang: "2092",
  yanqihuizuzizhixian: "3648",
  longmatan: "2904",
  baicheng: "421",
  changyiqu: "1299",
  wulatezhong: "1147",
  donge: "2127",
  ejina: "1182",
  dongguang: "915",
  qiubei: "3244",
  zherong: "1893",
  fumianqu: "2759",
  xihuqu: "1222",
  heihe: "433",
  lingao: "2826",
  tumoteyou: "1092",
  qingshanhu: "1899",
  qianfeng: "2979",
  nanjianyizu: "3255",
  hancheng: "3404",
  lusong: "2445",
  chaoxianxian: "1331",
  wuchuan: "2631",
  luhuo: "3028",
  tangyuan: "1438",
  mengyin: "2107",
  haibei: "680",
  changwu: "3389",
  yingshan: "2957",
  lingbi: "1785",
  guidong: "2529",
  shuangliao: "1315",
  taishan: "2079",
  shaoyang: "549",
  quanzhou: "481",
  longmen: "2649",
  changshou: "2847",
  baishui: "3402",
  linxia: "676",
  ababa: "3019",
  xiaodian: "954",
  binhai: "1569",
  nangang: "1354",
  juxian: "2095",
  yqyuxian: "980",
  changyi: "2066",
  zhenlai: "1340",
  chengduo: "3581",
  linghe: "1234",
  shangcai: "2320",
  jiaocheng: "1065",
  nanhuqu: "1638",
  guoluo: "683",
  haojiang: "2606",
  shaowu: "1877",
  xiangqiao: "2682",
  changzi: "989",
  yungang: "967",
  xingningqu: "2695",
  shenzhen: "561",
  dehua: "1854",
  dehong: "644",
  lingtai: "3495",
  dehui: "1297",
  tshaigang: "802",
  linhe: "1143",
  maqin: "3573",
  yeji: "1791",
  cele: "3683",
  yangxi: "2670",
  jiahe: "2526",
  shuangyashan: "427",
  junshan: "2491",
  xianxian: "921",
  ningde: "485",
  runan: "2325",
  zzyicheng: "2030",
  shchangning: "1482",
  jzqixian: "1014",
  zhalaite: "1165",
  heishan: "1236",
  wangkui: "1464",
  jinshi: "2508",
  jianshan: "1404",
  mangya: "3587",
  cenxi: "2740",
  xiwuzhumuqin: "1173",
  nanfen: "1224",
  gzzhongshan: "3067",
  yanqing: "747",
  ningbo: "451",
  fengxian: "3379",
  deqing: "2643",
  gaoping: "999",
  simao: "3198",
  zhaozhou: "1417",
  weiyang: "3356",
  zhenglan: "1177",
  yuanshi: "781",
  jlqita: "128",
  xixiu: "3085",
  zhaotong: "635",
  jinsha: "3093",
  yushui: "1927",
  chenxi: "2548",
  hygaoxinqu: "2473",
  taishanshi: "2619",
  linli: "2504",
  fuling: "2834",
  ningdu: "1942",
  shangchengqu: "1602",
  lanpingbaizupumizu: "3270",
  jiali: "3335",
  jmstongjiang: "1439",
  futian: "2593",
  yinjiang: "3105",
  fuliangxian: "1907",
  tangxian: "862",
  rizhao: "507",
  shaoyangxian: "2481",
  shfengxian: "1494",
  jianshui: "3231",
  shigu: "1069",
  aohan: "1110",
  shihe: "2296",
  zhangjiachuan: "3482",
  danba: "3024",
  pingqiao: "2297",
  minqin: "3484",
  wugang: "2198",
  lanzhou: "664",
  haining: "1642",
  qnbuyizu: "630",
  baise: "589",
  hanzhong: "660",
  gongnong: "1397",
  jlgaoxinqu: "1305",
  jiyang: "2003",
  jishou: "2563",
  baita: "1253",
  baojingxian: "2567",
  sjzgaoxin: "783",
  dongchuan: "3149",
  yajiang: "3026",
  lsshawan: "2943",
  jiangnan: "2697",
  yijinhuoluo: "1128",
  lingshi: "1016",
  suining: "608",
  changhai: "1204",
  zengcheng: "2581",
  yufengqu: "2708",
  shaoshan: "2461",
  longcheng: "1272",
  nmg: "38",
  lanshanxian: "2539",
  baodiqu: "759",
  saibeiguanli: "898",
  xuecheng: "2029",
  damaolianhe: "1094",
  btxitugaoxin: "1095",
  hongshan: "1099",
  yuanjiang: "2520",
  qiaojia: "3184",
  ezhou: "536",
  dongliao: "1319",
  babuqu: "2777",
  xinchengxian: "2794",
  kuancheng: "907",
  wenshang: "2073",
  dingtao: "2139",
  syjingkaiqu: "1337",
  fuxinxian: "1251",
  xifengqu: "3507",
  nayong: "3095",
  lanxixian: "1661",
  shijingshan: "736",
  qinnan: "2749",
  ritu: "3348",
  changyangtujiazu: "2365",
  lingbao: "2269",
  longyuqu: "2736",
  liuzhou: "581",
  gangba: "3303",
  dayuxian: "1936",
  dacheng: "935",
  wenjiang: "2879",
  liaoyang: "409",
  bozhou: "474",
  huanrenxian: "1226",
  xiejiaji: "1725",
  jianchuan: "3260",
  shannan: "651",
  baoding: "371",
  cdqingyang: "2872",
  jurong: "1588",
  baihe: "3450",
  jianyang: "2890",
  xykaifa: "2306",
  tanghe: "2279",
  ningwu: "1036",
  neijiang: "609",
  jingkai: "2156",
  yunanxian: "2693",
  rongxian: "2895",
  yulin: "661",
  wulanhaote: "1161",
  yichengxian: "1048",
  weinan: "658",
  gaer: "3347",
  lvshun: "1201",
  njjingkai: "2940",
  anduo: "3338",
  yuechengqu: "1650",
  dadongqu: "1186",
  suqian: "449",
  changning: "2476",
  gaoxian: "2973",
  yuanping: "1045",
  ziyangqu: "2513",
  chencang: "3371",
  longfeng: "1413",
  diao: "1389",
  shiqu: "3033",
  huitong: "2550",
  shangcheng: "2301",
  bozhouqu: "3073",
  guiping: "2757",
  renze: "837",
  laoting: "796",
  pingluo: "3600",
  heqing: "3261",
  huli: "1827",
  fushunxian: "1218",
  nyshifanqu: "2283",
  mengzi: "3228",
  shangshui: "2311",
  zibo: "499",
  wenfeng: "2200",
  syyunyang: "2351",
  ximengwazu: "3207",
  weidu: "2250",
  jstaizhou: "448",
  zhengding: "773",
  jishuixian: "1953",
  luchengqu: "1625",
  youxian: "2916",
  hezuo: "3539",
  hongkou: "1485",
  hangu: "800",
  yunzhou: "968",
  jincheng: "381",
  wuxue: "2413",
  linguiqu: "2722",
  hanyin: "3443",
  youyanxian: "1212",
  kaundianxian: "1230",
  yining: "3686",
  yangshuo: "2723",
  daguan: "1745",
  jiedong: "2686",
  fengxin: "1964",
  nantong: "442",
  liunanqu: "2709",
  luqiao: "1677",
  huizhou: "569",
  tongshan: "1518",
  gucheng: "948",
  hualong: "2242",
  alashankou: "3640",
  tuquan: "1166",
  pingtang: "3139",
  jinggangshan: "1962",
  huachixian: "3510",
  anyixian: "1903",
  mingguang: "1771",
  yichang: "534",
  baishan: "419",
  xinzheng: "2162",
  peicheng: "2915",
  duchangxian: "1921",
  tiantai: "1679",
  linzhang: "821",
  yanjiang: "3006",
  dongsheng: "1120",
  zjkhuaian: "892",
  nangqianxian: "3583",
  daqing: "428",
  neiqiu: "840",
  shouyang: "1013",
  maijiqu: "3477",
  yecheng: "3672",
  chengan: "822",
  pingjiang: "2495",
  xxkaifa: "2226",
  shuyang: "1598",
  jianan: "2251",
  qixia: "2052",
  shiqian: "3103",
  zhumadian: "529",
  yutianxian: "3684",
  hongjiang: "2556",
  huaining: "1747",
  linyixian: "1019",
  yongnian: "820",
  tiexiqu: "1188",
  haicang: "1826",
  jsqita: "119",
  ah: "14",
  xinghe: "1154",
  shixiaqu: "365",
  wulanchabu: "396",
  changchun: "414",
  luobei: "1402",
  huguan: "988",
  huaishang: "1717",
  daocheng: "3038",
  lianyungang: "443",
  huangguqu: "1187",
  jiuzhaigou: "3014",
  xiangfang: "1358",
  gashi: "3675",
  nanmulin: "3287",
  bj: "39",
  xinxiangxian: "2220",
  yangbiyizu: "3251",
  chayouzhong: "1157",
  yantian: "2597",
  zuoquan: "1010",
  bayan: "1365",
  renbu: "3295",
  smsanyuan: "1836",
  malipo: "3242",
  hetang: "2444",
  ziyang: "618",
  haiyang: "2053",
  nanjing: "437",
  wuchengqu: "1656",
  gaoyou: "1581",
  changqing: "2001",
  aletai: "704",
  xinhuaxian: "2560",
  ranghulu: "1414",
  xundianhuizuyizu: "3157",
  cq: "18",
  tumotezuo: "1080",
  laishan: "2044",
  lanzhouxinqu: "3467",
  luanchuan: "2181",
  danfeng: "3454",
  leibo: "3056",
  anxiang: "2501",
  henanxian: "3567",
  gannan: "677",
  yongningqu: "2700",
  dingxiang: "1032",
  suiyang: "2286",
  tongcheng: "1753",
  zuogong: "3311",
  tieli: "1431",
  sunwu: "1459",
  zepuxian: "3670",
  aimin: "1448",
  jingchuan: "3494",
  zhanang: "3323",
  liubei: "2710",
  xiantao: "2431",
  guangyang: "931",
  renhe: "2899",
  beihai: "584",
  lezhi: "3008",
  fj: "26",
  panji: "1727",
  puan: "3111",
  yuncheng: "2144",
  suyu: "1597",
  gaizhou: "1244",
  honggutanqu: "1901",
  zhenba: "3427",
  balinzuo: "1103",
  yingshang: "1778",
  zhaoshanqu: "2458",
  lnhuixian: "3529",
  xingan: "397",
  xiling: "2357",
  shangdu: "1153",
  chengde: "373",
  hetianshi: "3678",
  zhenan: "1229",
  gd: "12",
  hengyang: "548",
  subeixian: "3503",
  hgnanshan: "1398",
  tianlin: "2772",
  chengdu: "601",
  echeng: "2381",
  gs: "30",
  huyi: "3362",
  xihequ: "1250",
  jintan: "1529",
  jiaxian: "2195",
  jintai: "3370",
  gx: "41",
  gz: "17",
  wanbolin: "958",
  suning: "918",
  yunlong: "3258",
  taikang: "2314",
  zhenning: "3088",
  hb: "11",
  baoting: "2831",
  hn: "10",
  xingbin: "2793",
  hanyuan: "2995",
  tiexi: "1208",
  linfen: "386",
  qqhegannan: "1381",
  arong: "1131",
  longlinxian: "2774",
  ahsuzhou: "472",
  huinong: "3599",
  fzminqingxian: "1821",
  saihan: "1079",
  mianzhu: "2914",
  xifengxian: "3064",
  chengguan: "3274",
  ebian: "2950",
  tsgaoxin: "801",
  anshan: "402",
  tiedong: "1207",
  jinchengqu: "994",
  geyangxian: "1990",
  bjchaoyang: "734",
  caidian: "2339",
  keshenketeng: "1106",
  xiangning: "1055",
  dongyinggangqu: "2040",
  jl: "32",
  chenggu: "3421",
  daan: "2893",
  mengla: "3249",
  puer: "637",
  gaocheng: "769",
  js: "22",
  yueyang: "550",
  shanghe: "2007",
  jx: "20",
  wenzhou: "452",
  ningqiang: "3425",
  beiguan: "2201",
  hengyangxian: "2467",
  jiange: "2929",
  luoping: "3164",
  xinjiapoqu: "1306",
  dongfeng: "1318",
  wuding: "3225",
  linwei: "3395",
  jianhu: "1572",
  linqing: "2130",
  jimusaer: "3637",
  helongshi: "1350",
  haizhu: "2573",
  yingjiang: "1744",
  qingshuihe: "1083",
  hdshexian: "824",
  jianhe: "3125",
  xiangzhouqu: "2372",
  ln: "28",
  tzyiyaogxjscyks: "1592",
  erdaojiang: "1321",
  lnqita: "124",
  shushan: "1695",
  weichang: "908",
  qinyuan: "992",
  taibai: "3380",
  yongxin: "1961",
  xiangcheng: "2317",
  genhe: "1142",
  puge: "3047",
  dafang: "3092",
  xinyi: "2636",
  zhashui: "3458",
  xinye: "2280",
  longquan: "1692",
  anning: "3158",
  zhoucun: "2024",
  sjzjinzhou: "786",
  pxluxi: "1913",
  chayouqian: "1156",
  xinyu: "490",
  guluo: "2497",
  ycjingan: "1968",
  yingze: "955",
  yangshan: "2675",
  hengnan: "2468",
  hezhang: "3097",
  nx: "34",
  nxqita: "336",
  kmwuhua: "3145",
  nanzhao: "2272",
  shangli: "1912",
  fengcheng: "1970",
  longsheng: "2729",
  yutai: "2070",
  huangyan: "1676",
  nanxiong: "2591",
  jingkou: "1582",
  neihuang: "2207",
  dongxiangxian: "3537",
  foshan: "564",
  shangluo: "663",
  fulaerji: "1375",
  jzchengxiang: "2239",
  xinwu: "1511",
  jiande: "1614",
  panjin: "410",
  mingxi: "1838",
  yinhai: "2742",
  qiqihaer: "424",
  haiyanxian: "3562",
  xuhui: "1481",
  huaiyang: "2308",
  chaozhou: "577",
  yuanhui: "2257",
  qg: "9",
  qh: "37",
  xiangfen: "1049",
  fzjinanqu: "1816",
  shenzhou: "953",
  bzbazhou: "3000",
  delingha: "3586",
  xindu: "836",
  qt: "100",
  qinxian: "991",
  lilingshi: "2453",
  lyuliang: "387",
  wenchang: "2820",
  rongchengqu: "2685",
  hebi: "518",
  huazhou: "2635",
  litong: "3601",
  lingyuan: "1277",
  guanyang: "2728",
  daqinghsan: "1428",
  xinan: "2180",
  xiangshan: "1737",
  changan: "764",
  mancheng: "856",
  nujiang: "645",
  sc: "13",
  sd: "19",
  shanghang: "1883",
  sh: "33",
  nenjiang: "1462",
  chicheng: "895",
  hengshan: "3431",
  panshi: "1310",
  wuling: "2499",
  lechang: "2590",
  yueyanglou: "2489",
  hongweiqu: "1255",
  sx: "15",
  tongdao: "2555",
  xiaochang: "2388",
  wuxuan: "2796",
  bengbu: "463",
  tj: "29",
  tiefeng: "1373",
  jiaxiang: "2072",
  seda: "3034",
  jiaxing: "453",
  qingyang: "673",
  chengqu: "2658",
  lingdong: "1405",
  qianshanqu: "1210",
  aletaishi: "3704",
  liangcheng: "1155",
  qingshui: "3478",
  dali: "643",
  kepingxian: "3661",
  chaling: "2450",
  bdqingyuan: "857",
  lujiang: "1700",
  taishun: "1633",
  changde: "551",
  daning: "1056",
  dongxiang: "1974",
  guangning: "2640",
  luopu: "3682",
  xjqita: "337",
  ypchangning: "2972",
  nanhai: "2612",
  changdu: "649",
  deyang: "605",
  majiang: "3131",
  chengwu: "2142",
  daki: "3250",
  linxiang: "2498",
  libo: "3135",
  dujiangyan: "2886",
  wzjingkaiqu: "1634",
  yongqiao: "1782",
  xinpingyizudaizu: "3174",
  yushu: "684",
  yiwushi: "1662",
  yanting: "2919",
  xiaxian: "1026",
  zhongzhan: "2232",
  jingdongyizu: "3201",
  lizhou: "2924",
  xigangqu: "1198",
  fuding: "1895",
  dengzhou: "2284",
  xj: "35",
  laiyang: "2049",
  daying: "2934",
  yushe: "1009",
  liuba: "3428",
  kongtongqu: "3493",
  changge: "2256",
  zywuchuan: "3078",
  yuyang: "3430",
  xz: "42",
  qthxinxing: "1442",
  xingguoxian: "1944",
  dianjiang: "2861",
  lainyuan: "2562",
  yn: "21",
  xinle: "787",
  conghua: "2580",
  tiedongqu: "1312",
  lichengxian: "987",
  yuexiu: "2572",
  xintian: "2540",
  liangping: "2857",
  beichen: "757",
  qingxin: "2673",
  nanxun: "1646",
  guangshan: "2299",
  xichong: "2960",
  zj: "27",
  pujiang: "2885",
  longxian: "3376",
  anyangxian: "2204",
  renshou: "2964",
  xinmi: "2161",
  xiaogan: "538",
  zzxiangcheng: "1859",
  chenbaerhu: "1135",
  buerjin: "3705",
  hainqita: "338",
  chengxi: "3549",
  yanshanxian: "1988",
  minxian: "3521",
  xzxinyi: "1523",
  datonghu: "2518",
  xinji: "785",
  dari: "3576",
  danzhai: "3132",
  yongchun: "1853",
  changji: "695",
  wudang: "3060",
  muchuan: "2949",
  zijin: "2663",
  yanggu: "2125",
  dapu: "2652",
  zhangzhou: "482",
  jizhouqu: "763",
  donghe: "1086",
  yongqing: "933",
  feixiang: "819",
  wusheng: "2981",
  qiaoxi: "881",
  yushan: "1731",
  changle: "2059",
  shuangbai: "3218",
  quangang: "1850",
  kedong: "1384",
  qxnbuyizu: "628",
  xinhe: "845",
  zhalute: "1117",
  laishui: "859",
  changli: "811",
  dachuan: "2985",
  moladawa: "1132",
  xinjianqu: "1900",
  jinzhouxian: "2554",
  xingtai: "370",
  jxlishu: "1390",
  huachuan: "1437",
  liuhe: "1504",
  luannan: "795",
  boxing: "2136",
  seni: "3334",
  nanjiang: "3003",
  ningyang: "2081",
  suibin: "1403",
  xiangyang: "535",
  wutaishan: "1044",
  hequ: "1041",
  dengtaxian: "1259",
  panan: "1660",
  whjingkaiqu: "2088",
  baiyin: "667",
  lengshuitan: "2533",
  qzjinjiang: "1857",
  qiaokou: "2332",
  datongxian: "3552",
  xinfu: "1031",
  nanchnagxian: "1902",
  emin: "3700",
  czxinhua: "911",
  zhongxiang: "2385",
  songmuqu: "2474",
  hepu: "2744",
  hsjingxian: "949",
  guangnan: "3245",
  taicang: "1540",
  chengcheng: "3400",
  huzhou: "454",
  yongji: "1029",
  hanshan: "1734",
  dingnan: "1940",
  yunxiao: "1863",
  yunxian: "3210",
  dongtai: "1574",
  yuzhouqu: "2758",
  sxshifanqu: "963",
  fangchenggang: "585",
  dejiang: "3106",
  panlong: "3146",
  fzminhouxian: "1818",
  dongkou: "2483",
  jiangxia: "2340",
  zhuoni: "3541",
  loufan: "962",
  fangxian: "2355",
  pingdingshan: "516",
  leishan: "3130",
  liangzihu: "2379",
  dangxiong: "3278",
  huanjiang: "2789",
  heshuixian: "3511",
  kanglexian: "3533",
  xianggelila: "3271",
  dawu: "2389",
  funan: "1777",
  fengchengshi: "1232",
  tianmen: "2433",
  dabancheng: "3619",
  dazi: "3276",
  yonghe: "1058",
  hangkonggang: "2158",
  zhanjiang: "566",
  dazu: "2843",
  dongqu: "2897",
  chengzhong: "3548",
  dayi: "2884",
  daye: "2348",
  wannianxian: "1993",
  fanchang: "1709",
  ccjingkai: "1292",
  lichuan: "1976",
  lubei: "789",
  hailun: "1472",
  dongpo: "2962",
  shanyang: "2234",
  liucheng: "2712",
  baiyunebokuang: "1090",
  guanghan: "2912",
  changping: "742",
  dushanzi: "3622",
  tekesi: "3695",
  akesu: "698",
  longzihu: "1714",
  zouping: "2137",
  rongjiang: "3128",
  hunjiangqu: "1327",
  chongren: "1978",
  hekou: "2035",
  dzyunhequ: "2120",
  lushi: "2266",
  yangming: "1447",
  chongzhou: "2889",
  botiu: "926",
  hekouyaozu: "3238",
  xingqing: "3592",
  zhidan: "3410",
  mazhang: "2626",
  lincheng: "839",
  jintang: "2883",
  lvchun: "3237",
  lepingshi: "1908",
  hyshigu: "2464",
  qushui: "3280",
  wangjiang: "1750",
  wendeng: "2086",
  nanhua: "3220",
  shaodong: "2488",
  bijie: "626",
  yangyuan: "891",
  luchuanxian: "2761",
  qujiangqu: "1666",
  hbqianjiang: "2432",
  heping: "2666",
  kaiyang: "3063",
  huize: "3166",
  heze: "512",
  yongde: "3211",
  huichuan: "3072",
  dongcheng: "732",
  longgang: "2596",
  ningguoshi: "1810",
  luding: "3023",
  nanyue: "2466",
  dongli: "754",
  qhdgaoxin: "813",
  qingshan: "1088",
  qinzhou: "586",
  zhoukou: "528",
  shuangcheng: "1361",
  hailin: "1453",
  huoqiu: "1792",
  guandu: "3147",
  pukou: "1500",
  huarong: "2493",
  wuyixian: "1658",
  tengchong: "3181",
  muli: "3042",
  lintong: "3359",
  cuona: "3332",
  hdweixian: "830",
  jiangxian: "1024",
  qichun: "2409",
  daoli: "1353",
  putian: "479",
  shshixia: "436",
  benxi: "404",
  yongjia: "1629",
  yijiang: "1707",
  linzhou: "2209",
  yuanqu: "1025",
  rikaze: "648",
  baofeng: "2192",
  lyggaoxinjishuchanye: "1556",
  jiaozhou: "2017",
  daerbotemengguzu: "1420",
  hanbin: "3442",
  shuangfeng: "2559",
  liupanshui: "623",
  shanyangxian: "3456",
  nanqiao: "1763",
  xianyang: "657",
  jmsjiaoqu: "1435",
  jinhua: "456",
  ruoergai: "3020",
  balinyou: "1104",
  hgyingshan: "2407",
  tongren: "627",
  changanqu: "3360",
  shandan: "3492",
  qingshen: "2967",
  zizhong: "2939",
  anding: "3515",
  zhuozi: "1151",
  jianchang: "1282",
  rudong: "1544",
  huadu: "2578",
  songtao: "3108",
  yongdengqu: "3464",
  jiguan: "1387",
  huade: "1152",
  qibin: "2212",
  hebukesaier: "3703",
  guigang: "587",
  gaozhou: "2634",
  quyang: "869",
  haibowan: "1096",
  yunxi: "2490",
  qiongzhong: "2832",
  ganjingzi: "1200",
  huidong: "2648",
  youyu: "1004",
  hhhtxincheng: "1076",
  bagongshan: "1726",
  qianxishi: "3098",
  acheng: "1360",
  cangxi: "2930",
  wujin: "1528",
  youyi: "1409",
  lfjingkai: "938",
  wulatehou: "1148",
  yudong: "2293",
  jiujiang: "489",
  jinyun: "1686",
  xingtang: "774",
  kaijiang: "2987",
  xichou: "3241",
  wuzhong: "688",
  xinbaerhuzuo: "1136",
  cangwu: "2737",
  huaan: "1869",
  minquan: "2287",
  zanhuang: "778",
  huantai: "2025",
  zhaoping: "2779",
  cuomei: "3328",
  chaoyangqu: "1286",
  zhongshanqu: "1197",
  xiaoxian: "1784",
  gaotai: "3491",
  jinjiang: "2871",
  suixi: "1739",
  xupu: "2549",
  huayin: "3405",
  pubei: "2752",
  donglan: "2787",
  caofeidian: "794",
  tongrenshi: "3564",
  langxian: "3321",
  zhaotongzhaoyang: "3182",
  benxixian: "1225",
  fushan: "2042",
  kaihuaxian: "1668",
  rucheng: "2528",
  esjianshi: "2425",
  dashiqiao: "1245",
  zixing: "2531",
  xingye: "2763",
  xingyi: "3109",
  liangzhouqu: "3483",
  qingxu: "960",
  hanshou: "2502",
  dongguan: "575",
  zhongyuan: "2149",
  xinghualing: "956",
  ganquan: "3412",
  hbszx: "544",
  jxfuzhou: "495",
  taian: "505",
  aihui: "1457",
  whxinzhou: "2342",
  qingyun: "2113",
  maigaiti: "3673",
  hubeiqita: "121",
  gongbujiangda: "3316",
  ledong: "2829",
  fzgulouqu: "1812",
  panshanxian: "1263",
  hailaer: "1129",
  tskaiping: "791",
  cuiping: "2968",
  muye: "2219",
  jingyu: "1330",
  tianzhuxian: "3486",
  jiangzhouqu: "2799",
  binzhou: "511",
  ludian: "3183",
  helan: "3596",
  bazhou: "939",
  hzgaoxinqu: "2148",
  yunan: "2691",
  pingshun: "986",
  yanchang: "3408",
  chengkou: "2859",
  xihua: "2310",
  lasa: "647",
  funing: "809",
  weixin: "3191",
  suileng: "1469",
  hanjiang: "2331",
  minhang: "1487",
  shantou: "563",
  jingyang: "2909",
  zhengxiangbai: "1176",
  dongtou: "1628",
  whhongshan: "2336",
  kangbashi: "1121",
  dzlinyi: "2114",
  caoxian: "2140",
  yuelu: "2437",
  qiuxian: "826",
  lixia: "1996",
  fenggang: "3079",
  mudan: "2138",
  longquanyi: "2876",
  whjiangan: "2330",
  huangshan: "469",
  suzhouqu: "3500",
  yanjin: "2223",
  mangkang: "3312",
  huixian: "2229",
  xuyi: "1562",
  jingxi: "2775",
  zhaotongyanjin: "3185",
  zzdongshan: "1866",
  shanshan: "3627",
  zhenanxian: "3457",
  saertu: "1412",
  fzchanglequ: "1817",
  taihe: "1776",
  yintai: "3366",
  muleihasake: "3638",
  yuetang: "2455",
  pujiangxian: "1659",
  huaxi: "3059",
  lylanshan: "2097",
  taigu: "1008",
  lanxian: "1070",
  jingmen: "537",
  lixin: "1799",
  yqchengqu: "976",
  yuanzhou: "3606",
  ningxiang: "2443",
  alidi: "653",
  lingqiu: "972",
  nanzhang: "2373",
  chaoyangxian: "1273",
  haerbin: "423",
  npzhenghe: "1876",
  panzhou: "3070",
  yunlongqu: "2452",
  taihu: "1748",
  xiangdu: "835",
  jingshan: "2386",
  yzjingkai: "1579",
  yushanxian: "1987",
  angren: "3292",
  zhongba: "3298",
  xianning: "541",
  chunhua: "3391",
  wanning: "2821",
  lazi: "3291",
  maoxian: "3012",
  xiushan: "2868",
  bailang: "3294",
  yangxian: "3422",
  pthanjiang: "1832",
  gongshandulongzunuzu: "3269",
  hechi: "591",
  xiaojin: "3016",
  tongxiang: "1644",
  xiangfu: "2168",
  etuokeqian: "1124",
  huhehaote: "388",
  hanyang: "2333",
  suide: "3435",
  jiangyuan: "1328",
  dongxingshi: "2748",
  baoxing: "2999",
  gande: "3575",
  hailing: "1589",
  yunhe: "912",
  lingyun: "2770",
  jiulongpo: "2839",
  xiangan: "1830",
  lianshan: "2676",
  hhyuanyang: "3234",
  xietongmen: "3293",
  yuexi: "1751",
  gongjing: "2892",
  huanxian: "3509",
  njshizhong: "2936",
  xiuying: "2806",
  daxin: "2803",
  shifeng: "2446",
  yongzhou: "555",
  xichang: "3040",
  kelamayi: "692",
  tengxian: "2738",
  pxanyuan: "1909",
  yanling: "2252",
  xiajin: "2117",
  binzhoushi: "3394",
  zhuolu: "894",
  beichuan: "2921",
  taoshan: "1443",
  raoping: "2684",
  hongya: "2965",
  rugao: "1547",
  xisaishang: "2344",
  luochuan: "3414",
  luzhouqu: "981",
  yuhonguq: "1192",
  zhongxian: "2862",
  ganxian: "1934",
  luozha: "3329",
  ylrongxian: "2760",
  gxyulin: "588",
  xiuning: "1759",
  hlj: "23",
  cangnan: "1631",
  lingling: "2532",
  hongze: "1560",
  shilong: "2190",
  huoermusi: "3688",
  fakuxian: "1195",
  fanxian: "2245",
  hengshui: "376",
  kuiwen: "2057",
  shaxian: "1837",
  jianxian: "1952",
  yunfu: "579",
  taierzhuang: "2031",
  yangdong: "2669",
  haikou: "594",
  jiexiu: "1017",
  danjiangkou: "2356",
  shunchengqu: "1217",
  jinshan: "1491",
  huangshigang: "2343",
  wuqing: "758",
  xunyangqu: "1915",
  sanmenxia: "524",
  tahe: "523",
  xiahuayuan: "883",
  shijiazhuang: "366",
  lianshui: "1561",
  julu: "844",
  hengdong: "2470",
  chengzihe: "1391",
  yunchengqu: "2690",
  jiangyin: "1512",
  sanmen: "1678",
  tongliang: "2853",
  fengze: "1848",
  ningyuan: "2538",
  dingqing: "3308",
  huangnan: "681",
  yizhang: "2524",
  qianxian: "3386",
  angangxi: "1374",
  whqingshan: "2335",
  qinghe: "849",
  luozhuang: "2098",
  xzguhzou: "1514",
  songbei: "1357",
  liaocheng: "510",
  xixiangtang: "2698",
  xzsuining: "1521",
  yqjiaoqu: "978",
  gangnan: "2754",
  eerguna: "1141",
  jzjingzhou: "2395",
  zhada: "3346",
  maojian: "2349",
  sqkaifa: "2294",
  gxqita: "357",
  yanzhou: "2068",
  luzhou: "604",
  sanya: "595",
  hengshanxian: "2469",
  lankao: "2172",
  ceheng: "3115",
  xzjingkai: "1522",
  liaoyuan: "417",
  dantu: "1584",
  jinning: "3151",
  badong: "2426",
  sjzxinhua: "766",
  xinning: "2485",
  jiacha: "3330",
  xunhuaxian: "3559",
  yuhang: "1607",
  zhaoyuan: "2051",
  tianqiao: "1999",
  huadianshi: "1308",
  shawan: "3699",
  zhouqu: "3542",
  hongta: "3168",
  jining: "504",
  suixian: "2288",
  wujiagang: "2358",
  ouhai: "1627",
  chizhou: "475",
  doumen: "2602",
  xiguqu: "3461",
  huqiu: "1531",
  mulan: "1366",
  linxiaxian: "3532",
  jngaoxinqu: "2076",
  jingning: "3498",
  xinganxian: "2726",
  weifang: "503",
  rzjingkai: "2096",
  luoyang: "515",
  whwuchang: "2334",
  gaoming: "2615",
  pengyang: "3610",
  derong: "3039",
  hongqi: "2216",
  bengshan: "1715",
  fzlianjiangxian: "1819",
  dzningjin: "2112",
  sangzhuzi: "3286",
  rencheng: "2067",
  fushanxian: "1053",
  xinglongtai: "1261",
  shengsixian: "1674",
  shanglinxian: "2704",
  huaian: "444",
  xinbinxian: "1219",
  shidian: "3178",
  nanchong: "611",
  leiwuqi: "3307",
  hun: "24",
  npsongxi: "1875",
  bobai: "2762",
  hgxishui: "2408",
  sxiqita: "113",
  yjhanizuyizudaizu: "3175",
  yinchuan: "686",
  yljiaxian: "3437",
  yuwangtai: "2167",
  tainzhen: "970",
  keerqinyouyizhong: "1164",
  guilin: "582",
  wenquan: "3642",
  wfjingkaiqu: "2060",
  heshun: "1011",
  jianghai: "2617",
  hejin: "1030",
  guoyang: "1797",
  daixian: "1034",
  qihe: "2115",
  tuanfeng: "2404",
  jiangyan: "1591",
  nanling: "1710",
  mengzhou: "2241",
  yibin: "613",
  scqita: "110",
  lshuidong: "3045",
  baiyu: "3032",
  suiping: "2326",
  wanghuaqu: "1216",
  nielamu: "3301",
  wugangshi: "2487",
  puding: "3087",
  butuo: "3048",
  liuhexian: "1324",
  yuehu: "1929",
  siziwang: "1159",
  baiyinqu: "3471",
  fjqita: "122",
  luoshan: "2298",
  luocheng: "2788",
  qingpu: "1493",
  gzganzi: "3029",
  diebu: "3543",
  shucheng: "1793",
  kuerleshi: "3643",
  guangxin: "1986",
  zuoyun: "974",
  fenghua: "1620",
  xishan: "3148",
  shayibake: "3615",
  sucheng: "1596",
  sinan: "3104",
  bywuyuan: "1144",
  huayuan: "2566",
  weihai: "506",
  pingcheng: "966",
  xincheng: "3352",
  wuqiao: "920",
  shouning: "1891",
  manzhouli: "1138",
  malong: "3161",
  wuzhi: "2237",
  lianshanqu: "1278",
  juye: "2143",
  yijun: "3368",
  yiyang: "553",
  guan: "932",
  qzluojiang: "1849",
  chengzhongqu: "2707",
  huainan: "464",
  szwuzhong: "1532",
  laocheng: "2173",
  xzfengxian: "1519",
  pingyang: "1630",
  panzhihua: "603",
  xidongting: "2507",
  zhongshanxian: "2780",
  keshan: "1383",
  ningming: "2801",
  honghu: "2400",
  sifangtai: "1406",
  shinan: "2009",
  yangquan: "379",
  kundulun: "1087",
  longnan: "675",
  qichequ: "1295",
  jishan: "1022",
  taixing: "1595",
  dancheng: "2313",
  huaiji: "2641",
  foping: "3429",
  zhangwan: "2350",
  xining: "678",
  jinkouhe: "2945",
  maoming: "567",
  tongbai: "2281",
  lamaxian: "1275",
  mizhi: "3436",
  longganhu: "2411",
  tunchang: "2824",
  tangshan: "367",
  guantao: "829",
  fengqiu: "2224",
  pengshui: "2870",
  junan: "2106",
  heyang: "3399",
  liquan: "3387",
  qiaocheng: "1796",
  zhaoxian: "782",
  fupingxian: "3403",
  yongjixian: "1303",
  hengzhoushi: "2706",
  luanzhou: "805",
  tongchuan: "655",
  pizhou: "1524",
  daoshan: "3100",
  yuhuaqu: "2439",
  yongding: "2509",
  daowai: "1355",
  zhongmou: "2155",
  liping: "3127",
  luzhai: "2713",
  linchuan: "1973",
  shaya: "3656",
  kauncheng: "1285",
  honghe: "640",
  liandu: "1684",
  taiyuan: "377",
  langkazi: "3333",
  chaotian: "2926",
  weishan: "2069",
  qusong: "3327",
  changshun: "3141",
  chibi: "2419",
  longshanqu: "1316",
  daofu: "3027",
  qilian: "3561",
  jzyixian: "1237",
  songjiang: "1492",
  linqu: "2058",
  jiaoling: "2656",
  sanhe: "940",
  pengshan: "2963",
  debao: "2768",
  xinqiu: "1247",
  pinglexian: "2731",
  guangling: "971",
  liwan: "2571",
  guanxian: "2128",
  ganzhouqu: "3487",
  wucheng: "2118",
  binyangxian: "2705",
  jmsfuyuan: "1441",
  wuhou: "2874",
  szgongyeyuan: "1536",
  zigong: "602",
  dqgaozinjishuchanye: "1421",
  longzhou: "2802",
  haitang: "2810",
  xiqing: "755",
  xinyang: "527",
  huili: "3041",
  shimen: "2506",
  yongren: "3223",
  jinyang: "3049",
  fengdu: "2860",
  qingxiuqu: "2696",
  pingfang: "1356",
  toutunhe: "3618",
  luoning: "2185",
  qita: "107",
  danling: "2966",
  hejiang: "2906",
  lianhu: "3354",
  bjtongzhou: "740",
  emeishan: "2952",
  yuanbaoshan: "1100",
  suifenhe: "1452",
  suijiang: "3188",
  fuyangqu: "1608",
  guyang: "1093",
  huoshan: "1795",
  huiji: "2154",
  qingcheng: "2672",
  meishan: "612",
  yishui: "2102",
  fusong: "1329",
  yuxian: "890",
  huojia: "2221",
  huaping: "3196",
  nongan: "1291",
  ninglangyizu: "3197",
  mengcheng: "1798",
  duilongdeqing: "3275",
  changzhi: "380",
  hongan: "2405",
  lbheshan: "2798",
  zzyouxian: "2449",
  wulumuqi: "691",
  sandu: "3144",
  taihexian: "1957",
  yongchangqu: "3470",
  xinhua: "2188",
  fumin: "3152",
  xizangwenhualvyou: "3284",
  qianjin: "1433",
  xinhui: "2618",
  menghai: "3248",
  jiangshanxian: "1670",
  jinghong: "3247",
  yian: "1741",
  jiading: "1489",
  lindian: "1419",
  fenxi: "1060",
  linwu: "2527",
  licheng: "2000",
  weihui: "2228",
  fenyi: "1928",
  duyun: "3133",
  kangxian: "3526",
  yinzhouqu: "1264",
  bjmeixian: "3375",
  fengzhen: "1160",
  huzhu: "3557",
  yuanan: "2362",
  haishu: "1615",
  zjkjingkai: "896",
  kuangqu: "977",
  lygaoxinqu: "2187",
  zhenping: "2275",
  qionghai: "2819",
  pingshan: "2599",
  xinshao: "2480",
  linyi: "508",
  huancui: "2085",
  sanjiang: "2716",
  qinhuai: "1497",
  xushui: "858",
  yuquan: "1078",
  cdxindu: "2878",
  shahekou: "1199",
  huicheng: "2645",
  ningxian: "3513",
  linze: "3490",
  linxi: "850",
  jxqita: "117",
  ruzhou: "2199",
  linygaoxinqu: "2109",
  kaiyuanshi: "1270",
  yumin: "3702",
  jiangyou: "2923",
  huaxian: "2206",
  taipingqu: "1248",
  tacheng: "703",
  qingan: "1467",
  mdjjingkai: "1451",
  mldaizulahuzuwazu: "3205",
  wumingqu: "2701",
  zhanhua: "2132",
  miyun: "746",
  jinchuanqu: "3469",
  lixian: "870",
  zhucheng: "2062",
  yuping: "3102",
  suihua: "434",
  zaduo: "3580",
  fuqing: "1824",
  wuhua: "2654",
  tinghu: "1565",
  tuoketuo: "1081",
  jiayu: "2415",
  guancheng: "2151",
  yidu: "2367",
  wenxian: "2238",
  dongbao: "2382",
  qilihe: "3460",
  zmdjingkaiqu: "2328",
  tielingxian: "1266",
  linzi: "2023",
  shaoxing: "455",
  leiyang: "2475",
  longwen: "1860",
  zjkguyuan: "888",
  changlingxian: "1335",
  jinxiang: "2071",
  jianghua: "2541",
  zhangping: "1886",
  menyuan: "3560",
  chengbei: "3550",
  ningnan: "3046",
  zhaodong: "1471",
  jmsxiangyang: "1432",
  jiaozuo: "520",
  shenxian: "2126",
  weining: "3096",
  huian: "1851",
  gusu: "1534",
  zhifu: "2041",
  yumen: "3505",
  mianning: "3052",
  guta: "1233",
  guichi: "1800",
  naidong: "3322",
  gongxian: "2974",
  jinshui: "2152",
  luancheng: "771",
  fogang: "2674",
  dexing: "1995",
  liaoyangxian: "1258",
  wolong: "2271",
  zhanqianqu: "1240",
  shuicheng: "3069",
  smxjingkai: "2267",
  chaoan: "2683",
  lucheng: "984",
  jnshizhong: "1997",
  xishuangbanna: "642",
  xianyou: "1835",
  yuepuhu: "3674",
  guzhen: "1720",
  wangqing: "1351",
  wushen: "1127",
  yunyang: "2863",
  anhua: "2517",
  chenggong: "3150",
  longzi: "3331",
  longhua: "2598",
  yanchuan: "3409",
  zhenxing: "1228",
  xinyuan: "3693",
  laingshan: "2075",
  gaoling: "3361",
  weishanyizu: "3256",
  cangzhou: "374",
  xiushuixian: "1918",
  huaibin: "2304",
  qingchuan: "2928",
  guye: "790",
  yongsheng: "3195",
  jinanxin: "833",
  sheqi: "2278",
  haifeng: "2659",
  cili: "2511",
  duan: "2791",
  ninghequ: "761",
  kuitun: "3687",
  zhenyuan: "3121",
  wucui: "1423",
  shanxian: "2141",
  gangbequ: "2753",
  aba: "619",
  sihui: "2644",
  yanlingxian: "2451",
  bayinguole: "697",
  haixi: "685",
  ningling: "2289",
  danyang: "1586",
  lsyuexi: "3053",
  zhuji: "1654",
  jinxiuxian: "2797",
  pzhyanbian: "2901",
  longnanxian: "1949",
  zhangwuxian: "1252",
  xiangdong: "1910",
  zhengxiang: "2465",
  yanchi: "3603",
  quxian: "2989",
  longgangshi: "1637",
  raoyang: "946",
  wangchenghsnizuyizu: "3204",
  mozhugongka: "3281",
  meihekou: "1325",
  fugou: "2309",
  nansha: "2579",
  guixi: "1931",
  nimu: "3279",
  xinjin: "2882",
  wushan: "2865",
  yima: "2268",
  hualongxian: "3558",
  hulan: "1359",
  qqheyian: "1379",
  potou: "2625",
  hedongqu: "749",
  wenchuan: "3010",
  anningqu: "3462",
  yingzhou: "1772",
  lingcheng: "2111",
  changzhou: "440",
  wujiang: "2582",
  guiyangxian: "2523",
  daoxian: "2536",
  nima: "3343",
  yunhexian: "1689",
  dazhu: "2988",
  jingyuan: "3609",
  huaibei: "466",
  zhangjiagang: "1538",
  quanzhouxian: "2725",
  tieshangang: "2743",
  xinxing: "2692",
  xishui: "3082",
  qingtian: "1685",
  zhenpingxian: "3449",
  shangdang: "982",
  jingxing: "772",
  jinangaoxinqu: "2008",
  nanyang: "525",
  gonghexian: "3568",
  taizhou: "459",
  yicheng: "2318",
  czliyang: "1530",
  lingshou: "775",
  pengjiang: "2616",
  yanji: "1345",
  longxi: "3517",
  anyue: "3007",
  xunke: "1458",
  wanquan: "884",
  ruili: "3262",
  xcxiangcheng: "2253",
  huaying: "2983",
  ninghai: "1622",
  xinfuqu: "1214",
  xinbei: "1527",
  yaan: "616",
  huaning: "3171",
  linping: "1610",
  pingyu: "2321",
  lveyang: "3426",
  boai: "2236",
  pingyi: "2105",
  zhushan: "2353",
  lianghe: "3264",
  chifeng: "391",
  linkou: "1450",
  dongyang: "1663",
  zhaosu: "3694",
  donggangshi: "1231",
  tianning: "1525",
  tulufan: "693",
  qinanxian: "3479",
  lufeng: "2661",
  rongchang: "2855",
  xunyi: "3390",
  wenan: "936",
  zhengyang: "2322",
  lintan: "3540",
  xinshi: "3616",
  lintao: "3519",
  lingshan: "2751",
  zhongshan: "576",
  pengxi: "2933",
  chengjiang: "3176",
  milin: "3317",
  zigui: "2364",
  changtai: "1862",
  nancheng: "1975",
  wangdu: "866",
  zhongning: "3612",
  zunhua: "803",
  jingxian: "1806",
  xiangyuan: "985",
  fugong: "3268",
  dongzhi: "1801",
  nileke: "3696",
  zhouzhi: "3364",
  xiajiangxian: "1954",
  lulong: "812",
  shuimogou: "3617",
  yanshanqu: "2721",
  qinghexian: "3709",
  huangmei: "2410",
  quanshan: "1517",
  changshaxian: "2441",
  chaohu: "1704",
  xuanen: "2427",
  shibei: "2010",
  jinan: "497",
  gaoyi: "776",
  xunwu: "1946",
  zichuan: "2020",
  huishui: "3143",
  songzi: "2401",
  longchang: "2941",
  xuwen: "2628",
  decheng: "2110",
  gdqita: "109",
  chongxin: "3496",
  jiangan: "2971",
  lslinzhou: "3277",
  shishi: "1856",
  deqingxian: "1647",
  lsshizhong: "2942",
  yifeng: "1967",
  fuxin: "408",
  qinbei: "2750",
  duji: "1736",
  jmsdongfeng: "1434",
  ycyuanzhou: "1963",
  tieling: "411",
  maduo: "3578",
  yunchengshi: "384",
  longhui: "2482",
  mabian: "2951",
  baiyunqu: "3061",
  wulagai: "1179",
  lyyiyang: "2184",
  yiwu: "3631",
  jianpingxian: "1274",
  chaoyang: "2607",
  tianshan: "3614",
  dalixian: "3398",
  elunchun: "1133",
  huangzhong: "3551",
  xiangshanxian: "1621",
  qdgaoxinqu: "2016",
  jialing: "2955",
  linquan: "1775",
  jiangda: "3305",
  yilong: "2959",
  cixi: "1624",
  puning: "2689",
  jiangyang: "2902",
  huangyuan: "3553",
  zhaotongdaguan: "3186",
  hefeng: "2430",
  yueyangxian: "2492",
  macun: "2233",
  ganzhou: "492",
  tengzhou: "2033",
  gangcheng: "2005",
  chabeiguanli: "897",
  hejingxian: "3649",
  qiyang: "2544",
  shuangpai: "2535",
  longhu: "2604",
  fuquan: "3134",
  yanliang: "3358",
  baixiang: "841",
  huangping: "3118",
  yimei: "1422",
  yimen: "3172",
  zhongwei: "690",
  shanggao: "1966",
  sangzhi: "2512",
  zhenhai: "1618",
  pingyuan: "2655",
  zhoushan: "458",
  renhua: "2586",
  bohu: "3651",
  jimei: "1828",
  feixi: "1699",
  muleng: "1455",
  wenshanyanshan: "3240",
  anyuan: "1939",
  hbjingkaiqu: "2215",
  zhanyi: "3160",
  wuningxian: "1917",
  fengkai: "2642",
  zhangpu: "1864",
  haicheng: "2741",
  helingeer: "1082",
  fzluoyuanxian: "1820",
  feicheng: "2084",
  hanting: "2055",
  tongxin: "3604",
  shapingba: "2838",
  daxing: "743",
  tongchengxian: "2416",
  shicheng: "1947",
  chenzhou: "554",
  longli: "3142",
  baokang: "2375",
  nanshan: "2594",
  nanpi: "919",
  fuping: "860",
  jinniu: "2873",
  keqiaoqu: "1651",
  xiangtan: "547",
  kaiping: "2620",
  fangzi: "2056",
  daming: "823",
  tongzhou: "1541",
  wangmo: "3114",
  xinfeng: "2589",
  boshan: "2022",
  pudongxin: "1490",
  bomi: "3319",
  yanfeng: "2463",
  pingyin: "2006",
  guangan: "614",
  zhuhui: "2462",
  yadong: "3299",
  weicheng: "2054",
  chayouhou: "1158",
  huiyang: "2646",
  fangzheng: "1363",
  qinglongxian: "3112",
  qiananxian: "1336",
  bohaixin: "925",
  sysbaoshan: "1407",
  jinchang: "666",
  xinghaixian: "3571",
  suxian: "2522",
  beian: "1460",
  bole: "1445",
  renqiu: "927",
  shashi: "2394",
  geji: "3349",
  zhouning: "1892",
  eerduosi: "393",
  lanshan: "2093",
  furong: "2435",
  fuhai: "3707",
  xunxian: "2213",
  xinlong: "3030",
  napo: "2769",
  linshu: "2108",
  jingbian: "3433",
  njgulou: "1499",
  wanxiu: "2734",
  dulan: "3589",
  zhenfeng: "3113",
  congtai: "816",
  runzhou: "1583",
  zhenjiang: "447",
  andong: "2534",
  pengze: "1923",
  nanning: "580",
  habahe: "3708",
  hejian: "929",
  qilin: "3159",
  huishan: "1508",
  keche: "3654",
  beipiao: "1276",
  szwujiang: "1535",
  kechengqu: "1665",
  shayang: "2384",
  zhaoan: "1865",
  naqu: "652",
  yanhu: "1018",
  wuyang: "2260",
  yuyao: "1623",
  huludao: "413",
  yanhe: "3107",
  liangxi: "1510",
  wulan: "3588",
  jingyuanxian: "3473",
  njlishui: "1505",
  gsqita: "126",
  zhangye: "670",
  luan: "473",
  yata: "3357",
  ruian: "1635",
  shenze: "777",
  wanrong: "1020",
  qinghequ: "1265",
  yitongxian: "1314",
  bjfengtai: "735",
  tianyang: "2766",
  raohe: "1411",
  xiaoshan: "1606",
  xuanhan: "2986",
  nanxi: "2969",
  jiuhuaqu: "2459",
  puyang: "521",
  zhangbei: "886",
  liangqing: "2699",
  hainan: "682",
  zjqita: "123",
  louxing: "2558",
  chengguanqu: "3459",
  hulin: "1394",
  yongping: "3257",
  ninghua: "1840",
  qianshan: "1754",
  anqiu: "2064",
  hdjingkai: "832",
  yandu: "1566",
  taocheng: "941",
  zhaojue: "3050",
  jinwan: "2603",
  esenshi: "2423",
  liuyang: "2442",
  pingguiqu: "2778",
  weibinqu: "3369",
  fengtai: "1728",
  zhongyang: "1072",
  wuan: "834",
  huaianhuaiyin: "1558",
  lushanshi: "1926",
  muping: "2043",
  hunyuan: "973",
  xtjingkai: "851",
  yanan: "659",
  suniteyou: "1171",
  zhangshu: "1971",
  jiayin: "1425",
  diecai: "2718",
  sysjixian: "1408",
  dengkou: "1145",
  bjshixiaqu: "364",
  wudi: "2135",
  naxi: "2903",
  jingdezhen: "487",
  handan: "369",
  wuduqu: "3522",
  jingxingkuang: "767",
  quyuanqu: "2496",
  lianping: "2665",
  kelan: "1040",
  wuda: "1098",
  tianxin: "2436",
  jiancaoping: "957",
  xiyang: "1012",
  liaozhongqu: "1193",
  xiuwen: "3065",
  changjaingqu: "1905",
  guangchangxian: "1983",
  yingtan: "491",
  tongling: "467",
  feidong: "1698",
  pingan: "3555",
  sixian: "1786",
  jiawang: "1516",
  jiamusi: "430",
  moyu: "3680",
  zhanhe: "2191",
  renhuai: "3084",
  suzhou: "441",
  shuangluan: "900",
  kelaqin: "1108",
  yingkou: "407",
  longling: "3179",
  qianyang: "3377",
  yangzhong: "1587",
  gaoan: "1972",
  gaogang: "1590",
  shizuishan: "687",
  xintai: "2083",
  ytgaoxinqu: "2046",
  pingba: "3086",
  changshu: "1537",
  leshan: "610",
  qingliu: "1839",
  qintang: "2755",
  shibing: "3119",
  chuanhui: "2307",
  zhuhai: "562",
  boye: "872",
  jimunai: "3710",
  shihsou: "2399",
  shitai: "1802",
  hongjiangshi: "2557",
  yanping: "1870",
  qtqita: "339",
  gaochang: "3626",
  pdsgaoxinqu: "2196",
  fushun: "403",
  tianshui: "668",
  yongfeng: "1956",
  linghai: "1238",
  changsha: "545",
  ahqita: "111",
  hongdong: "1050",
  guyuan: "689",
  wuhu: "462",
  luojiang: "2910",
  hhluxi: "3233",
  pingxiangshi: "2805",
  dongfang: "2822",
  guinan: "3572",
  zhuanglang: "3497",
  chiping: "2124",
  jingjiang: "1594",
  wuhe: "1719",
  xiayi: "2292",
  jingle: "1037",
  hexiqu: "750",
  qiaodong: "880",
  guannan: "1554",
  luhe: "2660",
  anguo: "878",
  shenchi: "1038",
  shizong: "3163",
  fzmaweiqu: "1815",
  yanshi: "2177",
  anzhou: "2917",
  shhuangpu: "1480",
  guzhang: "2568",
  xinrong: "965",
  nanbu: "2956",
  ycxingan: "2363",
  jinghu: "1705",
  shancheng: "2211",
  pingdu: "2018",
  dafeng: "1567",
  nancha: "1429",
  guazhou: "3502",
  jiangzi: "3288",
  gejiu: "3226",
  xianghe: "934",
  fuyang: "471",
  beiliu: "2764",
  npwuyishan: "1878",
  wuji: "779",
  pingguo: "2776",
  shangqiu: "526",
  luquan: "770",
  beilin: "3353",
  nanao: "2610",
  nanan: "2840",
  hljqita: "120",
  leyexian: "2771",
  shule: "3668",
  bianba: "3314",
  czgaoxin: "924",
  yangcheng: "996",
  wangyi: "3365",
  sxkaifa: "975",
  yaozhou: "3367",
  midong: "3620",
  xtpingxiang: "847",
  ganzi: "620",
  hezheng: "3536",
  yujiang: "1930",
  mojianghenizu: "3200",
  yanggao: "969",
  chaonan: "2608",
  kangding: "3022",
  pinghu: "1643",
  liuzhite: "3068",
  jinghe: "3641",
  qitai: "3636",
  lishui: "460",
  pinghe: "1868",
  pinggu: "745",
  tonghua: "418",
  quannan: "1941",
  wenshan: "641",
  boluo: "2647",
  chaya: "3309",
  dachaidan: "3591",
  lygjk: "1555",
  xzyunlong: "1515",
  luxian: "2905",
  shunyi: "741",
  chanhe: "2175",
  ningjiangqu: "1333",
  donghai: "1552",
  chayu: "3320",
  xixiang: "3423",
  guanshanhu: "3062",
  hklonghua: "2807",
  xiping: "2319",
  shexian: "1758",
  ruyuanyaozu: "2588",
  ganyu: "1551",
  hubin: "2263",
  anjixian: "1649",
  haizhou: "1246",
  tumen: "1346",
  dege: "3031",
  xianan: "2414",
  longan: "2203",
  yiningxian: "3689",
  cdlixian: "2503",
  changfeng: "1697",
  tangwang: "1426",
  hedong: "2099",
  nankaiqu: "751",
  wutai: "1033",
  xinbaerhuyou: "1137",
  dzyucheng: "2122",
  zhaoqiang: "943",
  wuqi: "3411",
  beilun: "1617",
  haichenguq: "1213",
  songyuan: "420",
  zhangjiajie: "552",
  yuanbaoqu: "1227",
  cengong: "3122",
  suizhou: "542",
  yaonan: "1343",
  shuangyang: "1289",
  pingluxian: "1027",
  xiangxi: "558",
  suichang: "1687",
  luqu: "3545",
  nanhe: "838",
  songming: "3155",
  dayao: "3222",
  longde: "3608",
  yuancheng: "2662",
  dangchangxian: "3525",
  hhhthuimin: "1077",
  langfang: "375",
  laiwu: "2004",
  luotian: "2406",
  xianghuang: "1175",
  keerqinyouyiqian: "1163",
  ganjiang: "1576",
  zhalainuoer: "1130",
  zaoyang: "2377",
  shizhong: "2028",
  bachu: "3676",
  heyuan: "572",
  heshuoxian: "3650",
  qishan: "3373",
  laizhou: "2050",
  jingzhoujingkai: "2398",
  pingyao: "1015",
  yuzhongxian: "3466",
  yangzhou: "446",
  xingning: "2657",
  ptlicheng: "1833",
  siming: "1825",
  yixian: "1760",
  ganguxian: "3480",
  laixi: "2019",
  jiangjin: "2848",
  chenghai: "2609",
  kunshan: "1539",
  sdqita: "116",
  xiangshanqu: "2719",
  hubei: "25",
  yangxin: "2134",
  jingxiu: "854",
  keerqinzuoyizhong: "1112",
  hongyuan: "3021",
  xigong: "2174",
  naiman: "1116",
  bingjiang: "1605",
  jinping: "2605",
  tancheng: "2101",
  qzlicheng: "1847",
  qingyuanqu: "1951",
  huailai: "893",
  xincai: "2327",
  keerqinzuoyihou: "1113",
  tiane: "2785",
  taizihe: "1257",
  yongjing: "3534",
  xuanhua: "882",
  lieshan: "1738",
  anxin: "867",
  tianjiaan: "1724",
  jljingkaiqu: "1304",
  chongming: "1495",
  qianguoxian: "1334",
  luanping: "904",
  zeku: "3566",
  shangyou: "1937",
  nanle: "2244",
  dangshan: "1783",
  binxian: "1364",
  wengniute: "1107",
  wusu: "3698",
  bcdaan: "1344",
  dezhou: "509",
  shixing: "2585",
  gujiao: "964",
  erdao: "1287",
  luxi: "2564",
  yuhuan: "1683",
  xiangzhou: "2601",
  luquanyizumiaozu: "3156",
  daishanxian: "1673",
  gongchangling: "1256",
  gongliu: "3692",
  yuechi: "2980",
  hetianxian: "3679",
  wenshanfuning: "3246",
  jiutai: "1290",
  bdyixian: "868",
  zhenjiangxinqu: "1585",
  zhaoyang: "412",
  alashanyou: "1181",
  pdschengxiang: "2197",
  xuancheng: "476",
  weichengqu: "3383",
  xiahe: "3546",
  cdlonghua: "905",
  haimen: "1543",
  shbeilin: "1463",
  fenghuang: "2565",
  shache: "3671",
  sunitezuo: "1170",
  yilan: "1362",
  zhongfang: "2546",
  pulandian: "1203",
  wuyi: "944",
  shapotou: "3611",
  longhai: "1861",
  tuolixian: "3701",
  hexian: "1735",
  tiandeng: "2804",
  maonan: "2632",
  luyi: "2315",
  fukang: "3633",
  dingcheng: "2500",
  hzjingkaiqu: "2147",
  yayucheng: "2992",
  shuocheng: "1000",
  lishan: "1209",
  wuxi: "438",
  santai: "2918",
  linxian: "1067",
  dangyang: "2368",
  fengrun: "793",
  sgzhenjiang: "2583",
  shunde: "2613",
  nppucheng: "1873",
  guiding: "3136",
  huashan: "1730",
  yanbian: "422",
  zhenxiong: "3189",
  xiangtanxian: "2456",
  longjingshi: "1349",
  hukou: "1922",
  sujiatun: "1189",
  gongan: "2396",
  guxian: "1051",
  longyao: "842",
  qinghemen: "1249",
  longyan: "484",
  jiangchuan: "3169",
  sxi: "16",
  xinxian: "2300",
  jinxian: "1904",
  wenshui: "1064",
  cqxiaqu: "599",
  tongshanxian: "2418",
  tongliao: "392",
  wubao: "3438",
  huangdao: "2011",
  wufengtujiazu: "2366",
  sunanxian: "3488",
  guagnshui: "2422",
  shiping: "3232",
  zezhou: "998",
  jiaokou: "1073",
  pingwu: "2922",
  huinanxian: "1323",
  alashan: "399",
  laian: "1764",
  jiangbeiqu: "2837",
  songxian: "2182",
  liancheng: "1885",
  peixian: "1520",
  guangrao: "2038",
  yongxing: "2525",
  shennongjia: "2434",
  shiquan: "3444",
  gaomi: "2065",
  chengxian: "3523",
  qinhuangdao: "368",
  xuanzhou: "1804",
  yingjing: "2994",
  siping: "416",
  jilin: "415",
  yuhuatai: "1502",
  dahua: "2792",
  ningerhayizu: "3199",
  xiamen: "478",
  yuduxian: "1943",
  xiangchengqu: "2370",
  suqianjk: "1601",
  longting: "2164",
  dongyingqu: "2034",
  jiangbei: "1616",
  suizhongxian: "1281",
  jingzudaizuyizu: "3202",
  sanming: "480",
  shunhe: "2165",
  haidian: "737",
  lianxiqu: "1914",
  qinggang: "1466",
  xinjiang: "1023",
  shufu: "3667",
  chengdong: "3547",
  zitong: "2920",
  jinyuan: "959",
  sanyuan: "3384",
  qdmiaozu: "629",
  pingliang: "671",
  pulan: "3345",
  cdgaoxin: "909",
  motuo: "3318",
  wushanxian: "3481",
  weibin: "2217",
  fengjie: "2864",
  guide: "3570",
  laibin: "592",
  yiyuan: "2027",
  wencheng: "1632",
  zhengning: "3512",
  yongcheng: "2295",
  beidaihe: "808",
  gongga: "3324",
  xuanwu: "1496",
  awati: "3660",
  dongying: "501",
  huanglong: "3416",
  shenyang: "400",
  yanyuan: "3043",
  ningcheng: "1109",
  zhiduo: "3582",
  datong: "378",
  lukou: "2448",
  kangma: "3296",
  pingquan: "910",
  zhanggongqu: "1932",
  jiaoheshi: "1307",
  baoshanchangning: "3180",
  xiapu: "1888",
  haian: "1548",
  lutai: "799",
  pinglu: "1001",
  jingde: "1808",
  tonghai: "3170",
  fancheng: "2371",
  zizhou: "3440",
  chuzhou: "470",
  shbaoshan: "1488",
  alukeerqin: "1102",
  yantai: "502",
  yzjingkaiqu: "2542",
  pingli: "3448",
  feixian: "2104",
  yantan: "2894",
  nanguan: "1284",
  wulianxian: "2094",
  ncgaoping: "2954",
  dinghai: "1671",
  putuo: "1484",
  guangzhou: "559",
  eshanyizu: "3173",
  lianhua: "1911",
  yuzhou: "2255",
  ytjingkaiqu: "2047",
  luntai: "3644",
  dongshan: "1400",
  yunmeng: "2390",
  saga: "3302",
  sansui: "3120",
  shimian: "2996",
  dazhou: "615",
  haigang: "806",
  weishi: "2171",
  yexian: "2193",
  jindong: "1657",
  wancheng: "2270",
  fusuixian: "2800",
  liujiang: "2711",
  jingan: "1483",
  rongan: "2714",
  syjiyang: "2811",
  xingyang: "2160",
  jianhua: "1372",
  laobianqu: "1243",
  qiongjie: "3326",
  chenghua: "2875",
  licang: "2013",
  wushi: "3659",
  xialu: "2345",
  hangjinhou: "1149",
  changyuan: "2230",
  qujiang: "2584",
  dzjingkaiqu: "2119",
  longwanqu: "1626",
  hantai: "3419",
  shunqing: "2953",
  duanzhou: "2637",
  xingcheng: "1283",
  hain: "36",
  zhengan: "3076",
  jiangning: "1503",
  lingchuanxian: "2724",
  anlong: "3116",
  lianyun: "1549",
  lsjingkai: "3283",
  nanchuan: "2851",
  ledu: "3554",
  changxing: "1648",
  jiefang: "2231",
  pengan: "2958",
  wanyuan: "2991"
};
const NEW_CATEGORY_LIST = [
  {
    chars: "putonghua",
    id: "100",
    level: 2,
    name: "\u666E\u901A\u8BDD",
    newsClassifyId: "100_wx",
    pid: "2"
  },
  {
    chars: "tongzhi",
    id: "101",
    level: 2,
    name: "\u9762\u8BD5\u901A\u77E5",
    newsClassifyId: "101_wx",
    pid: "2"
  },
  {
    chars: "rending2",
    id: "102",
    level: 2,
    name: "\u8D44\u683C\u8BA4\u5B9A",
    newsClassifyId: "102_wx",
    pid: "2"
  },
  {
    chars: "jiqiao",
    id: "104",
    level: 2,
    name: "\u9762\u8BD5\u6280\u5DE7",
    newsClassifyId: "89_wx",
    pid: "2"
  },
  {
    chars: "gonggao",
    id: "109",
    level: 2,
    name: "\u8003\u8BD5\u516C\u544A",
    newsClassifyId: "51_wx",
    pid: "9"
  },
  {
    chars: "rukou",
    id: "110",
    level: 2,
    name: "\u62A5\u540D\u5165\u53E3",
    newsClassifyId: "52_wx",
    pid: "9"
  },
  {
    chars: "dongtai",
    id: "111",
    level: 2,
    name: "\u7279\u5C97\u52A8\u6001",
    newsClassifyId: "111_wx",
    pid: "9"
  },
  {
    chars: "zhinan",
    id: "112",
    level: 2,
    name: "\u8003\u8BD5\u6307\u5357",
    newsClassifyId: "84_wx",
    pid: "9"
  },
  {
    chars: "chaxun",
    id: "113",
    level: 2,
    name: "\u6210\u7EE9\u67E5\u8BE2",
    newsClassifyId: "34_wx",
    pid: "9"
  },
  {
    chars: "mingdan",
    id: "114",
    level: 2,
    name: "\u9762\u8BD5\u540D\u5355/\u516C\u544A",
    newsClassifyId: "82_wx",
    pid: "9"
  },
  {
    chars: "jiqiao",
    id: "116",
    level: 2,
    name: "\u9762\u8BD5\u6280\u5DE7",
    newsClassifyId: "89_wx",
    pid: "9"
  },
  {
    chars: "answer",
    id: "122",
    level: 2,
    name: "\u4E13\u5347\u672C\u95EE\u7B54",
    newsClassifyId: "122_wx",
    pid: "1"
  },
  {
    chars: "kaoshidagang",
    id: "123",
    level: 2,
    name: "\u8003\u8BD5\u5927\u7EB2",
    newsClassifyId: "123_wx",
    pid: "1"
  },
  {
    chars: "zhuanyejieshao",
    id: "124",
    level: 2,
    name: "\u4E13\u4E1A\u4ECB\u7ECD",
    newsClassifyId: "124_wx",
    pid: "1"
  },
  {
    chars: "meiri",
    id: "150",
    level: 2,
    name: "\u6559\u8D44\u9898\u5E93",
    newsClassifyId: "150_wx",
    pid: "2"
  },
  {
    chars: "meiri",
    id: "151",
    level: 2,
    name: "\u7279\u5C97\u9898\u5E93",
    newsClassifyId: "151_wx",
    pid: "9"
  },
  {
    chars: "meiri",
    id: "152",
    level: 2,
    name: "\u62DB\u6559\u9898\u5E93",
    newsClassifyId: "152_wx",
    pid: "3"
  },
  {
    chars: "gonggao",
    id: "200",
    level: 2,
    name: "\u8003\u8BD5\u516C\u544A",
    newsClassifyId: "51_wx",
    pid: "19"
  },
  {
    chars: "baoming",
    id: "201",
    level: 2,
    name: "\u62A5\u540D\u5165\u53E3",
    newsClassifyId: "52_wx",
    pid: "19"
  },
  {
    chars: "dongtai",
    id: "202",
    level: 2,
    name: "\u8003\u8BD5\u52A8\u6001",
    newsClassifyId: "907865556081487872_wx",
    pid: "19"
  },
  {
    chars: "mingdan",
    id: "203",
    level: 2,
    name: "\u9762\u8BD5\u540D\u5355/\u516C\u544A",
    newsClassifyId: "82_wx",
    pid: "19"
  },
  {
    chars: "zhinan",
    id: "204",
    level: 2,
    name: "\u8003\u8BD5\u6307\u5357",
    newsClassifyId: "84_wx",
    pid: "19"
  },
  {
    chars: "kstk",
    id: "205",
    level: 2,
    name: "\u8003\u8BD5\u9898\u5E93",
    newsClassifyId: "859697692504035328_wx",
    pid: "19"
  },
  {
    chars: "jiqiao",
    id: "206",
    level: 2,
    name: "\u9762\u8BD5\u6280\u5DE7",
    newsClassifyId: "89_wx",
    pid: "19"
  },
  {
    chars: "chaxun",
    id: "207",
    level: 2,
    name: "\u6210\u7EE9\u67E5\u8BE2",
    newsClassifyId: "34_wx",
    pid: "19"
  },
  {
    chars: "ksdt",
    id: "21",
    level: 2,
    name: "\u8003\u8BD5\u52A8\u6001",
    newsClassifyId: "907865556081487872_wx",
    pid: "1"
  },
  {
    chars: "ksdt",
    id: "23",
    level: 2,
    name: "\u8003\u8BD5\u52A8\u6001",
    newsClassifyId: "907865556081487872_wx",
    pid: "2"
  },
  {
    chars: "ksdt",
    id: "25",
    level: 2,
    name: "\u8003\u8BD5\u52A8\u6001",
    newsClassifyId: "907865556081487872_wx",
    pid: "3"
  },
  {
    chars: "zhengce",
    id: "31",
    level: 2,
    name: "\u8003\u8BD5\u653F\u7B56",
    newsClassifyId: "924525691275071488",
    pid: "1"
  },
  {
    chars: "baoming",
    id: "32",
    level: 2,
    name: "\u8003\u8BD5\u62A5\u540D",
    newsClassifyId: "32_wx",
    pid: "1"
  },
  {
    chars: "kaoshi",
    id: "33",
    level: 2,
    name: "\u8003\u8BD5\u65F6\u95F4",
    newsClassifyId: "33_wx",
    pid: "1"
  },
  {
    chars: "chaxun",
    id: "34",
    level: 2,
    name: "\u6210\u7EE9\u67E5\u8BE2",
    newsClassifyId: "34_wx",
    pid: "1"
  },
  {
    chars: "fenshuxian",
    id: "35",
    level: 2,
    name: "\u5206\u6570\u7EBF",
    newsClassifyId: "924525807197245440",
    pid: "1"
  },
  {
    chars: "kemu",
    id: "36",
    level: 2,
    name: "\u8003\u8BD5\u79D1\u76EE",
    newsClassifyId: "36_wx",
    pid: "1"
  },
  {
    chars: "zhenti",
    id: "37",
    level: 2,
    name: "\u8003\u8BD5\u771F\u9898",
    newsClassifyId: "37_wx",
    pid: "1"
  },
  {
    chars: "shiti",
    id: "38",
    level: 2,
    name: "\u6A21\u62DF\u8BD5\u9898",
    newsClassifyId: "38_wx",
    pid: "1"
  },
  {
    chars: "xuexiao",
    id: "39",
    level: 2,
    name: "\u62DB\u751F\u9662\u6821",
    newsClassifyId: "39_wx",
    pid: "1"
  },
  {
    chars: "zhaosheng",
    id: "40",
    level: 2,
    name: "\u62DB\u751F\u8BA1\u5212",
    newsClassifyId: "924525724871446528",
    pid: "1"
  },
  {
    chars: "gonggao",
    id: "41",
    level: 2,
    name: "\u8003\u8BD5\u516C\u544A",
    newsClassifyId: "51_wx",
    pid: "2"
  },
  {
    chars: "baoming",
    id: "42",
    level: 2,
    name: "\u62A5\u540D\u5165\u53E3",
    newsClassifyId: "52_wx",
    pid: "2"
  },
  {
    chars: "kaoshi",
    id: "43",
    level: 2,
    name: "\u8003\u8BD5\u65F6\u95F4",
    newsClassifyId: "33_wx",
    pid: "2"
  },
  {
    chars: "dagang",
    id: "44",
    level: 2,
    name: "\u8003\u8BD5\u5927\u7EB2",
    newsClassifyId: "123_wx",
    pid: "2"
  },
  {
    chars: "chaxun",
    id: "45",
    level: 2,
    name: "\u6210\u7EE9\u67E5\u8BE2",
    newsClassifyId: "34_wx",
    pid: "2"
  },
  {
    chars: "shiti",
    id: "47",
    level: 2,
    name: "\u6A21\u62DF\u8BD5\u9898",
    newsClassifyId: "38_wx",
    pid: "2"
  },
  {
    chars: "mianshi",
    id: "49",
    level: 2,
    name: "\u9762\u8BD5\u6280\u5DE7",
    newsClassifyId: "89_wx",
    pid: "2"
  },
  {
    chars: "rending",
    id: "50",
    level: 2,
    name: "\u8BA4\u5B9A\u516C\u544A",
    newsClassifyId: "50_wx",
    pid: "2"
  },
  {
    chars: "gonggao",
    id: "51",
    level: 2,
    name: "\u8003\u8BD5\u516C\u544A",
    newsClassifyId: "51_wx",
    pid: "3"
  },
  {
    chars: "baoming",
    id: "52",
    level: 2,
    name: "\u62A5\u540D\u5165\u53E3",
    newsClassifyId: "52_wx",
    pid: "3"
  },
  {
    chars: "zhunkaozheng",
    id: "53",
    level: 2,
    name: "\u51C6\u8003\u8BC1",
    newsClassifyId: "79_wx",
    pid: "3"
  },
  {
    chars: "chaxun",
    id: "54",
    level: 2,
    name: "\u6210\u7EE9\u67E5\u8BE2",
    newsClassifyId: "34_wx",
    pid: "3"
  },
  {
    chars: "zhaopin",
    id: "55",
    level: 2,
    name: "\u8003\u8BD5\u5927\u7EB2",
    newsClassifyId: "123_wx",
    pid: "3"
  },
  {
    chars: "shiti",
    id: "57",
    level: 2,
    name: "\u6A21\u62DF\u8BD5\u9898",
    newsClassifyId: "38_wx",
    pid: "3"
  },
  {
    chars: "mianshi",
    id: "59",
    level: 2,
    name: "\u9762\u8BD5\u6280\u5DE7",
    newsClassifyId: "89_wx",
    pid: "3"
  },
  {
    chars: "daiyu",
    id: "60",
    level: 2,
    name: "\u7279\u5C97\u52A8\u6001",
    newsClassifyId: "111_wx",
    pid: "3"
  },
  {
    chars: "zhunkaozheng",
    id: "79",
    level: 2,
    name: "\u51C6\u8003\u8BC1",
    newsClassifyId: "79_wx",
    pid: "1"
  },
  {
    chars: "zhiyuantianbao",
    id: "80",
    level: 2,
    name: "\u5FD7\u613F\u586B\u62A5",
    newsClassifyId: "80_wx",
    pid: "1"
  },
  {
    chars: "dayin",
    id: "81",
    level: 2,
    name: "\u51C6\u8003\u8BC1\u6253\u5370",
    newsClassifyId: "81_wx",
    pid: "3"
  },
  {
    chars: "mingdan",
    id: "82",
    level: 2,
    name: "\u9762\u8BD5\u540D\u5355/\u516C\u544A",
    newsClassifyId: "82_wx",
    pid: "3"
  },
  {
    chars: "dongtai",
    id: "83",
    level: 2,
    name: "\u62DB\u6559\u52A8\u6001",
    newsClassifyId: "83_wx",
    pid: "3"
  },
  {
    chars: "zhinan",
    id: "84",
    level: 2,
    name: "\u8003\u8BD5\u6307\u5357",
    newsClassifyId: "84_wx",
    pid: "3"
  },
  {
    chars: "sydwzkgg",
    id: "859693849913397248",
    level: 2,
    name: "\u62DB\u8003\u516C\u544A",
    newsClassifyId: "907865320340631552_wx",
    pid: "1003"
  },
  {
    chars: "sydwksdg",
    id: "859694294589313024",
    level: 2,
    name: "\u8003\u8BD5\u6307\u5357",
    newsClassifyId: "84_wx",
    pid: "1003"
  },
  {
    chars: "sydwksdt",
    id: "859695036645576704",
    level: 2,
    name: "\u8003\u8BD5\u52A8\u6001",
    newsClassifyId: "907865556081487872_wx",
    pid: "1003"
  },
  {
    chars: "sydwbmrk",
    id: "859695380133908480",
    level: 2,
    name: "\u62A5\u540D\u5165\u53E3",
    newsClassifyId: "52_wx",
    pid: "1003"
  },
  {
    chars: "sydwzkzdy",
    id: "859695797311967232",
    level: 2,
    name: "\u51C6\u8003\u8BC1\u6253\u5370",
    newsClassifyId: "81_wx",
    pid: "1003"
  },
  {
    chars: "sydwzwb",
    id: "859696133984555008",
    level: 2,
    name: "\u804C\u4F4D\u8868",
    newsClassifyId: "859696133984555008_wx",
    pid: "1003"
  },
  {
    chars: "sydwcjcx",
    id: "859696451770191872",
    level: 2,
    name: "\u6210\u7EE9\u67E5\u8BE2",
    newsClassifyId: "34_wx",
    pid: "1003"
  },
  {
    chars: "sydwmsmd",
    id: "859696859779502080",
    level: 2,
    name: "\u9762\u8BD5\u540D\u5355/\u516C\u544A",
    newsClassifyId: "82_wx",
    pid: "1003"
  },
  {
    chars: "sydwkszn",
    id: "859697294598803456",
    level: 2,
    name: "\u9762\u8BD5\u6280\u5DE7",
    newsClassifyId: "89_wx",
    pid: "1003"
  },
  {
    chars: "sydwkstk",
    id: "859697692504035328",
    level: 2,
    name: "\u8003\u8BD5\u9898\u5E93",
    newsClassifyId: "859697692504035328_wx",
    pid: "1003"
  },
  {
    chars: "sydwbkzl",
    id: "859697991432081408",
    level: 2,
    name: "\u5907\u8003\u8D44\u6599",
    newsClassifyId: "859697991432081408_wx",
    pid: "1003"
  },
  {
    chars: "tiaojian",
    id: "86",
    level: 3,
    name: "\u62DB\u8003\u6761\u4EF6",
    newsClassifyId: "86_wx",
    pid: "84"
  },
  {
    chars: "sydwmsjq",
    id: "862226815457759232",
    level: 2,
    name: "\u9762\u8BD5\u6280\u5DE7",
    newsClassifyId: "89_wx",
    pid: "1003"
  },
  {
    chars: "jiqiao",
    id: "89",
    level: 2,
    name: "\u9762\u8BD5\u6280\u5DE7",
    newsClassifyId: "89_wx",
    pid: "3"
  },
  {
    chars: "gwyzkgg",
    id: "907865320340631552",
    level: 2,
    name: "\u62DB\u8003\u516C\u544A",
    newsClassifyId: "907865320340631552_wx",
    pid: "17"
  },
  {
    chars: "gwykszn",
    id: "907865465492910080",
    level: 2,
    name: "\u8003\u8BD5\u6307\u5357",
    newsClassifyId: "84_wx",
    pid: "17"
  },
  {
    chars: "gwyksdt",
    id: "907865556081487872",
    level: 2,
    name: "\u8003\u8BD5\u52A8\u6001",
    newsClassifyId: "907865556081487872_wx",
    pid: "17"
  },
  {
    chars: "gwybmrk",
    id: "907865644287700992",
    level: 2,
    name: "\u62A5\u540D\u5165\u53E3",
    newsClassifyId: "52_wx",
    pid: "17"
  },
  {
    chars: "gwycjcx",
    id: "907865739070582784",
    level: 2,
    name: "\u6210\u7EE9\u67E5\u8BE2",
    newsClassifyId: "34_wx",
    pid: "17"
  },
  {
    chars: "gwymsmd",
    id: "907865862831910912",
    level: 2,
    name: "\u9762\u8BD5\u540D\u5355/\u516C\u544A",
    newsClassifyId: "82_wx",
    pid: "17"
  },
  {
    chars: "gwyksst",
    id: "907865965525250048",
    level: 2,
    name: "\u8003\u8BD5\u8BD5\u9898",
    newsClassifyId: "907865965525250048_wx",
    pid: "17"
  },
  {
    chars: "gwymsjq",
    id: "907866068793208832",
    level: 2,
    name: "\u9762\u8BD5\u6280\u5DE7",
    newsClassifyId: "89_wx",
    pid: "17"
  },
  {
    chars: "dongtai",
    id: "95",
    level: 2,
    name: "\u6559\u8D44\u52A8\u6001",
    newsClassifyId: "95_wx",
    pid: "2"
  },
  {
    chars: "dayin",
    id: "98",
    level: 2,
    name: "\u51C6\u8003\u8BC1\u6253\u5370",
    newsClassifyId: "81_wx",
    pid: "2"
  },
  {
    chars: "zhinan",
    id: "99",
    level: 2,
    name: "\u8003\u8BD5\u6307\u5357",
    newsClassifyId: "84_wx",
    pid: "2"
  },
  {
    chars: "lianxi",
    id: "103",
    level: 3,
    name: "\u6BCF\u65E5\u4E00\u7EC3",
    newsClassifyId: "150_wx",
    pid: "150"
  },
  {
    chars: "shijiang",
    id: "105",
    level: 3,
    name: "\u8BD5\u8BB2\u6280\u5DE7",
    newsClassifyId: "89_wx",
    pid: "104"
  },
  {
    chars: "shuoke",
    id: "106",
    level: 3,
    name: "\u7B54\u8FA9\u6280\u5DE7",
    newsClassifyId: "89_wx",
    pid: "104"
  },
  {
    chars: "jiegouhua",
    id: "107",
    level: 3,
    name: "\u7ED3\u6784\u5316\u6280\u5DE7",
    newsClassifyId: "89_wx",
    pid: "104"
  },
  {
    chars: "tongyong",
    id: "108",
    level: 3,
    name: "\u901A\u7528\u6280\u5DE7",
    newsClassifyId: "89_wx",
    pid: "104"
  },
  {
    chars: "lianxi",
    id: "115",
    level: 3,
    name: "\u6BCF\u65E5\u4E00\u7EC3",
    newsClassifyId: "151_wx",
    pid: "151"
  },
  {
    chars: "shijiang",
    id: "117",
    level: 3,
    name: "\u8BD5\u8BB2\u6280\u5DE7",
    newsClassifyId: "89_wx",
    pid: "116"
  },
  {
    chars: "shuoke",
    id: "118",
    level: 3,
    name: "\u8BF4\u8BFE\u6280\u5DE7",
    newsClassifyId: "89_wx",
    pid: "116"
  },
  {
    chars: "jiegouhua",
    id: "119",
    level: 3,
    name: "\u7ED3\u6784\u5316\u6280\u5DE7",
    newsClassifyId: "89_wx",
    pid: "116"
  },
  {
    chars: "dabian",
    id: "120",
    level: 3,
    name: "\u7B54\u8FA9\u6280\u5DE7",
    newsClassifyId: "89_wx",
    pid: "116"
  },
  {
    chars: "tongyong",
    id: "121",
    level: 3,
    name: "\u901A\u7528\u6280\u5DE7",
    newsClassifyId: "89_wx",
    pid: "116"
  },
  {
    chars: "moni",
    id: "707827529538727936",
    level: 3,
    name: "\u6A21\u62DF\u8BD5\u9898",
    newsClassifyId: "152_wx",
    pid: "152"
  },
  {
    chars: "xkzs",
    id: "708909959829204992",
    level: 3,
    name: "\u5B66\u79D1\u77E5\u8BC6",
    newsClassifyId: "152_wx",
    pid: "152"
  },
  {
    chars: "dfsdff",
    id: "709908413309202432",
    level: 3,
    name: "\u9762\u8BD5\u6280\u5DE72",
    newsClassifyId: "89_wx",
    pid: "116"
  },
  {
    chars: "linianzhenti",
    id: "709985778358677504",
    level: 3,
    name: "\u5386\u5E74\u771F\u9898",
    newsClassifyId: "152_wx",
    pid: "152"
  },
  {
    chars: "tglinianzhenti",
    id: "722604419697160192",
    level: 3,
    name: "\u5386\u5E74\u771F\u9898",
    newsClassifyId: "151_wx",
    pid: "151"
  },
  {
    chars: "sdddddd",
    id: "770546797651443712",
    level: 3,
    name: "\u6D4B\u8BD5\u4E00\u4E0B",
    newsClassifyId: "151_wx",
    pid: "151"
  },
  {
    chars: "tgmnst",
    id: "806294718810439680",
    level: 3,
    name: "\u6A21\u62DF\u8BD5\u9898",
    newsClassifyId: "151_wx",
    pid: "151"
  },
  {
    chars: "jiqiao",
    id: "85",
    level: 3,
    name: "\u8003\u8BD5\u6280\u5DE7",
    newsClassifyId: "84_wx",
    pid: "84"
  },
  {
    chars: "jzmnst",
    id: "856694922723131392",
    level: 3,
    name: "\u6A21\u62DF\u8BD5\u9898",
    newsClassifyId: "150_wx",
    pid: "150"
  },
  {
    chars: "sydwbsst",
    id: "859698440340049920",
    level: 3,
    name: "\u7B14\u8BD5\u8BD5\u9898",
    newsClassifyId: "859697692504035328_wx",
    pid: "859697692504035328"
  },
  {
    chars: "sydwmsst",
    id: "859699974608064512",
    level: 3,
    name: "\u9762\u8BD5\u8BD5\u9898",
    newsClassifyId: "859697692504035328_wx",
    pid: "859697692504035328"
  },
  {
    chars: "sydwbszt",
    id: "859700913024864256",
    level: 3,
    name: "\u7B14\u8BD5\u771F\u9898",
    newsClassifyId: "859697692504035328_wx",
    pid: "859697692504035328"
  },
  {
    chars: "sydwmszt",
    id: "859701188255092736",
    level: 3,
    name: "\u9762\u8BD5\u771F\u9898",
    newsClassifyId: "859697692504035328_wx",
    pid: "859697692504035328"
  },
  {
    chars: "sydwzc",
    id: "859701542581506048",
    level: 3,
    name: "\u804C\u6D4B",
    newsClassifyId: "859697991432081408_wx",
    pid: "859697991432081408"
  },
  {
    chars: "sydwzy",
    id: "859702231944728576",
    level: 3,
    name: "\u7EFC\u5E94",
    newsClassifyId: "859697991432081408_wx",
    pid: "859697991432081408"
  },
  {
    chars: "sydwgj",
    id: "859702610388389888",
    level: 3,
    name: "\u516C\u57FA",
    newsClassifyId: "859697991432081408_wx",
    pid: "859697991432081408"
  },
  {
    chars: "sydwszrd",
    id: "859703244692983808",
    level: 3,
    name: "\u65F6\u653F\u70ED\u70B9",
    newsClassifyId: "859697991432081408_wx",
    pid: "859697991432081408"
  },
  {
    chars: "sydwmsrd",
    id: "859703725733515264",
    level: 3,
    name: "\u9762\u8BD5\u70ED\u70B9",
    newsClassifyId: "859697991432081408_wx",
    pid: "859697991432081408"
  },
  {
    chars: "sydwmryl",
    id: "862227767900311552",
    level: 3,
    name: "\u6BCF\u65E5\u4E00\u7EC3",
    newsClassifyId: "859697692504035328_wx",
    pid: "859697692504035328"
  },
  {
    chars: "wenti",
    id: "87",
    level: 3,
    name: "\u5E38\u89C1\u95EE\u9898",
    newsClassifyId: "84_wx",
    pid: "84"
  },
  {
    chars: "lianxi",
    id: "88",
    level: 3,
    name: "\u6BCF\u65E5\u4E00\u7EC3",
    newsClassifyId: "152_wx",
    pid: "152"
  },
  {
    chars: "shijiang",
    id: "90",
    level: 3,
    name: "\u8BD5\u8BB2\u6280\u5DE7",
    newsClassifyId: "89_wx",
    pid: "89"
  },
  {
    chars: "gwyxctk",
    id: "907866216755671040",
    level: 3,
    name: "\u884C\u6D4B\u9898\u5E93",
    newsClassifyId: "907865965525250048_wx",
    pid: "907865965525250048"
  },
  {
    chars: "gwysltk",
    id: "907866313002364928",
    level: 3,
    name: "\u7533\u8BBA\u9898\u5E93",
    newsClassifyId: "907865965525250048_wx",
    pid: "907865965525250048"
  },
  {
    chars: "gwymstk",
    id: "907866429058756608",
    level: 3,
    name: "\u9762\u8BD5\u9898\u5E93",
    newsClassifyId: "907865965525250048_wx",
    pid: "907865965525250048"
  },
  {
    chars: "shuoke",
    id: "91",
    level: 3,
    name: "\u8BF4\u8BFE\u6280\u5DE7",
    newsClassifyId: "89_wx",
    pid: "89"
  },
  {
    chars: "dabian",
    id: "92",
    level: 3,
    name: "\u7B54\u8FA9\u6280\u5DE7",
    newsClassifyId: "89_wx",
    pid: "89"
  },
  {
    chars: "jiegouhua",
    id: "93",
    level: 3,
    name: "\u7ED3\u6784\u5316\u6280\u5DE7",
    newsClassifyId: "89_wx",
    pid: "89"
  },
  {
    chars: "tongyong",
    id: "94",
    level: 3,
    name: "\u901A\u7528\u6280\u5DE7",
    newsClassifyId: "89_wx",
    pid: "89"
  },
  {
    chars: "dagang",
    id: "96",
    level: 3,
    name: "\u8003\u8BD5\u5927\u7EB2",
    newsClassifyId: "95_wx",
    pid: "95"
  },
  {
    chars: "shijian",
    id: "97",
    level: 3,
    name: "\u8003\u8BD5\u65F6\u95F4",
    newsClassifyId: "95_wx",
    pid: "95"
  }
];

const regexCourse = /^\/(course|live|classroom|book|exam|offline_course)\/(\d+)(?:\.html)?/;
const regexNews = /^\/(zsb|sydw|jszp|jszg|tgjs|gwy|szyf|xmt|kkgw|kknews|cjkj|zjkj)\/(\d+)(?:\.html)?/;
const regexNewsTag = /^\/news\/tag\/([0-9a-zA-Z_-]+)/;
const regexNewsCategory = /^\/news\/(zsb|sydw|jszp|jszg|tgjs|gwy|szyf|xmt|kkgw|kknews|cjkj|zjkj)\/?(.+)?/;
const defaultResult = {
  // TYPE: null,
  // type: null,
  id: null
  // getPathname () {}
};
function redirectKuke99Url(url, isWap) {
  if (url.endsWith("_wx") || url.includes("_wx_") || url.endsWith("_wxc")) {
    return defaultResult;
  }
  const matchCourse = url.match(regexCourse);
  const matchNews = url.match(regexNews);
  const matchNewsTag = url.match(regexNewsTag);
  const matchNewsCategory = url.match(regexNewsCategory);
  if (matchNews) {
    return {
      TYPE: "news",
      type: matchNews[1],
      id: matchNews[2],
      getPathname(id) {
        return `/news/${id}_wx`;
      }
    };
  } else if (matchCourse) {
    const id = matchCourse[2];
    if ((id == null ? void 0 : id.length) === 18) {
      return defaultResult;
    }
    return {
      TYPE: "course",
      type: matchCourse[1],
      id,
      getPathname(id2, type) {
        return `/course/${id2}_wx_${type}`;
      }
    };
  } else if (matchNewsTag) {
    const id = matchNewsTag[1];
    if ((id == null ? void 0 : id.length) === 18) {
      return defaultResult;
    }
    return {
      TYPE: "news-tag",
      type: "news-tag",
      id,
      getPathname(id2) {
        const v = informationLabelMap[id2];
        if (!v) {
          return "/";
        }
        return `/news/tag/${v}`;
      }
    };
  } else if (matchNewsCategory) {
    const cateId = matchNewsCategory[1];
    let newsClassifyId = "";
    let province = "";
    let city = "";
    const ids = (matchNewsCategory[2] || "").split("/");
    const idsLength = ids.length;
    if (idsLength >= 1) {
      newsClassifyId = ids[0];
    }
    if (idsLength === 2) {
      province = ids[1];
    } else if (idsLength === 3) {
      province = ids[1];
      city = ids[2];
    } else if (idsLength === 4) {
      province = "";
      city = "";
    }
    const r = {
      TYPE: "news-category",
      type: "news-category",
      id: cateId,
      cateId,
      newsClassifyId,
      province,
      city
      // area: area || '',
    };
    return {
      ...r,
      getPathname(_) {
        var _a, _b;
        const id1 = CATE_ID_MAP[cateId];
        if (!id1) {
          return "/";
        }
        let str = `/news/category-list/${id1}`;
        const id2 = ((_a = findNewsCategoryId(id1, newsClassifyId)) == null ? void 0 : _a.newsClassifyId) || ((_b = findNewsCategoryId(id1, province)) == null ? void 0 : _b.newsClassifyId);
        if (id2) {
          str += `?newsClassifyId=${id2}`;
        }
        const id3 = CITY_TREE_MAP[newsClassifyId] || CITY_TREE_MAP[province];
        if (id3) {
          const id4 = CITY_TREE_MAP[city] || "";
          if (str.includes("?")) {
            str += `&province=${id3}&city=${id4}`;
          } else {
            str += `?province=${id3}&city=${id4}`;
          }
          if (isWap) {
            str += "&area=";
          }
        }
        return str;
      }
    };
  } else {
    return defaultResult;
  }
}
const findNewsCategoryId = (id, id2) => {
  return NEW_CATEGORY_LIST.find((v) => v.pid === id && id2 === v.chars);
};

const ENV_MAP = {
  STAGING: ["icloud-testc.tyyk99.com", "m-icloud-testc.tyyk99.com"],
  PRESSURE: ["kuke99-c.pt.kukewang.com", "m-kuke99-c.pt.kukewang.com"],
  PRE: ["kuke99-c.pre.kukewang.com", "m-kuke99-c.pre.kukewang.com"],
  PROD: ["www.kuke99.com", "m.kuke99.com", "kuke99.com", "kkqudao.kukecloud.cn", "m-kkqudao.kukecloud.cn"]
};
const getDomainMap = (isMobile) => {
  return /* @__PURE__ */ new Map([
    ["m..kuke99.com", "m.kuke99.com"],
    ["www..kuke99.com", "www.kuke99.com"],
    ["kuke99.com", `${isMobile ? "m" : "www"}.kuke99.com`],
    [".kuke99.com", `${isMobile ? "m" : "www"}.kuke99.com`],
    [".pt.kukewang.com", `${isMobile ? "m-kuke99-c" : "kuke99-c"}.pt.kukewang.com`],
    [".pre.kukewang.com", `${isMobile ? "m-kkceshi123-c" : "kkceshi123-c"}.pre.kukewang.com`]
  ]);
};
const getRedirectHost = () => {
  const { public: { BUILD_ENV } } = useRuntimeConfig();
  return ENV_MAP[BUILD_ENV] || ["127.0.0.1:3000", "127.0.0.1:3006"];
};
const getRedirectClient = (event) => {
  var _a;
  const ua = (_a = getRequestHeader(event, "User-Agent")) == null ? void 0 : _a.toLocaleLowerCase();
  return /ios|iphone|ipod|ipad|android|harmony/.test(ua || "");
};
const isLocal = (host) => /^(localhost\.|127)/.test(host);
const _MFX2co = defineEventHandler(async (event) => {
  var _a;
  const { path, method } = event;
  const host = getRequestHost(event);
  const [pathname, qs = ""] = path.split("?");
  const isMobile = getRedirectClient(event);
  const domainMap = getDomainMap(isMobile);
  const protocol = getRequestProtocol(event);
  console.log(JSON.stringify({
    name: "\u5F53\u524D\u8BF7\u6C42\u7684\u57DF\u540D",
    method,
    href: (_a = getRequestURL(event)) == null ? void 0 : _a.href
  }));
  if (method.toLocaleUpperCase() === "GET" && getRedirectHost().includes(host)) {
    const isWap = ["127.0.0.1:3006", "m-kuke99-c.pre.kukewang.com", "m-icloud-testc.tyyk99.com", "m.kuke99.com"].includes(host);
    const { type: goodsType, id: goodsId, TYPE, getPathname } = redirectKuke99Url(pathname, isWap);
    const orderPathList = ["/ucenter/watch_record", "/ucenter/order", "/ucenter/collect", "/ucenter/address", "/ucenter/resource", "/ucenter/coupon", "/ucenter/account_security", "/ucenter/system_message", "/ucenter/buy_goods"];
    if (orderPathList.includes(pathname)) {
      return await sendRedirect(event, "/user/order", 301);
    }
    const learnPathList = ["/ucenter/learn_record", "/ucenter/exam_record", "/ucenter/live", "/ucenter/course", "/ucenter/exam"];
    if (learnPathList.includes(pathname)) {
      return await sendRedirect(event, "/learn-center", 301);
    }
    if (TYPE && goodsId) {
      let redirectPath = getPathname(goodsId, goodsType);
      if (qs && redirectPath) {
        if (redirectPath.includes("?")) {
          redirectPath = redirectPath + "&" + qs;
        } else {
          redirectPath = redirectPath + "?" + qs;
        }
      }
      if (redirectPath && redirectPath !== "/") {
        const newHost = domainMap.get(host) || host;
        const domain = !isLocal(newHost) ? `https://${newHost}${redirectPath}` : redirectPath;
        return await sendRedirect(event, domain, 301);
      }
    }
    if (protocol === "http" || domainMap.get(host)) {
      const newHost = domainMap.get(host) || host;
      if (!isLocal(newHost)) {
        return await sendRedirect(event, `https://${newHost}${path}`, 301);
      }
    }
  }
});

const _PAW6Ho = defineEventHandler(async (event) => {
  var _a;
  const { method } = event;
  const METHOD = method.toLocaleUpperCase();
  console.log(JSON.stringify({
    reqTime: (/* @__PURE__ */ new Date()).toISOString(),
    method: METHOD,
    reqURL: getRequestURL(event),
    NODE_ENV: (_a = process == null ? void 0 : process.env) == null ? void 0 : _a.NODE_ENV,
    headers: getRequestHeaders(event),
    response: {
      headers: getResponseHeaders(event),
      status: getResponseStatus(event)
      // statusText: getResponseStatusText(event),
    }
  }));
});

const _JgLwG4 = defineEventHandler((event) => {
  if (getRequestHeader(event, "x-nuxt-no-ssr")) {
    event.context.nuxt = event.context.nuxt || {};
    event.context.nuxt.noSSR = true;
  }
});

const _lazy_COlOPN = () => Promise.resolve().then(function () { return imageMsg$1; });
const _lazy_0ccIOC = () => Promise.resolve().then(function () { return ____type_$1; });
const _lazy_xWTWmN = () => Promise.resolve().then(function () { return renderer$1; });

const handlers = [
  { route: '', handler: _Ui4b7p, lazy: false, middleware: true, method: undefined },
  { route: '', handler: _MFX2co, lazy: false, middleware: true, method: undefined },
  { route: '', handler: _PAW6Ho, lazy: false, middleware: true, method: undefined },
  { route: '/api/image-msg', handler: _lazy_COlOPN, lazy: true, middleware: false, method: undefined },
  { route: '/auth/**:type', handler: _lazy_0ccIOC, lazy: true, middleware: false, method: undefined },
  { route: '/__nuxt_error', handler: _lazy_xWTWmN, lazy: true, middleware: false, method: undefined },
  { route: '', handler: _JgLwG4, lazy: false, middleware: true, method: undefined },
  { route: '/**', handler: _lazy_xWTWmN, lazy: true, middleware: false, method: undefined }
];

function createNitroApp() {
  const config = useRuntimeConfig();
  const hooks = createHooks();
  const captureError = (error, context = {}) => {
    const promise = hooks.callHookParallel("error", error, context).catch((error_) => {
      console.error("Error while capturing another error", error_);
    });
    if (context.event && isEvent(context.event)) {
      const errors = context.event.context.nitro?.errors;
      if (errors) {
        errors.push({ error, context });
      }
      if (context.event.waitUntil) {
        context.event.waitUntil(promise);
      }
    }
  };
  const h3App = createApp({
    debug: destr(true),
    onError: (error, event) => {
      captureError(error, { event, tags: ["request"] });
      return errorHandler(error, event);
    },
    onRequest: async (event) => {
      event.context.nitro = event.context.nitro || { errors: [] };
      const fetchContext = event.node.req?.__unenv__;
      if (fetchContext?._platform) {
        event.context = {
          _platform: fetchContext?._platform,
          // #3335
          ...fetchContext._platform,
          ...event.context
        };
      }
      if (!event.context.waitUntil && fetchContext?.waitUntil) {
        event.context.waitUntil = fetchContext.waitUntil;
      }
      event.fetch = (req, init) => fetchWithEvent(event, req, init, { fetch: localFetch });
      event.$fetch = (req, init) => fetchWithEvent(event, req, init, {
        fetch: $fetch
      });
      event.waitUntil = (promise) => {
        if (!event.context.nitro._waitUntilPromises) {
          event.context.nitro._waitUntilPromises = [];
        }
        event.context.nitro._waitUntilPromises.push(promise);
        if (event.context.waitUntil) {
          event.context.waitUntil(promise);
        }
      };
      event.captureError = (error, context) => {
        captureError(error, { event, ...context });
      };
      await nitroApp$1.hooks.callHook("request", event).catch((error) => {
        captureError(error, { event, tags: ["request"] });
      });
    },
    onBeforeResponse: async (event, response) => {
      await nitroApp$1.hooks.callHook("beforeResponse", event, response).catch((error) => {
        captureError(error, { event, tags: ["request", "response"] });
      });
    },
    onAfterResponse: async (event, response) => {
      await nitroApp$1.hooks.callHook("afterResponse", event, response).catch((error) => {
        captureError(error, { event, tags: ["request", "response"] });
      });
    }
  });
  const router = createRouter$1({
    preemptive: true
  });
  const nodeHandler = toNodeListener(h3App);
  const localCall = (aRequest) => callNodeRequestHandler(
    nodeHandler,
    aRequest
  );
  const localFetch = (input, init) => {
    if (!input.toString().startsWith("/")) {
      return globalThis.fetch(input, init);
    }
    return fetchNodeRequestHandler(
      nodeHandler,
      input,
      init
    ).then((response) => normalizeFetchResponse(response));
  };
  const $fetch = createFetch({
    fetch: localFetch,
    Headers: Headers$1,
    defaults: { baseURL: config.app.baseURL }
  });
  globalThis.$fetch = $fetch;
  h3App.use(createRouteRulesHandler({ localFetch }));
  for (const h of handlers) {
    let handler = h.lazy ? lazyEventHandler(h.handler) : h.handler;
    if (h.middleware || !h.route) {
      const middlewareBase = (config.app.baseURL + (h.route || "/")).replace(
        /\/+/g,
        "/"
      );
      h3App.use(middlewareBase, handler);
    } else {
      const routeRules = getRouteRulesForPath(
        h.route.replace(/:\w+|\*\*/g, "_")
      );
      if (routeRules.cache) {
        handler = cachedEventHandler(handler, {
          group: "nitro/routes",
          ...routeRules.cache
        });
      }
      router.use(h.route, handler, h.method);
    }
  }
  h3App.use(config.app.baseURL, router.handler);
  const app = {
    hooks,
    h3App,
    router,
    localCall,
    localFetch,
    captureError
  };
  return app;
}
function runNitroPlugins(nitroApp2) {
  for (const plugin of plugins) {
    try {
      plugin(nitroApp2);
    } catch (error) {
      nitroApp2.captureError(error, { tags: ["plugin"] });
      throw error;
    }
  }
}
const nitroApp$1 = createNitroApp();
function useNitroApp() {
  return nitroApp$1;
}
runNitroPlugins(nitroApp$1);

function defineRenderHandler(render) {
  const runtimeConfig = useRuntimeConfig();
  return eventHandler(async (event) => {
    const nitroApp = useNitroApp();
    const ctx = { event, render, response: void 0 };
    await nitroApp.hooks.callHook("render:before", ctx);
    if (!ctx.response) {
      if (event.path === `${runtimeConfig.app.baseURL}favicon.ico`) {
        setResponseHeader(event, "Content-Type", "image/x-icon");
        return send(
          event,
          "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7"
        );
      }
      ctx.response = await ctx.render(event);
      if (!ctx.response) {
        const _currentStatus = getResponseStatus(event);
        setResponseStatus(event, _currentStatus === 200 ? 500 : _currentStatus);
        return send(
          event,
          "No response returned from render handler: " + event.path
        );
      }
    }
    await nitroApp.hooks.callHook("render:response", ctx.response, ctx);
    if (ctx.response.headers) {
      setResponseHeaders(event, ctx.response.headers);
    }
    if (ctx.response.statusCode || ctx.response.statusMessage) {
      setResponseStatus(
        event,
        ctx.response.statusCode,
        ctx.response.statusMessage
      );
    }
    return ctx.response.body;
  });
}

const scheduledTasks = false;

const tasks = {
  
};

const __runningTasks__ = {};
async function runTask(name, {
  payload = {},
  context = {}
} = {}) {
  if (__runningTasks__[name]) {
    return __runningTasks__[name];
  }
  if (!(name in tasks)) {
    throw createError({
      message: `Task \`${name}\` is not available!`,
      statusCode: 404
    });
  }
  if (!tasks[name].resolve) {
    throw createError({
      message: `Task \`${name}\` is not implemented!`,
      statusCode: 501
    });
  }
  const handler = await tasks[name].resolve();
  const taskEvent = { name, payload, context };
  __runningTasks__[name] = handler.run(taskEvent);
  try {
    const res = await __runningTasks__[name];
    return res;
  } finally {
    delete __runningTasks__[name];
  }
}

if (!globalThis.crypto) {
  globalThis.crypto = nodeCrypto;
}
const { NITRO_NO_UNIX_SOCKET, NITRO_DEV_WORKER_ID } = process.env;
trapUnhandledNodeErrors();
parentPort?.on("message", (msg) => {
  if (msg && msg.event === "shutdown") {
    shutdown();
  }
});
const nitroApp = useNitroApp();
const server = new Server(toNodeListener(nitroApp.h3App));
let listener;
listen().catch(() => listen(
  true
  /* use random port */
)).catch((error) => {
  console.error("Dev worker failed to listen:", error);
  return shutdown();
});
nitroApp.router.get(
  "/_nitro/tasks",
  defineEventHandler(async (event) => {
    const _tasks = await Promise.all(
      Object.entries(tasks).map(async ([name, task]) => {
        const _task = await task.resolve?.();
        return [name, { description: _task?.meta?.description }];
      })
    );
    return {
      tasks: Object.fromEntries(_tasks),
      scheduledTasks
    };
  })
);
nitroApp.router.use(
  "/_nitro/tasks/:name",
  defineEventHandler(async (event) => {
    const name = getRouterParam(event, "name");
    const payload = {
      ...getQuery$1(event),
      ...await readBody(event).then((r) => r?.payload).catch(() => ({}))
    };
    return await runTask(name, { payload });
  })
);
function listen(useRandomPort = Boolean(
  NITRO_NO_UNIX_SOCKET || process.versions.webcontainer || "Bun" in globalThis && process.platform === "win32"
)) {
  return new Promise((resolve, reject) => {
    try {
      listener = server.listen(useRandomPort ? 0 : getSocketAddress(), () => {
        const address = server.address();
        parentPort?.postMessage({
          event: "listen",
          address: typeof address === "string" ? { socketPath: address } : { host: "localhost", port: address?.port }
        });
        resolve();
      });
    } catch (error) {
      reject(error);
    }
  });
}
function getSocketAddress() {
  const socketName = `nitro-worker-${process.pid}-${threadId}-${NITRO_DEV_WORKER_ID}-${Math.round(Math.random() * 1e4)}.sock`;
  if (process.platform === "win32") {
    return join(String.raw`\\.\pipe`, socketName);
  }
  if (process.platform === "linux") {
    const nodeMajor = Number.parseInt(process.versions.node.split(".")[0], 10);
    if (nodeMajor >= 20) {
      return `\0${socketName}`;
    }
  }
  return join(tmpdir(), socketName);
}
async function shutdown() {
  server.closeAllConnections?.();
  await Promise.all([
    new Promise((resolve) => listener?.close(resolve)),
    nitroApp.hooks.callHook("close").catch(console.error)
  ]);
  parentPort?.postMessage({ event: "exit" });
}

const _messages = {"appName":"Nuxt","version":"","statusCode":500,"statusMessage":"Server error","description":"An error occurred in the application and the page could not be served. If you are the application owner, check your server logs for details.","stack":""};
const _render = function({ messages }) {
var __t, __p = '';
__p += '<!DOCTYPE html><html data-critters-container><head><title>' +
((__t = ( messages.statusCode )) == null ? '' : __t) +
' - ' +
((__t = ( messages.statusMessage )) == null ? '' : __t) +
' | ' +
((__t = ( messages.appName )) == null ? '' : __t) +
'</title><meta charset="utf-8"><meta content="width=device-width,initial-scale=1,minimum-scale=1" name="viewport"><style>.spotlight{background:linear-gradient(45deg, #00DC82 0%, #36E4DA 50%, #0047E1 100%);opacity:0.8;filter:blur(30vh);height:60vh;bottom:-40vh}*,:before,:after{box-sizing:border-box;border-width:0;border-style:solid;border-color:var(--un-default-border-color, #e5e7eb)}:before,:after{--un-content:""}html{line-height:1.5;-webkit-text-size-adjust:100%;-moz-tab-size:4;tab-size:4;font-family:ui-sans-serif,system-ui,sans-serif,"Apple Color Emoji","Segoe UI Emoji",Segoe UI Symbol,"Noto Color Emoji";font-feature-settings:normal;font-variation-settings:normal;-webkit-tap-highlight-color:transparent}body{margin:0;line-height:inherit}h1{font-size:inherit;font-weight:inherit}pre{font-family:ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,Liberation Mono,Courier New,monospace;font-feature-settings:normal;font-variation-settings:normal;font-size:1em}h1,p,pre{margin:0}*,:before,:after{--un-rotate:0;--un-rotate-x:0;--un-rotate-y:0;--un-rotate-z:0;--un-scale-x:1;--un-scale-y:1;--un-scale-z:1;--un-skew-x:0;--un-skew-y:0;--un-translate-x:0;--un-translate-y:0;--un-translate-z:0;--un-pan-x: ;--un-pan-y: ;--un-pinch-zoom: ;--un-scroll-snap-strictness:proximity;--un-ordinal: ;--un-slashed-zero: ;--un-numeric-figure: ;--un-numeric-spacing: ;--un-numeric-fraction: ;--un-border-spacing-x:0;--un-border-spacing-y:0;--un-ring-offset-shadow:0 0 rgb(0 0 0 / 0);--un-ring-shadow:0 0 rgb(0 0 0 / 0);--un-shadow-inset: ;--un-shadow:0 0 rgb(0 0 0 / 0);--un-ring-inset: ;--un-ring-offset-width:0px;--un-ring-offset-color:#fff;--un-ring-width:0px;--un-ring-color:rgb(147 197 253 / .5);--un-blur: ;--un-brightness: ;--un-contrast: ;--un-drop-shadow: ;--un-grayscale: ;--un-hue-rotate: ;--un-invert: ;--un-saturate: ;--un-sepia: ;--un-backdrop-blur: ;--un-backdrop-brightness: ;--un-backdrop-contrast: ;--un-backdrop-grayscale: ;--un-backdrop-hue-rotate: ;--un-backdrop-invert: ;--un-backdrop-opacity: ;--un-backdrop-saturate: ;--un-backdrop-sepia: }.fixed{position:fixed}.left-0{left:0}.right-0{right:0}.z-10{z-index:10}.mb-6{margin-bottom:1.5rem}.mb-8{margin-bottom:2rem}.h-auto{height:auto}.min-h-screen{min-height:100vh}.flex{display:flex}.flex-1{flex:1 1 0%}.flex-col{flex-direction:column}.overflow-y-auto{overflow-y:auto}.rounded-t-md{border-top-left-radius:.375rem;border-top-right-radius:.375rem}.bg-black\\/5{background-color:#0000000d}.bg-white{--un-bg-opacity:1;background-color:rgb(255 255 255 / var(--un-bg-opacity))}.p-8{padding:2rem}.px-10{padding-left:2.5rem;padding-right:2.5rem}.pt-14{padding-top:3.5rem}.text-6xl{font-size:3.75rem;line-height:1}.text-xl{font-size:1.25rem;line-height:1.75rem}.text-black{--un-text-opacity:1;color:rgb(0 0 0 / var(--un-text-opacity))}.font-light{font-weight:300}.font-medium{font-weight:500}.leading-tight{line-height:1.25}.font-sans{font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,"Apple Color Emoji","Segoe UI Emoji",Segoe UI Symbol,"Noto Color Emoji"}.antialiased{-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}@media (prefers-color-scheme: dark){.dark\\:bg-black{--un-bg-opacity:1;background-color:rgb(0 0 0 / var(--un-bg-opacity))}.dark\\:bg-white\\/10{background-color:#ffffff1a}.dark\\:text-white{--un-text-opacity:1;color:rgb(255 255 255 / var(--un-text-opacity))}}@media (min-width: 640px){.sm\\:text-2xl{font-size:1.5rem;line-height:2rem}.sm\\:text-8xl{font-size:6rem;line-height:1}}</style><script>(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const e of document.querySelectorAll(\'link[rel="modulepreload"]\'))i(e);new MutationObserver(e=>{for(const r of e)if(r.type==="childList")for(const o of r.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&i(o)}).observe(document,{childList:!0,subtree:!0});function s(e){const r={};return e.integrity&&(r.integrity=e.integrity),e.referrerPolicy&&(r.referrerPolicy=e.referrerPolicy),e.crossOrigin==="use-credentials"?r.credentials="include":e.crossOrigin==="anonymous"?r.credentials="omit":r.credentials="same-origin",r}function i(e){if(e.ep)return;e.ep=!0;const r=s(e);fetch(e.href,r)}})();</script></head><body class="font-sans antialiased bg-white px-10 pt-14 dark:bg-black text-black dark:text-white min-h-screen flex flex-col"><div class="fixed left-0 right-0 spotlight"></div><h1 class="text-6xl sm:text-8xl font-medium mb-6">' +
((__t = ( messages.statusCode )) == null ? '' : __t) +
'</h1><p class="text-xl sm:text-2xl font-light mb-8 leading-tight">' +
((__t = ( messages.description )) == null ? '' : __t) +
'</p><div class="bg-white rounded-t-md bg-black/5 dark:bg-white/10 flex-1 overflow-y-auto h-auto"><pre class="text-xl font-light leading-tight z-10 p-8">' +
((__t = ( messages.stack )) == null ? '' : __t) +
'</pre></div></body></html>';
return __p
};
const _template = (messages) => _render({ messages: { ..._messages, ...messages } });
const template$1 = _template;

const errorDev = /*#__PURE__*/Object.freeze({
  __proto__: null,
  template: template$1
});

function parseParams(str) {
  const params = {};
  const pairs = str.split(",");
  const regex = /(\w+)\s*[:=]\s*(.*)/;
  pairs.forEach((pair) => {
    const match = pair.match(regex);
    if (match) {
      const key = match[1];
      const value = match[2];
      params[key] = value;
    }
  });
  return params;
}
const imageMsg = defineEventHandler(async (event) => {
  const { callback, businessParam, visitorId, pageId, ...o } = getQuery$1(event);
  setResponseHeader(event, "Content-Type", "text/plain; charset=utf-8");
  console.log("[ /api/image-msg ] >", { callback, businessParam, visitorId, pageId, ...o });
  if (!callback) {
    setResponseStatus(event, 404);
    return "";
  }
  const data = {
    success: true,
    list: []
    // message: '200 ok!'
  };
  if (businessParam) {
    const listItem = parseParams(decodeURIComponent(businessParam));
    const _pageId = listItem == null ? void 0 : listItem.pageId;
    if (_pageId && pageId === _pageId) {
      data.list = [listItem];
      return `${callback} && ${callback}(${JSON.stringify(data)})`;
    }
  }
  return `${callback} && ${callback}(${JSON.stringify(data)})`;
});

const imageMsg$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: imageMsg
});

const router = createRouter$1();
router.get("/callback/:type", defineEventHandler((event) => {
  const query = getQuery$1(event);
  const { code, state } = query;
  if (getCookie(event, "OAUTH_CODE")) {
    return sendRedirect(event, state || "/", 301);
  } else {
    setCookie(event, "OAUTH_CODE", code);
    return sendRedirect(event, state || "/", 301);
  }
}));
const ____type_ = useBase("/auth", router.handler);

const ____type_$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: ____type_
});

const Vue3 = version[0] === "3";

function resolveUnref(r) {
  return typeof r === "function" ? r() : unref(r);
}
function resolveUnrefHeadInput(ref) {
  if (ref instanceof Promise || ref instanceof Date || ref instanceof RegExp)
    return ref;
  const root = resolveUnref(ref);
  if (!ref || !root)
    return root;
  if (Array.isArray(root))
    return root.map((r) => resolveUnrefHeadInput(r));
  if (typeof root === "object") {
    const resolved = {};
    for (const k in root) {
      if (!Object.prototype.hasOwnProperty.call(root, k)) {
        continue;
      }
      if (k === "titleTemplate" || k[0] === "o" && k[1] === "n") {
        resolved[k] = unref(root[k]);
        continue;
      }
      resolved[k] = resolveUnrefHeadInput(root[k]);
    }
    return resolved;
  }
  return root;
}

const VueReactivityPlugin = defineHeadPlugin({
  hooks: {
    "entries:resolve": (ctx) => {
      for (const entry of ctx.entries)
        entry.resolvedInput = resolveUnrefHeadInput(entry.input);
    }
  }
});

const headSymbol = "usehead";
function vueInstall(head) {
  const plugin = {
    install(app) {
      if (Vue3) {
        app.config.globalProperties.$unhead = head;
        app.config.globalProperties.$head = head;
        app.provide(headSymbol, head);
      }
    }
  };
  return plugin.install;
}
function createServerHead(options = {}) {
  const head = createServerHead$1(options);
  head.use(VueReactivityPlugin);
  head.install = vueInstall(head);
  return head;
}

const unheadPlugins = [];

const appHead = {"meta":[{"name":"viewport","content":"width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no, viewport-fit=cover"},{"charset":"utf-8"},{"name":"renderer","content":"webkit"}],"link":[{"rel":"preconnect","href":"https://oss.kuke99.com","crossorigin":"anonymous"},{"rel":"dns-prefetch","href":"https://oss.kuke99.com"},{"rel":"dns-prefetch","href":"https://kuke-utils.kuke99.com"},{"rel":"dns-prefetch","href":"https://oss.kuke99.com"},{"rel":"stylesheet","crossorigin":"anonymous","href":"https://kuke-utils.kuke99.com/common/js/kkeditor-build-all/v1.3.5/styles.css"}],"style":[],"script":[{"src":"https://hm.baidu.com/hm.js?bf5d69736f7c039d6994ad1bf979dd24","body":true,"defer":true},{"src":"/libs/unhandledrejection.js","body":true},{"src":"//at.alicdn.com/t/c/font_4140659_1eml475tpq6.js?t=*************","body":true},{"src":"https://res.wx.qq.com/open/js/jweixin-1.6.0.js","body":true},{"src":"//res.wx.qq.com/connect/zh_CN/htmledition/js/wxLogin.js","body":true,"defer":true},{"src":"https://oss.kuke99.com/kukecloud/mobile/@vant/touch-emulator/v1.4.0/index.js","async":true},{"src":"https://o.alicdn.com/captcha-frontend/aliyunCaptcha/AliyunCaptcha.js","body":true,"defer":true},{"src":"https://kuke-utils.kuke99.com/common/js/MathJax-3.2.0/es5/tex-chtml-full.js","async":true}],"noscript":[],"htmlAttrs":{"lang":"zh-CN"},"viewport":"width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no, viewport-fit=cover"};

const appRootId = "__nuxt";

const appRootTag = "div";

function buildAssetsDir() {
  return useRuntimeConfig().app.buildAssetsDir;
}
function buildAssetsURL(...path) {
  return joinURL(publicAssetsURL(), buildAssetsDir(), ...path);
}
function publicAssetsURL(...path) {
  const publicBase = useRuntimeConfig().app.cdnURL || useRuntimeConfig().app.baseURL;
  return path.length ? joinURL(publicBase, ...path) : publicBase;
}

globalThis.__buildAssetsURL = buildAssetsURL;
globalThis.__publicAssetsURL = publicAssetsURL;
const getClientManifest = () => import('file://D:/workspace/0.nuxt3/migration/kuke-cloud---next/mobile/.nuxt/dist/server/client.manifest.mjs').then((r) => r.default || r).then((r) => typeof r === "function" ? r() : r);
const getServerEntry = () => import('file://D:/workspace/0.nuxt3/migration/kuke-cloud---next/mobile/.nuxt/dist/server/server.mjs').then((r) => r.default || r);
const getSSRStyles = lazyCachedFunction(() => Promise.resolve().then(function () { return styles$1; }).then((r) => r.default || r));
const getSSRRenderer = lazyCachedFunction(async () => {
  const manifest = await getClientManifest();
  if (!manifest) {
    throw new Error("client.manifest is not available");
  }
  const createSSRApp = await getServerEntry();
  if (!createSSRApp) {
    throw new Error("Server bundle is not available");
  }
  const options = {
    manifest,
    renderToString: renderToString$1,
    buildAssetsURL
  };
  const renderer = createRenderer(createSSRApp, options);
  async function renderToString$1(input, context) {
    const html = await renderToString(input, context);
    if (process.env.NUXT_VITE_NODE_OPTIONS) {
      renderer.rendererContext.updateManifest(await getClientManifest());
    }
    return `<${appRootTag}${` id="${appRootId}"` }>${html}</${appRootTag}>`;
  }
  return renderer;
});
const getSPARenderer = lazyCachedFunction(async () => {
  const manifest = await getClientManifest();
  const spaTemplate = await Promise.resolve().then(function () { return _virtual__spaTemplate; }).then((r) => r.template).catch(() => "");
  const options = {
    manifest,
    renderToString: () => `<${appRootTag}${` id="${appRootId}"` }>${spaTemplate}</${appRootTag}>`,
    buildAssetsURL
  };
  const renderer = createRenderer(() => () => {
  }, options);
  const result = await renderer.renderToString({});
  const renderToString = (ssrContext) => {
    const config = useRuntimeConfig();
    ssrContext.modules = ssrContext.modules || /* @__PURE__ */ new Set();
    ssrContext.payload = {
      _errors: {},
      serverRendered: false,
      data: {},
      state: {}
    };
    ssrContext.config = {
      public: config.public,
      app: config.app
    };
    return Promise.resolve(result);
  };
  return {
    rendererContext: renderer.rendererContext,
    renderToString
  };
});
const PAYLOAD_URL_RE = /\/_payload(\.[a-zA-Z0-9]+)?.json(\?.*)?$/ ;
const renderer = defineRenderHandler(async (event) => {
  const nitroApp = useNitroApp();
  const ssrError = event.path.startsWith("/__nuxt_error") ? getQuery$1(event) : null;
  if (ssrError && ssrError.statusCode) {
    ssrError.statusCode = parseInt(ssrError.statusCode);
  }
  if (ssrError && !("__unenv__" in event.node.req)) {
    throw createError({
      statusCode: 404,
      statusMessage: "Page Not Found: /__nuxt_error"
    });
  }
  const islandContext = void 0;
  let url = ssrError?.url || islandContext?.url || event.path;
  const isRenderingPayload = PAYLOAD_URL_RE.test(url) && !islandContext;
  if (isRenderingPayload) {
    url = url.substring(0, url.lastIndexOf("/")) || "/";
    event._path = url;
    event.node.req.url = url;
  }
  const routeOptions = getRouteRules(event);
  const head = createServerHead({
    plugins: unheadPlugins
  });
  const headEntryOptions = { mode: "server" };
  head.push(appHead, headEntryOptions);
  const ssrContext = {
    url,
    event,
    runtimeConfig: useRuntimeConfig(),
    noSSR: event.context.nuxt?.noSSR || routeOptions.ssr === false && !islandContext || (false),
    head,
    error: !!ssrError,
    nuxt: void 0,
    /* NuxtApp */
    payload: ssrError ? { error: ssrError } : {},
    _payloadReducers: {},
    islandContext
  };
  const renderer = ssrContext.noSSR ? await getSPARenderer() : await getSSRRenderer();
  const _rendered = await renderer.renderToString(ssrContext).catch(async (error) => {
    if (ssrContext._renderResponse && error.message === "skipping render") {
      return {};
    }
    const _err = !ssrError && ssrContext.payload?.error || error;
    await ssrContext.nuxt?.hooks.callHook("app:error", _err);
    throw _err;
  });
  await ssrContext.nuxt?.hooks.callHook("app:rendered", { ssrContext, renderResult: _rendered });
  if (ssrContext._renderResponse) {
    return ssrContext._renderResponse;
  }
  if (ssrContext.payload?.error && !ssrError) {
    throw ssrContext.payload.error;
  }
  if (isRenderingPayload) {
    const response2 = renderPayloadResponse(ssrContext);
    return response2;
  }
  const inlinedStyles = Boolean(islandContext) ? await renderInlineStyles(ssrContext.modules ?? ssrContext._registeredComponents ?? []) : [];
  const NO_SCRIPTS = routeOptions.experimentalNoScripts;
  const { styles, scripts } = getRequestDependencies(ssrContext, renderer.rendererContext);
  head.push({ style: inlinedStyles });
  head.push({
    link: Object.values(styles).map(
      (resource) => ({ rel: "stylesheet", href: renderer.rendererContext.buildAssetsURL(resource.file) })
    )
  }, headEntryOptions);
  if (!NO_SCRIPTS) {
    head.push({
      link: getPreloadLinks(ssrContext, renderer.rendererContext)
    }, headEntryOptions);
    head.push({
      link: getPrefetchLinks(ssrContext, renderer.rendererContext)
    }, headEntryOptions);
    head.push({
      script: renderPayloadJsonScript({ id: "__NUXT_DATA__", ssrContext, data: ssrContext.payload }) 
    }, {
      ...headEntryOptions,
      // this should come before another end of body scripts
      tagPosition: "bodyClose",
      tagPriority: "high"
    });
  }
  if (!routeOptions.experimentalNoScripts) {
    head.push({
      script: Object.values(scripts).map((resource) => ({
        type: resource.module ? "module" : null,
        src: renderer.rendererContext.buildAssetsURL(resource.file),
        defer: resource.module ? null : true,
        crossorigin: ""
      }))
    }, headEntryOptions);
  }
  const { headTags, bodyTags, bodyTagsOpen, htmlAttrs, bodyAttrs } = await renderSSRHead(head);
  const htmlContext = {
    island: Boolean(islandContext),
    htmlAttrs: [htmlAttrs],
    head: normalizeChunks([headTags, ssrContext.styles]),
    bodyAttrs: [bodyAttrs],
    bodyPrepend: normalizeChunks([bodyTagsOpen, ssrContext.teleports?.body]),
    body: [_rendered.html],
    bodyAppend: [bodyTags]
  };
  await nitroApp.hooks.callHook("render:html", htmlContext, { event });
  const response = {
    body: renderHTMLDocument(htmlContext),
    statusCode: getResponseStatus(event),
    statusMessage: getResponseStatusText(event),
    headers: {
      "content-type": "text/html;charset=utf-8",
      "x-powered-by": "Nuxt"
    }
  };
  return response;
});
function lazyCachedFunction(fn) {
  let res = null;
  return () => {
    if (res === null) {
      res = fn().catch((err) => {
        res = null;
        throw err;
      });
    }
    return res;
  };
}
function normalizeChunks(chunks) {
  return chunks.filter(Boolean).map((i) => i.trim());
}
function joinTags(tags) {
  return tags.join("");
}
function joinAttrs(chunks) {
  return chunks.join(" ");
}
function renderHTMLDocument(html) {
  return `<!DOCTYPE html>
<html ${joinAttrs(html.htmlAttrs)}>
<head>${joinTags(html.head)}</head>
<body ${joinAttrs(html.bodyAttrs)}>${joinTags(html.bodyPrepend)}${joinTags(html.body)}${joinTags(html.bodyAppend)}</body>
</html>`;
}
async function renderInlineStyles(usedModules) {
  const styleMap = await getSSRStyles();
  const inlinedStyles = /* @__PURE__ */ new Set();
  for (const mod of usedModules) {
    if (mod in styleMap) {
      for (const style of await styleMap[mod]()) {
        inlinedStyles.add(style);
      }
    }
  }
  return Array.from(inlinedStyles).map((style) => ({ innerHTML: style }));
}
function renderPayloadResponse(ssrContext) {
  return {
    body: stringify(splitPayload(ssrContext).payload, ssrContext._payloadReducers) ,
    statusCode: getResponseStatus(ssrContext.event),
    statusMessage: getResponseStatusText(ssrContext.event),
    headers: {
      "content-type": "application/json;charset=utf-8" ,
      "x-powered-by": "Nuxt"
    }
  };
}
function renderPayloadJsonScript(opts) {
  const contents = opts.data ? stringify(opts.data, opts.ssrContext._payloadReducers) : "";
  const payload = {
    type: "application/json",
    id: opts.id,
    innerHTML: contents,
    "data-ssr": !(opts.ssrContext.noSSR)
  };
  if (opts.src) {
    payload["data-src"] = opts.src;
  }
  return [
    payload,
    {
      innerHTML: `window.__NUXT__={};window.__NUXT__.config=${uneval(opts.ssrContext.config)}`
    }
  ];
}
function splitPayload(ssrContext) {
  const { data, prerenderedAt, ...initial } = ssrContext.payload;
  return {
    initial: { ...initial, prerenderedAt },
    payload: { data, prerenderedAt }
  };
}

const renderer$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: renderer
});

const styles = {};

const styles$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: styles
});

const template = "<div class=\"kkc-loading2\">\r\n    <img class=\"kkc-loading2__img\" src=\"https://oss.kuke99.com/kukecloud/static/loading.png\" height=\"44\" width=\"44\">\r\n    <div class=\"kkc-loading2__con\">\r\n        加载中\r\n    </div>\r\n</div>\r\n<style>\r\n.kkc-loading2 {\r\n    position: fixed;\r\n    background: rgba(0, 0, 0, 0.7);\r\n    border-radius: 10px;\r\n    box-sizing: border-box;\r\n    top: 50%;\r\n    left: 50%;\r\n    transform: translate(-50%);\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    flex-direction: column;\r\n    font-size: 14PX;\r\n    width: 100PX;\r\n    height: 100PX;\r\n}\r\n\r\n.kkc-loading2__img {\r\n    position: relative; \r\n}\r\n\r\n.kkc-loading2__con {\r\n    margin-top: 5PX;\r\n    color: #ccc;\r\n}\r\n</style>";

const _virtual__spaTemplate = /*#__PURE__*/Object.freeze({
  __proto__: null,
  template: template
});
//# sourceMappingURL=index.mjs.map
