<script setup lang="ts">
import { TradeTypeEnum } from '../type'
import type { RegisterContent } from '~/apis/invite/types'
const { inviteApi } = useApi()
// 展示弹框
const show = ref<boolean>(false)
// 展示地址选择弹框
const showArea = ref<boolean>(false)
interface Props {
  detailData: any,
  // operateType:string,
  // payload:any,
}
const props = withDefaults(defineProps<Props>(), {
  detailData: () => {},
  // operateType: 'add',
  // payload: () => {},
})

const emit = defineEmits<{
  (e: 'ok'): void
  (e: 'openRecord'): void
}>()

const close = () => {
  emit('ok')
  /**
   * 当需要打开领取记录弹框时触发 'openRecord' 事件
   * 不带任何参数，事件触发后会进行相应的操作
   */
  if (types.value !== 'add') {
    emit('openRecord')
  }
  // 重置表单
  resetData()
}

// const confirm = () => {
//   emit('close')
//   emit('confirm')
// }

const formRef = ref()

const route = useRoute()

/**
 * 活动id
 */
const id :string = route.params.id as string
console.log('🚀 ~ id:', id)
/**
 * `formData` 用于存储表单数据的对象
 *
 * @property {string} nameContent - 用户的姓名
 * @property {string} mobileContent - 用户的联系方式
 * @property {string} addressDetail - 详细的地址信息，
 * @property {string} ads - 省市县存储
 * @property {string} provinceId - 所在省份的ID，用于与省份数据匹配
 * @property {string} cityId - 所在城市的ID，用于与城市数据匹配
 * @property {string} countyId - 所在区县的ID，用于与区县数据匹配
 * @property {string} regionName - 地区的名称，通常由省、市、区县组成的完整地区名称
 * @property {RegisterContent[]} goodsInformationFillList - 动态表单
 */
const formData = ref({
  // 姓名
  nameContent: '',
  // 手机号码
  mobileContent: '',
  // 地址的详细信息
  addressDetail: '',
  // 地址组件
  ads: '',
  // 省份ID
  provinceId: '',
  // 城市ID
  cityId: '',
  // 区县ID
  countyId: '',
  // 地区名称
  regionName: '',
  // 动态表单
  goodsInformationFillList: [] as RegisterContent[]
})
// 省市县中文名字
const fieldValue = ref('')

/**
 * `formRules` 验证规则
 * 每个字段包含一个数组，数组内的每个元素定义了一个验证规则。
 *
 * @property {Array<{type: string, message: string}>} addressDetail - 地址字段的验证规则
 * @property {Array<{type: string, message: string}>} nameContent - 姓名字段的验证规则
 * @property {Array<{type: string, message: string}>} mobileContent - 手机号字段的验证规则
 * @property {Array<{type: string, message: string}>} ads - 所在地区字段的验证规则
 */
const formRules = {
  addressDetail: [{
    type: 'required',
    message: '请输入联系地址'
  }],
  nameContent: [{
    type: 'required',
    message: '请输入姓名'
  }],
  mobileContent: [{
    type: 'required',
    message: '请输入手机号'
  }, {
    type: 'phone',
    message: '手机号不正确'
  }],
  ads: [{
    type: 'required',
    message: '请选择所在地区'
  }],
}
// 防止连点
let flag = true
// 提交收获地址
const sumbit = async () => {
  formRef.value.validate(async (valid: boolean) => {
    if (!valid || !flag) { return false }
    flag = false
    try {
      if (types.value === 'add') {
        // 新增
        add()
      } else {
        // 编辑
        edit()
      }
    } catch (error) {
      flag = true
      console.log(error)
    }

    // confirm()
  })
}
const add = async () => {
  const params = {
    id,
    goodsId: details.value.id,
    nameContent: formData.value.nameContent,
    mobileContent: formData.value.mobileContent,
    provinceId: formData.value.provinceId,
    cityId: formData.value.cityId,
    countyId: formData.value.countyId,
    addressDetail: formData.value.addressDetail,
    regionName: fieldValue.value,
    registerContentList: formData.value.goodsInformationFillList,
  }
  const { data } = await inviteApi.receiveWelfare(params)
  flag = true
  if (!data.value) {
    return
  }
  // 根据不同的商品类型，弹出不同的提示信息
  if (details.value.tradeType === TradeTypeEnum.Coupon) {
    Message('领取成功，请在我的卡券中查看', '', 4000)
  } else if (details.value.tradeType === TradeTypeEnum.Product && details.value.course) {
    Message('领取成功，请在学习中心查看', '', 4000)
  } else {
    Message('领取成功')
  }

  // if (details.value.tradeType === TradeTypeEnum.CustomProduct) {
  //   Message('领取成功')
  // }
  // if (details.value.tradeType === TradeTypeEnum.Product) {
  //   Message('领取成功，福利已发放，请在我的-我的已购中查看福利')
  // }
  // if (details.value.tradeType === TradeTypeEnum.Coupon) {
  //   Message('领取成功，福利已发放，请在我的-我的卡券中查看福利')
  // }
  nextTick(() => {
    show.value = false
    close()
  })
}
const edit = async () => {
  const params = {
    recordId,
    nameContent: formData.value.nameContent,
    mobileContent: formData.value.mobileContent,
    provinceId: formData.value.provinceId,
    cityId: formData.value.cityId,
    countyId: formData.value.countyId,
    addressDetail: formData.value.addressDetail,
    regionName: fieldValue.value,
    registerContentList: formData.value.goodsInformationFillList,
  }
  const { data } = await inviteApi.bindShippingAddress(params)
  flag = true
  if (!data.value) {
    return
  }
  Message('修改成功')
  nextTick(() => {
    show.value = false
    close()
  })
}
/**
 * 收货地址信息数据
 */
// const shippingAddressInfo = ref({
//   popupRemark: '',
// })
// 地区
const options = ref([])
const customFieldName = {
  text: 'areaName',
  value: 'id',
  children: 'children',
}
const areaRef = ref(null)
// const loading = ref(true)

const getArea = async () => {
  // 地区状态：1-启用 0-禁用 不传查全部
  const { data } = await getAreaList({ level: 3, status: 1 })
  // options.value = recursion(data.value?.list) // data.value?.list
  options.value = data.value?.list
}
const selectArea = () => {
  // 已发货点击不展示弹框
  if (types.value !== 'add' && details.value?.deliveryStatus > 1) {
    return
  }
  showArea.value = true
}
const onConfirm = ({ selectedOptions }) => {
  showArea.value = false
  fieldValue.value = selectedOptions.map(obj => obj.areaName).join('')
  formData.value.provinceId = selectedOptions[0]?.id || ''
  formData.value.cityId = selectedOptions?.[1]?.id || ''
  formData.value.countyId = selectedOptions?.[2]?.id || ''
}
const resetData = () => {
  formData.value.nameContent = ''
  formData.value.mobileContent = ''
  formData.value.addressDetail = ''
  formData.value.ads = ''
  formData.value.provinceId = ''
  formData.value.cityId = ''
  formData.value.countyId = ''
  formData.value.regionName = ''
  formData.value.goodsInformationFillList = []
  fieldValue.value = ''
}
/**
 * 获取收货地址
 */
const showForm = ref(false)
const getShippingAddressInfo = async (id:string) => {
  // loading.value = true
  const { data } = await inviteApi.getShippingAddressInfo({ recordId: id })
  console.log(data.value)
  // loading.value = false
  details.value = { ...data.value }
  const { nameContent, mobileContent, addressDetail, countyId, cityId, provinceId, regionName, registerContentList } = data.value
  formData.value = {
    ...formData.value, // 保留原有值，防止覆盖其他字段
    nameContent: nameContent || '',
    mobileContent: mobileContent || '',
    addressDetail: addressDetail || '',
    provinceId: provinceId || '',
    cityId: cityId || '',
    countyId: countyId || '',
    regionName: regionName || '',
    ads: countyId || cityId || provinceId || '',
    goodsInformationFillList: registerContentList || []
  }

  fieldValue.value = regionName || ''
  nextTick(() => {
    showForm.value = true
  })
}

// watch(
//   () => show.value,
//   (newlVal) => {
//     if (!newlVal) { close() }
//   }
// )
const copyAccount = (num:any, event:any) => {
  handleClipboard(num, event)
}
// onMounted(() => {
//   // getShippingAddressInfo()
// })
/**
 * 需要填写的表单信息
 */
const details:any = ref(null)
/**
 * 标题
 */
const diaTitle = ref('')
/**
 * 区分新增还是编辑
 */
const types = ref('')
/**
 * 记录id 编辑传参使用
 */
let recordId = ''
/**
 * 显示弹窗并根据类型和详细信息设置内容
 *
 * @param titles - 弹窗的标题
 * @param type - 弹窗的类型
 * @param detail - 详细信息，新增时使用商品内置的需要填写的信息，编辑根据记录id获取接口
 */
const showDia = (titles:string, type:string, detail:any) => {
  // 设置弹窗的类型
  types.value = type
  // 设置弹窗的标题
  diaTitle.value = titles
  // 根据弹窗类型判断显示内容
  if (type === 'add') {
    // 重置数据
    resetData()
    showForm.value = true
    details.value = { ...detail }
    // 动态表单信息
    formData.value.goodsInformationFillList = [...detail.goodsInformationFillList]
  } else {
    recordId = detail
    // 根据记录id获取接口
    getShippingAddressInfo(detail)
  }
  // 获取地区信息
  getArea()
  show.value = true
  // console.log(88, details.value.goodsInformationFillList)
}
/**
 * 动态表单下拉框展示隐藏
 */
const showPicker = ref(false)
/**
 * 动态表单下拉框枚举
 */
const columns = ref()
/**
 * 动态表单下拉框默认选中处理
 */
const defaultChioce = ref()

/**
 * 动态表单下拉框选择后
 */
const onConfirmSelect = ({ selectedValues }) => {
  console.log(selectedValues, 66)
  showPicker.value = false
  columns.value.fieldContent = selectedValues[0]
}
const showBottom = (item) => {
  // 已发货点击不展示弹框
  if (types.value !== 'add' && details.value?.deliveryStatus > 1) {
    return
  }
  columns.value = item
  const { fieldContent, options } = item
  showPicker.value = true
  nextTick(() => {
    defaultChioce.value = fieldContent ? [fieldContent] : [options?.[0].optionName]
    console.log(fieldContent, options, 11)
    console.log(defaultChioce.value, 12)
  })
}
const customSelectName = {
  text: 'optionName',
  value: 'optionName',
  children: 'children',
}
defineExpose({ showDia })
</script>

<template>
  <div v-if="show" class="invite-enter-form">
    <MaskDialog
      v-model="show"
      custom-class="activity-rule"
      :title="diaTitle"
      minheight="200px"
      maxheight="664px"
      maxheight-content="480px"
      @closeDia="close"
    >
      <div class="explain">
        {{ props?.detailData?.popUpExplain }}
      </div>
      <div v-if="showForm">
        <KKCForm ref="formRef">
          <KKCValidate
            v-if="details?.name"
            v-model="formData.nameContent"
            label-width="1.3rem"
            :rules="formRules.nameContent"
            placeholder="请输入姓名"
            type="text"
            maxlength="10"
            label="姓名"
            :disabled="types!=='add' && details?.deliveryStatus>1"
          />

          <!-- :disabled="shippingAddressInfo?.userIsFillIn?true:false" -->
          <KKCValidate
            v-if="details?.mobile"
            v-model="formData.mobileContent"
            :rules="formRules.mobileContent"
            maxlength="11"
            type="text"
            oninput="value = value.replace(/[^\d]/g, '')"
            placeholder="请输入电话"
            label="电话"
            label-width="1.3rem"
            :disabled="types!=='add' && details?.deliveryStatus>1"
          />

          <KKCValidate
            v-if="details?.address"
            ref="areaRef"
            v-model="formData.ads"
            :rules="formRules.ads"
            type="text"
            label="所在地区"
            :is-area="true"
            label-width="1.3rem"
          >
            <van-field
              v-model="fieldValue"
              is-link
              readonly
              placeholder="请选择所在地区"
              :disabled="types!=='add' && details?.deliveryStatus>1"
              @click="selectArea()"
            />
          </KKCValidate>
          <!-- :disabled="shippingAddressInfo?.userIsFillIn?true:false" -->
          <KKCValidate
            v-if="details?.address"
            v-model="formData.addressDetail"
            :rules="formRules.addressDetail"
            placeholder="请输入地址"
            :maxlength="100"
            type="text"
            label="地址"
            label-width="1.3rem"
            :disabled="types!=='add' && details?.deliveryStatus>1"
          />
          <template
            v-for="(item,index) in formData?.goodsInformationFillList"
            :key="index"
          >
            <KKCValidate
              v-if="item.optFor"
              v-model="item.fieldContent"
              :rules="item.isMust?[{
                type: 'required', message: '必填'}]:[]"
              :placeholder="`请${item.fieldType===1?'输入':'选择'}${item.fieldName}`"
              :maxlength="50"
              label-width="1.3rem"
              :is-area="item.fieldType===2"
              type="text"
              :label="item.fieldName"
              :disabled="types!=='add' && details?.deliveryStatus>1"
            >
              <van-field
                v-model="item.fieldContent"
                is-link
                readonly
                :placeholder="`请选择${item.fieldName}`"
                :disabled="types!=='add' && details?.deliveryStatus>1"
                @click="showBottom(item)"
              />
            </KKCValidate>
          </template>

          <KKCValidate
            v-if="details?.courierNumber"
            type="text"
            label="物流单号"
            label-width="1.3rem"
            :is-area="true"
          >
            <div class="flex items-center h-[64px]">
              <div class="text-[#999] w-[250px] inline-block relative">
                <span class="block pr-[15px] text-nowrap text-ellipsis overflow-hidden">{{ details?.courierNumber }}</span>
              </div>
              <span class="w-[1px] h-[24px] bg-[#999] inline-block mx-[18px] relative" />
              <span class="text-[#EB2330]" @click="copyAccount(details?.courierNumber,$event)">复制</span>
            </div>
          </KKCValidate>
        </KKCForm>
      </div>
      <div
        v-if="!(types!=='add' && details?.deliveryStatus>1)"
        class="submit-btn"
      >
        <img src="~/assets/images/invite/btn_submit.png" alt="无法加载" @click="sumbit">
      </div>
    </MaskDialog>
    <!--  地区级联 -->
    <van-popup v-model:show="showArea" round position="bottom" style="z-index:99999">
      <van-cascader
        v-model="formData.ads"
        title="请选择所在地区"
        :options="options"
        :field-names="customFieldName"
        @close="showArea = false"
        @finish="onConfirm"
      />
    </van-popup>

    <!--  动态表单下拉框 -->
    <van-popup v-model:show="showPicker" round position="bottom" style="z-index:99999">
      <van-picker
        v-model="defaultChioce"
        :columns="columns.options"
        :columns-field-names="customSelectName"
        @cancel="showPicker = false"
        @confirm="onConfirmSelect"
      />
    </van-popup>
  </div>
</template>

<style scoped lang="scss">

.invite-enter-form {
  .van-cell{
    background:none;
    padding:.1rem;
    font-size:.24rem;
  }
  .invite-select-form{
    width:100%;
    height:100%;
    background:#f7f7f7;
  }
  :deep(.form-item){
   align-items: center;
  }
  :deep(.form-label ){
    line-height: 36px !important;
  }
  :deep(.van-field__control:disabled){
    color:#999;
    -webkit-text-fill-color: #999;
  }
}
.submit-btn{
  @apply flex justify-center;
  img{
    @apply w-[394px] rounded-[120px] opacity-100 mt-[30px];
    background: linear-gradient(180deg, rgba(255,255,255,0.4) 0%, rgba(255,255,255,0) 100%);
  }
  .hideBtn{
    display:none;
  }
}

:deep(.activity-rule) {
  .mask {
    background: url("~/assets/images/invite/bg.png") no-repeat;
    background-size: 100%;
  }

  .explain{
    text-align: left;
    margin-bottom: 30px
  }

  .mask-content{
    padding: 34px !important;
  }
}

:deep(.write-info) {
  .form-label {
    @apply text-[24px] font-extrabold text-[#666666];
    font-family: PingFang SC-Medium, PingFang SC;
    line-height: 36px;
  }
  .form-input{
    @apply h-[66px] opacity-100;
  }
  .mask-content{
    padding: 0
  }
}

</style>
