// 收银台

export enum ApisCommon {
  /* 获取商品标题 */
  getGoodsTitleByIds = '/kukegoods/studentService/getGoodsTitleByIds',
  /* 行为记录枚举值 */
  getBehaviorRecordEnum = '/kukecluedispatch/kcdBehaviorClassification/pullBehaviorClassificationListOfTenantProt',
  /* 新增行为记录 */
  addBehaviorRecor = '/kukecluedispatch/KcdBehaviorRecord/addBehaviorRecordProt',
  /* 查询租户配置支付信息 */
  getPaymentConfig = '/kukebasesystem/payment/ksmerchant/isExistConfig',
  /* 获取安卓下载链接 */
  getAndroidLink = '/kukebasesystem/versionRelease/getAndroidProductVersionLink',
  /* 获取商品详情页的长链接 */
  getLongUrl = 'kukeopen/koShortCodeMap/getLongUrl',
  /* 购物车下单行为记录 */
  fetchAddOrdersBehaviorRecord = '/kukecluedispatch/KcdBehaviorRecord/addOrderBehaviorRecordProt',
  fetchGetEnumList = '/kukesystem/enum/enumList',
}
