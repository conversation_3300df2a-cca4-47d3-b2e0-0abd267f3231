<template>
  <Teleport to="body">
    <div class="homework-list-mask">
      <div class="homework-list">
        <template v-if="loading">
          <div class="p-[24px] py-[48px]">
            <!-- <el-skeleton :rows="7" animated style="margin-top: 8px;" /> -->
            <van-skeleton title :row="8" />
          </div>
        </template>
        <template v-else>
          <div class="homework-list-header">
            作业列表
            <span class="homework-list-close" @click="handleClose">×</span>
          </div>
          <div class="homework-list-body">
            <div v-for="(item, idx) in list" :key="idx" class="kefu-radio-item" @click="handleAction($event, item)">
              <div class="flex-1">
                <!-- <div
                  v-if="item.resourceType === 2"
                  class="line-clamp-2"
                  @click.stop="handlePlay(item)"
                >
                  <span class="">
                    {{ item.fileName }}
                  </span>
                </div>
                v-else-if="item.resourceType === 1" -->
                <div class="break-all">
                  <span class="text-[28px] text-[#111111] leading-[34px]">
                    {{ item.fileName }}
                  </span>
                </div>
                <div class="flex items-center mt-[12px]">
                  <template v-if="![1, 2].includes(item.videoType)">
                    <span class="flex items-center text-[24px] text-[#666666] leading-[30px]">
                      <KKCIcon name="icon-deta_icon_wenjian" :size="24" class="mr-[8px]" />
                      <span>
                        文件大小：
                      </span>
                      <span>
                        {{ item.fileSizeFormat || item.fileSize }}
                      </span>
                    </span>
                    <span class="flex items-center ml-[24px] text-[24px] text-[#666666] leading-[30px]">
                      <span>
                        格式：
                      </span>
                      <span>
                        {{ item.fileType || item.fileExt }}
                      </span>
                    </span>
                    <span
                      class="inline-flex items-center justify-center ml-[24px] text-[24px] text-[#eb2330] border border-[#eb2330] rounded-[12px] px-[16px] h-[48px] leading-[48px]"
                      @click.stop="handleDownload(item)"
                    >
                      下载
                    </span>
                  </template>
                  <template v-else>
                    <span class="flex items-center text-[24px] text-[#666666] leading-[30px]">
                      <KKCIcon name="icon-xiangqing-shijian" :size="24" class="mr-[8px]" />
                      <span>
                        <!-- 文件大小： -->
                      </span>
                      <span>
                        {{ item.durationFormat }}
                      </span>
                    </span>
                    <span class="flex items-center ml-[24px] text-[24px] text-[#666666] leading-[30px]">
                      <span>
                        {{ item.learnStatusName }}
                      </span>
                      <span class="ml-[8px]">
                        {{ item.studyProgressName }}
                      </span>
                    </span>
                  </template>
                </div>
                <!-- <div
                  v-else-if="item.resourceType === 2"
                  class="flex items-center"
                  @click.stop="handlePlay(item)"
                >
                  <span class="flex items-center text-[14px] text-[#666666] leading-[22px]">
                    <KKCIcon name="icon-xiangqing-shijian" :size="16" class="mr-[4px]" />
                    <span>
                      时长：
                    </span>
                    <span>
                      {{ item.durationFormat }}
                    </span>
                  </span>
                  <span class="flex items-center ml-[24px] text-[14px] text-[#666666] leading-[22px]">
                    <span>
                      {{ item.learnStatusName }}
                    </span>
                    <span>
                      {{ item.studyProgressName }}
                    </span>
                  </span>
                </div> -->
              </div>
              <div class="flex flex-shrink-0 cursor-pointer">
                <template v-if="unlocked === false">
                  <KKCIcon name="icon-suo" color="#C5CAD5" :size="40" />
                </template>
                <template v-else-if="[1, 2].includes(item.videoType)">
                  <div>
                    <!-- <span @click.stop="handlePlay(item)">
                      播放/暂停
                    </span> -->
                    <KKCIcon name="icon-xiangqing-bofang" color="#C5CAD5" :size="40" @click.stop="handlePlay(item)" />
                    <!-- <KKCIcon
                      name="icon-xiangqing-zanting"
                      :size="26"
                      @click.stop="handlePlay(item)"
                    /> -->
                    <!-- <KKCIcon
                      name="icon-xuexizhongxin-xiazai"
                      color="#C5CAD5"
                      :size="40"
                      class="ml-[16px]"
                      @click.stop="handleDownload(item)"
                    /> -->
                  </div>
                </template>
                <template v-else>
                  <KKCIcon name="icon-chakan" color="#C5CAD5" :size="40" @click.stop="handlePreview(item)" />
                  <!-- <KKCIcon
                    name="icon-xuexizhongxin-xiazai"
                    color="#C5CAD5"
                    :size="40"
                    class="ml-[16px]"
                    @click.stop="handleDownload(item)"
                  /> -->
                </template>
              </div>
            </div>
          </div>
          <!-- <div class="homework-list-footer">
            <button class="kefu-confirm-btn" @click="handleConfirm">
              确定
            </button>
          </div> -->
        </template>
      </div>
      <!-- 视频播放器 --- 弹窗形式 - m端 -->
      <KkVideoPlayer
        v-model:show="showVideo"
        :node-id="videoNodeId"
        :goods-master-id="(goodsMasterId as string)"
        :type="liveType"
        :live-org-id="liveOrgId"
        :try-listen-id="tryListenId"
        :teaser-show="teaserShow"
        :play-by-video-id="videoId"
        @playResume="onPlayResume"
        @playPause="onPlayPause"
      />
      <!-- 购买弹框 -->
      <Teleport to="body">
        <LearnBuyDialog
          ref="kkPopupRef"
          :select-goods="selectGoods"
          :goods-master-id="(goodsMasterId as string)"
          :specs-id="specsId"
        />
      </Teleport>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { events } from '@kukefe/kkutils'
import { ref, onMounted } from 'vue'
import { Base64 } from 'js-base64'
import { LearnBuyDialog } from '~/pages/learn-center/components/index'
import type { FilesListPageVO, FilesListVO } from '~/types/homework-files'
const { getViewUrl } = useFileView()
const route = useRoute()
const {
  id: goodsMasterId,
  tryListenId,
  // packageId = '',
  specsId = ''
} = route.query
const props = defineProps({
  unlocked: {
    type: Boolean,
    required: false
  },
  jieduanId: {
    type: String,
    required: true
  },
  outerRelationId: {
    type: String,
    required: true
  },
  classStartTime: {
    type: String,
    required: true
  },
})
const list = ref<FilesListVO[]>([])
const count = ref(0)
const loading = ref(false)
// const selectedId = ref<number | null>(null)
// const route = useRoute()
// const router = useRouter()
// 注入父组件提供的方法
const resetVideoId = inject('resetVideoId')
// 模拟接口请求
async function getList () {
  loading.value = true
  Loading(true)
  const { data } = await useHttp<FilesListPageVO>('/kukestudentservice/wap/kssFiles/listPageProt', {
    body: {
      outerRelationType: 1,
      outerRelationId: props.outerRelationId,
      page: 1,
      pageSize: 80,
    },
    transform: input => input.data,
    default: () => {
      return {
        list: [],
        count: 0
      }
    }
  })
  const { list: _list, count: _count } = data.value || {}
  list.value = _list?.map((v) => {
    // if (['mp4'].includes(v.fileExt)) {
    //   v._fileType = 'video'
    // } else if (['mp3'].includes(v.fileExt)) {
    //   v._fileType = 'audio'
    // }
    v._jieduanId = props.jieduanId

    return v
  })
  count.value = _count || 0
  loading.value = false
  Loading(false)
}

onMounted(() => {
  getList()
  window.addEventListener('keydown', handleEscClose)
})
onBeforeUnmount(() => {
  window.removeEventListener('keydown', handleEscClose)
})

function handleEscClose (e: KeyboardEvent) {
  if (playVisbile.value) {
    return
  }
  if (e.key === 'Escape' || e.key === 'Esc') {
    handleClose()
  }
}

const emit = defineEmits(['close', 'confirm', 'closeVideo'])
const handleClose = () => {
  emit('close')
}
const selectGoods = ref<any>({})
const kkPopupRef = ref(null)
// 判断是否过期
const checkGoodsExpired = async () => {
  const { data } = await useHttp<any>(ApisLc.checkGoodsExpire, {
    transform: input => input.data,
    body: {
      goodsMasterId,
      tryListenId,
      ...(specsId && { goodsSpecificationItemId: specsId })
    },
    default: () => [],
  })
  return data
}
/**
 * 预览讲义
 * */
const handlePreview = async (item: FilesListVO) => {
  console.log('handlePreview', item)
  //
  if ([1, 2].includes(item.videoType)) {
    // Message('唤起保利微播放器')
    if (typeof resetVideoId === 'function') {
      resetVideoId()
    }
    handlePlay(item)
    return
  }
  if (['xls', 'xlsx'].includes(item.fileExt)) {
    Message('Excel文件不支持在线预览，可下载查看')
    return
  }
  if (!item.fileUrl) {
    Message('fileUrl不存在')
    return
  }
  const checkData = await checkGoodsExpired()
  const { status } = checkData.value
  if (status !== 1) {
    //
    return
  }
  // await uploadHandOut(item)
  const link = getViewUrl(
    `/onlinePreview?url=${encodeURIComponent(
      Base64.encode(item.convertUrl)
    )}`
  )
  window.open(link)
}
/**
 * 下载讲义
 * */
const handleDownload = async (item: FilesListVO) => {
  console.log('handleDownload', item)
  if (!item.fileUrl) {
    Message('fileUrl不存在')
    return
  }
  const checkData = await checkGoodsExpired()
  const { status } = checkData.value
  if (status !== 1) {
    return
  }
  const _classStartTime = props.classStartTime
  if (_classStartTime) {
    Message(`课程将于${_classStartTime}开课，敬请期待`)

    return
  }
  // unlocked 学习计划阶段是否解锁
  if (props.unlocked === false) {
    if (!item._jieduanId) {
      return
    }
    const { error, data } = await useHttp('/kukestudentservice/wap/studyPlan/unlockNewStudyPlanStage', {
      body: {
        id: item._jieduanId
      }
    })
    console.log('error: ', unref(error)?.data)
    if (unref(error)?.data?.code === '22031') {
      // 再接再厉 请先学完上个阶段的课时
      if (process.client) {
        events.publish('studyPlan:checkJieDuan', item, 1)
      }
      // return
    }
    if (unref(error)?.data?.code === '10001' && unref(error)?.data?.msg === '用户已解锁该学习计划阶段') {
      if (process.client) {
        events.publish('studyPlan:checkJieDuan', item, 2)
      }
    }
    if (unref(data)?.code === '10000') {
      // 恭喜解锁本学习阶段
      if (process.client) {
        events.publish('studyPlan:checkJieDuan', item, 2)
      }
      // return
    }

    return
  }
  // const fileType = item.fileType || item.fileExt
  // downloadFile(item.fileUrl, item.fileName.replace(fileType, ''))
  downloadFile(item.fileUrl, item.fileName)
}

const handleAction = async (e: Event, item: FilesListVO) => {
  // emits('action', item)
  const _classStartTime = props.classStartTime
  if (_classStartTime) {
    Message(`课程将于${_classStartTime}开课，敬请期待`)
    e.stopPropagation()
    e.preventDefault()
    return
  }
  // unlocked 学习计划阶段是否解锁
  if (props.unlocked === false) {
    if (!item._jieduanId) {
      return
    }
    const { error, data } = await useHttp('/kukestudentservice/wap/studyPlan/unlockNewStudyPlanStage', {
      body: {
        id: item._jieduanId
      }
    })
    console.log('error: ', unref(error)?.data)
    if (unref(error)?.data?.code === '22031') {
      // 再接再厉 请先学完上个阶段的课时
      if (process.client) {
        events.publish('studyPlan:checkJieDuan', item, 1)
      }
      // return
    }
    if (unref(error)?.data?.code === '10001' && unref(error)?.data?.msg === '用户已解锁该学习计划阶段') {
      if (process.client) {
        events.publish('studyPlan:checkJieDuan', item, 2)
      }
    }
    if (unref(data)?.code === '10000') {
      // 恭喜解锁本学习阶段
      if (process.client) {
        events.publish('studyPlan:checkJieDuan', item, 2)
      }
      // return
    }

    e.stopPropagation()
    e.preventDefault()
    return
  }
  handlePreview(item)
}

const videoId = ref('')
// 1 加载片头
// 0 不加载片头
const teaserShow = ref(1)
const showVideo = ref(false)
const videoNodeId = ref('')

const currentPlayVideoId = ref('')

// 播放
const onPlayResume = (vid: string) => {
  currentPlayVideoId.value = vid
}

// 录播
const onPlayPause = () => {
  currentPlayVideoId.value = ''
}

//
watch(
  () => showVideo.value,
  (val) => {
    console.log('val: ', val)
    if (!val) {
      currentPlayVideoId.value = ''
      teaserShow.value = 1
      getList()
      emit('closeVideo')
    }
  }
)
// 获取直播平台
const liveType = ref(1)
const liveOrgId = ref('')
async function getLivePlatformByWap_ () {
  const { type: platType, orgId: platOrgId } = await getLivePlatformByWap(goodsMasterId as string)
  liveType.value = platType
  liveOrgId.value = platOrgId
}
// const playFileType = ref('')
// const playFileUrl = ref('')
const playVisbile = ref(false)
const handlePlay = async (item:FilesListVO) => {
  console.log('[ handlePlay ] >', item)
  // playFileType.value = item._fileType
  playVisbile.value = true
  if (!item.goodsFileId) {
    Message('goodsFileId不存在')
  }
  // const checkData = await checkGoodsExpired()
  const [checkData] = await Promise.all([
    checkGoodsExpired(),
    getLivePlatformByWap_()
  ])

  const { status } = checkData.value
  if (status !== 1) {
    selectGoods.value = checkData.value
    kkPopupRef.value?.show()
    return
  }
  videoId.value = item.goodsFileId
  if (isAudio(item.fileExt)) {
    teaserShow.value = 0
  } else {
    teaserShow.value = 1
  }
  //
  showVideo.value = true
}
/**
 * 判断是否音频
 * @param {string} ext 文件后缀
 * */
const isAudio = (ext?: string) => {
  if (!ext) { return false }
  // 常见音频后缀
  return [
    'mp3', 'm4a', 'wav'
  ].includes(String(ext).toLowerCase())
}
</script>

<style scoped lang="scss">
.homework-list-mask {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: end;
  // justify-content: end;
  z-index: 1110;
}

.homework-list {
  background: #fff;
  border-top-left-radius: 24px;
  border-top-right-radius: 24px;
  width: 750px;
  // min-width: 640px;
  // max-width: 540px;
  margin: 0 auto;
  // height: 364px;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.13);
  padding: 0;
  display: flex;
  flex-direction: column;
}

.homework-list-header {
  font-size: 34px;
  font-weight: 500;
  padding: 0 24px 0 24px;
  height: 108px;
  line-height: 108px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #111111;
  font-family: PingFang SC, PingFang SC;
}

.homework-list-close {
  font-size: 40px;
  cursor: pointer;
  user-select: none;
}

.homework-list-body {
  // 兼容部分机型px没有转换成rem
  padding: 0 0.32rem 0 0.32rem;
  // height: calc(236px - 24px);
  // max-height: calc(236px - 24px);
  min-height: 40vh;
  max-height: 80vh;
  overflow-y: auto;
}

.kefu-radio-item {
  margin: 0 0 24px 0;
  display: flex;
}

//
</style>
