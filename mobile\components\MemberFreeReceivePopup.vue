<template>
  <!-- 圆角弹窗（底部） -->
  <van-popup
    v-model:show="isShow"
    round
    position="bottom"
    teleport="body"
    closeable
    :close-on-click-overlay="false"
    safe-area-inset-bottom
    class="MemberFreeReceivePopup"
  >
    <!-- :style="{ height: '30%' }" -->
    <div class="MemberFreeReceivePopup-wrapper">
      <div class="MemberFreeReceivePopup-title">
        <h4 class="title__tit">
          领取免费商品
        </h4>
        <small class="title__small">
          领取成功后，可在“我的-邮寄列表”查看商品发货进度
        </small>
      </div>
      <div class="MemberFreeReceivePopup-address">
        <!-- <button @click="showBottom2 = true">
          请添加收货地址
        </button> -->
        <!-- 收货地址 -->
        <!-- v-if="isDeliveryMethod" -->
        <AddressInfo
          class="mb-[24px]"
          :is-member-free-receive-popup="isMemberFreeReceivePopup"
          @click="handleChoseAddress"
          @select-change="getChangeAddress"
        />
      </div>
      <div class="MemberFreeReceivePopup-goods flex-1">
        <MemberFreeReceivePopupGoodsCard />
      </div>
      <div class="bg-white w-full pt-[12px] pb-[32px]">
        <div
          class="MemberFreeReceivePopup-submit"
          @click="submitFn"
        >
          立即领取
        </div>
      </div>
    </div>
  </van-popup>
</template>

<script setup lang="ts" name="MemberFreeReceivePopup">
// mobile\pages\shopping\buy-now\components\Address.vue
import AddressInfo from '@/pages/shopping/buy-now/components/Address.vue'
import { getItNowProt, type DeliveryInfos, type getItNowProtRequestData } from '~/apis/memberUser'
import { useCourseStore } from '~/stores/course.store'
// import { GoodsType } from '@/apis/course/types'

const route = useRoute()
const courseStore = useCourseStore()
console.log('[ courseStore ] >', courseStore.courseInfo)
const isMemberFreeReceivePopup = ref(true)
const isShow = ref(false)
// const showBottom2 = ref(false)
// const showBottom3 = ref(false)
type Props = {
  info?:any
  // 是否重新渲染
  isReload?: boolean
  memberGoodsId?: string
}
const props = withDefaults(defineProps<Props>(), {
  info: false,
  isReload: true,
  memberGoodsId: undefined
})
const emits = defineEmits<{
  reset: [id: string]
}>()

/*
// 是否是多规格商品
const isSpecGoods = computed(() => {
  return courseStore.courseInfo.goodsType === GoodsType.MultiSpecification
})
// 商品标题
const goodsTitle = computed(() => {
  const { goodsTitle } = courseStore.courseInfo
  if (isSpecGoods.value && !courseStore.isSku) {
    return courseStore?.spuInfo?.goodsTitle
  }
  return goodsTitle
})
// 商品标题
const includeContent = computed(() => {
  const { includeContent = [] } = courseStore.courseInfo

  return includeContent.join('')
})
// 商品图片
const goodsImg = computed(() => {
  if (isSpecGoods.value && !courseStore.isSku) {
    return courseStore.spuInfo?.goodsImg
  }
  return courseStore.courseInfo.goodsImg
}) */

/**
 * 点击立即领取按钮
若未填写收货地址，toast提示“请先填写收货地址”
若已填写收货地址，toast提示“领取成功”，关闭此弹窗，领取成功不生成订单，订单列表不显示
 *
*/
const submitFn = async () => {
  // TODO 会员
  // 用户需要填写地址，地址必填，收货地址功能和提交订单的收货地址逻辑一样
  // if (!setAddressInfo.value.id) {
  if (!setAddressInfo.value.cityId) {
    Message('请先填写收货地址')
    return
  }
  const { resourceTypes, cateIds, id } = courseStore.courseInfo

  const body:getItNowProtRequestData = {
    cateId: cateIds,
    // TODO
    goodsId: id,
    // goodsId: '735875016151552000',
    goodsType: 1,
    // TODO
    resourceType: resourceTypes,
    receiveSchoolId: courseStore?.schoolInfo?.schoolId, //
    memberGoodsId: props.memberGoodsId,
    deliveryInfos: setAddressInfo.value
  }

  const { error, data } = await getItNowProt(body)
  if (!error.value && data.value?.code === '10000') {
    Message('领取成功')
    if (props.isReload) {
      setTimeout(() => {
        location.reload()
      }, 500)
    }
    closePopup()
    emits('reset', id)
  }
}

// 展示弹框
const showPopup = () => isShow.value = true
const closePopup = () => {
  isShow.value = false
}
const handleChoseAddress = (type: string) => {
  if (type === 'add') {
    goAddress()
  }
}

defineExpose({
  showPopup,
  closePopup,
})

const goAddress = () => {
  navigateTo({
    path: '/user/address/address',
    query: {
      redirect_uri: encodeURIComponent(route.fullPath),
    },
  })
}
// 获取设置的地址信息
const setAddressInfo = ref<DeliveryInfos>({})
const getChangeAddress = async (data: any) => {
  console.log('[ getChangeAddress ] >', data)
  setAddressInfo.value = {
    ...data,
    addressDetail: data?.address,
  }
  // isSupportDelivery.value = await verifyIsSupporDelivery(data)
}
</script>

<style lang="scss">
.MemberFreeReceivePopup{
  width: 750px;
  margin: 0 auto;
  left: 0px;
    right: 0px;
    &-wrapper {
      // width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      // min-height: 1200px;
      min-height: 86vh;
      padding-top: 32px;
      // padding-bottom: 32px;
      background: linear-gradient( 180deg, #FFFFFF 0%, #F5F6F9 260px);
    }
    &-title {
      width: 100%;
      margin-bottom: 32px;

      // text-align: center;
      .title__tit{
        display: block;
        // width: 204px;
        height: 42px;
        // margin: 0 auto;
// font-family: PingFang SC, PingFang SC;
font-weight: 500;
font-size: 34px;
color: #111111;
line-height: 42px;
text-align: center;
font-style: normal;
text-transform: none;
      }
      .title__small{
        display: block;
        // width: 569px;
        // height: 30px;
        margin: 0 auto;
        margin-top: 8px;
        // font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 24px;
        color: #999999;
        line-height: 30px;
        text-align: center;
        // font-style: normal;
        // text-transform: none;

      }
    }
    &-address {
      width: 100%;
      padding: 0 24px;
      // padding-top: 32px;
    }
    &-goods {
      padding: 0 24px;
      width: 100%;
      margin-bottom: 32px;

    }
    &-submit {
width: 702px;
height: 88px;
background: var(--kkc-brand);
border-radius: 16px 16px 16px 16px;
margin: 0 auto;
text-align: center;

// width: 128px;
// height: 40px;
// font-family: PingFang SC, PingFang SC;
font-weight: 500;
font-size: 32px;
color: #FFFFFF;
line-height: 88px;
// text-align: left;
// font-style: normal;
// text-transform: none;

    }
}
</style>
