import { ActivityType } from './../../base/constants/index'
import type { ActivityRes } from '~/apis/course/activetyTypes'
import type { CourseDetailModel } from '@/apis/course/types'
import { useCourseStore } from '~/stores/course.store'

interface QueryInfo {
  id: string
  specsId: string
  activityType?: number
  activityId?: string
  buyAtMarkupId?: string
  teamId?: string
  isBargain ?: string
}
const handleMarketingInfo = (info: CourseDetailModel, marketingInfo: Ref<ActivityRes>, queryInfo?: QueryInfo) => {
  const courseStore = useCourseStore()

  // 营销活动的信息以后都放这里 涉及到详情购买
  const { activityId, activityStatus, activityType, groupProperty } = courseStore.marketingInfo
  const { btnStatus } = info
  // 购买跳转订单页面携带参数
  if (marketingInfo?.value?.isMarketing !== 0 && (btnStatus === BtnStatus.BuyNow || btnStatus === BtnStatus.ContinueBuy)) {
    if (activityStatus === 2 && queryInfo) {
      switch (activityType) {
        case ActivityType.Group:
          if (groupProperty?.isGroup !== 'originBuy') {
            queryInfo.teamId = groupProperty?.teamId
            queryInfo.activityType = activityType
            queryInfo.activityId = activityId
          }
          break
        case ActivityType.Seckill:
        case ActivityType.Distribution:
        case ActivityType.Bargin:
          queryInfo.activityType = activityType
          queryInfo.activityId = activityId
          queryInfo.isBargain = '1'
          break
      }
    }
  }

  return {
    activityId,
    activityStatus,
    activityType,
    queryInfo
  }
}
export default handleMarketingInfo
