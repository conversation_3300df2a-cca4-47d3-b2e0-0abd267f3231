<!-- eslint-disable vue/no-v-html -->
<template>
  <div :id="`ques-` + questionItem?.id" class="multi-option">
    <!-- 多选题 -->
    <div class="exam-quest-title">
      <span class="quest-title">
        {{ question.chineseNum ? `${question.chineseNum}、` : '' }}
        {{ questionTitle }}
      </span>
      <span v-if="(moduleType === 2 || moduleType === 0) && !isMaterial" class="explain" @click="onDescriptionClick">
        <span v-if="question.description">（{{ question.description }}）</span>&nbsp;
        <KKCIcon
          v-if="question.description?.length > 15"
          class="quest-title-right-icon"
          name="icon-wode-youjiantou"
          :size="24"
        />
      </span>
    </div>
    <div class="mt-[16px] mb-[48px] text-[#111] text-[32px] exam-html">
      <div
        v-img-preview="question.stem"
      >
        <KKCHtmlMathJax
          :content="useQuestSortStem(
            question.stem,
            question.sort,
            moduleType,
            question.score
          )"
        />
      </div>
      <KKCAudioPlayer
        v-if="question.stemSrc"
        class="bg-[#f8f8f8]"
        :icon-name="'icon-yinpinbofang'"
        :playing-icon="'icon-yinpinzanting'"
        :icon-size="48"
        :url="question.stemSrc"
      />
    </div>

    <div
      v-for="item in metas"
      :key="item.key"
      v-img-preview="item"
      class="exam-model-default"
      :class="selectCellClass(item.key)"
      @click="isEdit && selectControl(item.key)"
    >
      <KKCAudioPlayer
        v-if="item.valueSrc && item.value"
        :icon-name="'icon-yinpinbofang'"
        :playing-icon="'icon-yinpinzanting'"
        :icon-size="48"
        :url="item.valueSrc"
      />
      <!-- 做题模式 -->
      <div
        v-if="isEdit"
        :class="['flex', 'px-[24px]', 'items-center', (item.valueSrc && !item.value) ? 'py-[22px]' : 'py-[30px]']"
      >
        <!-- 练习模式 查看解析时也可以更换选项  -->
        <template v-if="model === 1 || model === 3 || model === 4">
          <span class="select-status" :class="handleExerciseClassName(item.key)">
            {{ item.key }}
          </span>
          <KKCHtmlMathJax v-if="item.value" class="pl-[24px] chioceValue exam-html" :content="item.value" />
          <KKCAudioPlayer
            v-if="item.valueSrc && !item.value"
            class="flex-1 !pr-0"
            :icon-name="'icon-yinpinbofang'"
            :playing-icon="'icon-yinpinzanting'"
            :icon-size="48"
            :url="item.valueSrc"
          />
        </template>
        <template v-else>
          <span class="select-status" :class="handleExamClassName(item.key)">
            {{ item.key }}
          </span>
          <KKCHtmlMathJax v-if="item.value" class="pl-[24px] chioceValue exam-html" :content="item.value" />
          <KKCAudioPlayer
            v-if="item.valueSrc && !item.value"
            class="flex-1 !pr-0"
            :icon-name="'icon-yinpinbofang'"
            :playing-icon="'icon-yinpinzanting'"
            :icon-size="48"
            :url="item.valueSrc"
          />
        </template>
      </div>
      <!-- 解析模式 -->
      <div
        v-else
        :class="['flex', 'px-[24px]', 'items-center', (item.valueSrc && !item.value) ? 'py-[22px]' : 'py-[30px]']"
      >
        <span class="select-status" :class="handleAnalyzeClassName(item.key)">
          {{ item.key }}
        </span>
        <KKCHtmlMathJax v-if="item.value" class="pl-[24px] chioceValue exam-html" :content="item.value" />
        <KKCAudioPlayer
          v-if="item.valueSrc && !item.value"
          class="flex-1 !pr-0"
          :icon-name="'icon-yinpinbofang'"
          :playing-icon="'icon-yinpinzanting'"
          :icon-size="48"
          :url="item.valueSrc"
        />
      </div>
    </div>
    <Teleport to="body">
      <QuestionBankPopupDescription
        v-if="description"
        :show="isShowDescription"
        :description="description"
        @ok="onDescriptionClick"
      />
    </Teleport>
  </div>
</template>

<script setup lang="ts">
const route = useRoute()
// 1每日一练,3 章节练习,2 试卷，处理数据逻辑不同
const moduleType = Number(route.query.moduleType)
const { track } = useTrackEvent()

const props = defineProps({
  question: {
    type: Object as PropType<any>,
    default: () => ({}),
  },
  // 选项是否可编辑（做题可编辑，解析不可编辑）
  isEdit: {
    type: Boolean,
    default: true,
  },
  // 练习模式 一题一页1  考试模式 通栏2
  model: {
    type: Number,
    default: 1,
  },
  isExpand: {
    type: Boolean,
    default: false
  },
  // true材料题，false 非材料题
  isMaterial: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits<{
  (e: 'change', arr: any, state: number): void;
}>()

const currentSelectArr = ref<string[]>([])
const questionItem = ref(props.question || {})
watch(
  () => props.question,
  (newVal) => {
    questionItem.value = newVal
    if (newVal.logAnswer && typeof newVal.logAnswer === 'string') {
      currentSelectArr.value = JSON.parse(newVal.logAnswer)
    } else if (!newVal.logAnswer) {
      currentSelectArr.value = []
    }
  },
  {
    immediate: true,
    deep: true,
  }
)

// 参考答案-正确答案
const trueAnswer = computed(() => {
  return JSON.parse(questionItem.value.answer)
})

// 我的答案
const logAnswer = computed(() => {
  if (questionItem.value.logAnswer) {
    if (typeof questionItem.value.logAnswer === 'string') {
      return JSON.parse(questionItem.value.logAnswer)
    } else {
      return questionItem.value.logAnswer
    }
  }
  return []
})

// 题目选项
const metas = computed(() => {
  return JSON.parse(questionItem.value.metas)
})

// 用户操作选择的题目数组
const queState = ref(0)
const selectControl = debounceFunc(
  (key: string) => {
    // 做题->选项 埋点
    track({
      category: '题库',
      action: '选项',
      label: `${key}`
    })
    // 背题模式不可做题
    if (props.model === 4) {
      return
    }
    // 点开过解析 不能在更改答案
    // 快答模式，已有答案，不可更改答案
    if ((props.model === 1 || props.model === 3) && questionItem.value.isAnalyze) {
      return
    }
    const index = currentSelectArr.value.indexOf(key)
    if (index !== -1) {
      currentSelectArr.value.splice(index, 1)
    } else {
      // 将 questionItem.value.logAnswer 存储在一个变量中以便后续使用
      const currentLogAnswer = questionItem.value.logAnswer
      const logAnswer = typeof currentLogAnswer === 'string' && currentLogAnswer.length ? JSON.parse(currentLogAnswer) : [...currentLogAnswer]
      // 检查是否存在 logAnswer
      if (logAnswer) {
        const index = logAnswer.indexOf(key)
        if (index !== -1) {
          logAnswer.splice(index, 1)
        } else {
          logAnswer.push(key)
        }
        currentSelectArr.value = logAnswer
      } else {
        // 如果不存在 logAnswer，直接将当前元素添加到数组中
        currentSelectArr.value.push(key)
      }
    }

    // 选择的答案和正确答案作对比
    if (currentSelectArr.value.length === 0) {
      queState.value = 5 // 没做
    } else if (arraysEqual(currentSelectArr.value, trueAnswer.value)) {
      queState.value = 3 // 做对
    } else {
      queState.value = 4 // 做错
    }
    // questionItem.value.logStatus = queState.value

    emit('change', currentSelectArr.value, queState.value)
  },
  200,
  true
)

// 判断两个数组中的字符串元素是否完全相同，不考虑元素的顺序
function arraysEqual (arr1: any, arr2: any) {
  if (arr1.length !== arr2.length) {
    return false
  }
  const sortedArr1 = arr1.slice().sort()
  const sortedArr2 = arr2.slice().sort()
  for (let i = 0; i < sortedArr1.length; i++) {
    if (sortedArr1[i] !== sortedArr2[i]) {
      return false
    }
  }
  return true
}

// 找出用户选择的错误项
const userWrongAnswer = computed(() => {
  let result
  if (props.model === 1) {
    // if (typeof logAnswer.value === 'string') {
    //   logAnswer.value = JSON.parse(logAnswer.value)
    // }
    result = logAnswer.value.length
      ? logAnswer.value.filter((item: any) => !trueAnswer.value.includes(item))
      : currentSelectArr.value.filter(
        (item: any) => !trueAnswer.value.includes(item)
      )
  } else {
    result = logAnswer.value.filter(
      (item: any) => !trueAnswer.value.includes(item)
    )
  }
  return result
})

const isKeyInArray = (array: Set<string>, key: string) => array.has(key)
const isCorrect = (key: string) => isKeyInArray(new Set(trueAnswer.value), key)
const isWrong = (key: string) => isKeyInArray(new Set(userWrongAnswer.value), key)
const isSelectedOrRecorded = (key: string) =>
  isKeyInArray(new Set([...logAnswer.value, ...currentSelectArr.value]), key)

const isAnalyzeEnabled = computed(() => [1, 3, 4].includes(props.model) && questionItem.value.isAnalyze)

const getClassForNonEditMode = (key: string) => {
  if (isCorrect(key)) {
    return 'correct-status'
  } else if (isWrong(key)) {
    return 'wrong-status'
  } else {
    return ''
  }
}

const getClassForEditMode = (key: string, isActive: boolean) => {
  if (isAnalyzeEnabled.value) {
    let statusClass = ''
    if (isCorrect(key)) {
      statusClass = 'correct-status'
    }
    if (isWrong(key)) {
      statusClass = 'wrong-status'
    }

    return statusClass
  }
  return isActive ? 'exam-model-active' : ''
}

const selectCellClass = computed(() => (key: string) => {
  if (props.model === 4 && !questionItem.value.logAnswer && props.isEdit) {
    // 背题模式，不支持做题，但展示历史做题记录
    return ''
  }
  if (!props.isEdit) {
    return getClassForNonEditMode(key)
  } else {
    const isActive = isSelectedOrRecorded(key)
    return getClassForEditMode(key, isActive)
  }
})

// 处理选项 ABCD 样式问题
const handleExerciseClassName = computed(() => (key: string) => {
  if (props.model === 4 && !questionItem.value.logAnswer) {
    // 背题模式，不支持做题，但展示历史做题记录
    return ''
  }
  // 练习模式 点过解析
  if (questionItem.value.isAnalyze) {
    if (trueAnswer.value.includes(key)) {
      return '!text-[#fff] !bg-[#15CCC0]'
    } else if (userWrongAnswer.value.includes(key)) {
      // 错误答案
      return '!text-[#fff] !bg-[#EB2330]'
    }
  } else if (
    logAnswer.value.includes(key) ||
    currentSelectArr.value.includes(key)
  ) {
    // 没点过解析 当前选择项
    return '!text-[#111] !bg-[#fff]'
  }
})
const handleExamClassName = computed(() => (key: string) => {
  // 考试模式
  if (
    currentSelectArr.value.includes(key) ||
    logAnswer.value.includes(key) ||
    currentSelectArr.value.includes(key)
  ) {
    return '!text-[#ffffff] !bg-[#4464ff]'
  }
})

const handleAnalyzeClassName = computed(() => (key: string) => {
  if (props.model === 4 && !questionItem.value.logAnswer && props.isEdit) {
    // 背题模式，不支持做题，但展示历史做题记录
    return ''
  }
  // 解析模式
  if (trueAnswer.value.includes(key)) {
    return '!text-[#fff] !bg-[#15CCC0]'
  } else if (userWrongAnswer.value.includes(key)) {
    return '!text-[#fff] !bg-[#EB2330]'
  }
})

// 题型说明
const isShowDescription = ref(false)
const description = computed(() => questionItem.value.description)
const onDescriptionClick = () => {
  if ((moduleType === 2 || moduleType === 0) && !props.isMaterial && description.value) {
    isShowDescription.value = !isShowDescription.value
  }
}

/**
 * 题型title
 */
const questionTitle = computed(() => {
  const { ptypeValueIdTitle, questionTypeValueIdTitle } = props.question
  return handleQuestionTitle(ptypeValueIdTitle || '', questionTypeValueIdTitle, moduleType)
})

</script>
<style scoped lang="scss">
// 做题模式的选项类名
// 默认状态
// .multi-option {
//   padding: 24px;
// }
.exam-model-default {
  @apply rounded-[16px] text-[32px] text-[#333333];
  background: #f7f8fc;
  border: 1px solid #eeeeee;
  margin-bottom: 32px;

  .select-status {
    @apply flex items-center justify-center bg-white font-bold w-[52px] h-[52px] rounded-[12px] inline-block;
  }

  &.exam-model-active {
    background: #e8ecff;
  }

  &.correct-status {
    background: #e3f9f6;
    border: 1px solid #15ccc0;

    .select-status {
      @apply bg-[#15CCC0] text-white;
    }
  }

  &.wrong-status {
    background: #feeef0;
    border: 1px solid #eb2330;

    .select-status {
      @apply bg-[#EB2330] text-white;
    }
  }
}

.chioceValue {
  width: calc(100% - 52px);
}

.exam-html {
  word-wrap: break-word !important;
}
</style>
