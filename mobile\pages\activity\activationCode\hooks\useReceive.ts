import type { ReceiveT } from '../types/type'
import { useUserStore } from '~/stores/user.store'

export const useReceive = () => {
  const userStore = useUserStore()
  const { activationCodeApi } = useApi()
  const route = useRoute()
  const tab = ref(0)
  const id = ref<string>(route.query.id as string)
  const receiveInfo = ref<ReceiveT>({
    activityName: '',
    userName: '',
    photo: '',
    activationCode: '',
    activationCodeBeginTime: '',
    activationCodeEndTime: '',
    couponList: [],
    goodsList: [],
  })
  const getDetail = async () => {
    if (!userStore.isLogin) {
      userStore.isLoginFn()
      return
    }
    try {
      const { error } = await activationCodeApi.activationCodeVerify({ id: id.value })
      if (!error.value) {
        const { data } = await activationCodeApi.activationCodeDetail({ id: id.value })
        console.log(data, '=============')
        receiveInfo.value = data?.value || {}
        // 判断展示的数据
        if (receiveInfo.value.couponList?.length && !receiveInfo.value.goodsList?.length) {
          tab.value = 1
        } else if (
          receiveInfo.value.goodsList?.length &&
      !receiveInfo.value.couponList?.length
        ) {
          tab.value = 0
        }
      } else {
        Message(error.value.data.msg)
        setTimeout(() => {
          navigateTo({
            path: '/activity/activationCode/' + id.value,
          })
        }, 2000)
      }
    } catch (e) {
      console.log(e)
      setTimeout(() => {
        navigateTo({
          path: '/activity/activationCode/' + id.value,
        })
      }, 2000)
    }
  }
  return {
    receiveInfo,
    getDetail,
    tab
  }
}
