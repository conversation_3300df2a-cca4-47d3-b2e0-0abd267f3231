<template>
  <div>
    <div class="upload-wrapper">
      <div class="upload-input">
        <input
          type="file"
          accept="image/*"
          :multiple="false"
          class="input-cropper"
          @click="handleFileClick"
          @change="handleFileUpload"
        >
        <span class="text-[#111] text-[24px]">添加图片</span>
        <KKCIcon class="pb-[5px]" name="icon-tianjiatupian" :size="36" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const { commonApi } = useApi()
const { track } = useTrackEvent()

const emit = defineEmits<{
  (e: 'success', imgUrls: string[]): void;
}>()
const props = defineProps<{
  isDisabled?: boolean;
  questionTitle?: string;
}>()

const ossInstance = ref(null)
// 监测图片是否是 HEIF 格式
const isHeicFile = async (file: File) => {
  const blob = file.slice(0, 12)

  // 兼容性处理 ios 14 才支持 arrayBuffer
  let buffer: ArrayBuffer
  if (blob.arrayBuffer) {
    buffer = await blob.arrayBuffer()
  } else {
    buffer = await new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => resolve(reader.result as ArrayBuffer)
      reader.onerror = () => reject(new Error('读取失败'))
      reader.readAsArrayBuffer(blob)
    })
  }

  const header = new Uint8Array(buffer)
  const heicHeader1 = [0x66, 0x74, 0x79, 0x70, 0x68, 0x65, 0x69, 0x63] // 'ftypheic'
  const heicHeader2 = [0x66, 0x74, 0x79, 0x70, 0x68, 0x65, 0x69, 0x78] // 'ftypheix'

  // 检查是否匹配 HEIF 头
  return (
    header.slice(4, 12).every((byte, i) => byte === heicHeader1[i]) ||
    header.slice(4, 12).every((byte, i) => byte === heicHeader2[i])
  )
}
// 点击事件 在输入框选中时阻止默认行为
const handleFileClick = (event: any) => {
  if (props.isDisabled) {
    event.preventDefault()
    event.stopPropagation()
  }
  // 做题->做题添加图片 埋点
  track({
    category: '题库',
    action: '做题添加图片',
    label: props.questionTitle
  })
}
// 点击上传图片
const handleFileUpload = async (event: any) => {
  const { data } = await commonApi.fetchOSSConfig()
  ossInstance.value = initOSS(data || {})
  // const file = event.target; // 获取选中的图片文件
  if (event.target.files && event.target.files.length > 1) {
    Message('抱歉，每次最多选择1张图片')
    event.target.value = '' // 清空文件选择
    return
  }

  const file = event.target.files[0] // 获取选中的图片文件
  // 检查是否是 HEIF/HEIC
  const isHeic = await isHeicFile(file)

  if (isHeic) {
    Message('不支持 HEIC/HEIF 格式文件')
    event.target.value = '' // 清空选择
    return
  }

  // 清空上传资源，可以重复触发chang
  event.target.value = ''
  uploadHttp(file)
}
// 上传 oss
const uploadHttp = async (file: any) => {
  Loading(true)
  const result = await unref(ossInstance).put(
    generateFilename(file, 'img/kukecloud/wap/answer/'),
    file
  ) // 将图片上传到阿里云OSS并返回上传结果给客户端
  emit('success', result.url)
  Loading(false)
}
</script>

<style scoped lang="scss">
.upload-wrapper {
  display: flex;

  .upload-input {
    position: relative;
    width: 164px;
    height: 48px;
    // line-height: 48px;
    text-align: center;
    background: #dddddd;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;

    .input-cropper {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      opacity: 0;
      cursor: pointer;
    }

    svg {
      // margin-top: 15px;
      padding-bottom: 0;
    }
  }
}
</style>
