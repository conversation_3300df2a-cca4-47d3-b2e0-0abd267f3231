<template>
  <section class="home-header">
    <!-- Logo区域 -->
    <a
      class="home-header__logo"
      href="/"
    >
      <img
        :src="`${logo2}?x-oss-process=image/format,webp`"
        alt=""
        class="w-[208px] h-[52px]"
      >
    </a>

    <!-- 搜索框 -->
    <!-- @click="goToSearch" -->
    <a class="home-header__search" :href="`/search?pageId=${kuke99DefaultHomeData.pageMasterId || ''}`">
      <div class="home-header__search-text">
        搜索发现更多课程
      </div>
      <span class="home-header__search-icon">
        <KKCIcon name="icon-sousuo1" :size="40" />
      </span>
    </a>

    <!-- 通知图标 -->
    <div class="home-header__notify" @click="goToNotify">
      <KKCIcon name="icon-xiaoxi" :size="48" />
      <span
        v-if="unreadMsgNum"
        class="home-header__notify-num"
      >
        {{ unreadMsgNum }}
      </span>
    </div>

    <!-- 菜单图标 -->
    <div class="home-header__menu h-[48px]" @click="showMenu">
      <KKCIcon name="icon-gengduo" :size="48" />
      <!-- <nuxt-icon name="home-menu" class="text-[48px]" /> -->
    </div>

    <!-- 底部弹窗 -->
    <KKPopup
      ref="menuPopupRef"
      position="bottom"
      :height="800"
      :width="750"
      title=" "
      class="home-header__popup"
      :closeable="true"
      box-padding="0px"
      @close="onMenuClose"
    >
      <!-- <template #header>
        <div class="header__popup-title">
          <div class="header__popup-title-text">
            菜单
          </div>
        </div>
      </template> -->
      <div class="home-header__popup-content">
        <!-- 这里放菜单内容 -->
        <div class="text-[34px] leading-[44px] text-[#111] font-500 pl-[48px]">
          学习目标
        </div>
        <HomeIcons class="pb-[48px] mb-[24px]" type="home-menu" />
        <div class="text-[34px] leading-[44px] text-[#111] font-500 pl-[48px]">
          助学工具
        </div>
        <HomeIconsZhuxue class="pb-[48px]" />
      </div>
    </KKPopup>
  </section>
</template>

<script lang="ts" setup>

import { ref } from 'vue'
import { useUserStore } from '~/stores/user.store'
import { getMessageCount } from '~/apis/user'
const { logo2 } = useAppConfig()
const userStore = useUserStore()
const { data: kuke99DefaultHomeData } = useKuke99DefaultHomeData()

interface PopupInstance {
  showPopup: () => void;
  hide: () => void;
}

const menuPopupRef = ref<PopupInstance | null>(null)

/**
 * 显示菜单弹窗 [AI-GEN]
 */
const showMenu = () => {
  menuPopupRef.value?.showPopup()
}

/**
 * 关闭菜单弹窗后的回调 [AI-GEN]
 */
const onMenuClose = () => {
  console.log('菜单已关闭')
}

/**
 * 跳转到搜索页 [AI-GEN]
 */
// const goToSearch = () => {
//   navigateTo('/search')
// }

/**
 * 跳转到通知页 [AI-GEN]
 */
const goToNotify = () => {
  if (userStore.isLogin) {
    navigateTo('/user/notification?name=消息通知')
  } else {
    // navigateTo({
    //   path: '/login',
    //   query: {
    //     redirect_uri: encodeURIComponent(route.fullPath),
    //   },
    // })
    userStore.isLoginFn()
  }
}

// const unreadMsgNum = ref(0)
const unreadMsgNum = ref<number | string>(0)
onMounted(async () => {
  await nextTick()
  if (userStore.isLogin) {
    const { data: messageData } = await getMessageCount()
    // 设置消息数量
    const num = messageData.value?.unreadMsgNum || 0
    unreadMsgNum.value = num > 99 ? '99+' : num
  }
  // const num = /* messageData.value?.unreadMsgNum || */ 99
  // unreadMsgNum.value = num > 99 ? '99+' : num
})

</script>

<style lang="scss" scoped>
.home-header {
  width: 750px;
  height: 88px;
  padding: 0 24px;
  display: flex;
  align-items: center;
  //

  &__logo {
    flex-shrink: 0;
    // height: 40px;
    margin-right: 24px;
  }

  &__search {
    // flex: 1;
    width: 334px;
    // margin: 0 24px;
    background-color: #fff;
    border: 2px solid #EB2330;
    height: 64px;
    border-radius: 32px;
    padding: 2px;
    overflow: hidden;
    margin-right: auto;
    // height: 60px;
    // border-radius: 30px;
    // padding: 0 24px;
    padding-left: 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    // &-box {
    // }

    &-icon {
      color: #fff;
      width: 64px;
height: 56px;
background: #EB2330;
border-radius: 32px 32px 32px 32px;
display: flex;
align-items: center;
justify-content: center;
    }

    &-text {
      font-size: 24px;
      color: #999;
      line-height: 28px;
      flex: 1;
    }
  }

  &__notify {
    flex-shrink: 0;
    // margin-right: 24px;
    margin-right: 16px;
    position: relative;
    &-num {
      position: absolute;
      top: -20px;
      right: -20px;
      background-color: #EB2330;
      color: #fff;
      font-size: 20px;
      // width: 52px;
      height: 32px;
      line-height: 32px;
      padding: 0 8px;
      text-align: center;
      // line-height: 1;
      // padding: 4px 8px;
      // border-radius: 50%;
      border-radius: 16px;
    }
  }

  &__menu {
    flex-shrink: 0;
  }
  ::v-deep(.home-header__popup){
    // background-color: red;
    --van-popup-close-icon-margin:48px;
    .popup-box-title{
      height: 48px;
      margin: 0;
    }
  }
  &__popup {
    &-title {
      height: 88px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-bottom: 1px solid #EEEEEE;
      position: relative;

      &-text {
        font-size: 32px;
        font-weight: bold;
        color: #333;
      }
    }

    &-content {
      min-height: 712px;
      // padding: 24px;
    }
  }
}
</style>
