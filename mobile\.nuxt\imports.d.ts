export { isVue2, isVue3 } from 'vue-demi';
export { useAsyncData, useLazyAsyncData, useNuxtData, refreshNuxtData, clearNuxtData, defineNuxtComponent, useNuxtApp, defineNuxtPlugin, definePayloadPlugin, reloadNuxtApp, useRuntimeConfig, useState, clearNuxtState, useFetch, useLazyFetch, useCookie, useRequestHeaders, useRequestEvent, useRequestFetch, useRequestURL, setResponseStatus, setPageLayout, onNuxtReady, useRouter, useRoute, defineNuxtRouteMiddleware, navigateTo, abortNavigation, addRouteMiddleware, showError, clearError, isNuxtError, useError, createError, defineNuxtLink, useAppConfig, updateAppConfig, defineAppConfig, preloadComponents, preloadRouteComponents, prefetchComponents, loadPayload, preloadPayload, isPrerendered, getAppManifest, getRouteRules, definePayloadReducer, definePayloadReviver, requestIdleCallback, cancelIdleCallback } from '#app';
export { onBeforeRouteLeave, onBeforeRouteUpdate, useLink } from '#vue-router';
export { withCtx, withDirectives, withKeys, withMemo, withModifiers, withScopeId, onActivated, onBeforeMount, onBeforeUnmount, onBeforeUpdate, onDeactivated, onErrorCaptured, onMounted, onRenderTracked, onRenderTriggered, onServerPrefetch, onUnmounted, onUpdated, computed, customRef, isProxy, isReactive, isReadonly, isRef, markRaw, proxyRefs, reactive, readonly, ref, shallowReactive, shallowReadonly, shallowRef, toRaw, toRef, toRefs, triggerRef, unref, watch, watchEffect, watchPostEffect, watchSyncEffect, isShallow, effect, effectScope, getCurrentScope, onScopeDispose, defineComponent, defineAsyncComponent, resolveComponent, getCurrentInstance, h, inject, hasInjectionContext, nextTick, provide, defineModel, defineOptions, defineSlots, mergeModels, toValue, useModel, useAttrs, useCssModule, useCssVars, useSlots, useTransitionState, Component, ComponentPublicInstance, ComputedRef, ExtractPropTypes, ExtractPublicPropTypes, InjectionKey, PropType, Ref, MaybeRef, MaybeRefOrGetter, VNode } from 'vue';
export { showDialog, showConfirmDialog, showImagePreview, showNotify, showToast, showFailToast, showLoadingToast, showSuccessToast, closeDialog, setDialogDefaultOptions, resetDialogDefaultOptions, closeNotify, setNotifyDefaultOptions, resetNotifyDefaultOptions, closeToast, allowMultipleToast, setToastDefaultOptions, resetToastDefaultOptions } from 'vant';
export { injectHead, useHead, useSeoMeta, useHeadSafe, useServerHead, useServerSeoMeta, useServerHeadSafe } from '@unhead/vue';
export { Classify } from '../composables/Classify';
export { Dialog } from '../composables/Dialog';
export { ExerciseModelPopup } from '../composables/ExerciseModelPopup';
export { ShoppingTypePopup } from '../composables/ShoppingTypePopup';
export { TextBookPopup } from '../composables/TextBookPopup';
export { buyEffect } from '../composables/buyEffect';
export { dragFun } from '../composables/dragFun';
export { default as handleMarketingInfo } from '../composables/handleMarketingInfo';
export { default as handleOrderMarketingInfo } from '../composables/handleOrderMarketingInfo';
export { useApi, useKuke99DefaultHomeData, isInteger } from '../composables/index';
export { markDownItKateX } from '../composables/markDownItKateX';
export { receiveCoupon } from '../composables/receiveCoupon';
export { timeKeepingShow } from '../composables/timeKeepingShow';
export { useAnswerService } from '../composables/useAnswerService';
export { getComponentInteractiveInfo, useKKNews } from '../composables/useBusinessInteractive';
export { useCheckSchool, useCheckSchoolNotVerifyLogin, useCheckSchoolNotButtonStatus } from '../composables/useCheckSchool';
export { useCompatibleIOSBlur } from '../composables/useCompatibleIOSBlur';
export { useComponentEvents, IComponentData } from '../composables/useComponentEvents';
export { useComponentEventsHome } from '../composables/useComponentEventsHome';
export { useCourseUpdateStatus } from '../composables/useCourseUpdateStatus';
export { useCpClueSource, usePointsClueSource } from '../composables/useCpClueSource';
export { default as useCustomPageWap } from '../composables/useCustomPageWap';
export { useDownloadZip } from '../composables/useDownloadZip';
export { default as useGetAgentQuery, Query } from '../composables/useGetAgentQuery';
export { useHeLiKefu } from '../composables/useHeLiKefu';
export { useJumpQuestionPurchased } from '../composables/useJumpQuestionPurchased';
export { useKeyboardOpen } from '../composables/useKeyboardOpen';
export { default as useLabelSwitch } from '../composables/useLabelSwitch';
export { orderEffect } from '../composables/useOrder';
export { useOrderSns } from '../composables/useOrderSns';
export { usePreventDefaultLink } from '../composables/usePreventDefaultLink';
export { questionBankBackForApp } from '../composables/useQuestionBackForApp';
export { questionBankBack } from '../composables/useQuestionBankBack';
export { useQuestionBankClassifyInfo } from '../composables/useQuestionBankClassifyInfo';
export { useQuestionBankDataChange } from '../composables/useQuestionBankDataChange';
export { useQuestionBankHandleEvents } from '../composables/useQuestionBankHandleEvents';
export { useQuestionBankInit } from '../composables/useQuestionBankInit';
export { useQuestionBankInitQuery } from '../composables/useQuestionBankInitQuery';
export { useQuestionBankList } from '../composables/useQuestionBankList';
export { useQuestionBankModuleIcon } from '../composables/useQuestionBankModuleIcon';
export { useQuestionBankModuleInfo } from '../composables/useQuestionBankModuleInfo';
export { useQuestionBankModulePrivilege } from '../composables/useQuestionBankModulePrivilege';
export { useQuestionBankMultipleTag } from '../composables/useQuestionBankMultipleTag';
export { useQuestionBankTag } from '../composables/useQuestionBankTag';
export { useQuestionFilter } from '../composables/useQuestionFiltervajra';
export { useQuestionLandingPage } from '../composables/useQuestionLandingPage';
export { useQuestionListIsWhite } from '../composables/useQuestionListIsWhite';
export { useQuestionPurchasedModuleTag } from '../composables/useQuestionPurchasedModuleTag';
export { useRefreshQuestionListData } from '../composables/useRefreshQuestionListData';
export { default as useSKU } from '../composables/useSKU';
export { useScrollViewBack } from '../composables/useScrollViewBack';
export { useSelectExam } from '../composables/useSelectExam';
export { useSetQuestionBankShare } from '../composables/useSetQuestionBankShare';
export { teacherInfoEffect } from '../composables/useTeacherInfo';
export { useTrackEvent, TrackEventOptions } from '../composables/useTrackEvent';
export { useUserMember, useMemberUtils, useMemberWxShareInfo } from '../composables/useUserMember';
export { useVaildLT } from '../composables/useVaildLT';
export { handleWeChatPreview } from '../composables/useWeChat';
export { useWxShare } from '../composables/useWxShare';
export { userData, StoreUserInfoState } from '../composables/userData';
export { ApisHome, ApisHome, apiGetHome, getDefaultOfficialRecommend, getAssemblyForWx, getReleasePersonCenterORNavigationDetails, getCustomPageReleaseDetails, getCustomPageNavigationStatus, getLaunchAd, getPopUpAd, getAdvertisementPhotoList, getKukeMarketingInfo, queryProductDetailThirdList, removeUpdateRecordProt, getCourseUpdateStatus, userInfoProt, getRuleListByIdParams, ApisHome } from '../apis/home';
export { default as apis, userWxPhoto } from '../apis/index';
export { ApisLt, ApisLt, getUserLearningTarget, getLearningTargetList, saveLearningTarget, getLearningTargetById, getClassifyConfigEnable, getLearningTargetDefaultId, statisticalPublicAdvertExposure, judgePageMasterIdValid } from '../apis/learn-target/index';
export { ApisLc, ApisLc, kgUserStudyPlanSelectionSwitchProt, getStudyPlanStageInfoProt, getGoodsResourceType, getUnitStatusProt, IgetGoodsResourceType } from '../apis/learn-center/index';
export { ApisAddress, ApisAddress, getAreaList, getAddressList, postAddress, editAddress, delAddress, getAddressDetail, updateDefaultAddress, getDefaultAddressProt, getCurReceivingInfo } from '../apis/address/index';
export { BooleanEnum, BooleanEnum, ApiResponse, GetAdvertisementPhotoListRequestData, GetAdvertisementPhotoListResponseData } from '../apis/types';
export { ResourceFile, StudyPlan, ResourcePackStage, DataPackageResponse, DataPackageReq } from '../types/data-package';
export { OuterRelationType, OuterRelationType, FileSource, FileSource, VideoType, VideoType, LearnStatus, LearnStatus, FilesPageDTO, KssStudyProgress, FilesListVO, FilesListPageVO, Result, FilesListPageResponse } from '../types/homework-files';
export { HomeworkModel, HomeworkModel, ScoringWay, ScoringWay, TeacherState, TeacherState, DoStatus, DoStatus, GetStudyPlanHomeworksReq, HomeworkInfo, GetStudyPlanHomeworksResponse } from '../types/homework';
export { SubmitVal } from '../types/loginTypes';
export { PauseEmitType } from '../types/questionTime';
export { StudyProgress, StudyPlanFile } from '../types/study-plan-files';
export { UserSelectionMode, UserSelectionMode, ClassMode, ClassMode, GoodsModeDetail } from '../types/study-plan';
export { UseItem, toolsItem } from '../types/user';
export { Loading } from '../../base/composables/Loading';
export { Message } from '../../base/composables/Message';
export { updateBehaviorRecords, fetchAddOrdersBehaviorRecord, os, downloadFile, downLoadFileRename, isKukeCloud } from '../../base/composables/common';
export { countFunction } from '../../base/composables/countdown';
export { base642File } from '../../base/composables/fileChange';
export { getFormDisabled, recursion } from '../../base/composables/form';
export { passwordReg, maskedMobileNumber, validateNickname, regPhone, moneyInput } from '../../base/composables/isRegexp';
export { showMoreArrow } from '../../base/composables/showMoreArrow';
export { useAdCountdownRAF, useLocalStorage, getNextDayStartTimeDate } from '../../base/composables/useAdCountdown';
export { useAddQuestionBankOrderBehaviorRecords } from '../../base/composables/useAddQuestionBankOrderBehaviorRecords';
export { default as useApiBaseUrl } from '../../base/composables/useApiBaseUrl';
export { default as useAuditionValidity } from '../../base/composables/useAuditionValidity';
export { useBeforeunload } from '../../base/composables/useBeforeunload';
export { useBehaviorRecord } from '../../base/composables/useBehaviorRecord';
export { useBem } from '../../base/composables/useBem';
export { default as useBusinessComp } from '../../base/composables/useBusinessComp';
export { default as useCaptchaVerify, SlideStyle, CaptchaVerifyResult, Captcha } from '../../base/composables/useCaptchaVerify';
export { default as useCluesExtension } from '../../base/composables/useCluesExtension';
export { useComputedQuestionCount } from '../../base/composables/useComputedQuestionCount';
export { default as useCourseId } from '../../base/composables/useCourseId';
export { default as useCoursePayBtnText } from '../../base/composables/useCoursePayBtnText';
export { default as useCrmSharePrice } from '../../base/composables/useCrmSharePrice';
export { default as useCustomizedTenants, CUSTOMIZED_TENANTS } from '../../base/composables/useCustomizedTenants';
export { default as useDeviceDetection } from '../../base/composables/useDeviceDetection';
export { default as useFetchOptions } from '../../base/composables/useFetchOptions';
export { useFileView } from '../../base/composables/useFileView';
export { useHistoryState } from '../../base/composables/useHistoryState';
export { useHttp, loggerError, HttpOption } from '../../base/composables/useHttp';
export { useHttpClient } from '../../base/composables/useHttpClient';
export { useKuKeCloud } from '../../base/composables/useKuKeCloud';
export { useLearnTarget, useLearnTargetTree, ILearnTargetInfo } from '../../base/composables/useLearnTarget';
export { useLearnTargetFullpath } from '../../base/composables/useLearnTargetFullpath';
export { useLearnTargetState } from '../../base/composables/useLearnTargetState';
export { getLivePlatform, getLivePlatformByWap, getLivePlatformByPc } from '../../base/composables/useLivePlatformType';
export { useMessage } from '../../base/composables/useMessage';
export { defaultNamespace, useNamespace, UseNamespaceReturn } from '../../base/composables/useNamespace';
export { useRouteLeaveGuard } from '../../base/composables/useNewBeforeUnload';
export { useOnline } from '../../base/composables/useOnline';
export { generateFilename, initOSS } from '../../base/composables/useOss';
export { usePageTrack, useGetProductSide, defaultTrigger } from '../../base/composables/usePageTrack';
export { postBodyEncryption } from '../../base/composables/usePostBodyEncryPtion';
export { useQuestionBank, useQuestSortStem } from '../../base/composables/useQuestionBank';
export { useQuestionBankFillJudge, FillResult } from '../../base/composables/useQuestionBankFillJudge';
export { useQuestionBankHelp } from '../../base/composables/useQuestionBankHelp';
export { useQuestionBankPromotion } from '../../base/composables/useQuestionBankPromotion';
export { useQuestionCorrected } from '../../base/composables/useQuestionCorrected';
export { useQuestionExamModel } from '../../base/composables/useQuestionExamModel';
export { useQuestionVideoInfo } from '../../base/composables/useQuestionVideoInfo';
export { useRecorder } from '../../base/composables/useRecorder';
export { useScrollPenetrate } from '../../base/composables/useScrollPenetrate';
export { useSendBeacon } from '../../base/composables/useSendBeacon';
export { default as useTheme, formatTheme } from '../../base/composables/useTheme';
export { useThirdJumpQuestionBank } from '../../base/composables/useThirdJumpQuestionBank';
export { useUpdateQuestionBankBehaviorRecords } from '../../base/composables/useUpdateQuestionBankBehaviorRecords';
export { useVisibilityChange } from '../../base/composables/useVisibilityChange';
export { DEFAULT_INDEX, useZIndex } from '../../base/composables/useZIndex';
export { userDomainConfig, StoreAppDataState } from '../../base/composables/userDomainConfig';
export { audioRegex, extractedAudio, addAudio, pauseAudio, removeAudio, addAudioUniquely } from '../../base/utils/audio';
export { handleClipboard } from '../../base/utils/clipboard';
export { debounceFunc, debounceFuncPlus } from '../../base/utils/debounce';
export { deepClone } from '../../base/utils/deepClone';
export { getDeviceEnv, IDevice } from '../../base/utils/device';
export { getExamStatusName, handleToPaperPage } from '../../base/utils/examUtils';
export { base64toBlob } from '../../base/utils/file';
export { default as filterBody } from '../../base/utils/filterBody';
export { default as globalVariable } from '../../base/utils/globalVariable';
export { SECS_60, MINS_60, HOURS_24, DAY_999, formatDuring, formatTodayWatchDuration, formatTimeDuration, hexToRgb, objectToUrlParams, calculateTimeDifference, getCurrentTime } from '../../base/utils/index';
export { isClient, isDef, notNullish, isDefined, CLIENT_TYPE, CLIENT_TYPE, isPC, isWAP, isXcxBrowser, isKukeCloudAppWebviewBrowser, assert, now, timestamp, clamp, noop, rand, hasOwn, isIOS, getArrayLast, isUCBrowser, isQQBrowser, isQQBrowserWebView, isSogouBrowser, isDingTalk6_5, isDingTalk, isQuarkBrowser, isWxBrowser, isVivoBrowser, getDeviceOfUA, isDdefine, isIframe } from '../../base/utils/is';
export { emitter } from '../../base/utils/mitt';
export { splitNumber, formatDecimal, formatNumberWithoutTrailingZero, numberToChinese } from '../../base/utils/number';
export { default as px2rem } from '../../base/utils/px2rem';
export { compareTagParams } from '../../base/utils/questionTools';
export { redirectKuke99Url } from '../../base/utils/redirectKuke99Url';
export { flat, isMaterial, isFillQuestion, isMultipleChoice, isRadioChoice, isShowBankAnalyze, isOpenBankAnalyze, handleQuestionTitle, toBase64, toAnswerBase64, getQuestionDoStatusFromLearnStatusName, toChineseNum, formatTimeByPattern, formatDateWithDateObject } from '../../base/utils/tools';
export { buildUUID } from '../../base/utils/uuid';
export { decryptData, createDecipher } from '../../base/utils/video';
export { ApisCashier, ApisCashier } from '../../base/apis/cashier/index';
export { ApisCommon, ApisCommon } from '../../base/apis/common/index';
export { CATE_ID_MAP, CITY_TREE_MAP, NEW_CATEGORY_LIST, NewsCaegoryItem } from '../../base/constants/db-redirect-kuke99';
export { CHINESE_NUMBERS, PayStatus, PayStatus, OrderAction, OrderAction, ExamQuestionType, ExamQuestionType, ExamAnswerType, ExamAnswerType, NetCode, NetCode, IS_LOCAL_ENV, IS_DEBUG_ENV, IS_DEV_ENV, IS_STAGING_ENV, LT_LOCAL_KEY, LT_SEARCH_KEY, PROMOTION_SHARE_KEY, PROMOTION_SHARE_URL, SYSTEM_TYPE, POOL_ID, SALE_ADMIN_ID, TARGET_CODE, INNERNAL_SYSTEM, IS_AUDITION, AUDITION_VALIDITY, SHARE_PRICE, SHARE_LINK_ID, LOC_SPEC_ID, EXTERNAL_USER_ID, DicSkipId, DicSkipId, STUDY_TYPE__COURSE, STUDY_TYPE__TEST_QUESTIONS, STUDY_TYPE, ActionEnum, ActionEnum, ClientType, ClientType, SourceEnum, SourceEnum, GetCodeType, GetCodeType, OrderStatus, OrderStatus, AnswerStatus, AnswerStatus, BtnStatus, BtnStatus, ActivityType, ActivityType, ActivityTypeName, MarketingOrderTagStatus, MarketingOrderTagStatus, MarketingOrderTagMap, ExciseLabelEnum, ExciseLabelEnum, productSideEnum, productSideEnum, BooleanEnum, ExciseModeEnum, ExciseModeEnum } from '../../base/constants/index';
export { informationLabelMap } from '../../base/constants/informationLabelMap';
export { QuestionDoStatusFromLearnStatusName, QuestionDoStatusFromLearnStatusName, QuestionDoStatusAlias, QuestionModel } from '../../base/constants/questionBankType';
export { GradualDirectionType, ITheme, StyleSetting } from '../../base/constants/types';
export { usePinia } from '../../node_modules/@pinia/nuxt/dist/runtime/composables';
export { persistedState } from '../../node_modules/@pinia-plugin-persistedstate/nuxt/dist/runtime/storages';
export { definePageMeta } from '../../node_modules/nuxt/dist/pages/runtime/composables';