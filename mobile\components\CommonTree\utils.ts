// 辅助方法：查找节点
export const findNode = (nodes, id) => {
  for (const node of nodes) {
    if (node.id === id) {
      return node
    }
    if (node.children?.length) {
      const found = findNode(node.children, id)
      if (found) {
        return found
      }
    }
  }
  return null
}
// 辅助方法：获取节点路径方法
export const getNodePath = (nodes, targetId, path = []) => {
  for (const node of nodes) {
    const currentPath = [...path, node.id]
    if (node.id === targetId) {
      return currentPath
    }
    if (node.children?.length) {
      const found = getNodePath(node.children, targetId, currentPath)
      if (found) {
        return found
      }
    }
  }
  return null
}

// 获取所有子节点ID
export const getChildrenIds = (node) => {
  if (!node.children || node.children.length === 0) { return [] }

  let ids = []
  node.children.forEach((child) => {
    ids.push(child.id)
    if (child.children && child.children.length > 0) {
      ids = [...ids, ...getChildrenIds(child)]
    }
  })

  return ids
}

// 辅助方法
export const getAllNodeIds = (nodes) => {
  return nodes.reduce((acc, node) => {
    //   if (node?.children?.length) {
    acc.push(node.id)
    //   }
    if (node?.children) {
      acc.push(...getAllNodeIds(node.children))
    }
    return acc
  }, [])
}

export const getAllIds = (nodes) => {
  let ids = []
  nodes.forEach((node) => {
    ids.push(node.id)
    if (node.children?.length) {
      ids = [...ids, ...getAllIds(node.children)]
    }
  })
  return ids
}
