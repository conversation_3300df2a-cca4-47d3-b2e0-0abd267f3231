<template>
  <div :class="[bemOfHeader()]">
    <img src="~/assets/images/learn/bg-header.png" alt="" :class="[bemOfHeader('pic')]">

    <div :class="[bemOfHeader('main')]" class="w-[100%]">
      <div class="flex justify-between">
        <h2>我的学习目标</h2>
        <p
          :class="[!showClose ? 'hidden' : '', bemOfHeader('close')]"
          class="mt-[40px]"
          @click="handleClose"
        >
          <!-- :href="fullpath" -->
          <KKCIcon name="icon-com_guangbi" :size="45" color="#111111" />
        </p>
      </div>
      <small>请选择一个学习目标，我们将为您定制相关课程和题库~</small>
    </div>
  </div>
</template>

<script setup lang="ts">

const bemOfHeader = useBem('learn-target-header')

const emit = defineEmits<{
  (e:'close'):void
}>()

defineProps({
  content: {
    type: String,
    default: '',
  },
  showClose: {
    type: Boolean,
    default: false,
  },
})
const handleClose = () => {
  emit('close')
}
</script>

<style lang="scss">
.learn-target-header {
  padding: 20px 32px 90px 32px;
  //
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  position: relative;
  background: #fff;
  &__pic{
    width: 100%;
    height: 335px;
    left: 0px;
    top: 0px;
    position: absolute;
  }
  &__close {
    display: block;
    position: relative;
    &.hidden {
      visibility: hidden;
      pointer-events: none;
    }
  }
  &__main {
    position: relative;
    h2 {
      font-size: 50px;
      font-weight: 600;
      color: #111111;
      line-height: 50px;
      margin-top: 40px;
    }
    small {
      display: block;
      font-size: 26px;
      font-weight: 400;
      color: #666666;
      line-height: 26px;
      margin-top: 24px;
    }
  }
}
</style>
