import type { DoQuestionProtBodyType, StartType } from '~/apis/question-bank/types'

import { useQuestionBankStore } from '~/stores/question.bank.store'
import { useUserStore } from '~/stores/user.store'
const { track } = useTrackEvent()

export const useQuestionBankHandleEvents = () => {
  const { isXcx } = useAppConfig()
  const userStore = useUserStore()
  const { questionBankApi } = useApi()
  const questionBankStore = useQuestionBankStore()
  const { $dayjs: dayjs } = useNuxtApp()

  // 处理列表时间来进行倒计时刷行列表
  const second = ref(0)
  const timeArr = ref<number[]>([])
  const timeFn = ref()
  const curTime = ref(0)
  // 新增一个映射：时间点 => 试卷id
  const timeToExamMap = ref<{ [timestamp: number]: any[] }>({})
  const getIngList = async (list: any[], cb?: Function) => {
    second.value = 0
    timeArr.value = []
    clearSetInterVal()
    curTime.value = new Date().getTime() / 1000 // 当前时间的时间戳
    timeToExamMap.value = {} // 重置映射

    list.forEach((item: any) => {
      if (item.doStatus !== 4 && item.startTime && item.endTime) {
        const ksS = new Date(item.startTime.replace(/-/g, '/')).getTime() / 1000 // 开始时间
        const ksE = new Date(item.endTime.replace(/-/g, '/')).getTime() / 1000 // 结束时间
        const ksM: number = item.mustSubmitTime
          ? new Date(item.mustSubmitTime.replace(/-/g, '/')).getTime() / 1000
          : 0 // 最后交卷时间（0则忽略）

        ;[ksS, ksE, ksM].forEach((t: number) => {
          if (t && curTime.value <= t) {
            if (!timeToExamMap.value[t]) {
              timeToExamMap.value[t] = []
            }
            timeToExamMap.value[t].push(item)
            timeArr.value.push(t)
          }
        })

        // timeArr.value.push(ksS, ksE, ksM)
      }
    })
    timeArr.value = [...new Set(timeArr.value.sort())]

    // 将列表中，大于当前时间的过滤掉
    timeArr.value = timeArr.value.filter((item: any) => {
      return item !== 0 && curTime.value <= item
    })
    timeAdd(cb)
  }
  const timeAdd = (cb?: Function) => {
    if (process.client) {
      timeFn.value = setInterval(() => { // TODO 内存泄漏可疑点: 确保客户端执行并及时清除
        second.value++
        // // 如果时间相等，触发列表更新 ?
        // timeArr.value.forEach((item: number) => {
        //   if (item + 3 === Math.ceil(curTime.value + second.value)) {
        //     cb && cb()
        //   }
        // })
        const now = Math.ceil(curTime.value + second.value - 3) // 当前时间加上秒数，减去3秒，防止时间误差
        if (timeToExamMap.value[now]) {
          // 传递到期的试卷数组
          cb && cb(timeToExamMap.value[now])
        }
      }, 1000)
    }
  }
  const clearSetInterVal = () => {
    clearInterval(timeFn.value)
    timeFn.value = null
  }

  const objectToQueryString = (obj: { [key: string]: any }) => {
    const queryString = Object.keys(obj)
      .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(obj[key])}`)
      .join('&')
    return queryString
  }

  // 跳转试卷说明
  const handleNote = (item: any, moduleManageId: number | string) => {
    const queryParams = {
      id: item.id,
      moduleId: moduleManageId,
    }

    handleXcxJump(`/question-bank/paper/desc?${objectToQueryString(queryParams)}`)
    // if (isXcx) {
    //   userStore.jumpAppletWebPage(`/question-bank/paper/desc?${objectToQueryString(queryParams)}`)
    // } else {
    //   navigateTo({ path: '/question-bank/paper/desc', query: queryParams })
    // }
  }

  // 跳转登陆
  const handleLogin = () => {
    userStore.isLoginFn(history.state.current)
  }

  const modeList = ref<any[]>([]) // 模式
  const textBookList = ref<any[]>([]) // 教材
  const exerciseNumList = ref<number[]>([]) // 题量
  const exerciseNum = ref(0)

  // 获取考试模式数据
  const getExerciseModel = async (examinationPaperModuleId: number | string) => {
    try {
      const { data, error } = await questionBankApi.getReviewQuestionsModel({
        id: examinationPaperModuleId,
      })
      if (!error.value) {
      // 设置图标以及当前浏览类型
        const list = (data.value.list || []).map((item: any) => {
          if (item.isSelect) {
            questionBankStore.setActiveQuestionType(item.questionType)
            questionBankStore.setQueryData('questionType', item.questionType)
            questionBankStore.setLabelName(item.questionTypeName)
          }
          return {
            ...item,
            icon: '',
          }
        })
        modeList.value = list
        exerciseNumList.value = data.value.exerciseNumList
        exerciseNum.value = data.value.exerciseNum
        questionBankStore.setIsShowSwitchMode(list.length > 1)
      }
    } catch (error) {
      console.warn(error)
    }
  }

  // 获取教材数据
  const getTextBookList = async (body: any) => {
    try {
      const { studyLevel1, moduleManageId, subjectType, directionType, region, academicSection, examFormat } = body
      const { data, error } = await questionBankApi.getTextbookList({
        studyLevel1,
        moduleManageId,
        subjectType: subjectType || 0,
        directionType: directionType || 0,
        region: region || 0,
        academicSection: academicSection || 0,
        examFormat: examFormat || 0
      })
      if (!error.value) {
        if (data.value.length && data.value.every((v: any) => v.isSelect === 0)) {
          data.value[0].isSelect = 1
        }
        // 设置当前浏览项
        const text = userStore.isLogin ? (data.value?.find((v: any) => v.isSelect === 1) || data.value[0]) : data.value?.find((v: any) => v.id === questionBankStore.state.activeTextbook) || data.value[0]
        questionBankStore.setActiveTextbook(text.id || 0)
        questionBankStore.setQueryData('textbookId', text.id || '0')
        textBookList.value = data.value
        questionBankStore.setIsShowSwitchMaterial(data.value.length > 1)
        questionBankStore.setTextBookPrivilege({
          title: text.title,
          studyLevel1,
          moduleManageId,
          textbookId: text.id,
          subjectType: subjectType >= 0 ? subjectType : null,
          directionType: directionType >= 0 ? directionType : null,
          region: region >= 0 ? region : null,
          academicSection: academicSection >= 0 ? academicSection : null,
          examFormat: examFormat >= 0 ? examFormat : null
        })
      }
    } catch (error) {
      console.warn(error)
    }
  }
  // 设置做题模式
  const handleConfirmExerciseModel = async (item: any, moduleManageId: string, cb?: Function) => {
    try {
      if (!userStore.isLoginFn(history.state.current)) { return false }
      // 提交考试模式，设置选中数据
      const { error } = await questionBankApi.doQuestionTypeProt({
        questionType: item.questionType,
        moduleManageId,
        exerciseNum: item.exerciseNum
      })
      if (!error.value) {
        // 设置成功添加状态管理
        questionBankStore.setQueryData('questionType', item.questionType)
        questionBankStore.setLabelName(item.questionTypeName)
        cb && cb()
      }
    } catch (error) {
      console.warn(error)
    }
  }
  // 教材或练习模式切换
  const handleSwitchMaterial = async ({ moduleManageId, cb, ...body }:{ moduleManageId: number | string, cb?: Function}) => {
    // 章节练习的教材切换
    await getTextBookList({ ...body, moduleManageId })
    if (textBookList.value.length === 0) { return Message('暂无教材可切换') }
    TextBookPopup({
      list: textBookList.value,
      confirm: (item) => {
        // 设置 textbookId
        questionBankStore.setQueryData('textbookId', item.id)
        questionBankStore.setActiveTextbook(item.id)
        cb && cb()
      }
    })
    nextTick(() => {
      questionBankStore.setIsAllowClick(true)
    })
  }

  // 练习模式切换
  const handleSwitchModel = async ({ moduleType, moduleManageId, cb }:{moduleType: number, moduleManageId: number | string, cb?: Function}) => {
    if (!userStore.isLoginFn()) {
      return false
    }
    await getExerciseModel(moduleManageId)
    if (modeList.value.length === 0) { return Message('暂无做题模式切换') }
    // 每日一练的考试模式切换
    ExerciseModelPopup({
      modeList: modeList.value,
      exerciseNumList: exerciseNumList.value,
      isShowExerciseNum: moduleType === 3,
      defaultRule: 0,
      exerciseNum: exerciseNum.value,
      confirm: item => handleConfirmExerciseModel(item, moduleManageId.toString(), cb)
    })
    nextTick(() => {
      questionBankStore.setIsAllowClick(true)
    })
  }

  const handleTips = () => {
    Dialog({
      message: '试题抽取数量实时更新，请考生以实际抽取数量为准～',
      confirmText: '好的，知道啦',
      showCancelButton: false
    })
  }

  const handleWait = (item: any) => {
    // 判断是否需要收费
    const flag = handleCheckPrivilege(item)
    // 需要收费，先前往开通页面
    if (flag) {
      return handleJumpOpenPrivilege(item)
    }
    const examMode = questionBankStore.state.examModel
    if (examMode === 3 && item.btnStatus === 5) {
      track({
        category: '题库',
        action: '模块-预约估分',
        label: questionBankStore.state.moduleName
      })
    }
    const startTimeText = dayjs(item.startTime.replace(/-/g, '/')).format('YYYY.MM.DD HH:mm')
    const endTimeText = dayjs(item.endTime.replace(/-/g, '/')).format('YYYY.MM.DD HH:mm')
    const message = `
      <p>${examMode === 3 ? '您已成功预约当前试卷估分名额<br />估分时间：' : '考试尚未开始，考试时间：'}</p>
      <p class='text-brand font-[500]'>${startTimeText}</p>
      <p>至</p>
      <p class='text-brand font-[500]'>${endTimeText}</p>
    `
    Dialog({
      title: '温馨提示',
      message,
      confirmText: '好的，知道啦',
      showCancelButton: false
    })
  }

  const handleReset = (item: any, cb?: () => void) => {
    const { targetResultId } = item
    const textbookId = questionBankStore.queryData.textbookId
    Dialog({
      message: '本轮试题已全部练习结束，是否开启下一轮重新练习~',
      confirmText: '现在开启',
      cancelText: '暂不开启',
      onConfirm: () => {
        handleChapterReset({ textbookId, targetResultId }, cb)
      }
    })
  }

  const handleChapterReset = async (body: any, cb?: () => void) => {
    track({
      category: '题库',
      action: '模块-章节练习-开启下一轮',
    })
    try {
      // 开启下一轮，成功返回通知更新数据
      const { data, error } = await questionBankApi.chapterDoNextRoundProt(body)
      if (!error.value) {
        console.log(data.value)
        cb && cb()
      }
    } catch (error) {
      console.error(error)
    }
  }

  const handleSubscribe = async (body: any, item: any, cb?: Function) => {
    if (!userStore.isLoginFn(history.state.current)) {
      return false
    }

    // 判断是否需要收费
    const flag = handleCheckPrivilege(item)
    // 需要收费，先前往开通页面
    if (flag) {
      handleJumpOpenPrivilege({ ...item, ...body })
      return false
    }

    try {
      const query: StartType = {
        ...body,
        studyLevel2: 0,
        studyLevel3: 0,
        studyLevel4: 0,
      }

      const { error } = await questionBankApi.estimateSubscribe(query)
      if (!error.value) {
        handleWait(item)
        cb && cb()
        return Promise.resolve(true)
      }
      return Promise.reject(error.value)
    } catch (error) {
      console.warn(error)
      return Promise.reject(error)
    }
  }

  const checkModuleManageProt = async (body: any) => {
    try {
      // body中值为 -1 的字段更正为 0
      const newBody = Object.keys(body).reduce((acc: { [key: string]: any }, key) => {
        if (body[key] === -1 || body[key] === '' || !body[key] || body[key] === 'undefined') {
          acc[key] = 0
        } else {
          acc[key] = body[key]
        }
        return acc
      }, {})
      const { error } = await questionBankApi.checkModuleManageProt(newBody)
      if (error.value) {
        if (error.value.data.code === '10103') {
          Message(error.value.message, '', 3000, () => {
            location.href = location.origin + location.pathname
          })
        } else {
          Message(error.value.message)
        }
        return false
      }
      return true
    } catch (error) {
      console.warn(error)
      return false
    }
  }

  const setBuriedPopularForQuestion = async (body: DoQuestionProtBodyType & {examMode: number}) => {
    try {
      const { error } = await questionBankApi.buriedPopularForQuestion(body)
      if (!error.value) {
        return true
      }
      return false
    } catch (error) {
      console.warn(error)
      return false
    }
  }

  const { getQuestionLandingPageInfo } = useQuestionLandingPage()

  // 跳转事件
  const handleJumpClick = async (url: string, item?: any, replace?: boolean, moduleManageId?: string) => {
    const { moduleType, goodsMasterId } = item || {}

    if (moduleType === 4 && !url) {
      const { studyLevel1, moduleManageId, subjectType, region, directionType, academicSection, examFormat, moduleName } = questionBankStore.queryData
      const replaceExceptionNumber = (n:number) => n < 0 ? 0 : n
      const { id: brushVersionId, from, purchased } = item
      const catalogUrl = `/question-bank/fixed-exam/catalog?${objectToQueryString({
        from,
        purchased,
        moduleManageId: item.moduleManageId || moduleManageId,
        brushVersionId,
        studyLevel1: item.studyLevel1 || studyLevel1,
        academicSection: replaceExceptionNumber(item.academicSection || academicSection),
        examFormat: replaceExceptionNumber(item.examFormat || examFormat),
        region: replaceExceptionNumber(item.region || region),
        subjectType: replaceExceptionNumber(item.subjectType || subjectType),
        directionType: replaceExceptionNumber(item.directionType || directionType),
        moduleType,
        moduleName: moduleName || questionBankStore.state.moduleName,
        questionType: questionBankStore.getFixedQuestionType()
      })}`

      // isXcx ? userStore.jumpAppletWebPage(catalogUrl) : navigateTo(catalogUrl)
      handleXcxJump(catalogUrl)

      return false
    }

    // 校验试卷是否存在
    const isExist = await handleCheckPaperExist(moduleType, item.testPaperId, moduleManageId)
    if (!isExist) {
      return false
    }

    // 商品试卷包，直接跳转
    if (goodsMasterId && goodsMasterId !== '0') {
      if (item.freezeStatusValue === 2) {
        Message(item.freezeStatusMsg || '此订单退款权益冻结中，暂无法学习')
        return false
      }
      handleXcxJump(url, replace)
      return false
    }

    // 校验是否需要付费
    const flag = handleCheckPrivilege(item)
    const jumpFlag = handleCheckJumpPage(url)
    if (!flag && !jumpFlag) {
      if (!userStore.isLoginFn(history.state.current)) {
        return false
      }
      // isXcx ? userStore.jumpAppletWebPage(url) : navigateTo(url, { replace })
      // 跳转前先判断推广信息
      if (moduleType === 2 && route.name === 'question-bank-paper-detail') {
        const landingFlag = await getQuestionLandingPageInfo({
          moduleManageId: item.moduleManageId || questionBankStore.queryData.moduleManageId,
          testpaperId: item.id || item.testpaperId
        })
        if (!landingFlag) {
          return false
        }
      }

      handleXcxJump(url, replace)
      return false
    }

    if (flag) {
      // 需要付费，先前往开通页面
      handleJumpOpenPrivilege(item)
    }
  }

  // 试卷开通类型
  const handleQuestionShoppingType = (item: any) => {
    const { title, hasBuy, singlePrice, expiry, singlePriceModule, expiryModule, moduleFee, moduleType } = item
    const { moduleManageId, textbookId } = questionBankStore.queryData
    const getSingleTargetId = () => {
      switch (moduleType) {
        case 2:
          return item.id || item.testpaperId
        case 3:
          return textbookId
        default:
          return item.id
      }
    }
    // targetType 目标类型：99模块 2试卷 3教材 4固定刷题
    const single = {
      ...item,
      price: singlePrice,
      title: '单独购买',
      expiry,
      targetType: (moduleType === 4 || moduleType === 3) ? moduleType : 2,
      targetId: getSingleTargetId()
    }
    const module = {
      ...item,
      price: singlePriceModule,
      title: '购买整个模块',
      expiry: expiryModule,
      targetType: 99,
      targetId: item.moduleManageId || moduleManageId
    }
    const list = []
    if (hasBuy === 1) {
      // 可单独购买
      list.push(single)
    }
    if (moduleFee === 2) {
      // 可购买整个模块
      list.push(module)
    }
    ShoppingTypePopup({ list, title })
  }

  // 教材开通权益事件
  const handleTextBookRight = () => {
    handleJumpClick('', {
      moduleType: 3,
      targetType: 3,
      targetId: questionBankStore.queryData.textbookId,
      ...questionBankStore.state.textBookInfo,
    })
  }

  /**
   * 根据模块试卷等权益信息，判断是否需要付费开通
   * @param item 当前数据信息
   * @returns boolean 是否需要付费开通
   * */
  const handleCheckPrivilege = (item: any) => {
    const { rightType, hasFee, moduleType, isAllFree } = item

    // 免费 或者 有权益 试卷，直接跳转原做题页
    const isHasPrivilege = rightType === 1 || rightType === 3
    const isChapterHasPrivilege = questionBankStore.state.textBookInfo.rightType === 1 || questionBankStore.state.textBookInfo.rightType === 3
    if (moduleType === 4 && rightType !== 1 && hasFee === 0) {
      // 固定刷题， 免费，无权益，直接跳转原做题页
      return false
    }
    if ((moduleType !== 3 && isHasPrivilege) || (moduleType === 3 && isChapterHasPrivilege) || moduleType === 1 || isAllFree === 1 || (moduleType === 3 && hasFee === 0)) {
      return false
    }

    return true
  }

  // 判断试卷是否存在
  const handleCheckPaperExist = async (moduleType: number, id: string, moduleManageId?: string) => {
    // 已购题库页面不需要判断
    const { from } = route.query
    if (moduleType !== 4 || !moduleManageId || from === '1') {
      return true
    }
    try {
      console.log(id)
      // 固定刷题不再检测目录是否存在，做题接口判断
      // const { error } = await questionBankApi.getFixedQuestionExistBrush({
      //   brushChapterId: id,
      //   moduleManageId
      // })

      // if (error.value) {
      //   Dialog({
      //     message: error.value.message,
      //     confirmText: '好的，知道啦',
      //     showCancelButton: false,
      //     onConfirm: () => {
      //       location.reload()
      //     }
      //   })
      //   return false
      // }
      return true
    } catch (error) {
      console.log(error)
      return false
    }
  }

  const isIpad = () => {
    const ua = navigator.userAgent
    return /iPad|PlayBook/i.test(ua)
  }

  // 开通跳转
  const handleJumpOpenPrivilege = (item: any) => {
    if (!userStore.isLoginFn(history.state.current)) {
      return false
    }
    const { moduleFee, hasBuy, moduleType } = item

    if (isXcx && (isIpad() || os?.isPhone)) {
      Message('iOS系统用户如需购买解锁，可联系客服老师解决。')
      return false
    }
    const isAllFee = moduleFee === 2 && hasBuy === 1
    const isChapterAllFee = questionBankStore.state.textBookInfo.moduleFee === 2 && questionBankStore.state.textBookInfo.hasBuy === 1
    // 当模块收费，可购买。试卷收费，可单独购买，需让用户选择开通类型
    if ((moduleType !== 3 && isAllFee) || (moduleType === 3 && isChapterAllFee)) {
      const data = item
      if (moduleType === 3) {
        // 章节权益信息根据教材判断，存储在store中
        Object.assign(data, questionBankStore.state.textBookInfo)
      }
      handleQuestionShoppingType(data)
    } else {
      const { studyLevel1, moduleManageId, subjectType, region, directionType, academicSection, examFormat, textbookId } = questionBankStore.queryData
      // targetType 目标类型：99模块 2试卷 3教材 4固定刷题
      const targetType = (moduleType === 3 || moduleType === 4) ? moduleType : 2
      // targetId 目标id：模块传模块id，其他传对应资源id
      const targetId = moduleType === 3 ? textbookId : item.id
      const query: {[key: string]: number | string | null} = {
        moduleManageId: item.moduleManageId || moduleManageId,
        studyLevel1: item.studyLevel1 || studyLevel1,
        subjectType: item.subjectType || subjectType,
        region: item.region || region,
        directionType: item.directionType || directionType,
        academicSection: item.academicSection || academicSection,
        examFormat: item.examFormat || examFormat,
      }
      Object.keys(query).forEach((key) => {
        if (query[key] === null || query[key] === '-1' || query[key] === -1) {
          delete query[key]
        }
      })
      const realHasBuy = moduleType === 3 ? questionBankStore.state.textBookInfo.hasBuy : hasBuy
      const jumpData = {
        ...query,
        targetType: realHasBuy !== 1 ? 99 : targetType,
        targetId: realHasBuy !== 1 ? item.moduleManageId || moduleManageId : targetId,
        expiry: route.query.expiry || ''
      }
      const url = `/question-bank/order-place?${objectToQueryString(jumpData)}`
      const jumpFlag = handleCheckJumpPage(url)
      if (jumpFlag) {
        return false
      }
      // isXcx ? userStore.jumpAppletWebPage(url) : navigateTo(url)
      if (os?.isApp) {
        handleJumpPlaceInApp({ ...jumpData, expiry: '' })
      } else {
        handleXcxJump(url)
      }
    }
  }

  /**
   * 处理跳转开通页面
   */
  const handleJumpPlaceInApp = async (query: any) => {
    const {
      studyLevel1,
      moduleManageId,
      targetType,
      targetId,
      expiry,
      subjectType,
      region,
      directionType,
      academicSection,
      examFormat
    } = query
    const postData = {
      studyLevel1: studyLevel1.toString(), // 原生类型为string
      moduleManageId,
      targetType: targetType.toString(), // 原生类型为string
      targetId,
      expiry: expiry.toString(), // 原生类型为string
      subjectType,
      region,
      directionType,
      academicSection,
      examFormat,
    }
    const postMessageData = JSON.stringify(postData)

    if (os?.isAndroid) {
      window.android.goExamBuy(postMessageData)
    } else if (os?.isPhone || os?.isTablet) {
      window.webkit.messageHandlers.goExamBuy.postMessage(postMessageData)
    } else if (os?.isHarmonyOS) {
      window.harmony?.goExamBuy(postMessageData)
    }
  }

  const route = useRoute()

  // 判断跳转页面是否为当前页面
  const handleCheckJumpPage = (url: string) => {
    const currentPath = route.path
    const jumpPath = url.substring(0, url.indexOf('?'))
    return currentPath === jumpPath
  }

  // 小程序跳转需要特殊处理，筛选不需要新开webview的页面
  const handleXcxJump = (url: string, replace?: boolean) => {
    if (isXcx) {
      userStore.jumpToQuestionBank(url)
    } else {
      navigateTo(url, { replace })
    }
  }

  return {
    handleJumpPlaceInApp,
    objectToQueryString,
    handleCheckPrivilege,
    handleTextBookRight,
    handleJumpClick,
    handleXcxJump,
    getIngList,
    clearSetInterVal,
    handleNote,
    handleLogin,
    handleSwitchMaterial,
    handleSwitchModel,
    getExerciseModel,
    getTextBookList,
    handleTips,
    handleWait,
    handleReset,
    handleSubscribe,
    checkModuleManageProt,
    setBuriedPopularForQuestion,
    handleQuestionShoppingType,
    modeList,
    textBookList
  }
}
