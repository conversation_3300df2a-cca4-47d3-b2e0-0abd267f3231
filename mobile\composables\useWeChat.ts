import { Base64 } from 'js-base64'
import { useFileView } from '../../base/composables/useFileView'
/**
 * 小程序相关
 *
 * 小程序预览
 * */
export const handleWeChatPreview = (url: string, isDownload = 0, name?: string) => {
  const fileType = url.split('.').pop()
  const fileTypes = ['zip', 'rar', '7z', 'tar', 'txt', 'png', 'jpg', 'jpeg', 'webp']
  if (isDownload === 1) {
    // 移除zip
    fileTypes.splice(0, 1)
  }
  if (!fileTypes.includes(fileType || '')) {
    console.log('[ handleWeChatPreview ] >', {
      url,
      isDownload,
      name,
      fileType,
    })
    wx.miniProgram.navigateTo({
      url: `/pages/filePreview/index?redirect_uri=${encodeURIComponent(url)}&isDownload=${isDownload}&fileName=${name}.${fileType}`
    })
  } else {
    const noFileTypes = ['zip', 'rar', '7z', 'tar']
    if (!noFileTypes.includes(fileType || '')) {
      if (isDownload === 0) {
        const { getViewUrl } = useFileView()
        window.location.href = getViewUrl(`/onlinePreview?url=${encodeURIComponent(Base64.encode(url))}`)
      } else {
        Message('该文件格式暂不支持下载～')
      }
    } else {
      Message('该文件格式暂不支持预览～')
    }
  }
}
