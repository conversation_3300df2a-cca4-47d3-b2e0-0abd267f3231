// 定义按钮状态和试卷类型的枚举
enum BtnStatus {
  StartExam = 0, // 开始考试
  DoingQuestions = 1, // 刷题中
  Done = 2, // 已做完
  NotStarted = 3, // 暂未开始
  ExamEnded = 4, // 考试已结束
  ScheduleEstimate = 5, // 预约估分
  ReExamPractice = 6, // 补考练习
}

enum ExamMode {
  FixedTime = 1, // 固定作答时长
  FixedPeriod = 2, // 固定时间段
  Estimate = 3, // 估分
  BigExam = 4, // 大联考
  FixedBrush = 5, // 固定刷题
}

// 定义按钮状态返回对象的类型
interface StatusInfo {
  text: string;
  disabled: number;
  className?: string;
  tag: string;
}

// 定义状态名称映射的类型
type StatusNames = {
  [key: string]: StatusInfo;
};

// 封装成公共方法，传入按钮状态和试卷类型
export function getExamStatusName (btnStatus: BtnStatus, examMode: ExamMode): StatusInfo {
  const key = `${btnStatus}-${examMode}` as keyof StatusNames

  const statusNames: StatusNames = {
    '0-1': { text: '开始答题', disabled: 0, className: '', tag: 'a' },
    '0-2': { text: '开始考试', disabled: 0, className: '', tag: 'a' },
    '0-3': { text: '开始估分', disabled: 0, className: '', tag: 'a' },
    '0-4': { text: '开始考试', disabled: 0, className: '', tag: 'a' },

    '1-1': { text: '继续答题', disabled: 0, className: '', tag: 'a' },
    '1-2': { text: '继续考试', disabled: 0, className: '', tag: 'a' },
    '1-3': { text: '继续估分', disabled: 0, className: '', tag: 'a' },
    '1-4': { text: '继续考试', disabled: 0, className: '', tag: 'a' },

    '2-1': { text: '开始答题', disabled: 0, className: '', tag: 'a' },
    '2-2': { text: '已交卷', disabled: 1, className: '', tag: 'button' },
    '2-3': { text: '开始估分', disabled: 0, className: '', tag: 'a' },
    '2-4': { text: '已交卷', disabled: 1, className: '', tag: 'button' }, // 大联考没有 2-4的状态

    '3-1': { text: '暂未开始', disabled: 1, className: '', tag: 'button' },
    '3-2': { text: '暂未开始', disabled: 1, className: '', tag: 'button' },
    '3-3': { text: '等待估分', disabled: 1, className: '', tag: 'button' },
    '3-4': { text: '暂未开考', disabled: 1, className: '', tag: 'button' },

    '4-1': { text: '考试结束', disabled: 1, className: '', tag: 'button' },
    '4-2': { text: '考试结束', disabled: 1, className: '', tag: 'button' },
    '4-3': { text: '估分结束', disabled: 1, className: '', tag: 'button' },
    '4-4': { text: '开始结束', disabled: 0, className: '', tag: 'button' }, // 大联考没有 4-4的状态

    '5-1': { text: '', disabled: 0, tag: '' },
    '5-2': { text: '', disabled: 0, tag: '' },
    '5-3': { text: '预约估分', disabled: 0, tag: 'button' },
    '5-4': { text: '', disabled: 0, className: '', tag: 'button' }, // 大联考没有 5-4的状态

    '6-1': { text: '', disabled: 0, tag: '' },
    '6-2': { text: '', disabled: 0, tag: '' },
    '6-3': { text: '', disabled: 0, tag: '' },
    '6-4': { text: '补考练习', disabled: 0, className: '', tag: 'a' }
  }

  // 返回对应状态信息
  return statusNames[key] || { text: '', disabled: 0, tag: '' }
}

/**
 * 处理试卷页面跳转URL
 * @param {any} currentPaper 当前试卷信息
 * @param {any} routeQuery 路由查询参数
 * @param {number} questionType 题目类型
 * @returns {string} 跳转URL
 */
export function handleToPaperPage (
  currentPaper: any,
  routeQuery: any,
  questionType?: number
): string {
  const { examMode, targetResultId, doStatus, testpaperId } = currentPaper

  // 确定 examStatus 的值
  const examStatus: string = ['start', 'continue', 'again'][doStatus] || 'start' // 默认值是 'start'

  // 从路由查询参数中获取必要的字段
  const {
    goodsMasterId,
    moduleName,
    moduleType,
    moduleId,
    moduleManageId
  } = routeQuery

  // 检查goodsMasterId是否存在且不为'0'
  const hasValidGoodsMasterId = goodsMasterId && goodsMasterId !== '0'

  // 根据goodsMasterId的有效性决定其他字段的值
  const params = {
    goodsMasterId: goodsMasterId || 0,
    studyLevel1: hasValidGoodsMasterId ? 0 : (routeQuery.studyLevel1 || 0),
    subjectType: hasValidGoodsMasterId ? 0 : (routeQuery.subjectType || 0),
    region: hasValidGoodsMasterId ? 0 : (routeQuery.region || 0),
    directionType: hasValidGoodsMasterId ? 0 : (routeQuery.directionType || 0),
    academicSection: hasValidGoodsMasterId ? 0 : (routeQuery.academicSection || 0),
    examFormat: hasValidGoodsMasterId ? 0 : (routeQuery.examFormat || 0),
    moduleType: hasValidGoodsMasterId ? 0 : (moduleType || 0),
    moduleName: moduleName || '',
    moduleId: moduleManageId || moduleId || '',
    examStatus,
    testpaperId: testpaperId || '',
    targetResultId: targetResultId || ''
  }

  // 构造URL参数数组
  const baseParams = Object.entries(params).map(([key, value]) => `${key}=${value}`)

  // 如果questionType存在且不为0，添加到参数列表中
  if (questionType) {
    baseParams.push(`questionType=${questionType}`)
  }

  // 根据examMode选择URL路径
  const url = examMode === 3
    ? `/question-bank/estimating?${baseParams.join('&')}`
    : `/question-bank/paper?${baseParams.join('&')}`

  return removePathUndefined(url)
}

/**
 * 过滤掉 URL 查询参数中值为 'undefined' 的项
 * @param url 需要处理的 URL 字符串
 * @returns 过滤后的 URL 字符串
 */
function removePathUndefined (url: string): string {
  // 拆分 URL 和查询参数
  const [baseUrl, queryParams] = url.split('?')

  // 如果没有查询参数，直接返回原始 URL
  if (!queryParams) {
    return url
  }

  // 处理查询参数
  const params = queryParams.split('&')
  const filteredParams = params.filter((param) => {
    const [, value] = param.split('=')
    return value && !value.includes('undefined') && value !== 'undefined' // 过滤掉值中包含 'undefined' 的参数
  })

  // 如果没有剩余的查询参数，返回基础 URL
  if (filteredParams.length === 0) {
    return baseUrl
  }

  // 拼接新的 URL
  return baseUrl + '?' + filteredParams.join('&')
}
