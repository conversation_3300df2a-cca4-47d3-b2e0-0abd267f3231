<template>
  <span
    v-show="visible"
    class="kkc-tag"
    :class="{
      [`tag-` + type]: type,
      [`tag-` + size]: size,
    }"
    :style="{ background: bgColor, color: color, width,borderWidth,borderColor,borderStyle }"
    @click="click"
  >
    <slot />

    <i v-if="closable" class="icon-close" @click="closeClick" />
  </span>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
withDefaults(
  defineProps<{
    type?: string
    closable?: boolean
    color?: string
    borderColor?: string
    bgColor?: string,
    width?:string,
    size?: 'large' |'middle'| 'small' | 'mini'| '',
    borderWidth?: string,
    borderStyle?: string
  }>(),
  {
    type: '',
    closable: false,
    color: '',
    borderColor: '#EB2330',
    borderWidth: '',
    borderStyle: 'solid',
    bgColor: '',
    size: '',
    width: '',
  }
)

const emits = defineEmits<{
  (e: 'click'): void
  (e: 'close'): void
}>()

const visible = ref<boolean>(true)
const closeClick = () => {
  visible.value = false
  emits('close')
}
const click = () => {
  emits('click')
}
</script>

<style scoped lang="scss">
.kkc-tag {
  max-width: 100%;
}
</style>
