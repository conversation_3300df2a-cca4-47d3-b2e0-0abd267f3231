// 用于检测当前页面的可见性变化
export const useVisibilityChange = (pageUrl?: string) => {
  const route = useRoute()
  // 当前页面的可见性状态
  const isHidden = ref<boolean>(document.hidden)
  // 当前页面的路由路径是否与传入的页面路径相同
  const isCurrentRoute = ref<boolean>(route.path === pageUrl)

  const handleVisibilityChange = () => {
    isHidden.value = document.hidden
  }
  // QQ浏览器
  const handleVisibilityQQChange = (e: any) => {
    isHidden.value = e.hidden
  }

  onMounted(() => {
    window.addEventListener('visibilitychange', handleVisibilityChange, false)
    window.addEventListener('qbrowserVisibilityChange', handleVisibilityQQChange, false)
  })

  onBeforeUnmount(() => {
    window.removeEventListener('visibilitychange', handleVisibilityChange)
    window.removeEventListener('qbrowserVisibilityChange', handleVisibilityQQChange)
  })

  return {
    isHidden,
    isCurrentRoute
  }
}
