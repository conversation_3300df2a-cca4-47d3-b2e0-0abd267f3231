<template>
  <div v-if="isPc">
    <kk-popup
      ref="submitRef"
      confirm-text="确定"
      cancel-text="取消"
      :is-have-close="false"
      title="温馨提示"
      text-align="left"
      :is-show-cancel="true"
      :is-delete="false"
      @close="cancel"
      @ok="confirm"
    >
      <div
        class="text-center min-h-[100px] text-[14px] leading-[28px] box-border mb-[24px]"
        v-html="message"
      />
    </kk-popup>
  </div>
  <div v-else>
    <!-- 跳转做题提示 -->
    <KKPopup
      ref="submitRef"
      title="温馨提示"
      confirm-text="确定"
      cancel-text="取消"
      :is-have-close="false"
      :is-delete="true"
      @ok="confirm"
      @close="cancel"
    >
      <div class="text-center mb-[20px]" v-html="message" />
    </KKPopup>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
const { isPc } = useAppConfig()
const visible = ref(false)
const message = ref('')
let resolveCallback: ((value: boolean) => void) | null = null
const submitRef = ref()

// 打开弹窗
const open = (msg: string): Promise<boolean> => {
  message.value = msg
  visible.value = true
  submitRef.value.showPopup()
  return new Promise<boolean>((resolve) => {
    resolveCallback = resolve // 重置回调
  })
}

// 关闭弹窗
const close = () => {
  visible.value = false
}

// 确认
const confirm = () => {
  if (resolveCallback) {
    resolveCallback(true)
  }
  resolveCallback = null // 重置回调
  visible.value = false
  if (isPc) {
    submitRef.value.hide()
  }
  close()
}

// 取消
const cancel = () => {
  if (isPc) {
    if (resolveCallback) {
      resolveCallback(false)
    }
    resolveCallback = null // 重置回调
  } else if (resolveCallback) {
    console.log(resolveCallback, 'resolveCallback')
    resolveCallback(false)
  }
  // close()
}

// 暴露方法供外部调用
defineExpose({ open })
</script>
<style lang="scss" scoped>
:deep(.popup-box-title) {
  margin-top: 0;
  margin-bottom: 20px;
}
</style>
