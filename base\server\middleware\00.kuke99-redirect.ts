import { redirectKuke99Url } from '../../utils/redirectKuke99Url'
const ENV_MAP:Record<string, string[]> = {
  STAGING: ['icloud-testc.tyyk99.com', 'm-icloud-testc.tyyk99.com'],
  PRESSURE: ['kuke99-c.pt.kukewang.com', 'm-kuke99-c.pt.kukewang.com'],
  PRE: ['kuke99-c.pre.kukewang.com', 'm-kuke99-c.pre.kukewang.com'],
  PROD: ['www.kuke99.com', 'm.kuke99.com', 'kuke99.com', 'kkqudao.kukecloud.cn', 'm-kkqudao.kukecloud.cn'],
}

// 域名映射
const getDomainMap = (isMobile: boolean) => {
  return new Map([
    ['m..kuke99.com', 'm.kuke99.com'],
    ['www..kuke99.com', 'www.kuke99.com'],
    ['kuke99.com', `${isMobile ? 'm' : 'www'}.kuke99.com`],
    ['.kuke99.com', `${isMobile ? 'm' : 'www'}.kuke99.com`],
    ['.pt.kukewang.com', `${isMobile ? 'm-kuke99-c' : 'kuke99-c'}.pt.kukewang.com`],
    ['.pre.kukewang.com', `${isMobile ? 'm-kkceshi123-c' : 'kkceshi123-c'}.pre.kukewang.com`]
  ])
}

const getRedirectHost = () => {
  const { public: { BUILD_ENV } } = useRuntimeConfig()
  return ENV_MAP[BUILD_ENV] || ['127.0.0.1:3000', '127.0.0.1:3006']
}

const getRedirectClient = (event: any) => {
  const ua = getRequestHeader(event, 'User-Agent')?.toLocaleLowerCase()
  return /ios|iphone|ipod|ipad|android|harmony/.test(ua || '')
}

// 是否是本地
const isLocal = (host: string) => /^(localhost\.|127)/.test(host)

export default defineEventHandler(async (event) => {
  const { path, method } = event
  const host = getRequestHost(event)
  const [pathname, qs = ''] = path.split('?')
  // 获取所在的环境
  const isMobile = getRedirectClient(event)
  // 获取域名映射关系
  const domainMap = getDomainMap(isMobile)
  // 当前域名使用的协议
  const protocol = getRequestProtocol(event)

  // console.log('---当前请求的域名---', method, getRequestURL(event)?.href)
  console.log(JSON.stringify({
    name: '当前请求的域名',
    method,
    href: getRequestURL(event)?.href,
  }))
  // 非GET请求 并且 非 ['/api/', '/prod-api/'] 接口
  // if (method.toLocaleUpperCase() !== 'GET' && !pathname.startsWith('/api/')) {
  //   // if (pathname !== '/') {
  //   //   return await sendRedirect(event, '/', 302)
  //   // }
  //   throw createError({
  //     statusCode: 405,
  //   })
  //   return
  // }
  if (method.toLocaleUpperCase() === 'GET' && getRedirectHost().includes(host)) {
    const isWap = ['127.0.0.1:3006', 'm-kuke99-c.pre.kukewang.com', 'm-icloud-testc.tyyk99.com', 'm.kuke99.com'].includes(host)
    const { type: goodsType, id: goodsId, TYPE, getPathname } = redirectKuke99Url(pathname, isWap)
    // 老网校菜单（我的订单、我的账户）跳转至个人中心
    const orderPathList = ['/ucenter/watch_record', '/ucenter/order', '/ucenter/collect', '/ucenter/address', '/ucenter/resource', '/ucenter/coupon', '/ucenter/account_security', '/ucenter/system_message', '/ucenter/buy_goods']
    if (orderPathList.includes(pathname)) {
      return await sendRedirect(event, '/user/order', 301)
    }
    // 老网校菜单（我的学习、我的已购）跳转至学习中心
    const learnPathList = ['/ucenter/learn_record', '/ucenter/exam_record', '/ucenter/live', '/ucenter/course', '/ucenter/exam']
    if (learnPathList.includes(pathname)) {
      return await sendRedirect(event, '/learn-center', 301)
    }
    if (TYPE && goodsId) {
      let redirectPath = getPathname!(goodsId, goodsType)
      //
      if (qs && redirectPath) {
        if (redirectPath.includes('?')) {
          redirectPath = redirectPath + '&' + qs
        } else {
          redirectPath = redirectPath + '?' + qs
        }
      }

      if (redirectPath && redirectPath !== '/') {
        // 获取域名
        const newHost = domainMap.get(host) || host
        // 整理为合法的域名
        const domain = !isLocal(newHost) ? `https://${newHost}${redirectPath}` : redirectPath
        return await sendRedirect(event, domain, 301)
      }
    }
    if (protocol === 'http' || domainMap.get(host)) {
      // 获取域名
      const newHost = domainMap.get(host) || host
      if (!isLocal(newHost)) {
        return await sendRedirect(event, `https://${newHost}${path}`, 301)
      }
    }
  }
})
