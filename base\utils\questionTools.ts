/**
 *  [AI GEN]
 *  Function to compare API checkedList with URL parameters
 */
export function compareTagParams (checkedList: Record<string, any>, urlParams: Record<string, any>): boolean {
  // Define the parameters we want to compare
  const paramsToCompare = [
    'subjectType',
    'region',
    'examFormat',
    'directionType',
    'academicSection'
  ]

  // Check if all relevant parameters match
  return paramsToCompare.every((param) => {
    // Convert both values to strings for comparison since URL params are strings
    const checkedValue = String(checkedList[param] || 0)
    const urlValue = String(urlParams[param] || 0)
    return checkedValue === urlValue
  })
}
