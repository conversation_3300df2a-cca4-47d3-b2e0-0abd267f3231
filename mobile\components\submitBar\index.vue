<template>
  <div class="tooter-pay-box">
    <div class="footer-pay-bar safe-area">
      <!-- 左侧图标 -->
      <div
        v-if="isShowShare || showCollect"
        class="footer-pay-service"
      >
        <!-- 收藏 -->
        <div
          v-if="showCollect"
          class="service-item"
          @click.stop.prevent="collectClickHandle"
        >
          <KKCIcon
            :name="`icon-${courseInfo?.isCollect === 1
              ? 'wode-shoucang-xuanzhong'
              : 'wode-shoucang-moren'
            }`"
            :color="courseInfo?.isCollect === 1 ? '#FFAA00' : 'inherit'"
            :size="38"
          />
          <p>收藏</p>
        </div>

        <!-- 客服 -->
        <!-- v-if="isShowShare && kefuStore.kefuEnabled" -->
        <div
          v-if="isShowShare && !!visibleKefuBtn"
          class="service-item"
          @click="openKefu"
        >
          <!-- <KKCIcon name="icon-xiangqing-fenxiang" color="#111" :size="38" /> -->
          <nuxt-icon
            name="kefu"
            class="text-[#111] text-[38px]"
          />
          <p>客服</p>
        </div>

        <!-- 购物车 -->
        <div
          v-if="isShowCart && isShowCartComputed && isShowShare && isNormalAddCartOfConfigData"
          class="relative service-item"
          @click.stop.prevent="cartClickHandle"
        >
          <img :src="cartIcon" alt="" class="w-[38px]">
          <p>购物车</p>
          <div
            v-if="cartCount"
            class="cart-count"
            :class="cartCount < 10 ? '!w-[32px]  !rounded-[50%]':''"
          >
            {{ cartCount }}
          </div>
        </div>
      </div>
      <!-- 右侧按钮 -->
      <div class="btn-warp">
        <!-- {{ showSingleCart }}-[临时调试] -->
        <!-- 加入购物车弹窗 -->
        <template v-if="showSingleCart">
          <KKCButton
            v-if="showSingleCart === 'detail'"
            width="100%"
            height="0.8rem"
            font-size="0.28rem"
            type="info"
            size="normal"
            :font-weight="600"
            round
            color="#fff"
            bg-color="var(--kkc-brand)"
            :disabled="cartDisabled || isNotBtnStatus"
            @click="handleAddCart('add')"
          >
            加入购物车
          </KKCButton>
          <KKCButton
            v-if="showSingleCart === 'cart'"
            width="100%"
            height="0.8rem"
            font-size="0.28rem"
            :font-weight="600"
            type="info"
            size="normal"
            round
            color="#fff"
            bg-color="var(--kkc-brand)"
            :disabled="cartDisabled || isNotBtnStatus"
            @click="handleAddCart('submit')"
          >
            确认
          </KKCButton>
        </template>

        <!-- ios小程序审核状态不可购买 -->
        <template v-else-if="showIosBtn">
          <KKCButton
            width="100%"
            height="0.8rem"
            font-size="0.28rem"
            :font-weight="600"
            type="info"
            size="normal"
            :disabled="true"
            round
            color="#fff"
            bg-color="var(--kkc-brand)"
          >
            暂不可购买
          </KKCButton>
        </template>
        <template v-else>
          <!-- 详情页面-底部按钮 加入购物车 -->
          <!-- {{ showSingleBuy }} -
          {{ isShowCartComputed }} -
          {{ isNormalAddCartOfConfigData }} - -->
          <KKCButton
            v-if="showSingleBuy && isShowCartComputed && isNormalAddCartOfConfigData"
            width="100%"
            height="0.8rem"
            :font-weight="600"
            font-size="0.28rem"
            type="info"
            size="normal"
            round
            color="var(--kkc-brand)"
            bg-color="rgba(var(--kkc-brand-rgb)/ 0.1)"
            :disabled="isNotBtnStatus || disabled || isDisBuySchool || isBookDisabled"
            border="none"
            @click="onCartBtnClick"
          >
            加入购物车
          </KKCButton>

          <template v-if="normalBtnDisable">
            <!-- 多规格弹窗 加入购物车 |立即购买.. -->
            <!-- isCart:{{ isCart }}
            isShowCart:{{ isShowCart }} -->
            <KKCButton
              v-if="isCart && isShowCart && !isSystem"
              width="100%"
              height="0.8rem"
              type="info"
              size="normal"
              :font-weight="600"
              round
              color="var(--kkc-brand)"
              font-size="0.28rem"
              bg-color="rgba(var(--kkc-brand-rgb)/ 0.1)"
              :disabled="disabled || isBookDisabled || cartDisabled || isNotBtnStatus"
              border="none"
              @click="onAddCartMulti"
            >
              加入购物车
            </KKCButton>
            <!-- isDisabled: {{ isDisabled }}
            disabled: {{ disabled }}
            isDisBuySchool: {{ isDisBuySchool }}
            isBookDisabled: {{ isBookDisabled }} -->
            <KKCButton
              width="100%"
              height="0.8rem"
              :font-weight="600"
              type="info"
              size="normal"
              :disabled="isDisabled || disabled || isDisBuySchool || isBookDisabled"
              round
              color="#fff"
              font-size="0.28rem"
              bg-color="var(--kkc-brand)"
              @click="onBuyBtnClick"
            >
              {{ submitBtnText }}
            </KKCButton>
          </template>

          <!-- 拼团购买 -->
          <template
            v-else-if="marketingInfo?.activityType === ActivityType.Group
              && courseStore.isSku"
          >
            <template
              v-if="((courseInfo.includeBook && courseInfo.bookStock) || !courseInfo.includeBook) &&
                showCollect
              "
            >
              <div v-if="marketingInfo?.activityStatus === 2" class="teamBuy-btn">
                <span @click="onBuyBtnClick">
                  <div>
                    ￥<b class="font-gilroy-bold">{{ courseInfo.goodsPresentPrice }}</b>
                  </div>
                  原价购买
                </span>
                <span @click="onBuyBtnTeam">
                  <div>
                    ￥<b class="font-gilroy-bold">{{ marketingInfo?.activityPrice }}</b>
                  </div>
                  发起拼团
                </span>
              </div>
            </template>
            <template
              v-if="((courseInfo.includeBook && courseInfo.bookStock) || !courseInfo.includeBook) &&
                !showCollect
              "
            >
              <KKCButton
                v-if="!isGroup"
                width="100%"
                height="0.8rem"
                font-size="0.28rem"
                :font-weight="600"
                type="info"
                size="tiny"
                :disabled="isDisabled || disabled || isDisBuySchool"
                round
                :color="'var(--kkc-brand)'"
                :bg-color="'rgba(var(--kkc-marketing-rgb) / 0.1)'"
                @click="onBuyBtnClick"
              >
                原价购买
              </KKCButton>
              <KKCButton
                v-else
                width="100%"
                height="0.8rem"
                font-size="0.28rem"
                :font-weight="600"
                type="info"
                size="tiny"
                :disabled="isDisabled || disabled"
                round
                color="#fff"
                :bg-color="'var(--kkc-marketing)'"
                @click="onBuyBtnTeam"
              >
                发起拼团
              </KKCButton>
            </template>
          </template>
          <!-- 活动购买 -->
          <template
            v-else-if="marketingInfo?.isMarketing !== 0
              && marketingInfo.activityType !== ActivityType.Group"
          >
            <!-- 多规格弹窗 点击规格 展示购物车 -->
            <KKCButton
              v-if="isCart && isShowCart
                && courseInfo.goodsType === GoodsType.MultiSpecification
                && !isSystem"
              width="100%"
              type="info"
              size="normal"
              height="0.8rem"
              font-size="0.28rem"
              :font-weight="600"
              round
              color="var(--kkc-brand)"
              bg-color="rgba(var(--kkc-brand-rgb)/ 0.1)"
              :disabled="disabled || isBookDisabled || cartDisabled || isNotBtnStatus"
              border="none"
              @click="onAddCartMulti"
            >
              加入购物车
            </KKCButton>
            <KKCButton
              width="100%"
              type="info"
              height="0.8rem"
              size="normal"
              font-size="0.28rem"
              :font-weight="600"
              round
              color="#fff"
              :disabled="isDisabled || isDisBuySchool || isBookDisabled"
              bg-color="var(--kkc-marketing--1)"
              @click="onBuyBtnClick"
            >
              <span v-if="marketingInfo?.activityStatus === 1">立即购买</span>
              <span
                v-else-if="courseInfo.btnStatus === BtnStatus.ContinueBuy
                  && marketingInfo?.activityStatus === 2"
              >继续购买</span>
              <span
                v-else-if="marketingInfo?.activityStatus === 2
                  && courseInfo.btnStatus === BtnStatus.BuyNow"
              >立即抢购</span>
            </KKCButton>
          </template>
        </template>
      </div>
      <MaskDialog ref="maskDialog" v-model="isShowDialog" :info="courseInfo" />
    </div>
    <!-- 分享商品弹窗 -->
    <!-- <SharePopup
      ref="shareTypePopupRef"
      :sku-title-img="skuTitleImg"
    /> -->
    <!-- refMemberFreeReceivePopup  -->
    <LazyMemberFreeReceivePopup
      v-if="showMemberFreeReceivePopup"
      ref="refMemberFreeReceivePopup"
    />
  </div>
</template>

<script setup lang="ts">
import { events } from '@kukefe/kkutils'
import MaskDialog from './maskDialog.vue'
import { ResourceTypes, GoodsType } from '@/apis/course/types'
import { useCartStore } from '~/stores/cart.store'
import { useUserStore } from '~/stores/user.store'
import cartIcon from '@/assets/images/cart/cart-icon.png'
import { useConfirmOrderStore } from '~/stores/confirm-order.store'
import { useCourseStore } from '~/stores/course.store'
// import type SharePopup from '@/components/SharePopup/index.vue'
// import type SharePoster from '@/components/SharePoster/index.vue'
import { useKefuStore } from '~/stores/kefu.store'
import { getItNowProt } from '~/apis/memberUser'
const showMemberFreeReceivePopup = ref(false)
const refMemberFreeReceivePopup = ref()
const showPopup = () => {
  showMemberFreeReceivePopup.value = true
  nextTick(() => {
    setTimeout(() => {
      if (refMemberFreeReceivePopup.value) {
        refMemberFreeReceivePopup.value.showPopup()
      }
    }, 100)
  })
}
const FaceToFaceResourceTypes = [
  ResourceTypes.OMOPackage,
  ResourceTypes.FaceToFaceCourse,
  ResourceTypes.FacePackage
]

// const query = useRoute().query
const courseStore = useCourseStore()
const userStore = useUserStore()
const cartStore = useCartStore()
const isShowCart = cartStore.getShowCart()

const memberInfo = computed(() => {
  return courseStore.memberInfo || {}
})

const props = withDefaults(
  defineProps<{
    // 是否展示收藏
    showCollect?: boolean;
    model?: 'pay' | 'buy';
    // ios 是否展示不可购买
    showIosBtn?: boolean,
    // 是否只展示加入购物车
    showSingleCart?: string,
    // 是否只展示单一购买
    showSingleBuy?: boolean,
    cartCount?: number,
    cartDisabled?: boolean,
    // 是否展示加入购物按钮
    isCart?: boolean,
    cartNumber?: number,
    // 是否展示分享 icon
    isShowShare?: boolean,
    targetCodeShareId?:string,
    isDisBuySchool?: boolean
  }>(),
  {
    showCollect: true,
    model: 'buy',
    showSingleCart: '',
    showSingleBuy: false,
    cartCount: 0,
    cartDisabled: false,
    isCart: false,
    cartNumber: 1,
    isShowShare: true,
    isDisBuySchool: false
  }
)

const { cartNumber } = toRefs(props)

const schoolInfo = computed(() => {
  return courseStore.schoolInfo
})

const courseInfo = computed(() => courseStore.courseInfo)
const marketingInfo = computed(() => courseStore.marketingInfo)
// 是否是多规格商品
const isSpecGoods = computed(() => courseStore.courseInfo.goodsType === GoodsType.MultiSpecification)

// 购买按钮文案
const submitBtnText = computed(() => {
  const { getCoursePayBtnText } = useCoursePayBtnText()
  //  多规格 spu 商品规格展示立即购买其他根据后端接口返回展示
  if (!courseStore.isSku) {
    return '立即购买'
  } else {
    return getCoursePayBtnText()
  }
})

const { courseApi, cartApi } = useApi()
const isShowDialog = ref<boolean>(false)
const maskDialog = ref()
// 图书类型商品禁用()
const isBookDisabled = computed(() => {
  const { resourceTypes, bookStock } = courseStore.courseInfo
  // 除了秒杀营销商品
  if (Number(courseStore.marketingInfo?.activityType) !== 2) {
    // 图书单品或者图书套餐且库存为 0 禁用
    return bookStock === 0 &&
    [ResourceTypes.Books, ResourceTypes.BooksPackage].includes(Number(resourceTypes))
  }
  return false
})
// 是否禁用通过按钮状态（暂不可购买 暂不可售）
const isNotBtnStatus = computed(() => {
  return [BtnStatus.NotAvailable, BtnStatus.Overdue].includes(courseInfo.value.btnStatus)
})
// interface SkuTitleImg {
//   goodsTitle: string,
//   itemImg: string
// }
// sku 的商品标题和商品图片
// const skuTitleImg = computed(():SkuTitleImg => {
//   const { goodsTitle = '', goodsImg = '' } = courseStore.courseInfo
//   let specsItemData: SkuTitleImg
//   // 分享的非多规格商品数据
//   if (courseInfo.value.goodsType !== GoodsType.MultiSpecification) {
//     specsItemData = { goodsTitle, itemImg: goodsImg }
//     // 分享的 sku 商品数据
//   } else if (courseStore.isSku) {
//     specsItemData = { itemImg: goodsImg, goodsTitle }
//   } else {
//     // 分享的 spu 商品数据
//     specsItemData = { itemImg: courseStore?.spuInfo?.goodsImg, goodsTitle, }
//   }
//   return specsItemData
// })

// 是否是属于代理商/crm/推广系统/scrm
const isSystem = computed(() => {
  return [1, 2, 4, 8].includes(Number(useRoute().query['kk-system']))
})
// 立即购买按钮配置自定义后 不展示加入购物车
const isNormalAddCartOfConfigData = computed(() => {
  if (courseStore.configData?.buyButton === 2) {
    return false
  }
  return true
})
// const isMemberGoods = ref(false)
// TODO 请求接口后是设置true
// const isMemberGoods = ref(true)
const isMemberGoodsAndFree = computed(() => {
  const { goodsType, } = courseStore.courseInfo
  const { isMember, price } = memberInfo.value
  if (isMember === 1 && goodsType === GoodsType.MultiSpecification) {
    return true
  }
  return isMember === 1 && +price === 0
})
const isShowCartComputed = computed(() => {
  const { resourceTypes, goodsType, goodsPresentPrice } = courseStore.courseInfo
  const { isMarketing, activityType, activityStatus } = marketingInfo?.value
  //
  // spu 商品展示加入购物车
  if (!courseStore.isSku && isShowCart) { return true }
  // 单品 面授 面授套餐，omo套餐 不展示
  const isNoFaceCourse = !FaceToFaceResourceTypes.includes(Number(resourceTypes))
  const isNormalCart = goodsType !== GoodsType.MultiSpecification
    ? (isShowCart && isNoFaceCourse && !isSystem.value && !isMemberGoodsAndFree.value)
    : (isShowCart && !isSystem.value && !isMemberGoodsAndFree.value)
  // 营销活动 分销，拼团正在活动中不展示加入购物车按钮  未开始的分销和拼团，秒杀正常加入购物车
  const isSeckill = (activityType === ActivityType.Seckill) ||
  (activityType !== ActivityType.Seckill && activityStatus !== 2)
  // 0元商品 不展示
  const isZeroPrice = ['0', '0.0', '0.00'].includes(goodsPresentPrice)
  // 暂不可售或者暂不可购买按钮不展示
  const isBtnStatus = !isNotBtnStatus.value
  const isBuy = !isZeroPrice && isBtnStatus
  return isMarketing ? (isSeckill && isNormalCart && isBuy) : (isNormalCart && isBuy)
})
// 普通购买按钮
const normalBtnDisable = computed(() => {
  const { btnStatus, includeBook, bookStock } = courseStore.courseInfo
  const { isMarketing, activityStatus, activityType } = courseStore.marketingInfo
  // 按钮状态是立即领取 且是0元拼团 需要显示拼团按钮
  if (!courseStore.isSku) { return true }
  if (btnStatus === BtnStatus.Learn && activityType === ActivityType.Group) { return false }
  if (btnStatus !== BtnStatus.ContinueBuy &&
    btnStatus !== BtnStatus.BuyNow) {
    return true
  }
  if (!isMarketing) {
    return true
  }
  const flag = activityType === ActivityType.Group &&
  (activityStatus === 2 && includeBook && !bookStock)
  if (flag) {
    return true
  }
  if (isMarketing && activityStatus === 1) {
    return true
  }
  return false
})

// 活动商品异常的回调
const callBack = (info: any) => {
  const { activityMsg } = info
  if (activityMsg) {
    if (marketingInfo?.value.activityType === ActivityType.Bargin) {
      Message(activityMsg)
      return
    }
    if (marketingInfo?.value.activityType === ActivityType.Group) {
      Message(activityMsg)
      return
    }
    maskDialog.value.showDialog(info?.activityMsg)
  }
}

// 即将开售 || 暂不可购买按钮禁用状态
const isDisabled = computed(() => {
  const { btnStatus } = courseStore.courseInfo
  const isShowBtn = (btnStatus === BtnStatus.ComingSoon ||
        btnStatus === BtnStatus.NotAvailable || btnStatus === BtnStatus.Overdue)
  if (props.showCollect) {
    return isShowBtn && !isSpecGoods.value
  }
  return isShowBtn
})

// 收藏
const collectClick = async () => {
  const { id, isCollect, cateIds, goodsTitle, resourceTypes, goodsPresentPrice } = courseStore.courseInfo
  const state = userStore.isLoginFn()
  if (state && id) {
    const status = isCollect === 1 ? 0 : 1
    const { data, error } = await courseApi.changeCollect({
      goodsMasterId: id as string,
      status,
    })
    if (!error.value) {
      courseStore.setCourseInfo({ ...courseStore.courseInfo, isCollect: status })
      Message(data.value!.msg)

      // 如果不是收藏就不 进行上传收藏的用户行为
      if (status !== 1) { return false }
      // 上传用户行为
      const { appId, isXcx } = useAppConfig()
      await updateBehaviorRecords(ActionEnum.CollectGoods, SourceEnum.Wap, {
        userMobile: userStore.getUserInfo?.mobile, // 用户手机号
        productId: appId, // 产品线
        clientType: isXcx ? '5' : '7',
        cateIds, // 专业分类
        goodsMasterId: id as string, // 商品id
        goodsTitle, // 商品标题
        resourceTypes: resourceTypes || '', // 商品类型
        goodsPresentPrice, // 商品现价
        isPromote: 0 // 是否推广 0 否 1 是
      })
    }
  }
}

// 收藏
const collectClickHandle = debounceFunc(() => {
  collectClick()
  // 更新最新的商品模板数据
  courseStore.courseTemplateIsEqual()
}, 500, true)

const cartClickHandle = debounceFunc(() => {
  cartClick()
}, 500, true)
const cartClick = () => {
  const state = userStore.isLoginFn()
  if (state) {
    navigateTo('/cart')
  }
}
// 拼团增加是拼团购买还是原价购买的标识
const isGroupBuy = (flag: 'groupBuy' | 'originBuy') => {
  const { activityType, groupProperty } = courseStore.marketingInfo
  if (activityType === ActivityType.Group && groupProperty) {
    courseStore.setMarketingInfo({
      ...courseStore.marketingInfo,
      groupProperty: { ...groupProperty, isGroup: flag }
    })
  }
}

const isGroup = computed(() => {
  return courseStore.marketingInfo?.groupProperty?.isGroup === 'groupBuy'
})
// 购买
const emits = defineEmits<{
  (e: 'click', type: string): void;
}>()
// 拼团购买
const onBuyBtnTeam = () => {
  const { goodsType, goodsTitle, cateIds } = courseStore.courseInfo
  const { activityType, activityTitle } = courseStore.marketingInfo
  // 多规格没有组成正确的组合时提示
  if (goodsType === GoodsType.MultiSpecification && !courseStore.isSku) {
    Message('请先选择商品规格')
    return false
  }
  isGroupBuy('groupBuy')
  // 多规格商品
  // if (isSpecGoods.value) {
  //   emits('click', 'multi')
  //   // 分校 （面授课相关）
  // } else if (isShowSchool.value) {
  //   emits('click', 'school')
  // } else {
  checkSchoolId()
  // }
  // 判断当前商品是否是拼团商品
  if (activityType === 1) {
    const { isXcx } = useAppConfig()
    addBehaviorRecords(ActionEnum.groupManage, SourceEnum.Wap, {
      goodsTitle: activityTitle, // 用户参与活动的名称
      clientType: isXcx ? ClientType.WX_XCX : ClientType.WAP,
      extension8: goodsTitle, // 结果
      cateIds// 专业分类

    })
  }
}
const buyEffectInfo = computed(() => {
  const { schoolId, schoolName } = schoolInfo.value
  return {
    ...courseStore.courseInfo,
    receiveSchoolId: schoolId, // 收款分校id
    receiveSchoolName: schoolName, // 收款分校名称
  }
})
const { disabled } = buyEffect({
  info: buyEffectInfo,
  options: {
    marketingInfo,
    specsId: courseStore.courseSkuId,
    callBack,
  },
})
watch(() => courseStore.courseSkuId, () => {
  disabled.value = false
})
// 加入购物车
const handleAddCart = (type:string) => {
  const state = userStore.isLoginFn()
  if (state) {
    emits('click', type)
  }
}
// 多规格加购物车
const onAddCartMulti = () => {
  const state = userStore.isLoginFn()
  if (state) {
    emits('click', 'multi')
  }
}
// 购物车弹窗
const onCartBtnClick = () => {
  const state = userStore.isLoginFn()
  if (state) {
    emits('click', 'cart')
  }
}

const { addBehaviorRecords } = useBehaviorRecord()

// 营销活动新增埋点
const addRecord = () => {
  const { goodsTitle, cateIds, buyAtMarkupProperty } = courseStore.courseInfo
  const { activityTitle, activityType, isBuyGive, buyGiveTitle } = courseStore.marketingInfo
  const { isXcx } = useAppConfig()

  let params = {
    extension8: goodsTitle,
    cateIds,
    clientType: isXcx ? ClientType.WX_XCX : ClientType.WAP,
    goodsTitle: ''
  }
  let behaviorId = ''

  // 判断当前商品是否是秒杀商品
  if (activityType === 2) {
    behaviorId = ActionEnum.SeckillManagement
    params = {
      ...params,
      goodsTitle: activityTitle!, // 用户参与活动的名称
    }
  }
  // 判断当前商品是否是加价购商品
  if (buyAtMarkupProperty?.buyAtMarkupId) {
    behaviorId = ActionEnum.PurchaseManage
    params = {
      ...params,
      goodsTitle: buyAtMarkupProperty.buyAtMarkupTitle!, // 用户参与活动的名称
    }
  }
  // 判断是否是买赠商品
  if (isBuyGive) {
    behaviorId = ActionEnum.PurchaseGiftManage
    params = {
      ...params,
      goodsTitle: buyGiveTitle, // 用户参与活动的名称
    }
  }
  // 判断当前商品是否是分销商品
  if (activityType === 3) {
    behaviorId = ActionEnum.DistributionManage
    params = {
      ...params,
      goodsTitle: activityTitle!, // 用户参与活动的名称
    }
  }

  addBehaviorRecords(behaviorId, SourceEnum.Wap, params)
}
const { usableId } = useLearnTarget()
const { handleDefaultPublish } = useBusinessComp()
// 立即购买
const onBuyBtnClick = () => {
  // 购买按钮配置自定义后 不生成订单
  // 未购买，如果商品按钮配置自定义，则跳转自定义
  // 继续购买按钮，如果商品按钮配置自定义，则跳转自定义
  if (courseStore.configData?.buyButton === 2 && (!courseStore.courseInfo?.isBuy || courseStore.courseInfo.btnStatus === 2)) {
    const params = {
      id: courseStore.courseInfo.id,
      componentName: 'CreateOrderBtn',
      item: courseStore.configData.assemblyBaseVO || {},
      index: 0,
      pageMasterId: usableId.value,
    }
    handleDefaultPublish(params)
    return false
  }
  // 将当前商品所参加的所有营销活动都加入埋点
  addRecord()
  // 多规格没有组成正确的组合时提示
  if (isSpecGoods.value && !courseStore.isSku) {
    Message('请先选择商品规格')
    return false
  }

  isGroupBuy('originBuy')
  // if (isSpecGoods.value) {
  //   emits('click', 'multi')
  // } else
  // if (isShowSchool.value) {
  //   emits('click', 'school')
  // } else {
  // 购买
  checkSchoolId()
  // }

  // 更新最新的商品模板数据
  courseStore.courseTemplateIsEqual()
}
const confirmOrderStore = useConfirmOrderStore()
const checkSchoolId = async () => {
  const { activityType } = marketingInfo.value
  const {
    btnStatus,
    resourceTypes,
    goodsAreas,
    id,
    orgId
  } = courseStore.courseInfo

  if (cartNumber.value > 1 && [BtnStatus.BuyNow, BtnStatus.ContinueBuy].includes(btnStatus)) {
    // 多规格购物车
    // 多数量 立即购买 跳转购物车下单页
    if (userStore.isLoginFn()) {
      const preOrderListParams = [
        {
          orgId,
          goodsList: [
            {
              goodsMasterId: id,
              specificationItemId: courseStore.courseSkuId,
              quantity: cartNumber.value,
              activityTypes: activityType ? [activityType] : []
            }
          ]
        }
      ]
      const params = { orders: preOrderListParams }
      const { error } = await cartApi.checkCartGoodsListSettlementAble(params as any)
      if (!error.value) {
        confirmOrderStore.setPreOrderListParams(preOrderListParams)
        navigateTo({
          path: '/confirm-order'
        })
      }
    }
  } else {
    // 单品单数量 直接购买  之前逻辑
    const schoolLength = goodsAreas?.length
    // if (useCheckSchoolNotButtonStatus(resourceTypes, schoolLength, schoolInfo.value.schoolId)) {
    //   // emits('click', 'school')
    //   // courseStore.openGoodsSku()
    //   return
    // }
    const flag = useCheckSchool(resourceTypes, schoolLength, schoolInfo.value.schoolId, btnStatus)
    if (flag) {
      // 参考自 useCourseThumbnail openSpecDrawer 方法
      courseStore.triggerOpenGoodsSku()
      return
    }
    /**
     * 如果是: 购买了会员 && 会员优惠后0元 && 按钮是【立即领取】
     * */
    const isUserMember = memberInfo.value.isMember === 1
    const goodsPresentPriceMember = +memberInfo.value.price === 0
    // 从会员中心进入-辨识是会员免费商品
    // if (query.customType === 'MEMBER_FREE' && isUserMember && +goodsPresentPriceMember === 0) {
    if (isUserMember && goodsPresentPriceMember) {
      if (unref(courseInfo).deliveryMethod === 1) {
        showPopup()
      } else {
        //
        userMemberReceiveGoods()
      }
      return
    }

    // 多规格没有组成正确的组合时提示
    // if (info.value?.goodsType === GoodsType.MultiSpecification && isNotFitSku.value) {
    //   Message('请先选择商品规格')
    //   return false
    // }
    const { schoolId, schoolName } = schoolInfo.value
    const { handleBuyBtn } = buyEffect({
      info: {
        ...courseStore.courseInfo,
        receiveSchoolId: schoolId, // 收款分校id
        receiveSchoolName: schoolName, // 收款分校名称
      },
      options: {
        marketingInfo,
        specsId: courseStore.courseSkuId,
        callBack,
      },
    })
    handleBuyBtn(props.targetCodeShareId)
  }
}

/**
 * 不需要发货商品
 * 点击立即领取按钮
若未填写收货地址，toast提示“请先填写收货地址”
若已填写收货地址，toast提示“领取成功”，关闭此弹窗，领取成功不生成订单，订单列表不显示
 *
*/
const userMemberReceiveGoods = async () => {
  //
  const { resourceTypes, cateIds, id } = courseStore.courseInfo || {}

  const body = {
    cateId: cateIds,
    // TODO
    goodsId: id,
    // goodsId: '735875016151552000',
    goodsType: 1,
    receiveSchoolId: courseStore?.schoolInfo?.schoolId, //
    // TODO
    resourceType: resourceTypes,
    // deliveryInfos: null
  }
  const { error, data } = await getItNowProt(body)
  if (!error.value && data.value?.code === '10000') {
    Message('领取成功')
    setTimeout(() => {
      location.reload()
    }, 500)
  }
}

defineExpose({ onBuyBtnClick })
// 在线客服
const visibleKefuBtn = ref(false)
const kefuStore = useKefuStore()
const openKefu = () => {
  // window?.showKefuModal()
  kefuStore.getKefuDataOfBtn({
    networkSchoolPageId: 103,
    cateIds: courseStore.courseInfo?.cateIds
  })
  kefuStore.openKefuModal()
}
onMounted(() => {
  nextTick(async () => {
    console.log('cateIds', courseStore.courseInfo?.cateIds)
    const { visibleKefuBtn: _visibleKefuBtn } = await kefuStore.getKefuDataOfBtn({
      networkSchoolPageId: 103,
      cateIds: courseStore.courseInfo?.cateIds
    })
    visibleKefuBtn.value = _visibleKefuBtn
  })
})

onMounted(() => {
  if (process.client) {
    events.subscribe('ReceiveGoods:deliveryMethod', showPopup)
  }
})
onUnmounted(() => {
  events.unsubscribe(showPopup)
})
</script>

<style scoped lang="scss">
.tooter-pay-box {
  height: calc(env(safe-area-inset-bottom) + 112px);

  .footer-pay-bar {
    @apply flex justify-center items-center ;
    width: 7.5rem;
    position: fixed;
    left: 50%;
    transform: translateX(-50%);
    bottom: 0;
    display: flex;
    align-items: center;
    padding: 20px 24px;
    background-color: #fff;
    z-index: 61;
    box-shadow: inset 0px 1px 0px 0px rgba(238, 238, 238, 0.5);
    backdrop-filter: blur(10px);

    .footer-pay-service {
      @apply flex justify-center items-center;
      .service-item{
        @apply flex flex-col items-center justify-center;
        padding-right: 24px;
      }
      p {
        margin-top: 8px;
        font-size: 20px;
        font-weight: normal;
        color: $fontColor-666;
      }
      .cart-count{
        position: absolute;
        top: -14px;
        right: 8px;
        width: 44px;
        height: 32px;
        border: 2px solid #FFFFFF;
        background: #EB2330;
        text-align: center;
        line-height: 28px;
        font-size: 20px;
        color: #fff;
        border-radius: 22px;
      }
    }

    .btn-warp {
      @apply flex justify-between items-center flex-1;
      .teamBuy-btn {
        width: 100%;
        height: 80px;
        display: inline-block;

        span {
          display: inline-block;
          width: 50%;
          height: 80px;
          line-height: 30px;
          font-size: 26px;
          font-weight: 600;
          letter-spacing: 1px;
          text-align: center;

          div {
            padding-top: 12px;
            font-size: 24px;
          }

          b {
            font-size: 34px;
          }

          &:nth-of-type(1) {
            border-radius: 16px 0px 0px 16px;
            color: var(--kkc-marketing--1);
            background: rgba(var(--kkc-marketing-rgb) / 0.1) ;
          }

          &:nth-of-type(2) {
            color: #fff;
            border-radius: 0px 16px 16px 0px;
            background:var(--kkc-marketing);

          }
        }
      }
    }
  }
}
</style>
