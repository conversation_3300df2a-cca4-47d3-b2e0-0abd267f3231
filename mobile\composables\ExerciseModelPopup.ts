import { createApp } from 'vue'
import { SetModePopup } from '../pages/question-bank/components/index'

export const ExerciseModelPopup = ({ modeList, exerciseNumList, exerciseNum, isShowExerciseNum, confirm, defaultRule = 0 }: { modeList: any[], exerciseNumList: number[], exerciseNum: number, isShowExerciseNum: boolean, defaultRule: number, confirm: (item: any) => void }) => {
  if (process.client) {
    let AppInstance: any = null
    const container: HTMLDivElement = document.createElement('div')
    AppInstance = createApp(SetModePopup, {
      show: true,
      modeList,
      exerciseNumList,
      isShowExerciseNum,
      exerciseNum,
      defaultRule,
      close: () => {
        AppInstance && AppInstance.unmount()
        container && document.body.removeChild(container)
      },
      confirm
    })

    document.body.appendChild(container)
    AppInstance.mount(container)
  }
}
