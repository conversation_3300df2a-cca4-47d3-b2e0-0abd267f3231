/**
 * 处理库课云订制业务判断的hooks
 * @returns
 */
export const useKuKeCloud = () => {
  // const config = useRuntimeConfig() as any
  const { KUKE_CLOUD_ORG_ID_LIST } = useAppConfig()
  // eslint-disable-next-line
  const numStr = KUKE_CLOUD_ORG_ID_LIST.replace(/[\[\]']+/g, ''); // 去掉引号和方括号，得到 "1,2" 或 "1"
  const kukeCloudOrgIDList: string[] = numStr ? numStr.split(',') : [] // 将数字部分转为数组

  /**
   * 通过组织id判断是否属于库课云产品线
   * @param orgId
   * @returns
   */
  const isKukeCloudProductLineByOrgId = (orgId: string) => {
    return kukeCloudOrgIDList.includes(orgId)
  }

  return {
    isKukeCloudProductLineByOrgId,
  }
}
