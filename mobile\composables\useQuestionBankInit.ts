import { useQuestionBankStore } from '~/stores/question.bank.store'
import type { ClassBasicType } from '~/pages/question-bank/types/basic'

export const useQuestionBankInit = () => {
  const questionBankStore = useQuestionBankStore()
  const { transformObject } = useHistoryState()

  const { questionBankApi } = useApi()

  const route = useRoute()

  /**
   * 标识当前分类需要下次选中
   */
  const tabChangeFlag = ref(false)

  /**
   * 筛选条件
   */
  const initQueryData = computed(() => {
    if (route.query.studyLevel1) {
      return transformObject(route.query)
    } else {
      const historyList = questionBankStore.getHistory()
      const classIds = classList.value.map((item: any) => item.studyLevel1)
      // 获取当前上次选中分类的第一条记录
      const preSelectClass = historyList[0]
      // 获取所有历史记录并筛选包含当前模块记录
      const hasModuleHistory = historyList.filter((item: any) => item.moduleManageId === route.query.moduleManageId)
      if (!hasModuleHistory || hasModuleHistory.length === 0) {
        const queryData = questionBankStore.getQueryData()
        // 没有记录时，根据当前可选分类第一条记录作为默认
        return classIds.includes(queryData.studyLevel1)
          ? queryData
          : {
              subjectType: -1,
              region: -1,
              academicSection: -1,
              directionType: -1,
              examFormat: -1,
              studyLevel1: classIds[0],
              moduleManageId: route.query.moduleManageId
            }
      }

      // 根据customClassSelect筛选出标识上次选中分类记录
      const customClassSelect = hasModuleHistory.find((item: any) => item.customClassSelect)
      if (customClassSelect) {
        // 标识本次存储的记录
        tabChangeFlag.value = true
        return customClassSelect
      }

      // 筛选出拥有当前可选分类的第一条记录用以默认
      if (classIds.includes(preSelectClass.studyLevel1)) {
        const hasPreSelect = hasModuleHistory.find((item: any) => item.studyLevel1 === preSelectClass.studyLevel1)
        return hasPreSelect || {
          ...hasModuleHistory[0],
          studyLevel1: preSelectClass.studyLevel1
        }
      }

      const history = hasModuleHistory.find((item: any) => classIds.includes(item.studyLevel1))
      return history || hasModuleHistory[0]
    }
  })

  const UNDEFINED_TAG_VALUE = -1
  const NULL_TAG_VALUE = null
  const handelTagParams = (tagValue: any) => {
    if (tagValue === 0) {
      return 0
    } else if (Object.prototype.toString.call(tagValue) === '[object Null]') {
      return NULL_TAG_VALUE
    } else if (typeof tagValue === 'undefined') {
      return UNDEFINED_TAG_VALUE
    } else {
      return tagValue
    }
  }

  const handleInitParams = () => {
    const { subjectType, directionType, region, academicSection, examFormat } = initQueryData.value
    return {
      subjectType: handelTagParams(subjectType),
      directionType: handelTagParams(directionType),
      region: handelTagParams(region),
      academicSection: handelTagParams(academicSection),
      examFormat: handelTagParams(examFormat)
    }
  }

  /**
   * 获取分类列表数据
   */
  const getClassifyListData = async () => {
    const { moduleManageId = '', moduleId = '' } = useRoute().query
    const { data } = await questionBankApi.fetchCategoryListByModuleId({
      moduleManageId: moduleManageId as string || moduleId as string,
    })
    classList.value = data.value.list
    isEmpty.value = !data.value.list.length
  }

  /**
   * 分类列表数据
 */
  const classList = ref<ClassBasicType[]>([])

  /**
   * 初始化
   */
  const init = async () => {
    if (route.query.studyLevel1) {
      const customTagSelect = !!questionBankStore.getLastCustomTagHistory(Number(route.query.studyLevel1))
      const customListPageTagSelect = !!questionBankStore.getLastListPageCustomTagHistory(Number(route.query.studyLevel1))
      questionBankStore.setQuestionQuery({
        ...transformObject(route.query),
        customTagSelect,
        customListPageTagSelect
      })
    }
    await getClassifyListData()
    // 初始化分类
    const initCurrent = classList.value.find((item: any) => item.studyLevel1 === initQueryData.value.studyLevel1)
    if (!initCurrent && !!classList.value.length) {
      // 没有分类，直接展示暂无数据状态
      isEmpty.value = true
      modeList.value = []
      textBookList.value = []
      return false
    }
    const initClassify: number = initCurrent ? initQueryData.value.studyLevel1 : classList.value[0].studyLevel1
    // 根据存储获取当前分类
    currentClassify.value = initClassify
    if (initQueryData.value.studyLevel1) {
      questionBankStore.setQueryData('moduleManageId', initQueryData.value.moduleManageId)
      // 初始标签为0，存在最后一条记录则赋值
      const params = handleInitParams()
      await getModuleTagsList(true, params)
    } else {
      await getModuleTagsList(true)
    }
  }

  return {
    initQueryData,
    init,
    handleInitParams,
    handelTagParams
  }
}
