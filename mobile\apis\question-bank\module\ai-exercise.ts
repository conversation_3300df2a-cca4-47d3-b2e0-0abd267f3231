enum Api {
  // Ai练习开始做题
  startAiExercise = '/kukecorequestion/wap/ai/startProt',
  // Ai练习提交答案
  submitAiExercise = '/kukecorequestion/wap/ai/submitQuestionProt',
  // Ai练习暂存
  submitAiLeaveExercise = '/kukecorequestion/wap/ai/submitLeaveProt',
  // Ai练习交卷
  submitAiPaper = '/kukecorequestion/wap/ai/submitFinishProt',
  // Ai练习解析
  aiExerciseAnalysis = '/kukecorequestion/wap/ai/getMyAnswerProt',
  // Ai练习报告
  aiExerciseScoreReport = '/kukecorequestion/wap/ai/reportProt',
  // Ai练习历史
  aiExerciseHistory = '/kukecorequestion/wap/ai/historyProt',
  // Ai整体报告
  aiExerciseAllReport = '/kukecorequestion/wap/ai/systemReportProt',
  // Ai练习规则说明
  aiExerciseRule = '/kukecorequestion/wap/ai/getRule',
  // Ai练习知识版本
  aiExerciseKnowledgeVersion = '/kukecorequestion/wap/ai/moduleManageSystem',
  // Ai练习列表
  aiExerciseList = '/kukecorequestion/wap/ai/getPointList',
  // Ai练习配置
  aiUserExerciseConfig = '/kukecorequestion/wap/ai/getUserSettingProt',
  // Ai练习继续做题
  aiExerciseContinue = '/kukecorequestion/wap/ai/doContinueProt',
}

/**
 * Ai练习开始做题
 *
 * @param  body - 请求参数
 * @returns Promise
 */
export async function startAiExercise (body: any) {
  return useHttp<any>(Api.startAiExercise, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * Ai练习继续做题
 *
 * @param body - 请求参数
 * @returns Promise
 */
export async function aiExerciseContinue (body: any) {
  return useHttp<any>(Api.aiExerciseContinue, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * Ai练习提交答案
 *
 * @param  body - 请求参数
 * @returns Promise
 */
export async function submitAiExercise (body: any) {
  return useHttp<any>(Api.submitAiExercise, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * Ai练习暂存
 *
 * @param  body - 请求参数
 * @returns Promise
 */
export async function submitAiLeaveExercise (body: any) {
  return useHttp<any>(Api.submitAiLeaveExercise, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * Ai练习交卷
 *
 * @param  body - 请求参数
 * @returns Promise
 */
export async function submitAiPaper (body: any) {
  return useHttp<any>(Api.submitAiPaper, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * Ai练习解析
 *
 * @param  body - 请求参数
 * @returns Promise
 */
export async function aiExerciseAnalysis (body: any) {
  return useHttp<any>(Api.aiExerciseAnalysis, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * Ai练习报告
 *
 * @param  body - 请求参数
 * @returns Promise
 */
export async function aiExerciseScoreReport (body: any) {
  return useHttp<any>(Api.aiExerciseScoreReport, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * Ai练习历史
 *
 * @param  body - 请求参数
 * @returns Promise
 */
export async function aiExerciseHistory (body: any) {
  return useHttp<any>(Api.aiExerciseHistory, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * Ai整体报告
 *
 * @param  body - 请求参数
 * @returns Promise
 */
export async function aiExerciseAllReport (body: any) {
  return useHttp<any>(Api.aiExerciseAllReport, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * Ai练习规则说明
 *
 * @param  body - 请求参数
 * @returns Promise
 */
export async function aiExerciseRule (body: any) {
  return useHttp<any>(Api.aiExerciseRule, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * Ai练习知识版本
 *
 * @param body - 请求参数
 * @returns Promise
 */
export async function aiExerciseKnowledgeVersion (body: any) {
  return useHttp<any>(Api.aiExerciseKnowledgeVersion, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * Ai练习列表
 *
 * @param body - 请求参数
 * @returns Promise
 */
export async function aiExerciseList (body: any) {
  return useHttp<any>(Api.aiExerciseList, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * Ai练习配置
 *
 * @param body - 请求参数
 * @returns Promise
 */
export async function aiUserExerciseConfig (body: any) {
  return useHttp<any>(Api.aiUserExerciseConfig, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}
