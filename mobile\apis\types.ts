export interface ApiResponse<T = any> {
    code: string
    msg?: string
    time?: string
    success?: boolean
    data: T
  }

/**
 * 布尔枚举  （适用于 后端返回 0 1 的逻辑值）
 */
export enum BooleanEnum {
  Yes = 1, // 是
  Not = 0, // 否
}

export interface GetAdvertisementPhotoListRequestData {
  /** 专业分类id */
  cateId?: number;
  /** 市 */
  cityId?: number;
  /** 地区 */
  regionId?: number;
  /** 科目 */
  subjectId?: number;
  /** 班型 */
  classType?: number;
  /** 专业分类列表（资讯标签列表使用 或者 M端定制首页课程区轮播位专业分类是公考） ,Integer */
  cateIdList?: number[];
  /** 产品端(1 pc   5小程序  7wap  9app) */
  productSide: number;
  /** 方向 */
  directionId?: number;
  /** 地区 支付成功页使用 ,Integer */
  regionIdList?: number[];
  /** 考试形式 */
  examFormatId?: number;
  /** 科目 支付成功页使用 ,Integer */
  subjectIdList?: number[];
  /** 班型 支付成功页使用 ,Integer */
  classTypeList?: number[];
  /** 方向 支付成功页使用 ,Integer */
  directionIdList?: number[];
  /** 考试形式 支付成功页使用 ,Integer */
  examFormatIdList?: number[];
  /** 学段 */
  academicSectionId?: number;
  /**
   * 广告位id
   * （移动端
   * 1轮播组件
   * 10资讯详情页广告位
   * 8支付成功页广告位
   * 9商品列表页广告位
   * 11资讯详情页正文下方广告位1
   * 12资讯详情页正文下方广告位2
   * 13资讯详情页正文下方广告位3
   * 14资讯详情页正文下方广告位4
   * 15首页顶部轮播图
   * 16首页课程区轮播位）
   * （pc端 1轮播组件
   * 2资讯项目推荐页广告位
   * 3资讯列表页广告位
   * 4资讯分类列表页广告位
   * 5资讯标签列表页广告位
   * 6资讯详情页上方广告位
   * 7资讯详情页右侧侧边栏广告位
   * 8支付成功页广告位
   * 9商品列表页广告位
   * 11资讯详情页正文下方广告位1
   * 12资讯详情页正文下方广告位2
   * 13资讯详情页正文下方广告位3
   * 14资讯详情页正文下方广告位4
   * 15首页通屏广告
   * 16首页课程区轮播位
   * 17首页顶部广告）
   * */
  advertisementPlaceId: number;
  /** 学段 支付成功页使用 ,Integer */
  academicSectionIdList?: number[];
}

export interface GetAdvertisementPhotoListResponseData {
  id: string;
  /** 广告样式 */
  img: string;
  /** 链接地址 */
  url: string;
  /** 广告名称 */
  title: string;
  /** 小程序appid */
  appid: string;
  /** 专业分类id */
  cateId: number;
  /** 广告状态 0禁用 1启用 */
  status: number;
  /** 休息日 ,Integer */
  dayOff: number[];
  /** 商品分组id */
  groupId: string;
  /** 结束时间 */
  endTime: string;
  /** 排序值 */
  sorting: number;
  /** 专业分类 ,Integer */
  cateIds: number[];
  /** 小程序配置id */
  appletId: string;
  /** 订单编号 ,String */
  orderSns: string[];
  /** 产品线id */
  productId: number;
  /** 跳转类型类别 */
  dicSkipId: number;
  /** 开始时间 */
  startTime: string;
  /** 创建时间 */
  createdAt: string;
  /** 地区 ,Integer */
  regionIds: number[];
  /** 小程序名称 */
  appletName: string;
  /** 科目 ,Integer */
  subjectIds: number[];
  /** 班型 ,Integer */
  classTypes: number[];
  /** 商品类型 ,Integer */
  goodsTypes: number[];
  /** 投放终端 json ,Integer */
  putInClient: number[];
  /** 产品端 移动端 20 pc 1 */
  productSide: number;
  /** 商品分组 */
  groupIdName: string;
  /** 状态 0 正常 1 删除 2 不可用 */
  groupStatus: number;
  /** 自定义页面id */
  customPageId: string;
  /** 小程序状态 0 正常 1 删除 */
  appletStatus: number;
  /** 方向 ,Integer */
  directionIds: number[];
  /** 投放终端 ,Integer */
  putInClients: number[];
  /** 功能类型类别 */
  dicFunctionId: number;
  /** 功能类型类别 是大礼包使用  大礼包id  */
  giftPackageId: string;
  /** 配置状态 0 关闭 1 开启 */
  disableStatus: number;
  /** 考试形式 ,Integer */
  examFormatIds: number[];
  /** 自定义页面名称 */
  customPageName: string;
  /** 小程序秘钥 */
  appidSecretKey: string;
  /** 大礼包名称 */
  giftPackageName: string;
  /** 状态 0 正常 1 删除 2 禁用  */
  customPageStatus: number;
  /** 小程序原始id */
  appletOriginalId: string;
  /** 后续再有新的状态全部用这个  0 不可用 1 正常 2类型已修改 3已被删除 */
  publicSkipStatus: string;
  /** 打卡日 ,Integer */
  punchTheClockDay: number[];
  /** 大礼包状态 0 正常  4 活动已结束 5 活动已禁用  8 活动已删除 15 礼包不可用 */
  giftPackageStatus: number;
  /** 客服ID */
  customerServiceId: string;
  /** 学段 ,Integer */
  academicSectionIds: number[];
  /** 客服名称 */
  customerServiceName: string;
  /** 客服类型 0库课云客服  1企业微信 */
  customerServiceType: string;
}
