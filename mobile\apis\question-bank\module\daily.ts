import type {
  TargetResultId,
  submitQuestType,
  leaveSubmitQuestType,
  finishSubmitQuestType,
} from '../types'
import filterQuestionBankBody from '../../../../base/utils/filterBody'

enum Api {
  // 开始做题
  dailyStart = '/kukecorequestion/wap/daily/startProt',
  // 继续做题
  dailyDoContinue = '/kukecorequestion/wap/daily/doContinueProt',
  // 提交答案
  dailySubmitQuestion = '/kukecorequestion/wap/daily/submitQuestionProt',
  // 暂存
  dailySubmitLeave = '/kukecorequestion/wap/daily/submitLeaveProt',
  // 提交
  dailySubmitFinish = '/kukecorequestion/wap/daily/submitFinishProt',
  // 每日报告
  dailyReportProt = '/kukecorequestion/wap/daily/reportProt',
}

/**
 * 每日一练---开始练习
 *
 * @param {Object} body
 * @returns {Promise}
 * */
export async function getDailyStart (body: any) {
  return useHttp<any>(Api.dailyStart, {
    method: 'post',
    body: filterQuestionBankBody(body),
    transform: res => res.data,
  })
}

/**
 * 每日一练---继续练习
 *
 * @param {Object} body
 * @returns {Promise}
 * */
export async function getDailyDoContinue (body: any) {
  return useHttp<any>(Api.dailyDoContinue, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 每日一练---试题提交（一提一提交）
 *
 * @param {submitQuestType} body
 * @returns {Promise}
 * */
export async function dailySubmitQuestion (body: submitQuestType) {
  const { ptypeValueId, ...params } = body
  const isMultipleChoice = [2, 3].includes(ptypeValueId)
  return useHttp<any>(Api.dailySubmitQuestion, {
    method: 'post',
    body: params,
    transform: res => res.data,
    watch: false,
    isShowLoading: isMultipleChoice,
    isShowMask: isMultipleChoice
  })
}

/**
 * 每日一练---中途提交
 *
 * @param {leaveSubmitQuestType} body
 * @returns {Promise}
 * */
export async function dailySubmitLeave (body: leaveSubmitQuestType) {
  return useHttp<any>(Api.dailySubmitLeave, {
    method: 'post',
    body,
    transform: res => res.data,
    watch: false,
  })
}

/**
 * 每日一练---完成提交
 *
 * @param {finishSubmitQuestType} body
 * @returns {Promise}
 * */
export async function dailySubmitFinish (body: finishSubmitQuestType) {
  return useHttp<any>(Api.dailySubmitFinish, {
    method: 'post',
    body,
    transform: res => res.data,
    watch: false

  })
}

/**
 * 每日一练---报告页
 *
 * @param {TargetResultId} body
 * @returns {Promise}
 * */
export async function getDailyReportProt (body: TargetResultId) {
  return useHttp<any>(Api.dailyReportProt, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}
