import { productSideEnum, BooleanEnum } from '../../../base/constants/index'
import { CourseLiveStatus } from '~/components/catalog/type'
import type { CustomGoodsInfo } from '~/pages/course/types'

/**
 * @enum GoodsType
 * @description 商品类型枚举，用于标识不同种类的商品。
 * 其中包含以下类型：
 * - OnlineCourse (1): 网课
 * - FaceToFaceCourse (2): 面授
 * - Books (3): 图书
 * - Examination (4): 试卷
 * - Package (5): 套餐
 * - MultiSpecification (6): 多规格
 * - QuestionBank (7): 题库
 * - Collection (8): 合集
 * - Other (9): 其他
 * - Audio (10): 音频
 * - Ebook (12): 电子书
 */
export enum GoodsType {
  OnlineCourse = 1,
  FaceToFaceCourse,
  Books,
  Examination,
  Package,
  MultiSpecification,
  QuestionBank,
  Collection,
  Other,
  Audio,
  Ebook=12
}

/**
 * @enum ResourceTypes
 * @description 资源类型枚举，用于标识不同种类的资源。
 * 其中包含以下类型：
 * - OnlineCourse (1): 网课
 * - FaceToFaceCourse (2): 面授
 * - Books (3): 图书
 * - Examination (4): 试卷
 * - OnlinePackage (5): 网课套餐
 * - OMOPackage (6): OMO套餐
 * - BooksPackage (7): 图书套餐
 * - FacePackage (8): 面授套餐
 * - ExaminationPackage (9): 试卷套餐
 * - QuestionPackage (10): 题库套餐
 * - QuestionBank (11): 题库
 * - Collection (12): 合集
 * - Other (13): 其他
 * - OtherCourse (14): 其他套餐
 * - Audio (15): 音频
 * - AudioPackage(16): 音频套餐
 * - Ebook (19): 电子书
 * - EbookPackage (20): 电子书套餐
 *
 */
export enum ResourceTypes {
  /** 网课 */
  OnlineCourse = 1,

  /** 面授 */
  FaceToFaceCourse,

  /** 图书 */
  Books,

  /** 试卷 */
  Examination,

  /** 网课套餐 */
  OnlinePackage,

  /** OMO套餐 */
  OMOPackage,

  /** 图书套餐 */
  BooksPackage,

  /** 面授套餐 */
  FacePackage,

  /** 试卷套餐 */
  ExaminationPackage,

  /** 题库套餐 */
  QuestionPackage,

  /** 题库 */
  QuestionBank,

  /** 合集 */
  Collection,

  /** 其他 */
  Other,

  /** 其他套餐 */
  OtherCourse,

  /** 音频 */
  Audio,

  /** 音频套餐 */
  AudioPackage,
  /** 电子书 */
  Ebook=19,
  /** 电子书套餐 */
  EbookPackage
}

/**
 * @enum TagIcon
 * @description 枚举类，用于定义标签图标的映射关系。
 * 这些图标用于在用户界面中直观地表示不同的标签类型。
 * 包含以下映射：
 * - DIRECTION_TYPE: 方向图标 ('icon-fangxiang')
 * - REGION: 地区图标 ('icon-diqu')
 * - SUBJECT_TYPE: 科目图标 ('icon-kemu')
 * - ACADEMIC_SECTION: 学段图标 ('icon-xueduan')
 * - EXAM_FORMAT: 考试形式图标 ('icon-kaoshixingshi')
 */
export enum TagIcon {
  /** 方向图标 */
  DIRECTION_TYPE = 'icon-fangxiang',

  /** 地区图标 */
  REGION = 'icon-diqu',

  /** 科目图标 */
  SUBJECT_TYPE = 'icon-kemu',

  /** 学段图标 */
  ACADEMIC_SECTION = 'icon-xueduan',

  /** 考试形式图标 */
  EXAM_FORMAT = 'icon-kaoshixingshi',
}

export interface ExamTypes {
    /** 按钮状态：0开始考试 1刷题中 2已做完 3暂未开始 4考试已结束 5预约估分 6补考练习 */
  btnStatus: number;

  /** 人气（无意义字段） */
  clickCount: number;

  /** 已做题数 */
  doCount: number;

  /** 做题状态：0开始考试 1刷题中 2已做完 */
  doStatus: number;

  /** 考试结束时间 */
  endTime: string;

  /** 考试模式：1固定作答时长 2统考 3估分 4联考 */
  examMode: number;

  /** 估分模式下，是否有更新 */
  hasUpdated: number;

  /** 试卷id */
  id: string;

  /** 试卷里试题总数-兼容老版本字段 */
  itemCount: number;

  /** 试卷里试题总数-新代码用次字段 */
  allCount: number;

  /** 估分试卷记录id（无意义字段） */
  itemLogId: string;

  /** 考试时长 */
  limitTime: number;

  /** 模块id */
  moduleManageId: string;

  /** 联考模式：交卷时间 */
  mustSubmitTime: string;

  /** 考试说明 */
  note: string;

  /** 是否有报告：0无 1有 */
  reportTargetResultId: string;

  /** 轮次（无意义字段） */
  round: number;

  /** 总分 */
  score: string;

  /** 考试开始时间 */
  startTime: string;

  /** 用户做题记录id */
  targetResultId: string;

  /** 试卷类型标题（无意义字段） */
  testpaperTypeTitle: string;

  /** 试卷类型id */
  testpaperTypeValueId: number;

  /** 考试时间范围 */
  timeRange: string;

  /** 试卷标题 */
  title: string;

  /** 试卷别名 */
  name: string;

  userStartTime: string | null;
}

interface GoodsQuestion {
  cateId: number; // 专业分类
  regionId: string; // 地区
  academicSectionId: string; // 学段
  subjectId: string; // 科目
  examFormatId: string; // 考试形式
  directionId: string; // 方向
  id: string; // 商品题库id
  moduleManageId: string; // 模块id
  questionCount: string; // 试题总数
  questionScore: string; // 试卷总分
  targetId: string; // 目标id（试卷、教材或刷题id）
  targetType: number; // 目标类型：99模块 2试卷 3教材 4固定刷题
  targetTypeName: string; // 目标类型名称
  validityMonth: string; // 有效期
}
export interface PackageCourseListItem {
  /** 课时数 */
  statsCount:number,

    /** 当goodsType=1时的课表信息 */
  goodsCourseNodeList: string[];

  /** 当goodsType=4时的试卷列表 */
  testpapers: string[];

  /** 商品图片 */
  goodsImg: string;

  /** 商品主表id */
  goodsMasterId: string;

  /** 商品现价 */
  goodsPresentPrice: number;

  /** 商品标题 */
  goodsTitle: string;

  /** 商品类型 */
  goodsType: number;

  /** 右边统计信息 */
  statsInfo: string;

  /** 是否有试听视频 1是 0否 */
  hasFreeVideo: number;

  // 题库模块
  goodsQuestion: GoodsQuestion;
}

export interface PackageList {
  /** 商品类型：1 网课，2 面授，3 图书，4 试卷包，5 套餐，6 多规格，7 题库模块，8 合集，9 其他商品 */
  goodsType?: number;

  /** 类型对应名称 */
  goodsTypeTitle?: string;

  /** 商品列表 */
  list?: PackageCourseListItem[];
}

export interface Teacher {
  /** 简介 */
  briefIntroduction: string;

  /** 讲师ID */
  id: string;

  /** 形象照 */
  imagePhoto: string;

  /** 讲师类别：1:讲师 2:助教 */
  lecturerCategory: number;

  /** 讲师类型 */
  lecturerType: number;

  /** 讲师类型的名称 */
  lecturerTypeVaule: string;

  /** 讲师职务 */
  lectureship: string;

  /** 头像 */
  profilePicture: string;

  /** 昵称 */
  teacherNickName: string;

  /** 真实姓名 */
  trueName: string;

  /** 用户ID */
  userId: string;
}

export interface WordageLabels {
  goodsMasterId: string,
  backgroundColor: string,
  labelFillet: string,
  labelHeight: string,
  leftRightMargin: string,
  wlName: string,
  wordageColor: string,
  wordageSize: string,
  labelWidth?: string,
  outlineColor?: string,
}

// 1 直播 2 录播 3 讲义 4 试题 5 音频
export enum CourseType {
  'Live' = 1,
  'Record',
  'Handout',
  'Question',
  'Audio',
}

export enum CourseNodeType {
  'Chapter' = 1,
  'Section',
  'ClassHour',
}

export interface CourseNode {
  fileType: string;
  ccVideoId: string,
  children?: Array<CourseNode>,
  courseId: string,
  // 讲义文件名
  handoutFilename: string,
  handoutUrl: string,
  id: string,
  isFree: number,
  isBuy?: number,
  liveStartDate?: string,
  liveEndDate?: string,
  liveEndTime: number,
  liveStartTime: number,
  liveStatus: CourseLiveStatus,
  nodeType: CourseNodeType
  parentId: string
  polyvVideoId: string
  sorting: number
  title: string
  type: CourseType
  isCrossDay?: number
  // 视频时长（秒数）
  videoDuration: string
  itemCount: number
  score: number
}

// 规格信息
export interface Specs {
  name: string,
  id: string
}

export interface specsTypes {
  id: string,
  name: string,
  value: Specs[]
}
export interface SchoolType {
  id: string,
  departmentName: string
}
export interface GoodsSpecificationItem {
  academicSectionIds: string;
  cateIds: string;
  classTypeNames: string;
  classTypes: string;
  classroomType: number;
  courseModeNames: string;
  courseModes: string;
  examFormatIds: string;
  goodsPresentPrice: number;
  goodsPrice: number;
  goodsTitle: string;
  goodsType: number;
  id: string;
  isOnlineSell: number;
  isSell: number;
  isSupervise: number;
  lessonTypeSorting: string[];
  minChargePrice: number;
  minPrice: number;
  otherPrice: string;
  priceRate: string;
  regionIds: string;
  roomPrice: string;
  saleCount: number;
  saleEndTime: number;
  saleStartTime: number;
  specification: specsTypes[];
  specificationId: string;
  status: number;
  subjectIds: string;
  subjectNames: string;
  teachingMaterialPrice: string;
  validityDay: number;
  resourceTypes:string;
  goodsAreas?: SchoolType[], // 分校
  isAdGoods?:number,
  isPromote?:number,
  validityDate:string,
  validityType:string,
  itemContent: string,
  itemImg: string
}

export interface SeckillProperty {
  countDown: number
  seckillId: string
  activityStatus: number
  isSeckill: number
  seckillPrice: number
}

export interface Group {
  countDown: number
  groupId: string
  activityStatus: number
  isSeckill: number
  groupPrice: number
}

export interface RecentlyLiveInfo {
  liveStartTime: number;
  liveStartTimeFormat: string;
  liveStatus: number;
  liveStatusName: string;
}
export interface GoodsSpecification {
  specs: specsTypes
}

export interface ClassTypeList {
  bgColor: string;
  classType: number;
  classTypeName: string;
  fontColor: string;
}

export interface VideoInfo extends CourseNode {
  copyNodeId?: string,
  testpaperId?: string,
  createAdminId?: string,
  updateAdminId?: string,
  createdAt?: string,
  updatedAt?: string,
  deletedAt?: string
}

// 加购商品信息
export interface BuyAtMarkupGoodsItem {
  goodsImg: string
  goodsPrice: number
  goodsTitle: string
  raisePrice: number
  goodsMasterId: string
  specificationItemId?: string
  deliveryMethod: number
}

export interface TagType {
  title: string,
  values: string,
  category: string
}
export interface FieldType {
  field: string,
  choose: number,
}
export interface FieldInfoType {
  [key: string]: number
}

/**
 * @description 商品班型
 */
export interface ClassTypeInfo {
  classTypeName: string
  bgColor: string
  fontColor: string
}

export interface SpecificationItem {
  name: string
  value: string
}

interface SpecificationItemMapType {
  [key: string]: string
}
export interface CourseDetailModel {
  data: any;
  /** 班型 */
  classType: number,
  classroomList: PackageCourseListItem[],
  classroomListAll: PackageCourseListItem[],
  /** 商品详情 */
  content: string,
  /** 优惠券信息 */
  coupon: string,
  /** 授课方式 */
  courseMode: string
  /** 网课关联的课表信息,商品是多规格，并且是单品时，为某个规格的值 */
  goodsCourseNodeList: Array<CourseNode>,
  /** 商品图片 */
  goodsImg: string,
  /** 现价,多规格时为规格中的最低价，商品是多规格时为某个规格的值 */
  goodsPresentPrice: string,
  /** 原价,多规格时为规格中的最低价，iso端要求价格返回string */
  goodsPrice: string,
  /** 商品副标题 */
  goodsSubtitle: string,
  /** 商品标题 */
  goodsTitle: string,
  /** 商品主ID */
  goodsMasterId: string,
  /** 商品类型 1网课，2面授 3图书 4试卷 5套餐 6多规格 7题库模块 8合集 */
  goodsType: GoodsType,
  /** 1网课，2面授 3图书4 试卷 5网课套餐，6OMO套餐 7图书套餐 8面授套餐 9试卷套餐 10题库模块套餐 11题库模块 12合集 多规格是多个值 */
  resourceTypes: string,
  /** 商品主表id */
  id: string,
  /** 具体商品id */
  goodsId: string,
  /** 所属地区的集合 */
  regionIds: string,
  /** 所属学段的集合 */
  academicSectionIds: string,
  /** 考试方向 */
  directionIds: string,
  /** 考试形式的集合 */
  examFormatIds: string,
  /** 课程包含内容，商品是多规格时为某个规格的值 */
  includeContent: string[],
  /** 包含科目 */
  subjectIds: string,
  /** 包含科目名称 */
  subjectNames: string,
  /** 商品关联的讲师信息，商品是多规格时为某个规格的值  */
  teachers: Teacher[],
  /** 学员有效期 */
  validityDay: number,
  /** 用户是否收藏 */
  isCollect: number,
  /** 授课方式名称 */
  courseModeNames: string,
  /** 网课试听视频信息，商品是多规格时为某个规格的值 */
  videoInfo?: VideoInfo,
  /** 图书库存 */
  bookStock: number,
  /** 班型对应名称 */
  classTypeNames: string,
  /** 文字标签信息 */
  wordageLabels: WordageLabels[],
  /** 商品子规格信息 */
  specs: specsTypes[],
  /** 按钮状态  1 立即购买 2 继续购买 3 已购买，去学习 4 立即领取 5 已领取 6 立即学习 7 即将开售 8 暂不可购买 9 暂不可售 */
  btnStatus: number,
  /** 按钮状态名称，商品是多规格时为某个规格的值 */
  btnStatusName: string,
  /** 是否包含图书：1包含，商品是多规格时为某个规格的值 */
  includeBook: number,
  /** 秒杀属性 */
  seckillProperty: SeckillProperty,
  /** 可领优惠券 */
  couponText: string
  /** 最近直播的直播信息，商品是多规格时为某个规格的值 */
  recentlyLiveInfo: RecentlyLiveInfo,
  /** 拼团属性 */
  groupProperty: Group,
  /** 预售开始时间 */
  presaleStartTime: string,
  /** 用户是否购买 0未购买 1已购买 */
  isBuy: number,
  /** 试卷信息 */
  testpapers?: ExamTypes[],
  /** 是否是营销商品 */
  isMarketing: number,
  /** 班型信息 */
  classTypeList: ClassTypeList[],
  /** 规格包含商品为一个商品时的单品或套餐的masterId */
  specificationSingleGoodsMasterId: string
  /** 拼团购买类型  originBuy原价购买  groupBuy拼团购买 */
  isGroup?: boolean
  /** 专业分类ids，逗号隔开 */
  studyLevels: string,
  /** 直播类型 1保利威  2cc */
  liveType?: string,
  /** 库存 */
  stock?: string,
  /** 教材费 */
  teachingMaterialPrice: string,
  /** 住宿费 */
  roomPrice: string,
  /** 其他费用 */
  otherPrice: string,
  /** 分校列表 */
  goodsAreas?: SchoolType[],
  /** 标签 */
  tags?: TagType[],
  /** 分校id */
  receiveSchoolId?: string;
  /** 分校名称 */
  receiveSchoolName?: string;
  /** 权益id */
  userBuyUnitGoodsId?:string
  /** 加价购属性 */
  buyAtMarkupProperty?: {
    /** 加价购id */
    buyAtMarkupId: string
    /** 加价购换购文本 */
    buyAtMarkupMsg: string
    /** 使用门槛 */
    useThreshold: string
  }
  buyAtMarkup?: {
    useThreshold: number
    buyAtMarkupId: string
    buyAtMarkupGoods: BuyAtMarkupGoodsItem[]
  },
  /** 是否为督学商品  0否 1是 */
  isSupervise: number,
  /** 是否是引流商品 0否 1是 */
  isAdGoods: number,
  /** 是否是推广商品 0否 1是 */
  isPromote: number,
  /** 有效期截至日期 */
  validityDate: string,
  /** 有效期类型 1天数 2日期 */
  validityType: number,
  /** 套餐内容，套餐类型的商品用户手填的字段内容 */
  classroomContent: string,
  /** 子规格id */
  specsId?: string,
  /** crm分享价 */
  modifiedGoodsPrice?:string,
  /** 组织id */
  orgId: string,
  /** 专业分类 id */
  cateIds: string,
  /** 试听有效期 */
  tryListenValidityHour: string
  /** 是否禁止分享 0否 1是 */
  forbidShare: string
  /** 子规格id */
  goodsSpecificationItemId: string
  /** seo标题 */
  seoTitle: string
  /** seo关键字 */
  seoKeyword: string
  /** seo描述 */
  seoDesc: string,
  /** 规格组合 */
  specification: SpecificationItem[],
  /** 规格对应规格id键值对，格式为：规格1_规格2=>规格主键id(该参数为map) */
  specificationItemMap: SpecificationItemMapType,
  /** 上课开始时间 */
  classStartDate: string
  /** 上课结束时间 */
  classEndDate: string
  /** 是否需要发货 1 需要 0 不需要 */
  deliveryMethod: number
  /** 真实销量 */
  saleCount: string,
  /** 真实销量+虚拟销量 */
  saleCountSum: string,
  /** 班型信息 */
  classTypeInfo?: ClassTypeInfo,
  /** 自定义渲染商品信息返回的内容 */
  customGoodsInfo?: CustomGoodsInfo
  /** 子规格商品的商品类型（goodsType 是多规格商品商品类型） */
  specGoodsType?: number
}

// 收藏
export interface AddCollectParams {
  goodsMasterId: string
  status: number
}

export interface SpecsInfoParams {
  goodsSpecificationItemId: string
  id: string,
  promotion: any
}

export interface specsInfoTypes {
  names: string[];
  combinedName: string;
}

export interface CheckGoodsParams {
  id: string
  goodsSpecificationItemId?: string
  seckillId?: string
}
export interface CheckGoodsModel {
  data: {
    goodsCode: string
    goodsMsg: string
    activityStatus: number
    activityMsg: string
  }
  id: string
  goodsSpecificationItemId?: string
}
export interface LivePortResult {
  livePlatform: number
}
export interface LivePortModel {
  data: LivePortResult
}

export enum GoodsFieldChooseEnum {
  /** 未选中 */
  Disable = 0,

  /** 选中 */
  Enable = 1
}

export interface FetchGoodsFieldListParams {
  /** 商品ID */
  goodsMasterId: string;

  /** 规格ID（非多规格商品无需传递） */
  goodsSpecificationItemId?: string;

  /** 运营后台是否勾选：
   *  - 不传递时，默认获取全部字段；
   *  - 0表示不可用，1表示可用；
   *  - 可选字段 */
  choose?: GoodsFieldChooseEnum;
}

export interface GoodsFieldItem {
  field: string// 字段名
  choose: GoodsFieldChooseEnum// 是否选中
}

export interface FetchGoodsFieldListResponseData {
  masterFields: GoodsFieldItem[],
  specFields: GoodsFieldItem[]
}
export interface TeacherParams {
  userBuyUnitGoodsId?: string,
  orderId?: string

}
export interface QrList {
  qrCode?: string,
  goodsTitle?: string,
  img?: string
  content?: string,
  id?: string
}
export interface TeacherResponseData {
  isShow: number
  qrCode?: string
  msg: string,
  superviseTeacherList: QrList[]
}
export interface adGoodsParams {
  id: string
}
export interface adGoodsParamsCart {
  goodsMasterIds: string[]
}
export interface adGoodsResponseData {
  id: string
  qrCode: string
  content: string
  img: string
}
export interface btnStatusParam {
  id: string
  goodsSpecificationItemId?: string
  deptId: string
}
export interface btnStatusResponseData {
  btnStatus: number
  btnStatusName: string
}
export interface miniResponseData {
  templateId: string
  status: number
}

export interface GoodsItem {
  id: string
  specification: SpecificationItem[]
  status: number
  goodsPresentPrice: string
}
export interface SKUPrice {
  goodsItemList: GoodsItem[]
  goodsTitle: string
  priceRange: string
}

// 套餐内容
export interface PackageListData {
  /** 套餐信息 */
  classroomList: PackageList[],
  /** 套餐总列表 */
  classroomListAll: PackageList[],
}

export interface SchoolInfo {
  schoolId: string, schoolName: string
}

/**
 * 请求商品详情页面配置参数
 */
export interface FetchGoodsDetailsPageConfigParams {
  productSide: productSideEnum,
  goodsId: string
}

/**
 * 菜单设置实体
 */
export interface MenuSetEntity {
  /**
   * 排序
   */
  sort: number;
  /**
   * 字典code
   */
  dictCode?: string; // 非必须，mock: @string
  /**
   * 数据显示类型 (1 显示, 2 隐藏)
   */
  showType: number; // 必须
  /**
   * 字典名称
   */
  dictName?: string; // 非必须，mock: @string
}

/**
 * 商品详情页面配置DTO
 */
export interface FetchGoodsDetailsPageConfigDTO {
  /**
   * 广告位图片
   */
  img: string;

  /**
   * 是否展示广告位 0不显示 1显示
   */
  isAdvertisement: BooleanEnum;

  /**
   * 跳转类型类别
   */
  dicSkipId?: number; // 非必须

  /**
   * 功能类型类别
   */
  dicFunctionId?: number; // 非必须

  /**
   * 链接地址
   */
  url?: string; // 非必须

  /**
   * 大礼包id
   */
  giftPackageId?: string; // 非必须

  /**
   * 自定义页面id
   */
  customPageId?: string; // 非必须

  /**
   * 自定义页面名称
   */
  customPageName?: string; // 非必须

  /**
   * 大礼包名称
   */
  giftPackageName?: string; // 非必须

  /**
   * 大礼包状态 0正常 4 活动已结束 5 活动已禁用 8 活动已删除 15 礼包不可用
   */
  giftPackageStatus?: number; // 非必须

  id: string;

  /**
   * 引导浮窗停留几秒后出现
   */
  remainTime: number;

  /**
   * 入口文案
   */
  entranceCopyWriting: string;

  /**
   * 浮窗打开图片
   */
  windowImg: string;

  /** 已售数量类型：
   *  - 0: 隐藏
   *  - 1: 显示实际已售数量
   *  - 2: 显示实际已售数量 + 虚拟已售数量
   * 可选字段 */
  soldNumberType?: number;

  /** 免费商品名称 */
  freeGoodsName?: string;

  /** 付费商品名称 */
  payGoodsName?: string;

  /** 是否显示商品价格：
   *  - 0: 不显示商品价格
   *  - 1: 显示商品价格
   * 可选字段 */
  isGoodsPrice?: number;

  /**
   * 菜单设置
   */
  menuSet?: MenuSetEntity[]
}

/**
 * 讲义DTO
 */
export interface TeachingMaterialsDTO {
  /**
   * 下载地址 （必须）
   */
  downloadUrl: string;
  /**
   * 文件格式 （必须）
   */
  handoutFormat: string;
  /**
   * 讲义id （必须）
   */
  handoutId: string;
  /**
   * 讲义名称 （必须）
   */
  handoutName: string;
  /**
   * 文件大小 kb （必须）
   */
  handoutSize: number;
  /**
   * 预览地址 （必须）
   */
  previewUrl: string;
}

/**
 * 合集商品请求参数
 */
export interface CollectionsListQuery {
  /**
   * 界面配置ID
   * @type {string}
   * @required
   */
  pageMasterId: string;

  /**
   * 销量排序方向，默认传值 'desc'，可选 'asc' 或 'desc'
   * @type {string}
   * @optional
   */
  saleCountSort?: 'asc' | 'desc';

  /**
   * 价格排序方向，传 'asc' 或 'desc'
   * @type {string}
   * @optional
   */
  priceSort?: 'asc' | 'desc';

  /**
   * 当前页面的页码
   * @type {number}
   * @optional
   */
  page?: number;
  // 如果是合集商品参数的话 固定参数 为1 音频无需此参数
  hasFree?: number
}
/**
 * 合集商品列表返回数据
 */
export interface CollectionResponse {
  /**
   * 列表总数
   * @type {number}
   * @optional
   */
  count?: number;

  /**
   * 商品列表信息
   * @type {Array<Object>}
   * @optional
   */
  list?: Array<{
    /**
     * 班型对应名称
     * @type {string}
     * @required
     */
    classTypeNames: string;

    /**
     * 班型
     * @type {string}
     * @required
     */
    classTypes: string;

    /**
     * 商品内容，如直播、录播、讲义等
     * @type {Array<string>}
     * @required
     */
    content: string[];

    /**
     * 商品ID
     * @type {string}
     * @required
     */
    goodsId: string;

    /**
     * 商品图片
     * @type {string}
     * @required
     */
    goodsImg: string;

    /**
     * 商品价格
     * @type {string}
     * @required
     */
    goodsPresentPrice: string;

    /**
     * 商品原价
     * @type {string}
     * @required
     */
    goodsPrice: string;

    /**
     * 商品标题
     * @type {string}
     * @required
     */
    goodsTitle: string;

    /**
     * 商品类型
     * @type {number}
     * @required
     */
    goodsType: number;

    /**
     * 商品主表ID
     * @type {string}
     * @required
     */
    id: string;

    /**
     * 商品销量
     * @type {number}
     * @required
     */
    saleCount: number;

    /**
     * 文字标签信息
     * @type {Array<Object>}
     * @required
     */
    wordageLabels: Array<{
      /**
       * 商品的背景色
       * @type {string}
       * @required
       */
      backgroundColor: string;

      /**
       * 商品主ID
       * @type {string}
       * @required
       */
      goodsMasterId: string;

      /**
       * 标签圆角
       * @type {string}
       * @required
       */
      labelFillet: string;

      /**
       * 标签高度
       * @type {string}
       * @required
       */
      labelHeight: string;

      /**
       * 标签宽度
       * @type {string}
       * @required
       */
      labelWidth: string;

      /**
       * 标签的左右边距
       * @type {string}
       * @required
       */
      leftRightMargin: string;

      /**
       * 标签轮廓颜色
       * @type {string}
       * @required
       */
      outlineColor: string;

      /**
       * 商品的名称
       * @type {string}
       * @required
       */
      wlName: string;

      /**
       * 文字标签的颜色
       * @type {string}
       * @required
       */
      wordageColor: string;

      /**
       * 文字标签的大小
       * @type {string}
       * @required
       */
      wordageSize: string;
    }>;
    /**
     * 商品标识名称，例如“体验”或“图书”
     * @type {string}
     * @required
     */
    mark: string;
  }>;
}
/**
 * 合集商品详情列表请求参数
 */
export interface CollectionsDetailQuery {
  /**
   * 合集商品ID
   * @type {string}
   * @required
   */
  id: string;

  /**
   * 当前页
   * @type {number}
   * @required
   */
  page: number;

  /**
   * 每页条数
   * @type {number}
   * @required
   */
  pageSize: number;

  /**
   * 销量排序方向，默认传 'desc'，可选 'asc' 或 'desc'
   * @type {string}
   * @optional
   */
  saleCountSort?: 'asc' | 'desc';

  /**
   * 价格排序方向，传 'asc' 或 'desc'
   * @type {string}
   * @optional
   */
  priceSort?: 'asc' | 'desc';
}

/**
 * 合集商品详情列表返回数据
 */

export interface CollectionsDetailResponse {
  /**
   * 返回的消息
   * @type {string}
   * @optional
   */
  msg?: string;

  /**
   * 请求是否成功
   * @type {boolean}
   * @optional
   */
  success?: boolean;

  /**
   * 时间戳或时间信息
   * @type {string}
   * @optional
   */
  time?: string;

  /**
   * 列表总数
   * @type {number}
   * @optional
   */
  count?: number;

  /**
   * 商品列表信息
   * @type {Array<Object>}
   * @optional
   */
  list?: Array<{
    /**
     * 参与的营销活动
     * @type {string[]}
     * @optional
     */
    activityTypes?: string[];

    /**
     * 班型列表信息
     * @type {string[]}
     * @optional
     */
    classTypeList?: string[];

    /**
     * 班型
     * @type {string}
     * @optional
     */
    classTypes?: string;

    /**
     * 商品包含的内容
     * @type {string[]}
     * @optional
     */
    content?: string[];

    /**
     * 课时数
     * @type {number}
     * @optional
     */
    courseHour?: number;

    /**
     * 商品ID
     * @type {string}
     * @optional
     */
    goodsId?: string;

    /**
     * 商品图片链接
     * @type {string}
     * @optional
     */
    goodsImg?: string;

    /**
     * 商品现价
     * @type {string}
     * @optional
     */
    goodsPresentPrice?: string;

    /**
     * 商品原价
     * @type {string}
     * @optional
     */
    goodsPrice?: string;

    /**
     * 商品名称
     * @type {string}
     * @optional
     */
    goodsTitle?: string;

    /**
     * 商品类型
     * @type {number}
     * @optional
     */
    goodsType?: number;

    /**
     * 商品唯一标识
     * @type {string}
     * @optional
     */
    id?: string;

    /**
     * 商品标识名称
     * @type {string}
     * @optional
     */
    mark?: string;

    /**
     * 销量数量
     * @type {number}
     * @optional
     */
    saleCount?: number;

    /**
     * 商品副标题
     * @type {string}
     * @required
     */
    goodsSubtitle: string;

    /**
     * 海报位置
     * @type {string[]}
     * @optional
     */
    wordageLabels?: string[];
  }>;
   /**
     * 商品标题
     * @type {string}
     * @required
     */
   goodsTitle: string;

   /**
    * 商品包含内容
    * @type {string}
    * @required
    */
   content: string;

   /**
    * 商品图片
    * @type {string}
    * @required
    */
   goodsImg: string;
}
export interface FetchGoodsTagParamsType {
  /**
   * 类型 1.商品id 2.权益id
   */
  targetType: '1' | '2' | '3';
  /**
   * 商品/权益id
   */
  targetId: string;
  specificationItemId?: string;
}

/**
 * 根据商品id或权益id分类以及标签信息
 */
export interface GoodsTagDTO {
  /**
   * 单品标题
   */
  title: string;
  /**
   * 模块类型 1每日练习 2试卷 3章节 4固定刷题
   */
  moduleType: string;
  /**
   * 模块ID
   */
  moduleManageId: string;
  /**
   * 模块名称
   */
  moduleName: string;
  /**
   * 专业分类id
   */
  studyLevel1: string;
  /**
   * 专业分类名字
   */
  categoryName: string;
  /**
   * 目标id
   */
  targetId: string;
  /**
   * 目标类型 1每日练习 2试卷 3章节知识点 4模块刷题 99模块
   */
  targetType: number;
  /**
   * 学科
   */
  subjectType: number;
  /**
   * 省份
   */
  region: number;
  /**
   * 学段
   */
  academicSection: number;
  /**
   * 考试形式
   */
  examFormat: number;
  /**
   * 方向
   */
  directionType: number;
  /**
   * 是否上架 0.否 1.是
   */
  isPublish: number;
}
