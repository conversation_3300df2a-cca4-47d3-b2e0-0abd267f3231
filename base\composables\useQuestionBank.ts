/**
 * @deprecated 废弃
 * */
export const useQuestionBank = () => {
  const { learnTargetNodes } = useLearnTarget()

  /**
 * 获取最后一级学习目标参数数据
 */
  const getLearnTargetNodesLastLevelParamsData = () => {
    if (!learnTargetNodes.value.length) { return null }
    const arr = learnTargetNodes.value.sort(function (a: any, b: any) { return a.level - b.level })
    const { level, usedId: studyLevelId, pageMasterId } = arr[arr.length - 1]
    return {
      level,
      studyLevelId,
      pageMasterId
    }
  }

  /**
   * 获取第一级学习目标参数数据
   * @returns
   */
  const getLearnTargetNodesFirstLevelParamsData = () => {
    if (!learnTargetNodes.value.length) { return null }
    const arr = learnTargetNodes.value.sort(function (a: any, b: any) { return a.level - b.level })
    const { level, usedId: studyLevelId } = arr[0]
    return {
      level,
      studyLevelId
    }
  }

  return {
    getLearnTargetNodesLastLevelParamsData,
    getLearnTargetNodesFirstLevelParamsData
  }
}
/* 做题页面-序号和题干处理 */
export const useQuestSortStem = (value: any, sort: number, moduleType: number, score: string) => {
  // 为和ui样式保持统一，去掉题干的浮动样式
  const newValue = value.replace(/float: (left|right);/g, '')
  const num = value.indexOf('>')
  const scoreValue = (moduleType === 2 && score && score !== '0') ? `(${score}分)` : ''
  const questionSort = sort ? `${sort}、` : ''
  const firstTag = num > -1 ? newValue.slice(0, num + 1) : '<p>'
  const questionStem = num > -1 ? newValue.slice(num + 1, -4) : newValue
  return firstTag + questionSort + questionStem + scoreValue + '</p><div class="clear-both" />'
}
