<template>
  <div class="bg-[#FFFFFF]">
    <QuestionBankAnalyzeTitle title="查看解析" :class="['!pt-[0px]', !isDetail ? '!my-[32px]' : '!mt-0']">
      <slot>
        <div
          class="flex items-center px-[10px] bg-[#F7F7F7] h-[48px] leading-[48px] items-center content-center rounded-[12px] justify-center"
          @click="handleAction()"
        >
          <span class="text-[24px]">{{ isExpand ? '收起答案/解析' : '查看答案/解析' }}</span>
          <KKCIcon name="icon-xiangqing-zhankai" :size="36" class="more-icon" :class="{ active: isExpand }" />
        </div>
      </slot>
    </QuestionBankAnalyzeTitle>
  </div>
</template>
<script setup lang="ts">
const props = defineProps({
  isExpand: {
    type: Boolean,
    default: false
  },
  isDetail: {
    type: Boolean,
    default: false
  }
}
)
const { track } = useTrackEvent()

const emits = defineEmits<{
  (e: 'changeIsShow', v: boolean): void
}>()
const handleAction = () => {
  emits('changeIsShow', !props.isExpand)
  const trackAction = props.isExpand ? '收起答案解析' : '查看答案解析'
  // 做题->答案解析 埋点
  track({
    category: '题库',
    action: trackAction
  })
}
</script>
<style scoped lang="scss">
.more-icon {
  @apply transition-all duration-300;
}

.active {
  /* 添加active类的时候将 .more-icon旋转90度 带过度动画 去掉active 回到0度 */
  @apply transition-all duration-300;
  transform: rotate(-180deg);
}
</style>
