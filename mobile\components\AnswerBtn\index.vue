<template>
  <div v-if="isShowManualService || isShowAiService" class="answer-btn-wrap">
    <div class="footer-pay-bar flex justify-center items-center safe-area">
      <div class="btn-warp flex justify-between items-center flex-1">
        <!-- 拍照结果按钮 -->
        <template v-if="showResult">
          <KKCButton
            v-if="isShowManualService"
            width="100%"
            type="info"
            size="normal"
            round
            color="#111"
            bg-color="#f2f2f2"
            border="none"
            class="mr-[20px]"
            @click="handlePerson"
          >
            <p class="btn-tips">
              对结果不满意
            </p>
            <p class="btn-content">
              发起人工答疑
            </p>
          </KKCButton>
          <div v-if="isShowAiService" class="upload-input w-[100%] upload-brand " @click.stop="handleCloseSearchRes(true)">
            再拍一题
          </div>
        </template>

        <!-- 未拍照按钮 -->
        <template v-else>
          <template v-if="isShowAiService">
            <div v-if="!showPerson" class="upload-input w-[100%]" @click.stop="handleCloseSearchRes(false)">
              拍照答疑
            </div>
          </template>

          <KKCButton
            v-if="isShowManualService"
            width="100%"
            type="info"
            size="normal"
            round
            color="#fff"
            bg-color="var(--kkc-brand)"
            @click.stop="handlePerson"
          >
            发起人工答疑
          </KKCButton>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// hooks
import { useCamera } from '~/pages/answerQuestion/hooks/useCamera'
// store
import { useAnswerStore } from '~/stores/answer.store'
const { state } = useAnswerStore()
const props = defineProps<{
  // 拍照结果按钮
  showResult?: boolean,
  // 仅展示人工答疑按钮
  showPerson?:boolean,
  // 关闭搜索结果
  closeSearchResult?: any,
  // 跳转拍照页面
  replaceBtn?: boolean,
  // ai结果页面 是否有权益
  showEquity?: boolean
}>()
console.log(props)
// const { answerApi } = useApi()

const emits = defineEmits<{
    (e: 'showPicture'): void;
    (e: 'showbtnPerson'): void;
}>()

const { openCamera } = useCamera(props.closeSearchResult)
/**
 *
 * 拍照答疑结果页面
 *
 *  */
const handleCloseSearchRes = async (bol:boolean) => {
  // const flag = await useAnswerService('picture')
  // if (flag) {
  //   return
  // }
  // 关闭拍照答疑权限校验
  // const { data, error } = await answerApi.getPhotoEquityVerifyPort({})
  // if (!error.value) {
  //   console.log(data.value)
  //   if (!data.value.isValid) {
  //     return Message('您没有答疑权益，请先购买指定商品获取权益吧')
  //   }
  // }
  state.isShowPop = 1
  openCamera(bol)
}

/**
 * 人工答疑按钮
 */
const handlePerson = debounceFunc(async () => {
  console.log('showbtnPerson')
  const flag = await useAnswerService('manual')
  if (flag) {
    return
  }

  emits('showbtnPerson')
}, 1000, true)

const photoString = (val:any) => {
  console.log('photoString1', val)
  state.shootSearchBase64 = val
  navigateTo({
    path: '/answerQuestion/picture-echo',
  })
}
const closeCamera = () => {
  console.log('iosclose')

  // window.history.back()
}

const isShowManualService = computed(() => {
  return state.manualServiceFlag
})

const isShowAiService = computed(() => {
  return state.searchFlag
})

onMounted(() => {
  window.photoString = photoString
  window.closeCamera = closeCamera
  useAnswerService('manual')
})

useHead({
  script: [
    {
      src: 'https://gosspublic.alicdn.com/aliyun-oss-sdk-6.17.1.min.js',
    },
  ],
})
</script>

<style scoped lang="scss">
.answer-btn-wrap {
    height: calc(env(safe-area-inset-bottom) + 136px);

    .footer-pay-bar {
        width: 7.5rem;
        position: fixed;
        left: 50%;
        transform: translateX(-50%);
        bottom: 0;
        display: flex;
        align-items: center;
        padding: 24px 24px 24px;
        // height: 112px;
        background-color: #fff;
        z-index: 61;
        box-shadow: inset 0px 1px 0px 0px rgba(238, 238, 238, 0.5);
        backdrop-filter: blur(10px);

        .upload-input {
          width: 100%;
          border-radius: 16px;
          height: 88px;
          line-height: 88px;
          font-size: 32px;
          font-weight: 500;
          background-color: #f2f2f2;
          text-align: center;
          margin-right: 20px;
          position: relative;

          input{
            width: 100%;
            height: 100%;
            position: absolute;
            top: 0;
            left: 0;
            z-index: 9;
            opacity: 0;
          }
        }

        .upload-brand{
          @apply bg-brand text-[#fff];
          margin-right: 0;
        }

        .btn-tips{
          font-family: PingFang SC;
          font-weight: 400;
          font-size: 20px;
          color: #999999;
          line-height: 24px;
          text-align: center;
          font-style: normal;
          text-transform: none;
          margin-bottom: 4px;
        }

        .btn-content{
          font-size: 28px;
          line-height: 34px;
          text-align: center;
          font-weight: 500;
        }
    }
}
</style>
