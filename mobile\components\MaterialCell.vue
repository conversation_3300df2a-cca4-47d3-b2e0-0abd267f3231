<template>
  <div :class="pageType === 3 ? 'p-[24px] mb-[16px]':pageType === 1 ? 'p-[24px]':'py-[24px]'" class="rounded-[16px] bg-[#fff]">
    <div class="flex justify-between text-[28px] text-[#111]">
      <div class="w-[64px] h-[64px] mr-[18px]">
        <img
          class="w-[64px] h-[64px]"
          :src="getMaterialIcon(material.fileType)"
          loading="lazy"
          alt=""
        >
      </div>
      <div class="flex-1">
        <div :class="pageType === 3 ? 'line-clamp-2' : 'line-clamp-1'" class=" font-medium break-all">
          {{ material.title }}
        </div>
        <div class="flex justify-between items-center text-[24px] text-[#999]">
          <div class="mt-[5px] flex items-center">
            <div class="w-[200px]">
              <KKCIcon name="icon-deta_icon_wenjian" :size="28" class="mr-[4px]" />
              {{ material.fileSize }}
            </div>
            <div v-if="pageType !== 1" class="pl-[12px] mr-[4px]">
              <KKCIcon name="icon-xiazai1" :size="28" />
              {{ material.downloadNum }}
            </div>
          </div>
          <div>
            <KKCIcon
              name="icon-xuexizhongxin-xiazai"
              color="#C5CAD5"
              :size="36"
              @click="handleDownload(material)"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import materialPDFIcon from '../../base/assets/material/material-pdf.png'
import materialDOCIcon from '../../base/assets/material/material-doc.png'
import materialDOCXIcon from '../../base/assets/material/material-docx.png'
import materialJPGIcon from '../../base/assets/material/material-jpg.png'
import materialPPTIcon from '../../base/assets/material/material-ppt.png'
import materialPPTXIcon from '../../base/assets/material/material-pptx.png'
import materialTXTIcon from '../../base/assets/material/material-txt.png'
import materialXLSIcon from '../../base/assets/material/material-xls.png'
import materialXLSXIcon from '../../base/assets/material/material-xlsx.png'
import materialZIPIcon from '../../base/assets/material/material-zip.png'

import { useUserStore } from '~/stores/user.store'

// 新增方法：根据文件类型获取对应图标
const getMaterialIcon = (fileType: string) => {
  switch (fileType.toLowerCase()) {
    case 'pdf':
      return materialPDFIcon
    case 'doc':
      return materialDOCIcon
    case 'docx':
      return materialDOCXIcon
    case 'jpg':
    case 'jpeg':
    case 'png':
      return materialJPGIcon
    case 'ppt':
      return materialPPTIcon
    case 'pptx':
      return materialPPTXIcon
    case 'txt':
      return materialTXTIcon
    case 'xls':
      return materialXLSIcon
    case 'xlsx':
      return materialXLSXIcon
    case 'zip':
    case 'rar':
      return materialZIPIcon
    default:
      return materialPDFIcon // 默认返回PDF图标
  }
}

const props = defineProps({
  // 材料卡片每一项内容
  material: {
    type: Object,
    default: () => ({ }),
  },
  // 1 首页 2 资讯详情 3 资料列表
  pageType: {
    type: Number,
    default: 3,
  },
})
const { materialApi } = useApi()
const userStore = useUserStore()

// const { isXcx, isPc } = useAppConfig()
// import { isWeChat } from '@kukefe/kkutils'
const route = useRoute()
// 上报下载次数 调接口
const uploadNumber = async (item: any) => {
  const { data } = await materialApi.downloadInc({ id: item.id })
  // 不同环境设置
  // console.log('🚀 ~ uploadNumber ~ data:', data.value)
  // if (isXcx && !os?.isPc) {
  //   handleWeChatPreview(data.value?.fileSourceUrl, 1, item.title)
  // } else
  // if (isWeChat() && !isXcx) {
  //   Message('请使用浏览器打开并进行下载～')
  // } else
  if (os?.isApp && os.isAndroid) {
    window.android.downloadFile(data.value?.fileSourceUrl)
  } else if (os?.isApp && os.isPhone) {
    window.webkit.messageHandlers.downloadFile.postMessage(data.value?.fileSourceUrl)
  } else {
    downloadFile(data.value?.fileSourceUrl, item.title)
  }
}
/**
 * 下载资料
 * */
const handleDownload = async (item) => {
  console.log('🚀 ~ handleDownload ~ item:', item)
  // if (!item.fileSourceUrl) {
  //   Message('fileSourceUrl 不存在')
  //   return
  // }
  /**
   * [AI-GEN] 数据处理
   */
  const fields = [
    // item.cateName && `专业分类：${item.cateName}`,
    item.regionName,
    item.subjectName,
    item.academicSectionName,
    item.examFormatName,
    item.directionName
  ].filter(Boolean)
  const extension6 = fields.join('；')
  // 下载方式 1仅登陆后可下载 2未登陆可下载
  if (item.downloadWay === 1) {
    // 拼接标签
    // 判断登录状态
    if (userStore.isLogin) {
      if (userStore.getUserInfo?.mobile) {
        updateBehaviorRecordsFn({
          cateIds: item.cateId,
          extension6,
          extension9: item.id
        })
      }
      uploadNumber(item)
    } else if (os?.isApp && os.isAndroid) {
      // window.android.login()
      window.android.dataDownloadLogin(item.cateId, extension6, item.id)
    } else if (os?.isApp && os.isPhone) {
      // window.webkit.messageHandlers.login.postMessage(null)
      const params = {
        cateIds: item.cateId,
        extension6,
        extension9: item.id,
      }
      window.webkit.messageHandlers.dataDownloadLogin.postMessage(JSON.stringify(params))
    } else {
      const queryParams = {
        ...route.query,
        cateId: item.cateId,
        tag: extension6,
        id: item.id,
        action_type: 'material_download',
        page_type: props.pageType
      }
      userStore.isLoginFn(`${route.path}?${new URLSearchParams(queryParams).toString()}`)
    }
  } else {
    if (userStore.getUserInfo?.mobile) {
      updateBehaviorRecordsFn({
        cateIds: item.cateId,
        extension6,
        extension9: item.id
      })
    }
    uploadNumber(item)
  }
}
/**
 * [AI-GEN] 处理不同客户端的传参
 */
function getPlatform () {
  const { isXcx } = useAppConfig()
  if (isXcx) {
    return 5
  }
  const { isApp, isAndroid, isPhone, isTablet } = os || {}
  if (isApp && isAndroid) {
    return 3
  }
  if (isApp && (isPhone || isTablet)) {
    return 4
  }

  return 7
}

// 资料下载上报行为线索
const updateBehaviorRecordsFn = async (options: any) => {
  // 上传用户行为
  const { appId, isXcx } = useAppConfig()
  const params = {
    productId: appId,
    userMobile: userStore.getUserInfo?.mobile,
    clientType: getPlatform(),
    ...options
  }
  const behaviorSource = (os?.isApp && os.isAndroid)
    ? '1092594488748433408'
    : (os?.isApp && (os?.isPhone || os?.isTablet))
        ? '1092594417348796416'
        : isXcx ? '1092594244800905216' : '1092594144028557312'

  await updateBehaviorRecords(
    ActionEnum.materialDownload,
    behaviorSource,
    { ...params })
}
</script>
<style lang="scss" scoped>
</style>
