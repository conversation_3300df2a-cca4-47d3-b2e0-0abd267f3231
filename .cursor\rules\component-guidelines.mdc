---
description:
globs:
alwaysApply: false
---
# Vue 组件开发指南

## 组件命名规范
- 使用 PascalCase 命名组件
- 基础组件以 Base 开头
- 业务组件使用功能模块前缀

## 组件结构
```vue
<template>
  <!-- 模板内容 -->
</template>

<script setup lang="ts">
// 组件逻辑
</script>

<style lang="scss" scoped>
// 组件样式
</style>
```

## 最佳实践
- 使用 TypeScript 编写组件
- 使用 Composition API 和 `<script setup lang="ts">`
- 组件属性使用 TypeScript 类型定义
- 使用 props 验证
- 使用 emit 声明事件

## 组件通信
- 使用 props 向下传递数据
- 使用 emit 向上传递事件
- 复杂状态考虑使用 Pinia
- 避免过度使用全局状态

## 性能优化
- 合理使用 v-show 和 v-if
- 使用 keep-alive 缓存组件
- 避免不必要的组件渲染
- 使用异步组件

## 样式规范
- 使用 scoped 样式
- 遵循 BEM 命名规范
- 使用 SCSS 预处理器
- 主题变量统一管理
