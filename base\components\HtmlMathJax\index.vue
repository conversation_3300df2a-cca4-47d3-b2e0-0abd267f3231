<template>
  <div
    ref="mathContainer"
    class="contentHtml item"
    :class="{ 'answer-questions-html': isAnswerQuestions }"
    v-html="htmlContent"
  />
</template>

<script setup lang="ts">
import commonsVariable from '../../utils/globalVariable'

const props = withDefaults(defineProps<{
  content: string;
  isAnswerQuestions?: boolean;
  // renderAll: boolean;
}>(), {
  content: '',
  isAnswerQuestions: false,
  // renderAll: false
})

// 定义响应式变量
const mathContainer = ref<HTMLElement | null>(null)
const htmlContent = ref<string>('')

// 验证字符串的函数
// const validateString = (str: string) => {
//   str = filterkGHtml(str)
//   const englishRegex = /[a-zA-Z]/g
//   const chineseRegex = /[\u4E00-\u9FA5]/g
//   const englishMatches = str.match(englishRegex) || []
//   const chineseMatches = str.match(chineseRegex) || []
//   const englishPercentage = (englishMatches.length / str.length) * 100
//   const chinesePercentage = (chineseMatches.length / str.length) * 100
//   return {
//     englishPercentage,
//     chinesePercentage,
//     isEn: englishPercentage > chinesePercentage
//   }
// }

// // 过滤 HTML 和空格标签的函数
// const filterkGHtml = (str: string) => {
//   if (str) {
//     // 使用正则表达式字面量替代 RegExp 构造函数
//     const reg = /(<([^>]+)>|&nbsp;)/gi
//     return str.replace(reg, '').replace(/\s+/g, '')
//   }
//   return ''
// }

// 格式化公式
const processMathContent = (str: string) => {
  // console.log('processMathContent', str)

  // 处理HTML实体的辅助函数
  const replaceHtmlEntities = (formula: string) => {
    return formula
      .replace(/&amp;/g, '&')
      .replace(/&nbsp;/g, ' ')
      .replace(/&#39;/g, "'")
      .replace(/&gt;/g, '>')
      .replace(/&lt;/g, '<')
      .replace(/&quot;g/, '"')
      .replace(/&apos;/g, "'")
      .replace(/<br\/>/g, ' ')
      .replace(/<br>/g, ' ')
  }

  // 处理不同类型的公式
  let content = str

  // 处理 $$...$$ 块级公式
  content = content.replace(/\$\$([\s\S]*?)\$\$/g, (_, formula) => {
    return `$$${replaceHtmlEntities(formula)}$$`
  })

  // 处理 \[...\] 块级公式
  content = content.replace(/\\\[([\s\S]*?)\\\]/g, (_, formula) => {
    return `\\[${replaceHtmlEntities(formula)}\\]`
  })

  // 处理 \(...\) 行内公式
  content = content.replace(/\\\(([\s\S]*?)\\\)/g, (_, formula) => {
    return `\\(${replaceHtmlEntities(formula)}\\)`
  })

  // 匹配 $$...$$ 块级公式进行渲染
  const blockRegex = /\$\$([\s\S]*?)\$\$/g

  // 分割内容为文本和公式块
  const parts = content.split(blockRegex)

  // 异步处理所有公式块
  const processedParts = parts.map((part, index) => {
    if (index % 2 === 1) { // 奇数索引是公式内容
      const html = window.MathJax.tex2chtml(part,
        // {
        //   display: false // 设置为 true 表示块级公式，false 表示行内公式
        // }
      ).outerHTML

      return html
    }
    return part // 偶数索引是普通文本
  })

  return processedParts.join('')
}

// 处理公式转换
const handleConvertTeXToHTML = async () => {
  const str = (props.content || '')
  // 若无mathjax公式，直接显示
  if (!commonsVariable.containsLatex(str)) {
    htmlContent.value = str
    return
  }

  // 等待 MathJax 加载完成
  await commonsVariable.waitForMathJax()

  const content = processMathContent(str)

  htmlContent.value = content

  nextTick(() => {
    window.MathJax.typesetPromise()
  })
}

watch(() => props.content, (content: string) => {
  if (content) { handleConvertTeXToHTML() }
}, {
  immediate: true,
  flush: 'post',
})

// onMounted(() => {
//   handleConvertTeXToHTML()
// })

</script>

<style>
/* .qutype_gn_content {
  word-break: keep-all;
  overflow-wrap: keep-all;
}

.qutype_gn_content_ch {
  word-break: break-all;
   overflow-wrap: break-all;
} */

.contentHtml {
  display: inline-block;
  width: 100%;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.contentHtml .MathJax {
  font-size: 130%;
}

.contentHtml .MJX-TEX {
  white-space: normal;
}

.contentHtml .MJXc-display {
  display: inline-block !important;
  margin: 0 !important;
  padding: 0 !important;
}

.contentHtml .MathJax_Display {
  /* margin: 0 !important; */
  padding: 0 !important;
  display: inline !important;
  width: auto !important;
  max-width: 100% !important;
  margin-right: 4px!important;
}

.answer-questions-html .MathJax_Display {
  width: 100% !important;
  display: block !important;
  margin: 0 !important;
  padding: 0 !important;
}

.contentHtml img.kfformula {
  vertical-align: middle !important;
}

.contentHtml p {
  margin: 0 !important;
  padding: 0 !important;
}

/* .contentHtml img {
  max-width: 550px !important;
  width: auto !important;
} */

.contentHtml table tr td {
  border: 2px solid rgb(52, 40, 40);
  /* text-align: center;
  font-weight: bold; */
}

.hidden {
  opacity: 0 ;
}
mjx-container[jax="CHTML"][display="true"]{
  display: inline-block !important;
}
</style>
