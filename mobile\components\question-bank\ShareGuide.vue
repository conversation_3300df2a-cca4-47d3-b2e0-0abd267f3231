<template>
  <div v-if="isShowGuide" class="guide-overlay fixed inset-0 bg-black bg-opacity-70 flex justify-center" @click="closeGuide">
    <!-- 引导线 -->
    <div class="guide-line flex w-[219px] h-[360px] flex-col items-center absolute left-[245px] top-[10px]" />

    <!-- 提示框 -->
    <div class="guide-content w-[673px] mt-[380px]">
      <div class="flex flex-col bg-brand text-white p-[16px] rounded-[22px] items-center">
        <div class="guide-content-icon flex w-[94px] h-[66px] justify-center items-center rounded-[66px] my-[16px] bg-[#fff] gap-[8px]">
          <span class="w-[8px] h-[8px] rounded-full bg-[#333]" />
          <span class="w-[13px] h-[13px] rounded-full bg-[#333]" />
          <span class="w-[8px] h-[8px] rounded-full bg-[#333]" />
        </div>
        <span class="text-[34px] text-[#fff] font-[500]">点击右上角“...”按钮，一键分享给好友</span>
      </div>

      <!-- 确认按钮 -->
      <div class="mt-[32px] flex justify-center">
        <button
          class="confirm-btn w-[192px] h-[84px] rounded-[16px] border border-[#fff] bg-gray-50 bg-opacity-10 text-center text-white"
          @click="closeGuide"
        >
          我知道了
        </button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
defineProps<{
  shareTitle?: string;
  isShowGuide?: boolean;
  link?: string;
}>()

const emits = defineEmits<{
  (e: 'close'): void;
}>()

/**
 * 关闭
 */
const closeGuide = () => {
  // 关闭引导
  emits('close')
}

</script>

<style lang="scss" scoped>
.guide-overlay {
  z-index: 9999;
}

.guide-line {
  transform: translateX(50%);

  background: url('~/assets/images/question-bank/guide_bg.png') no-repeat center;
  background-size: 100% 100%;
}

.guide-content {
  &-icon {
    box-shadow: 0 0 0 8px var(--kkc-brand);
  }
}

.confirm-btn:active {
  opacity: 0.8;
}
</style>
