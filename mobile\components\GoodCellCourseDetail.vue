<template>
  <nuxt-link :to="goDetail(goods)" external @click="handleClick(goods)">
    <div
      class="course-detail-cell"
      :title="goods?.title || goods?.goodsTitle"
    >
      <!-- @click="goDetail(goods)" -->
      <div class="flex flex-[1]">
        <slot />
        <div class="img">
          <kkcImage :src="goods?.goodsImg || CourseCover" :error-type="2" />
          <div
            v-if="(goods?.isSell === 0 || goods?.isShelf === 0) && isShowMask"
            class="off-shelf flex justify-center flex-col items-center bg-opacity-60 text-[#fff]"
          >
            <KKCIcon name="icon-suobeifen1" color="#ffffff" :size="48" class="" />
            <span v-if="goods?.isSell === 0 && goods?.isShelf !== 0">
              不可售
            </span>
            <span v-if="goods?.isSell === 0 && goods?.isShelf === 0 || goods?.isShelf === 0">
              已下架
            </span>
          </div>
        </div>
        <div class="course-detail-cell-info flex justify-between flex-col">
          <div
            class="course-detail-cell-info-title font-medium text-[#111] line-clamp-2 h-[76px]"
          >
            <!-- {{ goods }} -->
            <!-- v-if="goods?.isBook || goods?.queryType === 3 || goods?.mark === '图书'" -->
            <template v-if="goods?.resourceTypes">
              <span
                v-if="['3','7'].includes(goods.resourceTypes)"
                class="good-list-tag !bg-[#FF6F00] !text-[#fff] !rounded-[8px]"
              >图书</span>
            </template>
            <template v-if="goods?.classTypeList?.length">
              <template
                v-for="(item, i) in goods.classTypeList"
                :key="i"
              >
                <span
                  v-if="item?.classTypeName"
                  class="good-list-tag"
                  :style="{ background: item.bgColor, color: item.fontColor }"
                >{{ item.classTypeName }}</span>
              </template>
            </template>
            <template v-else>
              <span
                v-if="goods?.classTypeInfo?.classTypeName"
                class="good-list-tag"
                :style="{
                  background: goods.classTypeInfo.bgColor,
                  color: goods.classTypeInfo.fontColor
                }"
              >{{ goods.classTypeInfo.classTypeName }}</span>
            </template>
            <span>
              {{ goods?.title || goods?.goodsTitle }}</span>
          </div>
          <!-- {{ goods.includeGoodsCountList }} -->
          <div
            v-if="goods?.content?.length"
            class="course-detail-cell-info-package line-clamp-1"
          >
            <span v-for="(item, index) in goods.content" :key="index">{{ item }}<span
              v-if="goods.content.length !== index + 1"
            >/</span></span>
          </div>
          <div
            v-else-if="goods?.includeGoodsCountList?.length"
            class="course-detail-cell-info-package line-clamp-1"
          >
            <span v-for="(item, index) in goods.includeGoodsCountList" :key="index">{{ item }}<span
              v-if="goods.includeGoodsCountList.length !== index + 1"
            >/</span></span>
          </div>
          <div class="course-detail-cell-info-price font-bold text-right flex justify-end items-center text-red">
            <nuxt-icon
              v-if="(goods.isTrialClass || goods?.mark === '体验') && !isSearch"
              name="free-class"
              filled
              class="course-detail-cell-info-price-free-class"
            />
            <del
              v-if="isGoodsPrice||((isSearch||isCourse)&&isShowActivityType(goods, true))"
              class="text-[#999] text-[20px] leading-[24px]"
            >￥{{ goods?.goodsPrice }}</del>

            <div class="flex flex-1 justify-end items-end">
              <div
                v-if="handleActivityTypeName(goods)"
                class="kb-new-goods-price__current-activity kb-goods-start__tag1 course-detail-cell-activity-tag"
              >
                <!-- <p class="kb-new-goods-price__current-activity-state activity-state-size"> -->
                {{ handleActivityTypeName(goods) }}
                <!-- 秒杀 -->
                <!-- </p> -->
              </div>
              <span class="course-detail-cell-info-price-texticon">￥</span>
              <span class="text-[24px] course-detail-cell-info-price-text">{{ goods?.goodsPresentPrice || goods?.goodsPrice
              }}</span>
              <span v-if="Number(goods.goodsType) == 6 || Number(goods.goodsType) == 8" class="text-[22px] pl-[2px] leading-[28px]">起</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 文字标签 -->
      <div v-if="goods?.wordageLabelList?.length || isSaleCount" class="course-detail-cell-divide" />
      <div class="flex justify-between items-center">
        <div v-if="goods.wordageLabelList.length" class="course-detail-cell-tag">
          <KKCGoodsTags v-for="tag in goods.wordageLabelList" :key="tag.goodsMasterId" :tag="tag" />
        </div>
        <span v-else />
        <span v-if="isSaleCount" class="text-[#999] text-[20px]">已售 {{ goods?.saleCount }}</span>
        <span v-else />
      </div>
    </div>
  </nuxt-link>
</template>
<script setup lang="ts">
//
import { useUserStore } from '~/stores/user.store'
const CourseCover = 'https://oss.kuke99.com/kukecloud/static/common/course-cover.png'
const { isXcx } = useAppConfig()
const userStore = useUserStore()
const { isKukeCloudAppWebview } = useAppConfig()
const props = defineProps({
  goods: {
    type: Object,
    default: () => ({ }),
  },
  // 是否可跳转商品详情
  isSkip: {
    type: Boolean,
    default: true
  },
  // 跳转商品详情是否判断异常情况
  isConfirmAbnormal: {
    type: Boolean,
    default: true
  },
  // 是否是搜索页面
  isSearch: {
    type: Boolean,
    default: false
  },
  // 是否展示标签
  isCourse: {
    type: Boolean,
    default: false
  },
  // 是否显示不可售,已下架蒙层
  isShowMask: {
    type: Boolean,
    default: true
  },
  // 是否显示划线价
  isGoodsPrice: {
    type: Boolean,
    default: false
  },
  // 是否显示销量
  isSaleCount: {
    type: Boolean,
    default: false
  },
})
const emit = defineEmits<{
  (e:'toGoodsDetail'): void,
}>()

const handleClick = (item: any) => {
  emit('toGoodsDetail')
  if (isXcx && props.isSkip) {
    const paths = +item.goodsType === 8 ? '/courseList/collectGoods/' : '/course/'
    userStore.jumpAppletWebPage(`${paths}${item?.id || item?.goodsMasterId}`)
  }
}

// 跳转商品详情
const goDetail = (item: any) => {
  if (!props.isSkip || isXcx) {
    return 'javascript: void(0)'
  }

  const courseId = item?.id || item?.goodsMasterId
  const collectGoodsPath = `/courseList/collectGoods/${courseId}`
  const coursePath = `/course/${courseId}`

  const isCollectGoodsPath = +item.goodsType === 8

  const path = isCollectGoodsPath ? collectGoodsPath : coursePath

  if (isKukeCloudAppWebview) {
    return isClient ? window.origin + path : path
  }

  if (props.isSkip) {
    return path
  }

  // if (isKukeCloudAppWebview) {
  //   if (isClient) {
  //     return window.origin + `/course/${item?.id || item?.goodsMasterId}`
  //   } else {
  //     return `/course/${item?.id || item?.goodsMasterId}`
  //   }
  // } else if (props.isSkip) {
  //   return (`/course/${item?.id || item?.goodsMasterId}`)
  // }
  // if (props.isConfirmAbnormal) {
  //   if (item.isSell === 0 && item.isShelf !== 0) {
  //     Message('该商品暂不可售~')
  //     return
  //   } else if ((item.isSell === 0 && item.isShelf === 0) || item.isShelf === 0) {
  //     Message('该商品已下架~')
  //     return
  //   }
  // }
  // if (isKukeCloudAppWebview) {
  //   window.location.href = window.origin + `/course/${item?.id || item?.goodsMasterId}`
  // } else if (props.isSkip) {
  //   navigateTo(`/course/${item?.id || item?.goodsMasterId}`)
  // }
}
const activityTypesMap = {
  1: '秒杀',
  2: '拼团',
  4: '分销',
}
// 是否显示活动状态
const isShowActivityType = (item, val: boolean) => {
  const filterArr = val ? [1, 2, 4] : [1, 2]
  const intersection = item.activityTypes?.filter(data => filterArr.includes(data)) || []
  return intersection.length && item.goodsType !== 6
}
// 获取活动状态名称
const handleActivityTypeName = (item = {}) => {
  const { activityTypes = [], goodsType } = item
  if (goodsType === 6) {
    return
  }
  const intersection = activityTypes.filter(data => [1, 2, 4].includes(data)) || []
  // console.log('%c [ intersection ]-227', 'font-size:13px; background:pink; color:#bf2c9f;', intersection, activityTypesMap[1])
  if (intersection.includes(1)) {
    return activityTypesMap[1]
  } else if (intersection.includes(2)) {
    return activityTypesMap[2]
  } else if (intersection.includes(4)) {
    return activityTypesMap[4]
  } else {
    return ''
  }
  // return intersection?.join(',').includes('1') ? '秒杀' : '拼团'
}
</script>
<style lang="scss">
@mixin tagLighten($color, $num) {
  background-color: rgba($color, $num);
  color: $color;
}

.good-list-tag{
  @include tagLighten($primaryTagColor, 0.1);
  border-radius: 8px;
  box-sizing: border-box;
  white-space: nowrap;
  text-align: center;
  padding: 6px;
  font-size: 20px;
  border-radius: 4px;
  margin-right:5px;
  vertical-align:text-bottom!important;
}
.course-detail-cell {
  // padding: 20px;
  // margin-bottom: 10px;
  // border-radius: 15px;
  margin-top: 24px;

  .img {
    width: 280px;
    height: 170px;
    border-radius: 12px;
    margin-right: 16px;
    overflow: hidden;
    position: relative;
    flex-shrink: 0;
  }

  .off-shelf {
    position: absolute;
    width: 280px;
    height: 186px;
    background: rgba(0, 0, 0, 0.6);
    border-radius: 8px;
    // opacity: 0.6;
    top: 0;
    left: 0;

    div {
      font-size: 24px;
      font-weight: 500;
      color: #FFFFFF;
    }

  }

  &-info {
    // min-height: 186px;
    flex:1;
    &-title {
      font-size: 28px;
      line-height: 38px;
      // padding-top: 3px;
      // height: 0.8rem;
      word-break: break-all;

      span {
        vertical-align: text-bottom;
      }
    }

    .w-306 {
      width: 306px;
    }

    .w-430 {
      width: 430px;
    }

    .tip-img {
      width: 86px;
      height: 37px;
    }

    &-package {
      margin: 12px 0 0;
      font-size: 22px;
      line-height: 28px;
      color: #999;
    }

    &-price {
      font-family: DINPro-Bold;
      width: 100%;
      height: 40px;
      justify-content: space-between;
      align-items: flex-end;

      &-free-class {
        display: flex;
        align-self: center;
        font-size: 56px;
        margin-top: 3px;
      }

      &-texticon {
        font-size: 24px;
        line-height: 28px;
      }

      &-text {
        font-size: 40px;
        line-height: 40px;
      }
    }
  }

  &-divide {
    height: 1px;
    border-bottom: 1px dashed #E5E6EC;
    margin: 12px 0;
  }
}
.course-detail-cell-activity-tag{
  width: .64rem;
  height: .36rem;
  color: #fff;
  // text-align: left;
  text-align: center;
  line-height: .36rem;
  font-size: .24rem;
  // padding-left: .08rem;
}
</style>
