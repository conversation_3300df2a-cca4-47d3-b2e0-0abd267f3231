// 废弃
import { createApp } from 'vue'
import { ChooseClassifyPopup } from '../pages/question-bank/components/index'

export const Classify = ({ classifyList, confirm, defaultChecked }: { classifyList: any[], defaultChecked: any[], confirm: (choosed: any[]) => void }) => {
  let container: HTMLDivElement
  let AppInstance: any = null
  const handleClose = () => {
    AppInstance && AppInstance.unmount()
    container && document.body.removeChild(container)
  }
  const handleConfirm = (choosed: any[]) => {
    handleClose()
    confirm && confirm(choosed)
  }

  if (process.client) {
    container = document.createElement('div')
    AppInstance = createApp(ChooseClassifyPopup, {
      classifyList,
      defaultChecked,
      close: handleClose,
      confirm: handleConfirm,
    })

    document.body.appendChild(container)
    AppInstance.mount(container)
  }
}
