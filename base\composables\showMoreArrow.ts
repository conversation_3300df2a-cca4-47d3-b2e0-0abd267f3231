/**
 *@description function 两行后面有个向上向下箭头点击展开剩余数据
 * @param {Ref<HTMLElement | undefined>} containerWrapRef 最外层的包裹元素
 * @param {Ref<HTMLElement | undefined>} container 内容区
 * @param {Ref<HTMLElement | undefined>} arrowEl 内容区后面的箭头元素
 * @param {string} arrowClassName 箭头元素的类名用于更改箭头方向
 * @return {
 * isShowArrow:{boolean} 是否显示箭头
 * isExpandArrow:{boolean} 箭头是否展开
 * initContainer:{()=>void} 初始化容器
 * showMoreTitle:{()=>void} 展开或收起内容
 * } 返回一个对象，包含是否展开、是否显示箭头、初始化容器、展开或收起标题的方法
 * @example
 * ````
 * --->html start
 * 最外层元素需要设置元素的最大高度为2行行高 和 overflow:hidden;
 * 内容区初始需要指定行高和overflow:hidden 用于计算高度
 * <div ref="containerWrapRef" class="max-h-[88px] overflow-hidden">
      <h1 ref="courseTitleRef">
              内容区文字
        <span v-if="isShowArrow" ref="arrowRef" class="arrow-absolute" @click="showMoreTitle">
          <span v-if='isExpandArrow'>展示内容</span>
          <span v-else>收起</span>
        </span>
      </h1>
   </div>
 *<--- html end
 *
 *
 *--->js start
 *const courseTitleRef = ref()
 *const arrowRef = ref()
 *const containerWrapRef = ref()
 *const {showMoreTitle,isShowArrow,isExpandArrow,initContainer} = showMoreArrow(containerWrapRef, courseTitleRef, arrowRef, 'arrow-absolute')
 *
 * onMounted(() => {
 *  initContainer()
 * })
 *--->js end
 *
 * ````
 */

export const showMoreArrow = (containerWrapRef: Ref<HTMLElement | undefined>, container: Ref<HTMLElement | undefined>, arrowEl: Ref<HTMLElement | undefined>, arrowClassName: string) => {
  const isShowArrow = ref(false)
  const isExpandArrow = ref(true)
  const titleLineHeight = ref(0)
  // 获取课程标题的高度和行高，并设置标题的初始高度和箭头状态
  const initContainer = () => {
    if (container.value && containerWrapRef.value) {
      containerWrapRef.value.style.maxHeight = 'none'
      const styles = window.getComputedStyle(container.value)
      const height = parseInt(styles.height)
      const lineHeight = parseInt(styles.lineHeight)
      titleLineHeight.value = lineHeight
      // 初始设置标题高度和箭头状态
      if (parseInt(height / lineHeight) > 2) {
        isShowArrow.value = true
        container.value.style.height = px2rem((lineHeight * 4 - 4) + 'px')
      } else {
        isShowArrow.value = false
      }
    }
  }

  // 更新箭头状态
  const updateArrow = (isExpandArrow:boolean) => {
    if (arrowEl.value) {
      if (isExpandArrow) {
        arrowEl.value?.classList.add(arrowClassName)
      } else {
        arrowEl.value?.classList.remove(arrowClassName)
      }
    }
  }

  // 显示更多或收起标题
  const showMoreTitle = () => {
    if (container.value) {
      isExpandArrow.value = !isExpandArrow.value

      if (!isExpandArrow.value) {
        container.value.style.height = 'auto'
      } else {
        container.value.style.height = px2rem((titleLineHeight.value * 4) + 'px')
      }
      updateArrow(isExpandArrow.value) // 更新箭头状态
    }
  }

  return {
    isShowArrow,
    isExpandArrow,
    initContainer,
    showMoreTitle
  }
}
