<script setup lang="ts">
// 排行榜前三名的图片
import type { ToplistItemEntity } from '@/apis/invite/types'
import top1 from '@/assets/images/invite/top1.png'
import top2 from '@/assets/images/invite/top2.png'
import top3 from '@/assets/images/invite/top3.png'
import defaultAvatar from '@/assets/images/user/default-avatar.png'

interface PropsType {
  info:ToplistItemEntity,
  isSelf:boolean,
}
withDefaults(defineProps<PropsType>(), {
  info: () => ({
    ranked: 1,
    userName: '',
    inviteCount: 0,
    photo: '',
  }),
  isSelf: false
})

const topArr = [top1, top2, top3]

</script>

<template>
  <div class="item" :class="{'self-item':isSelf}">
    <div class="rank">
      <div v-if="info.ranked <= 3" class="box">
        <img class="num" :src="topArr[info.ranked-1]" alt="">
        <img class="portrait" :src="info.photo || defaultAvatar" alt="">
      </div>
      <div v-else class="box">
        <div class="num">
          {{ info.ranked }}
        </div>
        <img class="portrait" :src="info.photo || defaultAvatar" alt="">
      </div>
    </div>
    <div class="nickname">
      {{ info.userName }}
    </div>
    <div class="count">
      {{ info.inviteCount }}
    </div>
  </div>
</template>

<style scoped lang="scss">
.item {
  @apply box-border overflow-hidden rounded-[24px];
  margin: 10px auto;
  padding:0 20px;
  .rank{
    @apply  float-left text-right;
    .box{
      @apply h-[105px] flex items-center;
      .num{
        @apply w-[60px] h-[60px] text-[28px] font-bold text-[#000000] mr-[24px] text-center leading-[60px];
        font-family: DINPro-Bold;
      }
      .portrait{
        @apply w-[80px] h-[80px] rounded-[9999px];
        line-height: 105px;
      }
    }
  }
  .nickname{
    @apply inline-block text-[28px] font-bold items-center ml-[24px] text-[#111];
    line-height: 0rem;
    line-height: 105px;
  }
  .count{
    @apply w-[114px] float-right text-[28px] font-bold text-[#000000] text-right;
    font-family: DINPro-Bold;
    line-height: 105px;
  }
}
.self-item{
  background-color: #fdf6e1;
}
</style>
