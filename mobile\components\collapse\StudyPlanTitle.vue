/**
 * 学习计划标题组件
 * @component StudyPlanTitle
 * @description 用于显示学习计划的标题，支持展开/收起功能
 *
 * @prop {IItem} item - 学习计划项数据
 * @prop {boolean} isOpen - 是否展开
 * @prop {boolean} hasChild - 是否有子项
 * @prop {string} title - 标题文本
 * @prop {string} countName - 计数名称
 * @prop {string} type - 组件类型
 *
 * @event {IItem} action - 点击事件，返回当前项数据
 */

<template>
  <div>
  <!-- <div
    class="StudyPlanTitle break-all"
    :class="[type]"
    @click="handleAction(item)"
  >
    <div class="StudyPlanTitle__main">
      {{ title }}
    </div>
    <div class="StudyPlanTitle__right">
      <span class="StudyPlanTitle__icon">
        <span class="text-[24px] text-brand">
          {{ countName }}
        </span>
        <KKCIcon
          class="ml-[4px]"
          :class="{ 'rotate-180': isOpen }"
          name="icon-xiangqing-zhankai"
          :size="18"
        />
      </span>
    </div>
  </div> -->
  <!-- <div v-if="type === 'nodeNum'">
      <span>课时</span>
      <span @click.stop="handleAction(item)">资料</span>
      <span @click.stop="handleAction(item)">作业</span>
    </div> -->
  </div>
</template>

<script setup lang="ts">
// import { debounce } from 'lodash-es'
// import type { StudyPlanFile } from '~/types/study-plan-files'
// import type { HomeworkInfo } from '~/types/homework'

// // 常量定义
// const NODE_TYPES = {
//   FILE_NUM: 'fileNum',
//   HOMEWORK_NUM: 'homeworkNum',
//   FILE: 'file',
//   HOMEWORK: 'homework'
// } as const

// // API端点定义
// const API_ENDPOINTS = {
//   GET_FILES: '/kukestudentservice/wap/studyPlan/getStudyPlanFiles',
//   GET_HOMEWORKS: '/kukestudentservice/wap/studyPlan/getStudyPlanHomeworks'
// } as const

// interface IItem {
//   _nodeType?: 'fileNum' | 'homeworkNum' | 'file' | 'homework';
//   _jieduanId?: string;
//   unlocked?: boolean;
//   _userSelectionMode?: string;
//   _classStartTime?: string;
//   _studyPlanId?: string;
//   children?: IItem[];
//   id?: string;
// }

// const emits = defineEmits<{
//   (e: 'action', event?: IItem): void;
// }>()

// const props = defineProps({
//   item: {
//     type: Object,
//     required: true,
//   },
//   isOpen: {
//     type: Boolean,
//     required: true,
//   },
//   hasChild: {
//     type: Boolean,
//     required: true,
//   },
//   title: {
//     type: String,
//     required: true,
//   },
//   countName: {
//     type: String,
//     required: true,
//   },
//   type: {
//     type: String,
//     required: true,
//   },
// })

/**
 * 获取学习计划 资料
*/
// const fetchStudyPlanFiles = async (item: IItem) => {
//   try {
//     Loading(true)
//     const id = item._jieduanId
//     if (!id) {
//       throw new Error('缺少必要的 阶段ID')
//     }
//     const { data: files } = await useHttp<{list:StudyPlanFile[]}>(API_ENDPOINTS.GET_FILES, {
//       method: 'post',
//       body: { id },
//       transform: input => input.data
//     })
//     const list = (files.value.list || []).map((item2: StudyPlanFile) => ({
//       ...item2,
//       _nodeType: NODE_TYPES.FILE,
//       unlocked: item.unlocked,
//       _jieduanId: item._jieduanId,
//       _userSelectionMode: item._userSelectionMode,
//       _classStartTime: item._classStartTime,
//       _studyPlanId: item._studyPlanId
//     }))
//     item.children = list
//   } catch (error) {
//     console.error('获取学习计划文件失败:', error)
//     // TODO: 可以添加错误提示UI
//   } finally {
//     Loading(false)
//   }
// }
/**
 * 获取学习计划 作业
*/
// const fetchStudyPlanHomeworks = async (item: IItem) => {
//   try {
//     Loading(true)
//     const id = item._jieduanId
//     if (!id) {
//       throw new Error('缺少必要的 阶段ID')
//     }
//     const { data: homeworks } = await useHttp<{list:HomeworkInfo[]}>(API_ENDPOINTS.GET_HOMEWORKS, {
//       method: 'post',
//       body: { id },
//       transform: input => input.data
//     })
//     const list = (homeworks.value.list || []).map((item2: HomeworkInfo) => ({
//       ...item2,
//       _nodeType: NODE_TYPES.HOMEWORK,
//       unlocked: item.unlocked,
//       _jieduanId: item._jieduanId,
//       _userSelectionMode: item._userSelectionMode,
//       _classStartTime: item._classStartTime,
//       _studyPlanId: item._studyPlanId
//     }))
//     item.children = list
//   } catch (error) {
//     console.error('获取学习计划作业失败:', error)
//     // TODO: 可以添加错误提示UI
//   } finally {
//     Loading(false)
//   }
// }

/**
 * 点击事件
*/
// const handleAction = debounce(async (item: IItem) => {
//   if (!props.isOpen) {
//     if (item._nodeType === NODE_TYPES.FILE_NUM) {
//       await fetchStudyPlanFiles(item)
//     } else if (item._nodeType === NODE_TYPES.HOMEWORK_NUM) {
//       await fetchStudyPlanHomeworks(item)
//     }
//   }
//   emits('action', item)
// }, 300)
</script>

<style lang="scss" scoped>
// .StudyPlanTitle {
//   display: flex;
//   align-items: center;
//   justify-content: space-between;
//   height: 88px;
//   border-radius: 8px;

//   &.nodeNum {
//     background: linear-gradient(0, rgba(255,255,255,0) 0%, #F2F3F5 100%);
//     width: 750px;
//     margin-left: -24px;
//     padding-left: 24px;
//     padding-right: 24px;
//   }

//   &__main {
//     position: relative;
//     font-size: 30px;
//     line-height: 36px;
//     color: #111;
//     padding-left: 16px;
//     &::before {
//       content: '';
//       position: absolute;
//       left: 0;
//       top: 3px;
//       width: 8px;
//       height: 26px;
//       background: var(--kkc-brand-text);
//       border-radius: 4px;
//     }
//   }

//   &__right {
//     display: flex;
//     align-items: center;
//   }

//   &__icon {
//     width: 157px;
//     height: 48px;
//     border-radius: 12px;
//     border: 1px solid var(--kkc-brand-text);
//     display: flex;
//     align-items: center;
//     justify-content: center;
//     .kkc-icon {
//       color: var(--kkc-brand-text);
//     }
//   }
// }
</style>
