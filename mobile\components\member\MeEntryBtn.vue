<template>
  <!-- class="inline-flex ml-[16px] text-[22px] text-[#FED8B8] px-[8px] h-[36px] bg-[linear-gradient(_134deg,_#453D5C_0%,_#1B142A_100%)] rounded-[12px] leading-[36px] text-center items-center justify-center" -->
  <span
    :class="{
      'no-name':!len
    }"
    class="MeEntryBtn-item"
    @click.stop="clickFn"
  >
    <!-- <KKCIcon
      name="icon-chuangguan"
      class="mr-[4px]"
    /> -->
    <!-- {{ logoSetting }} -->
    <img
      :src="logoSettingImg"
      class="w-[40px] h-[40px]"
      :class="[`logoSetting-${logoSetting}`] "
      alt=""
    >
    <!-- <span
      v-if="len > 0"
    >{{ memberName }}</span> -->
  </span>
</template>

<script lang="ts" setup>
import logoSetting1 from 'assets/images/member/<EMAIL>'
import logoSetting2 from 'assets/images/member/<EMAIL>'
import logoSetting3 from 'assets/images/member/<EMAIL>'
import logoSetting4 from 'assets/images/member/<EMAIL>'
import logoSetting5 from 'assets/images/member/<EMAIL>'
import logoSetting6 from 'assets/images/member/<EMAIL>'
type Props = {
memberName?:string
logoSetting:number
}
const props = withDefaults(defineProps<Props>(), {
  memberName: ''
})
const len = computed(() => {
  return props.memberName?.length || 0
})
const logoSettingImg = computed(() => {
  switch (props.logoSetting) {
    case 1:
      return logoSetting1
    case 2:
      return logoSetting2
    case 3:
      return logoSetting3
    case 4:
      return logoSetting4
    case 5:
      return logoSetting5
    case 6:
      return logoSetting6
    default:
      return logoSetting1
  }
})
const _emit = defineEmits<{
  (e: 'membershipOpen', val?: string): void;
}>()

const clickFn = () => {
  _emit('membershipOpen')
}

</script>

<style lang="scss">
// .no-name{
//   height: 40px;
//   width: 40px;
// }
.MeEntryBtn-item{
  // margin-left: 8px;
  margin-right: 8px;
  flex-shrink: 0;
  &:last-child{
    margin-right: 0;
  }
}
.MeEntryBtn-items {
  display: flex;
  &.is-more {
    // & > .flex {
      .MeEntryBtn-item {
        margin-right: -6px;
      }
    // }
  }
}
</style>
