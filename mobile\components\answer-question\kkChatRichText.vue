<template>
  <div ref="richTextRef" class="relative" v-html="richText" />
</template>
<script lang="ts" setup>
const props = withDefaults(
  defineProps<{
      richText: string
      signFun?: Function
      isPrivate?: boolean // 图片是否私有
      ossInstance?: any
    }>(),
  {
    richText: '',
    signFun: () => {
      return () => {}
    },
    isPrivate: true
  }
)
const richTextRef = ref()

const isMarkdown = (text: string): boolean => {
  const markdownPatterns = [
    /^#{1,6}\s/, // 标题
    /^\s*[-*+]\s/, // 无序列表
    /^\s*\d+\.\s/, // 有序列表
    /\*\*.*?\*\*/, // 加粗
    /__.*?__/, // 加粗
    /\*.*?\*/, // 斜体
    /_.*?_/, // 斜体
    /\[.*?\]\(.*?\)/, // 链接
    /`{3}.*?`{3}/s, // 代码块
    /`.+?`/, // 行内代码
    /^---$/, // 水平线
    /^___$/, // 水平线
    /^(\*{3}|-{3}|_{3})$/, // 水平线
    /(\$[^$]*\$)|(\\\([^\\)]*\\\))|(\\\[([\s\S]*?)\\\])/g
  ]

  return markdownPatterns.some(pattern => pattern.test(text))
}
const formatRichText = async () => {
  // const katexRegex = /(\$[^\$]*\$)|(\\\([^\\)]*\\\))/g
  const aiMarkDownDiv = richTextRef.value.getElementsByClassName(
    'answer_ai_markDown_wrap'
  )
  for (const key of aiMarkDownDiv) {
    console.log(key)
    // key.innerHTML = katexRegex.test(key.textContent) || !key.innerHTML.includes('<img')
    key.innerHTML = isMarkdown(key.textContent)
      ? markDownItKateX(key.textContent)
      : key.innerHTML
  }
  if (!props.isPrivate) { return }
  const richImgTag = richTextRef.value.querySelectorAll('img')
  for (const key of richImgTag) {
    const { result } = await props.signFun(key.dataset.src || key.src, {}, props.ossInstance)
    key.src = result[0]
    key.style.maxWidth = '2.7rem'
    key.style.maxHeight = '3.5rem'
    key.style.objectFit = 'contain'
  }
}

onMounted(() => {
  formatRichText()
})
</script>
