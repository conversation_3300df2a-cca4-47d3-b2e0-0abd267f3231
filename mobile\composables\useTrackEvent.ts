/**
 * 埋点统计 Composable
 * 提供类型安全的埋点功能，支持友盟统计和 CNZZ 统计
 *
 * @example 基础使用
 * ```typescript
 * const { track } = useTrackEvent()
 *
 * // 用户行为埋点
 * track({
 *   category: '用户行为',
 *   action: '点击按钮',
 *   label: '登录按钮'
 * })
 * ```
 *
 * @example 带数值的埋点
 * ```typescript
 * track({
 *   category: '商品',
 *   action: '购买',
 *   label: '课程ID_123',
 *   value: 299,
 *   nodeid: 'product_123'
 * })
 * ```
 *
 * @example 错误追踪埋点
 * ```typescript
 * try {
 *   // 业务逻辑
 * } catch (error) {
 *   track({
 *     category: '错误',
 *     action: '接口调用失败',
 *     label: 'loginAPI'
 *   })
 * }
 * ```
 */

export interface TrackEventOptions {
  category: string
  action: string
  label?: string
  value?: number
  nodeid?: string
}

export function useTrackEvent () {
  /**
   * 执行埋点事件
   * @param options 埋点参数
   */
  const track = (options: TrackEventOptions) => {
    const { category, action, label, value, nodeid } = options

    // 确保在客户端环境
    if (typeof window === 'undefined') {
      console.warn('useTrackEvent: Cannot track on server side')
      return
    }

    try {
      let tracked = false

      // 1. 友盟统计
      if (window.uma && typeof window.uma.trackEvent === 'function') {
        window.uma.trackEvent(category, action, label, value, nodeid)
        tracked = true
      }

      // 2. CNZZ 统计
      if (window._czc && typeof window._czc.push === 'function') {
        window._czc.push(['_trackEvent', category, action, label, value, nodeid])
        tracked = true
      }

      // 3. 开发环境日志
      if (process.env.NODE_ENV === 'development') {
        console.log('🔍 Track Event:', { category, action, label, value, nodeid, tracked })
      }
    } catch (error) {
      console.error('Track event failed:', error)
    }
  }

  return {
    track
  }
}

// 类型声明扩展
declare global {
  interface Window {
    uma?: {
      trackEvent: (category: string, action: string, label?: string, value?: number) => void
    }
    _czc?: {
      push: (args: any[]) => void
    }
  }
}
