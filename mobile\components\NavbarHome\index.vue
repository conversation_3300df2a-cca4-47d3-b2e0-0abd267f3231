<template>
  <header :class="[bem(),'fixed']">
    <div :class="[bem('logo')]">
      <Logo />
      <!-- <NavbarHomeSearch placeholder="" /> -->
    </div>
  </header>
</template>

<script setup lang="ts">

const bem = useBem('home-navbar')
</script>

<style lang="scss">
.home-navbar {
  padding: 0 24px;
  background-color: #fff;
  width: 100%;
  z-index: 21;
  &.fixed {
    position: sticky;
    top: 0;
  }
  &__logo {
    display: flex;
    align-items: center;
    justify-content: space-between;
    //
  }
}
</style>
