<template>
  <div class="w-[100%]">
    <NavBar v-if="!isXcx && !isKukeCloudAppWebview" :title="receiveInfo.activityName" />
    <div class="code_bg w-[100%] h-[580px]">
      <div class="h-[100%] p-[32px]">
        <div>
          <div class="flex justify-between items-center">
            <div class="flex items-center">
              <img class="w-[88px] h-[88px] rounded-[100%] border-[4px] border-[#fff]" :src="receiveInfo.photo||DefaultAva" alt="">
              <div class="flex flex-col justify-between ml-[28px]">
                <span class="text-[32px] text-[#111] font-medium">{{ receiveInfo.userName }}</span>
                <img class="w-[210px]" src="@/assets/images/activationCode/get_code.png" alt="">
              </div>
            </div>
            <img class="w-[313px] h-[123px]" src="@/assets/images/activationCode/code_logo.png" alt="">
          </div>
          <div class="mt-[-7px] ml-[30px]">
            <img
              class="w-[30px]"
              src="@/assets/images/activationCode/top.png"
              alt=""
            >
          </div>
        </div>

        <div class="code_content w-[100%] h-[386px] rounded-[32px] py-[40px] px-[22px] flex flex-col justify-around items-center">
          <span
            class="text-[48px] text-[#fff] leading-[56px] font-semibold  "
            @click="copy($event)"
          >
            <span>
              <span>{{ receiveInfo.activationCode }}</span>
            </span>
          </span>
          <span
            class="inline-flex w-[100%] items-center justify-center"
            @click="copy($event)"
          >
            <img
              class="w-[32px]"
              src="@/assets/images/activationCode/copy.png"
              alt=""
            >
            <span class="ml-[8px] text-[24px] text-[#fff]">点击复制</span>
          </span>
          <span
            class="inline-block w-[100%] py-[30px] bg-[#fff] text-[32px] text-[#111] rounded-[24px] text-center"
            @click="goRedem"
          >去兑换</span>
          <span
            class="text-[24px] text-[#fff] font-normal"
          >有效期：{{ receiveInfo.activationCodeBeginTime }} - {{ receiveInfo.activationCodeEndTime }} </span>
        </div>
      </div>
    </div>
    <!-- goods -->
    <div class="p-[32px] bg-[#fff]">
      <img
        class="w-[100%]"
        src="@/assets/images/activationCode/title.png"
        alt=""
      >
      <div class="mt-[32px]">
        <div
          v-if="isShowTab"
          class="bg-[#FFF1E5] text-[28px] text-[#FF6F00] p-[8px] rounded-[24px] text-center"
        >
          <span
            class="inline-block w-[50%] py-[23px]"
            :class="tab === 0 && 'active_tab'"
            @click="tab = 0"
          >课程</span>
          <span
            class="inline-block w-[50%] py-[23px]"
            :class="tab === 1 && 'active_tab'"
            @click="tab = 1"
          >优惠券</span>
        </div>
        <!-- 课程 -->
        <div v-if="tab === 0">
          <ul class="flex flex-wrap">
            <li
              v-for="item in receiveInfo.goodsList"
              :key="item.goodsMasterId"
              class="flex mt-[32px] w-[100%]"
            >
              <img
                class="w-[210px] h-[140px] object-cover rounded-[16px]"
                :src="item.goodsImg"
                alt=""
              >
              <div class="flex flex-col justify-between ml-[16px]">
                <span class="text-[#111] text-[28px] kk-two-ellipsis">{{
                  item.goodsTitle
                }}</span>
                <span
                  class="bg-[#F5F6F9] py-[9px] w-[120px] text-center text-[#999] text-[24px] rounded-[12px]"
                >兑换商品</span>
              </div>
            </li>
          </ul>
        </div>
        <!-- 优惠券 -->
        <div v-if="tab === 1">
          <ul class="flex flex-wrap justify-between">
            <li
              v-for="item in receiveInfo.couponList"
              :key="item.id"
              class="w-[100%] p-[24px] mt-[32px] rounded-[16px] bg-[#FFF2F5] border-color-[#FFD0DB]"
            >
              <div class="flex justify-between items-center mb-[16px]">
                <div
                  class="flex flex-col justify-between text-[24px] text-[#666]"
                >
                  <span class="text-[28px] text-[#111] font-bold">{{
                    item.couponName
                  }}</span>
                  <span class="my-[8px]">有效期：
                    <span v-if="item.expiredType===1">{{ item.expiredStart }} - {{ item.expiredEnd }}</span>
                    <span v-else>领取后{{ item.expiredDays }}天内有效</span>
                  </span>
                  <div>
                    <template v-if="item?.couponType === 1">
                      {{
                        item?.couponThreshold === 0
                          ? '无门槛'
                          : `满${item?.couponThreshold}元`
                      }}{{ item?.rewardAmount?`减${item?.rewardAmount}元`:'' }}
                    </template>
                    <template v-else>
                      {{
                        item?.couponThreshold === 0
                          ? '无门槛'
                          : `满${item?.couponThreshold}元`
                      }}{{ item?.discountRate?`享${item?.discountRate}折`:'' }}{{ item?.discountAmount?`,最多优惠${ item?.discountAmount }元`:'' }}
                    </template>
                  </div>
                </div>
                <div class="flex text-[24px] text-[#EB2330]">
                  <div>
                    <div v-if="item?.couponType == 2">
                      <span class="text-[48px]">{{
                        item?.discountRate
                      }}</span>
                      <span class="">折</span>
                    </div>
                    <div v-else>
                      <span class="text-[48px]">{{
                        item?.rewardAmount
                      }}</span>
                      <span class="">元</span>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="pt-[16px] text-[22px] text-[#999] border-dashed border-t-[1px] border-[#FFD0DB]"
              >
                {{ item.goodsSettingName }}
              </div>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { useReceive } from './hooks/useReceive'
import { useUserStore } from '~/stores/user.store'
import DefaultAva from '@/assets/images/user/default-avatar.png'

const { isXcx, isKukeCloudAppWebview } = useAppConfig()
const { getUserInfo } = useUserStore()
console.log(getUserInfo)
const { receiveInfo, getDetail, tab } = useReceive()

const isShowTab = computed(() => {
  return receiveInfo.value.couponList?.length && receiveInfo.value.goodsList?.length
})
const copy = (event: any) => {
  handleClipboard(receiveInfo.value.activationCode, event)
}
const goRedem = () => {
  navigateTo({
    path: '/redemption-center',
  })
}
useHead({
  titleTemplate: () => {
    return receiveInfo.value.activityName || '激活码'
  }
})
getDetail()
</script>
<style lang="scss" scoped>
.code_bg{
    background: linear-gradient( 180deg, #D3FB00 0%, #FFFFFF 100%);
    // border-radius: 0px 0px 24px 24px;
}
.code_content{
    background: linear-gradient(180deg, #fc5303 0%, #ff9500 100%);
}
.active_tab {
  background: #ff6f00;
  color: #fff;
  border-radius: 24px;
}
.kk-two-ellipsis {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  word-break: break-word;
}
</style>
