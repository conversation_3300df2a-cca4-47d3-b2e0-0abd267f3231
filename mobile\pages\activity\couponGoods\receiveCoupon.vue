<script setup lang="ts">
import { CouponReceiveStatus } from './hooks/receiveCoupon'
import CouponGet from '@/assets/images/couponGoods/get.png'
import CouponOver from '@/assets/images/couponGoods/over.png'
import NotStart from '@/assets/images/couponGoods/not_start.png'
import CouponNull from '@/assets/images/couponGoods/null.png'
import { useUserStore } from '~/stores/user.store'
definePageMeta({
  ignoreLearnTarget: true,
})
useHead({
  titleTemplate: () => {
    return '优惠券'
  },
})
const userStore = useUserStore()

const { commonApi, activityApi, userApi } = useApi()

const mobile = ref('')
const code = ref('')
const titcketStyle:any = {
  1: CouponGet,
  2: CouponOver,
  3: CouponNull,
  7: CouponGet,
  9: NotStart,
}
const route = useRoute()
watch(
  () => mobile.value,
  (newValue) => {
    if (newValue.length < 11) { return }
    couponStatusList[CouponReceiveStatus.continue].content = couponStatusList[
      CouponReceiveStatus.Received
    ].content = `优惠券已发放至 ${newValue.slice(0, 3)}****${newValue.slice(
      -4,
    )} 账户中`
  },
)
const { isXcx } = useAppConfig()
const pageId = useCookie('LT') || ''
const toClient = () => {
  const xcxUrl = pageId.value ? `/courseList/1?title=网课&pageId=${pageId.value}` : '/courseList/1?title=网课'
  if (coupon.value.goodsSetting === 1) {
    if (isXcx) {
      userStore.jumpAppletWebPage(xcxUrl)
    } else {
      navigateTo({
        path: '/courseList/1',
        query: {
          title: '网课',
          pageId: pageId.value,
          pageType: 100,
          isPageId: '1'
        }
      })
    }
  } else if (coupon.value.goodsSetting === 4) {
    navigateTo(
      {
        path: `/activity/couponGoods/${coupon.value.id}`,
        query: {
          assignTime: new Date().getTime()
        }
      }
    )
  } else {
    navigateTo({
      path: `/user/coupon/${coupon.value.id}`,
      query: {
        assignTime: new Date().getTime(),
        isPageId: '1'
      }
    })
  }
}
const toIndex = () => {
  window.location.replace(window.location.origin)
}
const toMyCoupon = () => {
  if (os?.isApp && os.isAndroid) {
    try {
      window.android.goMyCoupon()
    } catch (error) {
      navigateTo({
        path: '/user/coupon'
      })
    }
  } else if (os?.isApp && os.isHarmonyOS) {
    try {
      window.harmony?.goMyCoupon()
    } catch (error) {
      navigateTo({
        path: '/user/coupon'
      })
    }
  } else if (os?.isApp && os.isPhone) {
    try {
      window.webkit.messageHandlers.goMyCoupon.postMessage(null)
    } catch (error) {
      navigateTo({
        path: '/user/coupon'
      })
    }
  } else {
    navigateTo({
      path: '/user/coupon'
    })
  }
}

const couponStatusList = [
  {
    couponStatus: CouponReceiveStatus.WaitingReceive,
    title: '填写手机号，领取优惠券',
    buttonText: '开心收下',
    handle: receiveCoupons,
    tips: '输入手机号并确认，优惠券自动发放至账户',
  },
  {
    couponStatus: CouponReceiveStatus.Received,
    title: '领取成功',
    content: '',
    buttonText: '',
    tips: '',
  },
  {
    couponStatus: CouponReceiveStatus.ActivityFinished,
    title: '来晚啦，活动已结束',
    content: '进入首页，浏览发现更多惊喜哦~ ',
    buttonText: '去首页',
    handle: toIndex,
    tips: '',
  },
  {
    couponStatus: CouponReceiveStatus.NoneCoupon,
    title: '很遗憾，优惠券已领完',
    content: '进入首页，浏览发现更多惊喜哦~',
    buttonText: '去首页',
    handle: toIndex,
    tips: '',
  },
  {
    couponStatus: CouponReceiveStatus.Unqualified,
    tips: '暂不符合领取条件哟～',
    content: '进入首页，浏览发现更多惊喜哦~',
    buttonText: '去首页',
    handle: toIndex,
  },
  {
    couponStatus: CouponReceiveStatus.WaitingReceive,
    tips: '已达领取上限，不要太贪心哦～',
    content: '您已领到优惠券，快去使用吧',
    buttonText: '立即使用',
    handle: toClient,
  },
  {
    couponStatus: CouponReceiveStatus.Deleted,
    title: '优惠券不存在',
    content: '进入首页，浏览发现更多惊喜哦~',
    buttonText: '去首页',
    handle: toIndex,
    tips: '',
  },
  {
    couponStatus: CouponReceiveStatus.continue,
    title: '领取成功',
    content: '',
    buttonText: '',
    tips: '',
  },
  {
    couponStatus: CouponReceiveStatus.isNotNew,
    title: '仅限新用户可领哦～',
    content: '进入首页，浏览发现更多惊喜哦~',
    buttonText: '去首页',
    handle: toIndex,
    tips: '仅限新用户可领哦～',
  },
  {
    couponStatus: CouponReceiveStatus.NotStart,
    title: '活动未开始',
    content: '进入首页，浏览发现更多惊喜哦~',
    buttonText: '去首页',
    handle: toIndex,
    tips: '活动未开始哦～',
  },
]
const id = computed(() => {
  return route.query.id as string
})
const couponStatus = ref<any>(null)
const coupon = ref<any>()
async function initCoupon () {
  couponStatus.value = couponStatusList[0]
  const { data } = await activityApi.getPromotionCouponDetails({ id: id.value })
  coupon.value = data.value
  couponStatus.value = couponStatusList[data.value.receiveStaus]
  if (coupon.value.receiveStaus === '9') {
    Message('活动暂未开始，无法领取')
  }
}

initCoupon()

watch(
  () => route,
  () => {
    mobile.value && (mobile.value = '')
    code.value && (code.value = '')
    initCoupon()
  },
  {
    deep: true,
  },
)

// 验证码验证的回调函数
const captchaVerifyCallback = async (captchaVerifyParam: string) => {
  // 获取验证码接口参数
  const params = {
    mobile: mobile.value,
    scene: GetCodeType.login,
    captchaVerifyParam
  }

  return getVerificationCodeStatus(commonApi.sendCode, params)
}
// 执行成功的回调地址
const onBizResultCallback = (bizResult: boolean) => {
  // 开始执行倒计时
  if (bizResult) { countDown() }
}
// 初始化验证码配置文件
const captchaConfig: globalThis.Captcha = { captchaVerifyCallback, onBizResultCallback }
// 注册验证码验证函数
const { initAliyunCaptcha, destroyAliyunCaptcha, getVerificationCodeStatus, captchaButton } = useCaptchaVerify(captchaConfig)
// 初始化验证码
onMounted(initAliyunCaptcha)
onBeforeUnmount(destroyAliyunCaptcha)

const timer = ref(0)
const canClick = ref<boolean>(true)
const countDown = () => {
  if (!canClick.value) {
    return
  }
  timer.value = 60
  canClick.value = false
  // TODO 内存泄漏可疑点: 确保客户端执行并及时清除
  const timersInter = setInterval(() => {
    timer.value--
    if (timer.value === 0) {
      clearInterval(timersInter)
      canClick.value = true
    }
  }, 1000)
}
const getCode = async () => {
  if (mobile.value.length <= 0) { return Message('请输入手机号') }
  if (!regPhone.test(mobile.value)) { return Message('手机号输入有误，请检查后重新输入～') }
  if (timer.value > 0) { return }

  captchaButton.value?.click()
}
async function receiveCoupons () {
  if (!userStore.isLogin) {
    if (mobile.value.length <= 0) { return Message('请输入手机号') }
    if (!regPhone.test(mobile.value)) { return Message('手机号输入有误，请检查后重新输入～') }
    if (code.value.length <= 0) { return Message('请输入验证码') }
    if (!/^[0-9]{6}$/.test(code.value)) { return Message('验证码有误，请重新输入～') }
    const token = await userStore.loginByCode({
      mobile: btoa(mobile.value),
      code: code.value,
    }, false)
    if (!token) { return }
  }

  const { addBehaviorRecords } = useBehaviorRecord()
  addBehaviorRecords(ActionEnum.CouponManagement, SourceEnum.Wap, {
    goodsTitle: coupon.value?.couponName,
    clientType: isXcx ? ClientType.WX_XCX : ClientType.WAP,
    extension8: coupon.value?.discountContent
  })

  // 领取
  const {
    data
  } = await beginReceiveCoupon()
  if (timer.value > 0) { timer.value = 0 }
  const status = Number(data.value.receiveStaus)
  if (status <= 3 || status === 7) {
    couponStatus.value = couponStatusList[status]
  } else {
    Message(couponStatusList[status].tips)
  }
}
async function beginReceiveCoupon () {
  const { data }:any = await userApi.getUserInfo()
  mobile.value = data.value.data.mobile
  return await activityApi.receiveTemp({
    mobile: mobile.value,
    templateId: unref(id),
    userId: data.value.data.userId,
    extensionUserId: route.query.extensionUserId
  })
}
const mobileFormatter = () => {
  mobile.value =
    mobile.value
      .replace(/[^\d]+/g, '')
      .replace(/^0+(\d)/, '$1')
      .match(/^[1-9]\d{0,10}$/)?.[0] || ''
}
const codeFormatter = () => {
  code.value =
    code.value
      .replace(/[^\d]+/g, '')
      .match(/^\d{0,6}$/)?.[0] || ''
}
// const discountContentSplit = (v:any) => {
//   return v?.split(',')
// }
</script>

<template>
  <div class="poster-wrap">
    <div class="poster relative">
      <div
        class="poster__ticket ticket--normal"
      >
        <!-- 已领取 -->
        <!-- 已结束 -->
        <!-- 已领完 -->
        <img v-if="[1,2,3,7,8,9].includes(couponStatus?.couponStatus)" :src="titcketStyle?.[couponStatus?.couponStatus]" alt="" class="absolute w-[144px] h-[111px] left-[35px] bottom-0 z-[101]">
        <div
          v-if="couponStatus?.couponStatus === CouponReceiveStatus?.Deleted"
          class="is-empty"
        >
          空空如也～
        </div>
        <div class="flex py-[30px]" :class="[couponStatus?.couponStatus !== CouponReceiveStatus?.WaitingReceive?'opacity-3':'']">
          <div v-if="coupon?.couponType == 2 || coupon?.couponThresholdName" class="w-[228px] h-[180px] flex flex-col items-center justify-center text-[#B84806]  left-right ">
            <div

              class="ticket-money"
            >
              <div v-if="coupon?.couponType == 2">
                <span class="ticket-money__prefix">{{
                  coupon?.couponThresholdName
                }}</span>
                <span class="ticket-money__unit">折</span>
              </div>
              <div v-else-if="coupon?.couponThresholdName">
                <span class="ticket-money__prefix">{{
                  coupon?.couponThresholdName?.split('.')[0]
                }}</span>
                <span v-if="coupon?.couponThresholdName?.split('.')[1]" class="ticket-money__suffix">.{{ coupon?.couponThresholdName?.split('.')[1] }}</span>
                <span class="ticket-money__unit">元</span>
              </div>
            </div>
            <div v-if="coupon?.receiveTypeName" class="px-[12px] py-[5px] text-[20px] coupon-tips leadin-[20px]">
              {{ coupon?.receiveTypeName }}
            </div>
          </div>
          <div class="flex-1 py-[10px]">
            <div class="flex flex-col  justify-center text-[#A24209] pl-[22px]">
              <h1 class="text-[32px] mb-[12px]  line-clamp-1">
                {{ coupon?.couponName }}
              </h1>
              <div class="text-[22px] mb-[20px] leading-[22px]">
                {{ coupon?.discountContent }}
              </div>
              <div class="text-[22px] mb-[20px] leading-[22px]">
                <!-- v-for="(item, index) in discountContentSplit(coupon?.discountContent)"
                  :key="index" -->
                <div>
                  {{ coupon?.goodsSettingName }}
                  <!-- <span v-if="coupon?.goodsSetting===1 || coupon?.goodsSetting===2">(特殊商品除外)</span> -->
                </div>
              </div>
              <div class="text-[22px] leading-[22px]">
                {{ coupon?.expiredTimeName }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="poster__info">
        <div v-if="couponStatus?.couponStatus === CouponReceiveStatus?.WaitingReceive && !userStore.isLogin" class="info__title">
          <img src="@/assets/images/couponGoods/title.png">
          <span>{{ couponStatus?.title }}</span>
          <img src="@/assets/images/couponGoods/title.png">
        </div>
        <div class="info__content">
          <div
            v-if="
              couponStatus?.couponStatus === CouponReceiveStatus?.WaitingReceive && !userStore.isLogin
            "
          >
            <input
              v-model="mobile"
              maxlength="11"
              placeholder="请输入手机号"
              :oninput="mobileFormatter"
            >
            <div class="input_code">
              <input
                v-model="code"
                maxlength="6"
                placeholder="请输入验证码"
                :oninput="codeFormatter"
              >
              <span
                :disabled="timer > 0"
                style="opacity: 0.8"
                @click="getCode"
              >
                {{ timer <= 0 ? '获取验证码' : `${timer}s 后重试` }}
              </span>
              <button id="captcha-button" hidden />
            </div>
          </div>
          <div
            v-if="
              couponStatus?.couponStatus !== CouponReceiveStatus?.WaitingReceive
            "
            class="content"
          >
            <span v-html="couponStatus?.content" />
          </div>
          <button
            v-if="
              couponStatus?.couponStatus === CouponReceiveStatus?.Received ||
                couponStatus?.couponStatus === CouponReceiveStatus?.continue
            "
            class="button space"
            @click="toMyCoupon"
          >
            查看优惠券
          </button>
          <div
            v-if="
              couponStatus?.couponStatus === CouponReceiveStatus?.Received ||
                couponStatus?.couponStatus === CouponReceiveStatus?.continue
            "
            class="btn-use mt-[16px]"
            @click="toClient"
          >
            立即使用
          </div>
          <button
            v-if="couponStatus?.buttonText && couponStatus?.handle"
            class="button"
            :class="couponStatus?.couponStatus === CouponReceiveStatus?.WaitingReceive && userStore.isLogin && 'btnCenter'"
            @click="couponStatus?.handle"
          >
            {{ couponStatus?.buttonText }}
          </button>
        </div>
        <div v-if="couponStatus?.tips && !userStore.isLogin" class="info__tips">
          <KKCIcon name="icon-buketixianshuoming" />
          <span>{{ couponStatus?.tips }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.poster-wrap {
  margin: 0 auto;
  width: 100%;
  background: #f50c28;
  font-family: PingFang SC-Regular, PingFang SC;
}

.poster {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 100vh;
  background-repeat: no-repeat;
  // background-size: cover;
  background-image: url('~/assets/images/couponGoods/bg.png');
  background-size: 100%;
  background-position-y: 140px;

  &__ticket {
    position: relative;
    margin-top: 368px;
    font-size: 32px;
    width: 702px;
    height: 240px;
    color: #a24209;
    // background: #FFE1D1;
    border-radius: 24px 24px 24px 24px;
    box-shadow: 0px 4px 16px 0px rgba(198,13,13,0.4);
    background-size: 100%;
    background-repeat: no-repeat;
    .coupon-tips{
      border: 1px solid #B84806;
      border-radius: 20px;
    }
    .left-right{
      border-right: 1px dashed rgba(235, 77, 69, 0.4);
    }
  }

  .ticket--normal {
    background-image: url('~/assets/images/couponGoods/ticket_normal.png');
  }

  .is-cover {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 100;
    background-color: #ffe1d1;
    opacity: 0.4;
    border-radius: 32px;
    backdrop-filter: blur(10px);
  }
  .is-empty {
    position: absolute;
    left: 229px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 40px;
    font-weight: 600;
    color: #a24209;
    opacity: 0.6;
  }

  .ticket__name {
    position: absolute;
    font-weight: 600;
    width: 300px;
    height: 36px;
    line-height: 36px;
    top: 48px;
    left: 40px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 32px;
  }

  .ticket__des {
    color: #a24209;
    position: absolute;
    font-size: 24px;
    top: 95px;
    left: 40px;
    opacity: 0.6;
    width: 50%;
    div {
      line-height: 1.5;
    }
  }

  .ticket__time {
    position: absolute;
    font-size: 22px;
    bottom: 17px;
    left: 53px;
  }

  .ticket-money {

    font-size: 12px;
    div {
      display: inline-block;
      font-weight: bold;
    }
    &__prefix {
      font-size:54px;
      line-height: 54px;
      height: 64px;
      display: inline-block;
      margin-bottom: 8px;
    }
    &__suffix {
      font-size: 36px;
      line-height: 36px;
      height: 36px;
    }
    &__unit {
      font-size: 28px;
      line-height: 28px;
      height: 28px;
    }
  }

  .poster__info {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: calc(100% - 32px);
    margin-top: 48px;
    bottom: 74px;
    left: 43px;
    color: #fff;
    font-size: 14px;
    text-align: center;
  }

  .info__content {
    margin: 0 64px;

    input {
      width: 590px;
      height: 88px;
      border-radius: 24px;
      margin-bottom: 48px;
      overflow: hidden;
      background: #EB3C21;
      font-size: 30px;
      padding-left: 32px;
      color: #fff;
    }
    input::placeholder{
      font-size: 30px;
      color: #fff;
      opacity: 0.5;
    }
    input:focus{
      outline: none;
    }
    .input_code{
      position: relative;
      >span{
        position: absolute;
        font-size: 30px;
        right: 32px;
        line-height: 88px;
      }
    }
    .content {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 85px 37px;
      margin-bottom: 32px;
      width: 590px;
      height: 224px;
      background: #eb3c21;
      border-radius: 32px;
      font-weight: 600;
      font-size: 30px;
    }

    .button {
      background: linear-gradient(#ffe07f, #fc9f52);
      color: #b60014;
      width: 590px;
      height: 100px;
      border-radius: 24px;
      font-size: 40px;
      font-weight: bold;
      border: 0;
      margin: 30px 0 34px;
      box-shadow: 0 5px 2px #ec4619;
    }
    .btnCenter{
      margin: 190px 0 278px 0 ;
    }
    .space {
      margin-top: -16px;
      margin-bottom: 0px;
    }
    .btn-use {
      width: 590px;
      height: 100px;
      border: 2px solid;
      color: #ffe07f;
      display: flex;
      font-size: 40px;
      font-weight: 600;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      border-radius: 24px;
    }
  }

  .info__tips {
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0.8;
    gap: 4px;
    font-size: 24px;
  }

  .info__title {
    display: flex;
    align-items: center;
    font-size: 36px;
    font-weight: 600;
    margin: 43px 0;
    gap: 10px;

    img {
      width: 40px;
      height: 20px;
    }

    img:nth-child(3) {
      transform: rotateZ(180deg);
    }
  }

  .qr-wrap {
    width: 200px;
    height: 200px;
    background-color: #eb3c21;
    border-radius: 16px;
    padding: 12px;
    margin-bottom: 64px;
  }
}
</style>
