/**
 * @description 监听处理是否打开/关闭键盘
 */
export const useKeyboardOpen = (close: () => void) => {
  const isKeyboardOpen = ref<boolean>(false)
  // 初始化记录页面的高度
  let initialHeight = 0

  // 键盘关闭检测（visualViewport + resize）
  const checkKeyboardClose = debounceFunc(() => {
    if (!isKeyboardOpen.value) {
      return
    }

    // 方法 1：优先使用 visualViewport（现代浏览器）
    if (window.visualViewport) {
      if (window.visualViewport.height >= initialHeight) {
        close()
      }
      return
    }

    // 方法 2：回退方案（旧浏览器，如 iOS <13）
    if (window.innerHeight >= initialHeight) {
      close()
    }
  }, 200, false)

  // 重制信息
  const handleResetInfo = () => {
    isKeyboardOpen.value = false
    handleUnregisterEvent()
  }

  // 处理事件挂载
  const handleRegisterEvent = () => {
    if (window.visualViewport) {
      window.visualViewport?.addEventListener('resize', checkKeyboardClose)
    } else {
      // 兼容方案
      window.addEventListener('resize', checkKeyboardClose)
    }

    isKeyboardOpen.value = true
  }

  // 处理事件卸载
  const handleUnregisterEvent = () => {
    window.visualViewport?.removeEventListener('resize', checkKeyboardClose)
    window.removeEventListener('resize', checkKeyboardClose)
  }

  // 初始化打开页面变化监测
  onMounted(() => {
    nextTick(() => {
      // 减去 100 是为了兼容部分手机浏览器 顶部导航栏可以收起 导致初始化获取的高度异常的问题
      initialHeight = window.innerHeight - 90
    })
  })

  return {
    isKeyboardOpen,
    handleResetInfo,
    handleRegisterEvent
  }
}
