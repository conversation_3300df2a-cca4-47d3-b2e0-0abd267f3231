// 小程序更新题库列表页面
export const useRefreshQuestionListData = async (path?: string) => {
  const { isXcx } = useAppConfig()
  // 获取本地存储的questionBankHistoryState，如果没有则默认为'/'
  // const questionPath = localStorage.getItem('questionBankHistoryState') || '/'
  // 如果有传入的path，则使用传入的path，否则使用questionPath
  // const jumpPath = path || questionPath

  if (isXcx) {
    console.log('小程序更新题库列表页面', path)
    // 向小程序发送消息，刷新路由数据
    // wx.miniProgram.postMessage({
    //   data: {
    //     refreshRouteData: JSON.stringify({
    //       path: jumpPath,
    //     })
    //   }
    // })
  }
}
