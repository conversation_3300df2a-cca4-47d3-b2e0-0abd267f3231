export const isClient = typeof window !== 'undefined'

export const isDef = <T = any>(val?: T): val is T => typeof val !== 'undefined'

export const notNullish = <T = any>(val?: T | null | undefined): val is T =>
  val != null

export const isDefined = (val: unknown) => {
  return isDef(val) && notNullish(val)
}
// kk-from 类型
export enum CLIENT_TYPE {
  PC = '1',
  WAP = '7',
  XCX = '5',
  APP = '9',
  // '1' = 'PC',
  // '7' = 'WAP',
}
/**
 * @deprecated
 * 请使用 useAppConfig().isPc
 * */
export function isPC (type: string) {
  return String(type) === CLIENT_TYPE.PC
}
/** @deprecated 请使用 useAppConfig().isMobile */
export function isWAP (type: string) {
  return String(type) === CLIENT_TYPE.WAP
}
/** @deprecated 请使用 getDeviceOfUA */
export function isXcxBrowser (UA?: string) {
  let ua = UA?.toLowerCase()
  if (process.client) {
    ua = window.navigator.userAgent.toLowerCase()
  }
  // return ua && ua.includes('miniprogram')
  if (ua && ua.includes('miniprogram')) {
    return true
  }
  return false
}
/** @deprecated 请使用 getDeviceOfUA */
export function isKukeCloudAppWebviewBrowser (UA?: string) {
  let ua = UA
  if (process.client) {
    ua = window.navigator.userAgent.toLowerCase()
  }
  if (ua && ua.includes('kukecloudappwebview')) {
    return true
  }
  return false
}

export const assert = (condition: boolean, ...infos: any[]) => {
  if (!condition) {
    console.warn(...infos)
  }
}

export const now = () => Date.now()
export const timestamp = () => +Date.now()
export const clamp = (n: number, min: number, max: number) =>
  Math.min(max, Math.max(min, n))
export const noop = () => { }
export const rand = (min: number, max: number) => {
  min = Math.ceil(min)
  max = Math.floor(max)
  return Math.floor(Math.random() * (max - min + 1)) + min
}
export const hasOwn = <T extends object, K extends keyof T>(
  val: T,
  key: K,
): key is K => Object.prototype.hasOwnProperty.call(val, key)

export const isIOS = /* #__PURE__ */ getIsIOS()

function getIsIOS () {
  return (
    isClient &&
    /* #__PURE__ */ window.navigator.userAgent &&
    /* #__PURE__ */ /iP(ad|hone|od)/.test(
      /* #__PURE__ */ window.navigator.userAgent,
    )
  )
}
export function getArrayLast (list: any[]) {
  if (!list) { return {} }
  const len = list.length
  return list[len - 1]
}

// 检测是否在 UC 浏览器中
export const isUCBrowser = getBrowserType('UCBrowser')

// 检测是否在 QQ 浏览器中
export const isQQBrowser = getBrowserType('MQQBrowser')

export const isQQBrowserWebView = getBrowserType('QBWebViewType') || getBrowserType('MQQBrowser')

// 检测是否在搜狗浏览器中
export const isSogouBrowser = getBrowserType('SE ')
export const isDingTalk6_5 = getBrowserType('DingTalk/6.5')
export const isDingTalk = getBrowserType('DingTalk')

// 是否是夸克浏览器
export const isQuarkBrowser = getBrowserType('Quark')

// 检测是否在微信浏览器中
export const isWxBrowser = getBrowserType('MicroMessenger')

// 检测是否在vivo浏览器中
export const isVivoBrowser = getBrowserType('VivoBrowser')

function getBrowserType (ua: string) {
  return isClient && window.navigator.userAgent.includes(ua)
}

export function getDeviceOfUA (UA?: string) {
  const r = {
    isXcx: false,
    isKukeCloudAppWebview: false,
  }
  let ua = UA?.toLowerCase()
  if (process.client) {
    ua = window.navigator.userAgent.toLowerCase()
  }
  if (ua) {
    if (ua.includes('miniprogram')) {
      r.isXcx = true
    } else if (ua.includes('kukecloudappwebview')) {
      r.isKukeCloudAppWebview = true
    }
  }
  return r
}
export const isDdefine = (value) => {
  return value !== undefined && value !== null
}

/**
 * @description 是否是 iframe 的子项目
 */
export const isIframe = () => isClient && window.parent !== window.self
