import type {
  FeeFirstAddBodyType,
  FeeDelBodyType,
  FeeOtherAddBodyType,
  FeeEditBodyType,
  FeeSubmitBodyType,
  QuestionLogRemoveBodyType,
} from '../types'
import filterQuestionBankBody from '../../../../base/utils/filterBody'

enum Api {
  // 收费设置
  feeFirstAddProt = '/kukecorequestion/wap/fee/firstAddProt', // 初始化获取下单列表
  feeDetailProt = '/kukecorequestion/wap/fee/detailProt', // 通过记录id获取下单列表
  feeOtherAddProt = '/kukecorequestion/wap/fee/otherAddProt', // 非第一次添加商品计算价格
  feeDelProt = '/kukecorequestion/wap/fee/delProt', // 删除商品计算价格
  feeEditProt = '/kukecorequestion/wap/fee/editProt', // 编辑商品计算价格
  // 提交订单
  feeSubmitProt = '/kukeonlineorder/wap/order/addQuestionOrderProt',
}

/**
 * 第一次添加商品计算价格
 *
 * @param {FeeFirstAddBodyType} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function feeFirstAddProt (body: FeeFirstAddBodyType) {
  return useHttp<any>(Api.feeFirstAddProt, {
    method: 'post',
    body: filterQuestionBankBody(body),
    transform: res => res.data,
  })
}

/**
 * 费用详情
 *
 * @param {QuestionLogRemoveBodyType & { expiry?: number }} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function feeDetailProt (body: QuestionLogRemoveBodyType & { expiry?: number }) {
  return useHttp<any>(Api.feeDetailProt, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 删除
 *
 * @param {FeeDelBodyType} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function feeDelProt (body: FeeDelBodyType) {
  return useHttp<any>(Api.feeDelProt, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 非第一次添加商品计算价格
 *
 * @param {FeeOtherAddBodyType} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function feeOtherAddProt (body: FeeOtherAddBodyType) {
  return useHttp<any>(Api.feeOtherAddProt, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 编辑商品计算价格
 *
 * @param {FeeEditBodyType} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function feeEditProt (body: FeeEditBodyType) {
  return useHttp<any>(Api.feeEditProt, {
    method: 'post',
    body,
    transform: res => res.data,
  })
}

/**
 * 提交订单
 *
 * @param {FeeSubmitBodyType} body - 请求参数
 * @returns {Promise<any>} - 返回数据
 */
export async function feeSubmitProt (body: FeeSubmitBodyType) {
  return useHttp<any>(Api.feeSubmitProt, {
    method: 'post',
    body,
  })
}
