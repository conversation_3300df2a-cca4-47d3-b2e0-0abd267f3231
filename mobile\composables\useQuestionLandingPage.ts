interface GetQuestionLandingPageType {
  moduleManageId: string;
  testpaperId: string;
}

export const useQuestionLandingPage = () => {
  const { questionBankApi } = useApi()
  const { handleJumpPromotionPage } = useQuestionBankPromotion()

  const getQuestionLandingPageInfo = async (params: GetQuestionLandingPageType) => {
    try {
      const { data, error } = await questionBankApi.getLandingPageInfo(params)

      if (error.value) {
        throw new Error('Error fetching landing page info')
      }

      const { landPageState, doLandPage, landPage } = data.value
      if (!landPage) {
        return true
      }
      return handleJumpPromotionPage({
        landPageState,
        doLandPage,
        landLink: landPage.address,
      })
    } catch (err) {
      console.error('Failed to get landing page info', err)
    }
  }

  return {
    getQuestionLandingPageInfo,
  }
}
