<!-- @deprecated v2.1+ 该文件不再使用 -->
<template>
  <button
    :class="{
      'kkc-button': true,
      'kkc-button--colours': colours,
      'kkc-button--round': round,
      'kkc-button--disabled': disabled,
    }"
  >
    <slot />
    <!-- <span v-show="disabled" class="kkc-button--disabled-mask" /> -->
  </button>
</template>

<script setup>
defineProps({
  colours: {
    type: Boolean,
    default: true,
  },
  round: {
    type: Boolean,
    default: false,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
})
</script>
<style lang="scss">
.kkc-button {
  display: flex;
  align-items: center;
  justify-content: center;

  width: 100%;
  height: 44px;
  position: relative;
  font-size: 15px;
  letter-spacing: 1px;
  //

  &--colours {
    //
    @apply bg-brand;
    color: #fff;
    border: none;
  }
  &--round {
    border-radius: 22px;
  }
  &--disabled {
    //
    cursor: not-allowed;
    opacity: 0.6;
    //
  }
}
</style>
