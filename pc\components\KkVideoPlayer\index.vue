<template>
  <KKCOverlay v-if="showDialog">
    <div :class="[bem(), 'relative']">
      <KKCVideoPlayer
        ref="videoPlayerRef"
        width="1000px"
        height="570px"
        :node-id="nodeId"
        :preview="preview"
        :goods-master-id="goodsMasterId"
        :type="type"
        :play-by-video-id="playByVideoId"
        :live-org-id="liveOrgId"
        :try-listen-id="tryListenId"
        :teaser-show="teaserShow"
        :is-no-login-see-course-node="isNoLoginSeeCourseNode"
        @playOver="onPlayOver"
        @resumeVideo="onPlayResume"
        @pauseVideo="onPlayPause"
        @buy="onBuy"
      />
      <!--
        :get-node-id-info-fn="getNodeIdInfoFn" -->
      <KKCIcon name="icon-dankuang-guanbi" :size="40" class="absolute close" color="#fff" @click="handleCloseDialog" />
      <slot />
    </div>
  </KKCOverlay>
</template>

<script setup lang="ts">
import { getClassifiedInformation, getCCNodeInfoProt } from '../../../base/apis/video'
import type { KKCVideoPlayer } from '#components'
const bem = useBem('player-box')
const props = defineProps<{
  show: boolean;
  nodeId: string;
  goodsMasterId: string;
  preview?: boolean;
  type: number;
  teaserShow?: number;
  isNoLoginSeeCourseNode?: boolean;
  liveOrgId: string;
  tryListenId?: string
  playByVideoId?: string
}>()
//
// getNodeIdInfoFn?: Function
const emits = defineEmits<{
  (e: 'update:show', val: boolean): void;
  (e: 'playOver', val: string): void;
  (e: 'playResume', val: string): void;
  (e: 'playPause', val: string): void;
  (e: 'buy'): void;
}>()
const showDialog = ref(false)
// 方法
const videoPlayerRef = ref<InstanceType<typeof KKCVideoPlayer>>()

watch(
  () => props.show,
  async (val) => {
    // console.log((await checkVideo()), 'checkVideo()')

    if (!(await checkVideo())) {
      return false
    }
    showDialog.value = val
  }
)
watch(
  () => showDialog.value,
  (val) => {
    if (val) {
      window.addEventListener('wheel', handleStopWheel, {
        passive: false,
      })
    } else {
      window.removeEventListener('wheel', handleStopWheel)
    }
    showDialog.value = val
    emits('update:show', val)
  }
)
onUnmounted(() => {
  window.removeEventListener('wheel', handleStopWheel)
})
const kkIsAudition = useCookie<string>(IS_AUDITION)
const kkPoolId = useCookie<string>(POOL_ID)
const route = useRoute()
// 检测视频是否正常
const checkVideo = async () => {
  // if (props.playByVideoId) {
  //   return true
  // }
  if (route.name === 'learn-center-myclass') {
    return true
  }
  if (route.name === 'learn-center-data-package-id') {
    return true
  }
  if (props.isNoLoginSeeCourseNode) {
    return true
  }
  // nodeId 是必传字段
  if (props.nodeId) {
    const fetchUrl = props.type === 1 ? getClassifiedInformation : getCCNodeInfoProt
    // if (props.isNoLoginSeeCourseNode) {
    //   fetchUrl = props.type === 1 ? getClassifiedInformation : getCCNodeInfoProt
    // }
    const { error } = await fetchUrl({
      nodeId: props.nodeId,
      goodsMasterId: props.goodsMasterId as string,
      orgId: props.liveOrgId,
      tryListenId: props.tryListenId,
      // testParam: 1,
      poolId: Number(kkIsAudition.value) === 1 && kkPoolId.value ? kkPoolId.value : undefined,
      isCheck: 1 // 校验视频标识
    })
    console.log('checkVideo error.value?.data?.code:', error.value?.data?.code)
    if (error.value && error.value?.data?.code === '10741') {
      return false
    }
  }
  return true
}

const handleCloseDialog = () => {
  showDialog.value = false
  emits('update:show', false)
}

// 播放结束事件
const onPlayOver = (vid: string, courseNodeId: string) => {
  emits('playOver', vid, courseNodeId)
}
// 播放开始
const onPlayResume = (vid: string) => {
  emits('playResume', vid)
}
// 播放暂停
const onPlayPause = (vid: string) => {
  emits('playPause', vid)
}
// 购买弹窗
const onBuy = () => {
  emits('buy')
}

// 暂停
const onPauseVideo = (): void => {
  videoPlayerRef.value?.onPauseVideo()
}
// 播放
const onResumeVideo = (): void => {
  videoPlayerRef.value?.onResumeVideo()
}

defineExpose({
  onPauseVideo,
  onResumeVideo,
})

const handleStopWheel = (e: Event) => {
  e.preventDefault()
}
</script>

<style scoped lang="scss">
.player-box {
  @apply w-[1000px] h-[570px];

  .close {
    @apply right-[0px] top-[-48px] cursor-pointer;
  }
}
</style>
