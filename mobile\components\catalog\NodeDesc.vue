<template>
  <div>
    <!-- 直播 -->
    <span v-if="item.type === CourseType.Live" class="">
      <span class="mr-[32px]">
        <KKCIcon name="icon-xiangqing-shijian" :size="28" />
        {{ item.desc }}
      </span>
      <KKCIcon v-if="item.learnProgress === 100" name="icon-xiangqing-wancheng" :size="28" color="#00A492" />
      <template v-if="!isLock">
        <span v-if="item.videoDuration">
          {{ item.learnStatusName === '' ? '未开始' : item.learnStatusName }} {{ item.learnProgress + '%' }}
        </span>
      </template>
      <!-- <template v-else>
        <template v-if="item.learnProgress">
          <span v-if="item.videoDuration">
            {{ item.learnStatusName === '' ? '未开始' : item.learnStatusName }} {{ item.learnProgress + '%' }}
          </span>
        </template>
      </template> -->
    </span>

    <!-- 录播 -->
    <span v-if="item.type === CourseType.Record" :class="[{ 'learn-status': item.learnProgress === 100 }]">
      <span class="mr-[32px]">
        <KKCIcon name="icon-xiangqing-shijian" :size="28" />
        {{ item.desc }}
      </span>

      <KKCIcon v-if="item.learnProgress === 100" name="icon-xiangqing-wancheng" :size="28" color="#00A492" />
      <template v-if="!isLock">
        <span v-if="item.videoDuration">
          {{ item.learnStatusName === '' ? '未开始' : item.learnStatusName }} {{ item.learnProgress + '%' }}
        </span>
      </template>
    </span>

    <!-- 讲义 -->
    <span v-if="item.type === CourseType.Handout">
      格式： {{ item.fileType }}
    </span>

    <!-- 试题 -->
    <div v-if="item.type === CourseType.Question">
      <KKCIcon name="icon-deta_icon_shijian" :size="28" />
      <span class="mr-[32px]">
        题数：{{ item.itemCount }}道
      </span>
      <span>
        总分： {{ item.score }}分
      </span>
    </div>

    <!-- 音频 -->
    <span v-if="item.type === CourseType.Audio">
      <KKCIcon
        name="icon-deta_icon_wenjian"
        :size="32"
      />
      <span class="mr-[26px] ml-[8px]">格式：{{ capitalize(item.fileType) }}</span>
      <template v-if="!isLock">
        <span>
          {{ item.learnStatusName === '' ? '未开始' : item.learnStatusName }} {{ item.learnProgress + '%' }}
        </span>
      </template>
    </span>
  </div>
</template>

<script setup lang="ts">
import { CourseType } from './type'
import type { IItem } from './type'
defineProps<{
  item: IItem,
  isLock: boolean,
}>()
//
// const isFree = computed(() => {
//   // eslint-disable-next-line eqeqeq
//   return props.item.isFree == 1
// })
/** 首字母大写 */
function capitalize (str: string) {
  if (!str) { return '' }
  return str.charAt(0).toUpperCase() + str.slice(1)
}
</script>

<style scoped lang="scss">
.learn-status {
  color: #00A492
}
</style>
