<template>
  <div class="kuke99DefaultHome">
    <!-- 顶部导航 -->
    <HomeHeader />
    <!-- 轮播图 -->
    <section class="px-[24px] py-[16px]">
      <HomePublicAdBanner
        :advertisement-place-id="15"
        :list="adList"
      />
    </section>

    <!-- 资讯推荐 -->
    <!-- v-if="indexHotNewsList?.length" -->
    <HomeNewsBanner :list="indexHotNewsList" />
    <!-- <HomeNewsBanner :list="upgradedCollegesNewsList" /> -->
    <!-- 金刚区图标 -->
    <HomeIcons type="home" />
    <HomeBaoKao />
    <!-- 专升本 -->
    <section class="px-[24px] pt-[24px]">
      <HomeBooksTitle
        title="专升本"
        :icon-src="module4"
        class="mb-[24px]"
        more-link="/courseList?cateId=1&hasFree=1"
        more-link-text="查看全部"
      />
      <HomePublicAdZsb :advertisement-place-id="16" />
      <!-- {{ zsbOne }} -->
      <HomeCourseCard
        v-if="zsbOne.goodsMasterId || zsbOne.id"
        :good="zsbOne"
        class="mt-[24px] mb-[16px]"
      />
      <!-- \mobile\pages\activity\teamBuy\[id].vue -->
      <KKCollageGoodsWap
        class="homePinTuanCard"
        :group-goods="{...groupGoods,list:[groupOne],countdownShow:groupGoods.countdownShow,collageShow:groupOne.collageShow}"
      />
    </section>

    <!-- 专升本资讯 -->
    <section class="px-[24px] py-[24px]">
      <HomeNews
        title="专升本资讯"
        more-link="https://m.kuke99.com/news/category-list/1"
        :news-list="upgradedCollegesNewsList"
        :category-links="zsbNewsCategoryLinks"
      />
    </section>
    <!-- 公考 -->
    <section class="px-[24px] pt-[24px]">
      <HomeBooksTitle
        title="公考"
        :icon-src="module1"
        class="mb-[24px]"
        more-link="/courseList?cateId=3&hasFree=1"
        more-link-text="查看全部"
      />
      <HomePublicAdGongKao :advertisement-place-id="16" />
      <div class="home-gongkao">
        <HomeCourseCard
          v-if="jiaoShiZiGeOne.goodsMasterId"
          :good="jiaoShiZiGeOne"
          class="CourseCard__no-1"
        />
        <!-- type="xueLiTiSheng" -->
        <HomeCourseCard
          v-if="gongWuYuanOne.goodsMasterId"
          class="CourseCard__no-2"
          :good="gongWuYuanOne"
        />
      </div>
    </section>
    <!-- 公考资讯 -->
    <section class="px-[24px] pt-[24px]">
      <HomeNewsGongkao
        title="公考"
        :news-list="gongKaoNewsList"
        @change="changeHomeNewsGongkao"
      />
      <!-- more-link="https://m.kuke99.com/news/category-list/3" -->
      <!-- :category-links="gongKaoNewsCategoryLinks" -->
    </section>
    <!-- 学历提升 -->
    <section class="px-[24px] pt-[48px]">
      <HomeBooksTitle
        title="学历提升"
        :icon-src="module3"
        class="mb-[24px]"
        more-link="https://m.kuke99.com/courseList?cateId=1002&hasFree=1"
        more-link-text="查看全部"
      />
      <div
        v-if="xueLiTiShengList.length"
        class="home-xuelitisheng"
      >
        <HomeCourseCard
          v-for="item in xueLiTiShengList"
          :key="item.goodsMasterId"
          type="xueLiTiSheng"
          :good="item"
        />
      </div>
      <KKCEmpty v-else />
    </section>
    <!-- 题库资料 -->
    <HomeTiKuMaterial />

    <!-- 精品图书 -->
    <HomeBooks />
    <section class="bg-white pt-[48px] mt-[24px]">
      <!-- 功勋教练团队 保驾护航 -->
      <img
        src="@/assets/images/home/<USER>"
        loading="lazy"
        class="h-[43px]"
        height="43"
        alt="功勋教练团队 保驾护航"
      >
      <HomeTeacher />
    </section>
    <HomeBeiKao />
    <ClientOnly>
      <HomeFloatingWidget class="break-all" />
    </ClientOnly>
  </div>
</template>

<script lang="ts" setup>
import { getAdvertisementPhotoList } from '~/apis/home'
//
import module1 from '@/assets/images/home/<USER>'
// import module2 from '@/assets/images/home/<USER>'
import module3 from '@/assets/images/home/<USER>'
import module4 from '@/assets/images/home/<USER>'
import {
  getGroupGoodsList,
  hotGoodsList,
  experienceListByHome,
  getAllCoursePage,
} from '~/apis/course'
import {
  indexClassifyNewsNews,
  indexHotNews,
} from '~/apis/news'
// import pageImg from '@/assets/images/home/<USER>'
const { productSide } = useGetProductSide()
const adList = ref([])
/**
 * 首页顶部轮播图
 * 15
 */
const fetchAdList = async () => {
  const body = {
    /**
     * 首页顶部轮播图
      15
      尺寸待定
      同一终端/考试目标下取排序值最大的前10个启用广告，排序值相同按创建时间倒序
     *
     */
    advertisementPlaceId: 15,
    // cateId: 1,
    productSide: unref(productSide),
  }
  //
  const { data, error } = await getAdvertisementPhotoList(body)
  console.log('fetchAdList', data.value, error.value, body)
  const _list = data.value?.list || []
  adList.value = _list.map((item:any) => {
    if (!item.img.includes('?')) {
      // 如果图片URL已经是完整的，则不需要修改
      item.img = item.img + '?x-oss-process=image/format,webp'
    }
    return item
  })
}
// zsb 0元课程一仅包含课程，展示第一个专升本课程
const zsbOne = ref({})
const fetchZsbOne = async () => {
  const body = {
    cateId: 1,
    // minGoodsPresentPrice: 0,
    // getNum: 1,
    // getMode: 4,
    page: 1,
    pageSize: 1,
    startCreatedAt: getLastYearDate(),
    // startCreatedAt: '2025-08-26 11:50:45',
  }
  // const { data, error } = await hotGoodsList(body)
  const { data, error } = await experienceListByHome(body)
  console.log('/hotGoodsList', body, data.value, error.value)
  const _list = data.value?.list || []
  zsbOne.value = _list[0] || {}
}
const groupOne = ref({})
// 团购配置
const groupGoods = ref({
  // 拼团配置
  choiceType: 1,
  // 样式配置-首页列表商品数量
  style: 1,
  homePageShowNum: -1,
  // 样式配置-商品样式
  costShow: 1,
  collageShow: 1,
  countdownShow: 1,
  goodsDisplayType: 1,
  // 标题配置
  title: '拼团',
  inletContent: '查看更多',
  seeMoreType: 1,
  showTitle: 0,
  seeMore: 0,
  seeUrl: '',
  titleIcon: '',
  limitDay: 7,
  url: '',
  appid: '',
  pageUrl: '',
  giftPackageId: '',
  giftPackageName: '',
  receiveInlet: 1,
  loginInlet: 1,
  dicSkipId: undefined,
  dicFunctionId: undefined,
  // 商品列表
  list: [],
})
const fetchGroupOne = async () => {
  const body = {
    productId: 1,
    page: 1,
    pageSize: 1,
    cateId: 1,
    // startCreatedAt: getLastYearDate(),
    queryType: 2,
  }
  const { data, error } = await getGroupGoodsList(body)
  console.log('fetchGroupOne', data.value, error.value)
  const _list = data.value?.goods || []
  groupOne.value = _list[0] || {}
}
const getLastYearDate = () => {
  const startDate = new Date()
  startDate.setFullYear(startDate.getFullYear() - 1)
  const Y = startDate.getFullYear()
  const M = startDate.getMonth() + 1
  const D = startDate.getDate()
  // return startDate.toLocaleString().replaceAll('/', '-')
  // return startDate.toLocaleDateString().replaceAll('/', '-') + ' 00:00:00'
  return Y + '-' + M + '-' + D + ' 00:00:00'
}
// 教师资格
const jiaoShiZiGeOne = ref({})
const fetchJiaoShiZiGeOne = async () => {
  const body = {
    minGoodsPresentPrice: 99,
    getNum: 1,
    getMode: 1,
    cateId: 2,
    // startCreatedAt: getLastYearDate(),
  }
  const { data, error } = await hotGoodsList(body)
  console.log('fetchJiaoShiZiGeOne', data.value, error.value)
  const _list = data.value?.list || []
  jiaoShiZiGeOne.value = _list[0] || {}
}
// 公务员
const gongWuYuanOne = ref({})
const fetchGongWuYuanOne = async () => {
  const body = {
    minGoodsPresentPrice: 59,
    getNum: 1,
    getMode: 1,
    cateId: 17,
    // startCreatedAt: getLastYearDate(),

  }
  //
  const { data, error } = await hotGoodsList(body)
  console.log('fetchGongWuYuanOne', data.value, error.value)
  const _list = data.value?.list || []
  gongWuYuanOne.value = _list[0] || {}
}
// 公考资讯
const gongKaoNewsList = ref([])
const fetchGongKaoNewsList = async (cateId = 3) => {
  const body = {
    //
    cateId,
  }
  //
  const { data, error } = await indexClassifyNewsNews(body)
  console.log('fetchGongKaoNewsList', data.value, error.value)
  gongKaoNewsList.value = data.value?.list || []
}
const changeHomeNewsGongkao = (activeId) => {
  fetchGongKaoNewsList(activeId)
}
// zsb资讯
const zsbNewsCategoryLinks = ref([
  { text: '考试政策', url: 'https://m.kuke99.com/news/category-list/1?newsClassifyId=924525691275071488' },
  { text: '考试科目', url: 'https://m.kuke99.com/news/category-list/1?newsClassifyId=36_wx' },
  { text: '分数线', url: 'https://m.kuke99.com/news/category-list/1?newsClassifyId=924525807197245440' },
  { text: '招生计划', url: 'https://m.kuke99.com/news/category-list/1?newsClassifyId=924525724871446528' },
  { text: '考试动态', url: 'https://m.kuke99.com/news/category-list/1?newsClassifyId=907865556081487872_wx' },
  { text: '考试时间', url: 'https://m.kuke99.com/news/category-list/1?newsClassifyId=33_wx' },
  { text: '备考经验', url: 'https://m.kuke99.com/news/category-list/1?newsClassifyId=967993044287762432' },
  { text: '考试真题', url: 'https://m.kuke99.com/news/category-list/1?newsClassifyId=37_wx' },

])
const indexHotNewsList = ref([])
const upgradedCollegesNewsList = ref([])
const _indexHotNews = async () => {
  const body = {
    //
    cateId: 1,
  }
  //
  const { data, error } = await indexHotNews(body)
  const { indexHotNews: indexHotNewsList2, upgradedCollegesNews } = data.value || {}
  indexHotNewsList.value = indexHotNewsList2 || []
  upgradedCollegesNewsList.value = upgradedCollegesNews || []
  console.log('_indexHotNews', data.value, error.value, { indexHotNewsList2, upgradedCollegesNews })
}
// 学历提升
const xueLiTiShengList = ref([])
const fetchXueLiTiShengOne = async () => {
  const body = {
    cateId: 1002, // 学历提升的id是否是1002
    // minGoodsPresentPrice: 0,
    // getNum: 2,
    // getMode: 4,
    page: 1,
    pageSize: 2,
    hasFree: 1,
    // startCreatedAt: getLastYearDate(),

  }
  //
  // const { data, error } = await hotGoodsList(body)
  const { data, error } = await getAllCoursePage(body)
  console.log('getAllCoursePage', data.value, error.value)
  const _list = data.value?.list || []
  xueLiTiShengList.value = _list
}
const list = [
  fetchAdList(),
  fetchZsbOne(),
  fetchGroupOne(),
  fetchJiaoShiZiGeOne(),
  fetchGongWuYuanOne(),
  fetchGongKaoNewsList(),
  fetchXueLiTiShengOne(),
  _indexHotNews(),
]
await Promise.all(list)
// const pageStyle = computed(() => {
//   const result: Record<string, string> = {

//   }

//   result.backgroundImage = `url(${pageImg})`

//   return result
// })

</script>

<style lang="scss">
.home-gongkao {
    margin-top: 16px;
    .CourseCard:first-child {
      margin-bottom: 16px;
    }
  //
}
.home-xuelitisheng {
    // margin-top: 16px;
    .CourseCard:first-child {
      margin-bottom: 16px;
    }
  //
}
.kuke99DefaultHome{
  background-image: url('@/assets/images/home/<USER>');
  background-repeat: no-repeat;
  background-size: contain;
  background-position: top center;
}
.homePinTuanCard {
  margin: 0 !important;
  .invalidity-item{
    z-index: 11;
  }
}

</style>
