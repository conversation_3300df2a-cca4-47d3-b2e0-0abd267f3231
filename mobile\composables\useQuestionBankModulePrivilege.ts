import type { GetModuleManagePrivilegeType } from '~/apis/question-bank/types'
export const useQuestionBankModulePrivilege = () => {
  const { questionBankApi } = useApi()

  const modulePrivilege = ref({
    rightType: 0,
    moduleType: 0,
    moduleManageId: ''
  })

  // 获取模块标签是否有权益
  const getModulePrivilegeAsync = async (body: GetModuleManagePrivilegeType) => {
    try {
      // 获取模块标签是否有权益
      const { data, error } = await questionBankApi.getModulePrivilege(body)
      if (!error.value) {
        modulePrivilege.value = data.value
      }
    } catch (error) {
      console.error(error)
    }
  }

  return {
    modulePrivilege,
    getModulePrivilegeAsync
  }
}
