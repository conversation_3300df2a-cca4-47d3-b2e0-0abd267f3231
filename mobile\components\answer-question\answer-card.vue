<template>
  <div class="answer-card-wrap">
    <template v-if="answerList?.length">
      <!-- tabs -->
      <QuestionTabs :tabs="tabs" @change="handleChange" />

      <!-- 题目答案 start -->
      <div class="answer-card-main">
        <!-- 题目类型  -->
        <QuestionType :catagory="answerItem?.ptypeTitle" />

        <div class="answer-card-content">
          <!-- 题干 -->
          <QuestionStem :stem="answerItem?.stem" :stem-src="answerItem?.stemSrc" />
          <!-- 材料题-子题 -->
          <div v-if="isQuestionCL">
            <template v-for="(item,index) in answerItem.childQuestions" :key="index">
              <QuestionStem :stem="`<div class='flex'>${index+1}、${item?.stem}</div>`" :stem-src="item?.stemSrc" />
              <QuestionOptions :options="(item?.metas as string)" />
            </template>
          </div>
          <!-- 选项 -->
          <QuestionOptions :options="answerItem?.metas" />
          <!-- 知识点 -->
          <QuestionPoint v-if="answerItem?.knowledgePointList?.length" :point="answerItem?.knowledgePointList" />

          <!-- 答案 -->
          <QuestionAnswer v-if="!isQuestionCL" :size="32" :answer="answerItem?.answer" :ptype-value-id="answerItem?.ptypeValueId" />

          <!-- 解析 -->
          <QuestionAnalysis v-if="!isQuestionCL && answerItem?.analysis" :analysis="answerItem?.analysis" :stem-src="answerItem?.analysisSrc" />

          <!-- 视频解析 -->
          <div v-if="answerItem?.videoId">
            <div class="text-[24px] w-[112px] text-left">
              视频解析
            </div>
            <QuestionBankVideo :polyv-ref="'polyvRef'+ answerItem?.videoId" :type="'answer'" :question-id="answerItem?.id" />
          </div>

          <!-- 材料题-子题 -->
          <div v-if="isQuestionCL" class="detail-content">
            <template v-for="(item,index) in answerItem.childQuestions" :key="index">
              <p>子题{{ index+1 }}</p>
              <QuestionAnswer :size="32" :show-label="true" :answer="(item?.answer as string)" :ptype-value-id="answerItem?.ptypeValueId" />
              <QuestionAnalysis :size="32" :show-label="false" :analysis="item?.analysis&&`<div>解析：${item?.analysis}</div>`" :stem-src="item?.analysisSrc" />

              <p v-if="item?.knowledgePointList.length" class="text-[14px] m-[0]">
                知识点：
              </p>
              <QuestionPoint :size="32" :show-label="false" :point="(item?.knowledgePointList as string[])" />
            </template>
          </div>
        </div>
      </div>
    </template>
    <template v-else>
      <div class="ai-no-data">
        <img :src="AIEmpty" alt="">
        无拍搜结果～
      </div>
    </template>

    <!-- 题目答案 end -->
  </div>
</template>
<script lang="ts" setup>

import { extractedAudio } from '../../../base/utils/audio'

import { QuestionTabs, QuestionType, QuestionStem, QuestionOptions, QuestionPoint, QuestionAnswer, QuestionAnalysis } from './components'
import AIEmpty from '~/assets/images/answer/ai-no.png'

const { answerApi } = useApi()

const props = defineProps<{
    tabs: string[]
}>()

const activeIndex = ref(0)
const answerList = ref<any[]>()
const answerItem = computed(() => {
  return answerList.value?.[activeIndex.value]
})
// 是否是材料题
const isQuestionCL = computed(() => {
  return answerItem.value?.childQuestions.length > 0
})
// const emits = defineEmits<{
//     (e: 'change', id: string): void
// }>()

/**
 * 切换tabs
 * @param id
 */

const handleChange = (val:string, index: number) => {
  // emits('change', val)
  console.log(val)

  activeIndex.value = index
}
/**
 * 获取题库试题信息
 */
const getAnswerList = async () => {
  const { data: dataList, error } = await answerApi.fetchQKById({
    questionIds: props.tabs
  })

  if (!error.value) {
    answerList.value = extractedAudio(JSON.parse(JSON.stringify(dataList.value.list)))
  }
}

onMounted(async () => {
  await getAnswerList()
})
</script>
<style lang="scss" scoped>

.answer-card-wrap{
    max-width: 478px;
    padding: 16px;
    // background: #FFFFFF;

    .answer-card-main{
        background: #F7F7F7;
        border-radius: 8px 8px 24px 24px;
        border: 2px solid #E5E7EB;

    }
    .answer-card-content{
      padding: 24px
    }
    .ai-no-data{
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 270px;
      height: 180px;
      background: rgba(255,255,255,0.5);
      border-radius: 8px 8px 8px 8px;
      font-size: 21px;
      color: #999999;

      img{
        width: 120px;
        height: 95px;
        margin-bottom: 16px;
      }
    }
}
</style>
