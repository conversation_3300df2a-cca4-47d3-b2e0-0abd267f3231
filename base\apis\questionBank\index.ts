import type { GoodsPaperStatusParams, GoodsPaperStatusResult } from './type'
// TODO 删除 试卷状态校验接口弃用
export enum ApisQuestionBank {
  // 校验商品试卷状态
  checkGoodsPaperStatus = '/kukecorequestion/outer/goodsCenter/checkDoTestpaperProt',
}

// 校验商品试卷状态
export async function checkGoodsPaperStatus (body: GoodsPaperStatusParams) {
  return useHttp<GoodsPaperStatusResult>(ApisQuestionBank.checkGoodsPaperStatus, {
    method: 'post',
    body,
    transform: res => res.data,
    isShowLoading: true
  })
}
