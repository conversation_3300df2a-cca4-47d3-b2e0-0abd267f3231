import { useQuestionBankStore } from '~/stores/question.bank.store'
import { useUserStore } from '~/stores/user.store'

interface ShareInfoType {
  title?: string,
  desc?: string,
  img?: string,
  url?: string
}

export const useSetQuestionBankShare = async (paramsData?: ShareInfoType) => {
  const questionBankStore = useQuestionBankStore()
  const { setShareInfo } = useWxShare()
  const { isXcx, logoImg } = useAppConfig()
  const userStore = useUserStore()
  // 获取推广设置
  const { data } = await userStore.wxGetShareDataInfo()
  // 分享以及SEO信息
  const shareInfo = {
    title: paramsData?.title?.trim() || questionBankStore.state.moduleName || '题库',
    desc: paramsData?.desc,
    img: paramsData?.img !== 'undefined' ? paramsData?.img : (data.value.shareImg || logoImg),
    url: paramsData?.url
  }
  // seo设置（题库不需要）
  const setQuestionBankHead = () => {
    useHead({
      meta: [
        {
          name: 'keywords',
          content: shareInfo.title
        },
        {
          name: 'description',
          content: shareInfo.desc
        }
      ]
    })
  }
  // 微信分享
  const setQuestionBankShareInfo = (option?: ShareInfoType) => {
    const { title = '', desc, img = '', url } = option || shareInfo
    const path = window.location.href
    if (isXcx) {
      userStore.wxMiniProgramPostShareData('', title, img, url || path)
    } else {
      setShareInfo({
        title,
        desc,
        shareImg: img,
        url: url || path
      }, false)
    }
  }

  return {
    setQuestionBankHead,
    setQuestionBankShareInfo
  }
}
