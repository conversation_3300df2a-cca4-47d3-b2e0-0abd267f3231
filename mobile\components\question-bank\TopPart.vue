<template>
  <div
    v-if="isShowPartDesc"
    :class="['h-full', 'relative', 'bg-[#ffffff]', 'part-wapper', detail.description ? 'pt-[100px]' : 'pt-[250px]']"
  >
    <div class="part-title w-[694px] mx-[28px] flex relative justify-center align-center z-10">
      <span class="line-clamp-2 text-[#111111] font-[600] text-[56px] break-words">
        {{ detail.part }}
      </span>
    </div>
    <div
      v-if="detail.partPrev"
      class="flex justify-center relative truncate w-[694px] m-auto text-[28px] text-[#666666] font-[400] z-10"
    >
      上一部分：{{ detail.partPrev }}
    </div>
    <div
      v-if="detail.description"
      class="relative w-[694px] h-[820px] px-[28px] pt-[40px] mt-[100px] mx-auto z-10 overflow-y-auto break-all rounded-[12px] text-[28px] text-[#333333] leading-[42px] font-[400] part-content"
    >
      {{ detail.description }}
    </div>
    <Teleport to="body">
      <div
        v-if="isShowPartDesc"
        class="fixed flex w-[750px] left-0 bottom-0 px-[24px] z-[99] bg-[#ffffff] py-[12px] mt-[63px] justify-between part-footer"
      >
        <div
          :class="['part-footer-left', detail.partPrev ? 'part-footer-active' : 'part-footer-disabled']"
          @click="next(-1)"
        >
          上一部分
        </div>
        <div
          :class="['part-footer-right', detail.partNext ? 'part-footer-active' : 'part-footer-disabled']"
          @click="next(1)"
        >
          下一部分
        </div>
      </div>
    </Teleport>
  </div>
</template>

<script setup lang="ts">
interface Props {
  detail: {
    part?: string;
    partPrev?: string;
    description?: string;
    partNext?: string;
    index?: number;
  }; // 详情
  isShowPartDesc?: boolean; // 是否显示部分页脚
}
const props = withDefaults(defineProps<Props>(), {})
const emits = defineEmits(['next'])

const next = (val: number) => {
  const { partPrev, partNext, index } = props.detail // 获取当前部分数据
  if (val === -1 && !partPrev) {
    Message('暂无上一部分数据')
    return false
  } else if (val === 1 && !partNext) {
    Message('暂无下一部分数据')
    return false
  }
  const swipeToPartIndex = (index || 0) + val
  emits('next', swipeToPartIndex)
}
</script>

<style lang="scss" scoped>
.part {
  &-wapper {
    background: linear-gradient(135deg, #DFEFFF 0%, #EFEBFF 32%, #FFFFFF 74%);

    &::before {
      @apply absolute block top-0 left-0 w-full h-[520px] z-[1];
      content: '';
      // TODO 迁移 public
      background-image: url(https://oss.kuke99.com/img/question/uniapp/20230517/part_top_bg.png);
      background-repeat: no-repeat;
      background-size: 100%;
    }

    &::after {
      @apply absolute block bottom-[80px] left-0 w-full h-[489px] z-[1];
      content: '';
      // TODO 迁移 public
      background-image: url(https://oss.kuke99.com/img/question/uniapp/20230517/part_bottom_bg.png);
      background-repeat: no-repeat;
      background-size: 100%;
    }
  }

  &-content {
    background: linear-gradient(180deg, #F0E7FF 0%, rgba(255, 255, 255, 0.64) 100%);
    border: 2px solid #ffffff;
  }

  &-footer {
    padding-bottom: calc(constant(safe-area-inset-bottom) + 12px);
    /*兼容 IOS<11.2*/
    padding-bottom: calc(env(safe-area-inset-bottom) + 12px);

    /*兼容 IOS>11.2*/
    &-left,
    &-right {
      @apply w-[341px] h-[88px] leading-[88px] rounded-[16px] text-center text-[28px] font-[500]
    }

    &-active {
      @apply text-[#ffffff] bg-brand
    }

    &-disabled {
      @apply text-[#999999] bg-[#f2f2f2]
    }
  }
}
</style>
