<template>
  <ul class="kkc-catalog">
    <!-- 学习详情页 tree -->
    <li
      v-for="(item,index) in tree"
      :key="item.id"
      :data-level="item.level"
      :class="['kkc-catalog-node', `node-${item.nodeType || item._nodeType}`, isOpenStyle(item)]"
    >
      <div class="title">
        <template v-if="item.level === 1">
          <KKCollapseChapter
            :item="item"
            :is-open="isOpen(item)"
            :select="select"
            @action="handleAction(item)"
          />
        </template>

        <template v-else-if="item.level === 2">
          <KKCollapseNode
            :item="item"
            mode="section"
            :is-open="isOpen(item)"
            :select="select"
            :live-type="liveType"
            :is-all-free="isAllFree"
            @action="handleAction(item)"
          />
        </template>
        <template v-else-if="item.level === 3">
          <KKCollapseNode
            :item="item"
            mode="class-hour"
            :tree="tree"
            :select="select"
            :select-node-id="selectNodeLevelThreeId"
            :live-type="liveType"
            :is-all-free="isAllFree"
            @action="handleAction(item)"
          />
        </template>
        <template v-else-if="item._nodeType === 'JIE_DUAN'">
          <StudyPlanJieDuanTitle
            :item="item"
            :index="index"
            :is-open="isOpen(item)"
            @action="handleAction(item)"
            @updateOpenPaths="updateOpenPaths"
          />
          <!-- @studyRequirement="handleStudyRequirement(item)" -->
        </template>
        <!-- <template v-else-if="item._nodeType === 'nodeNum'">
           <StudyPlanTitle
             type="nodeNum"
             title="学习课时"
             :item="item"
             :has-child="item.nodeNum > 0"
             :count-name="`共${item.nodeNum}个课时`"
             :is-open="isOpen(item)"
             @action="handleAction(item)"
           />
         </template>
         <template v-else-if="item._nodeType === 'fileNum'">
           <StudyPlanTitle
             v-if="item.fileNum > 0"
             type="fileNum"
             title="学习资料"
             :item="item"
             :has-child="true"
             :count-name="`共${item.fileNum}个资料`"
             :is-open="isOpen(item)"
             @action="handleAction(item)"
           />
         </template>
         <template v-else-if="item._nodeType === 'homeworkNum'">
           <StudyPlanTitle
             v-if="item.homeworkNum > 0"
             type="homeworkNum"
             title="作业"
             :item="item"
             :has-child="true"
             :count-name="`共${item.homeworkNum}个作业`"
             :is-open="isOpen(item)"
             @action="handleAction(item)"
           />
        </template> -->
        <template v-else-if="item._nodeType === 'file'">
          <StudyPlanFiles
            :item="item"
            :live-type="liveType"
            :is-all-free="isAllFree"
            @action="handleAction(item)"
          />
        </template>
        <template v-else-if="item._nodeType === 'homework'">
          <StudyPlanHomework
            :item="item"
            :live-type="liveType"
            :is-all-free="isAllFree"
            @action="handleAction(item)"
            @homeworkSubmit="handleHomeworkSubmit(item)"
          />
        </template>
        <template v-else>
          <!-- {{ item.numName }}： -->
          {{ item.name }}
        </template>
      </div>
      <template v-if="item.children && item.children.length">
        <!-- {{ courseDetail }} -->
        <KKCollapseTree
          :tree="item.children"
          :open-paths="openPaths"
          :course-detail="courseDetail"
          :select="select"
          :live-type="liveType"
          :node-id="nodeId"
          :is-all-free="isAllFree"
          @homeworkSubmit="handleHomeworkSubmit"
          @action="handleAction"
          @updateOpenPaths="updateOpenPaths"
        />
      </template>
    </li>
  </ul>
</template>

<script setup lang="ts">
import KKCollapseTree from './tree.vue'
import KKCollapseNode from './node.vue'
import KKCollapseChapter from './chapter.vue'
import StudyPlanJieDuanTitle from './StudyPlanJieDuanTitle.vue'
// import StudyPlanTitle from './StudyPlanTitle.vue'
import StudyPlanFiles from './StudyPlanFiles.vue'
import StudyPlanHomework from './StudyPlanHomework.vue'
//
import type { IItem } from './type'
const props = defineProps<{
  tree: IItem[];
  courseDetail: any;
  openPaths: Array<string>;
  select: any;
  liveType?: number;
  isAllFree?: number;
  nodeId?:string
}>()
const emits = defineEmits<{
  (e: 'action', event: IItem): void;
  (e: 'update:openPaths', event: string[]): void;
  (e: 'homeworkSubmit', event: IItem): void;
  (e: 'updateOpenPaths', list: string[]): void;
}>()

const selectNodeLevelThreeId = ref('')
const openPath = ref<string[]>()

watch(() => props.nodeId, (nv) => {
  selectNodeLevelThreeId.value = nv
})

watch(() => props.openPaths, (v) => {
  openPath.value = v.length ? v : ['1', '1-1']
}, { deep: true, immediate: true })

const handleAction = (item: IItem) => {
  // selectNodeLevelThreeId.value = ''
  // if (item.level === 3) {
  //   selectNodeLevelThreeId.value = item?.ccVideoId || item?.polyvVideoId || ''
  // }
  const { children } = item
  if (children && children.length) {
    openPath.value = isOpen(item)
      ? openPath.value.filter(path => path !== item.path)
      : [...openPath.value, item.path]
  }
  emits('action', item)
}

const isOpen = (item: IItem) => {
  return openPath.value.includes(item.path)
}
const updateOpenPaths = (paths: string[]) => {
  emits('updateOpenPaths', paths)
}
const isOpenStyle = computed(() => {
  return (item: IItem) => {
    if (openPath.value.includes(item.path)) {
      return 'is-open'
    } else {
      return 'is-hidden'
    }
  }
})
const handleHomeworkSubmit = (item: IItem) => {
  emits('homeworkSubmit', item)
}
</script>
<style lang="scss" scoped>
.kkc-catalog {
  .kkc-catalog-node {
    &.node-class-hour:last-child {
      .catalog-node.class-hour {
        :deep(.catalog-node__line::before) {
          display: none;
        }
      }
    }

    &.node-chapter {
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: initial;
      }
    }

    &.is-open {
      display: block;
    }

    &.is-hidden {
      .kkc-catalog {
        display: none;
      }
    }
  }
  :deep(.kkc-catalog-node.node-homework:last-of-type) {
    .homework-item-line{
      &::before{
        display: none;
      }
    }
  }
  :deep(.kkc-catalog-node.node-file:last-of-type) {
    .plan-files-item-line{
      &::before{
        display: none;
      }
    }
  }
}
</style>
<style>
.animate-180 {
  transition: transform 0.3s ease-in-out;
}

.rotate-180 {
  transform: rotate(180deg);
}
</style>
