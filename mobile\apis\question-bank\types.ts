/**
 * 模块列表查询参数
 */
export interface QueryModuleListParams {
  studyLevel?: number,
  level?: number
}

export interface TargetResultId {
  targetResultId: string,
  goodsMasterId?: string,
  questionType?: number,
  activityId?: string
}

/**
 * 模块实体
 */
export interface ModuleEntity {
  id: string,
  title: string, // 模块名称
  moduleType?: number // 模块类型
  url?: string, // 图标url
  activeUrl?: string, // 激活状态图标url
  examMode?: number
}

/**
 * 专业信息
 */
export interface StudyLevel {
  studyLevel1: number,
  studyLevel2?: number,
  studyLevel3?: number,
  studyLevel4?: number,
}

export interface QueryModuleCategoryListParams extends StudyLevel {
  moduleId: string,
}

/**
 * 修改专业信息参数
 */
export interface UpdateMajorInfoParams {
  studyLevel: StudyLevel,
}

/**
 * 模块列表
 */
export interface ModuleList {
  examMode: number
  [x: string]: number
  list: ModuleEntity[]
}
export interface StartProtParams {
  [key: string]: any
  testpaperId: string
  moduleManageId: string
  getUserData: number
  subjectType?: number | string
  region?: number | string
  directionType?: number | string
  academicSection?: number | string
  examFormat?: number | string
}

/*
  试卷，开始做题
*/
export interface StartType {
  testpaperId: string
  moduleManageId: string,
  studyLevel1: number
  subjectType: number,
  region: number,
  directionType: number,
  academicSection: number,
  examFormat: number,
  goodsMasterId?: string,
  questionType?: number,
}

/**
 * 试卷详情
 */
export interface PaperDetailType extends StartType {
  withTag: 1 | 0
  purchased?: 1 | 2
}

/*
  试卷，一题目一提交
*/
export interface submitQuestType extends TargetResultId {
  questionId: string,
  state: number,
  answer: string
  isAnalyze?: number,
  score?: number,
  ptypeValueId: number
}
/*
试卷，完成提交
*/
export interface submitFinishType extends TargetResultId {
  usedTime?: number,
  startTime?: string
}
/*
  试卷，中途提交
*/
export interface submitLeaveType extends submitFinishType {
  doCount: number,
}
/*
  每日一练，章节练习  中途提交
*/
export interface leaveSubmitQuestType {
  targetResultId: string,
  doCount: number,
  usedTime?: number,
  startTime?: string
}

/*
  章节练习  中途/完成提交
*/
export interface chapterSubmitQuestType {
  targetResultId: string,
  usedTime: number,
  questionId?:string,
  doAllCount: number,
  doObjectCount: number,
  rightCount: number
}

/*
  每日一练，章节练习  完成提交
*/
export interface finishSubmitQuestType {
  targetResultId: string,
  usedTime: number
}

/*
  章节练习， 开启下一轮
*/
export interface DoNextRoundProtBodyType extends TargetResultId {
  textbookId: string
}

/**
  排行榜
*/
export enum RankProtBodyTypeEnum {
  Day = 1,
  Week = 2,
  Month = 3,
  Total = 4,
}

export interface RankProtBodyType {
  type: RankProtBodyTypeEnum
}

export interface QuestionClassAndTagType {
  studyLevel1: number,
  studyLevel2?: number,
  studyLevel3?: number,
  studyLevel4?: number,
  academicSection: number,
  examFormat: number,
  region: number,
  subjectType: number,
  directionType: number
}

export interface QuestionPageType {
  page: number,
  pageSize: number,
}

export interface QuestionCountForQuestionType extends QuestionClassAndTagType {
  logType: number,
}

export interface QuestionAddUserCollectBodyType extends QuestionClassAndTagType {
  questionId: string,
  parentId: string,
  ptypeValueId: string,
  state: number,
  addAnswer: string,
  childlist?: [],
  moduleType: number,
  targetResultId?: string,
  isDetails: 0 | 1
}

export interface QuestionAddUserCollectV2BodyType extends QuestionClassAndTagType {
  questionId: string,
  parentId: string,
  ptypeValueId: string,
  state: number,
  answer: string,
}

export interface QuestionDeleteUserCollectBodyType {
  id: string
}

export interface QuestionErrorBaseType {
  settingType: 2,
  title?: string,
  state: 1,
}

export interface QuestionErrorSubmitType {
  questionId: string,
  feedbackType: number[],
  sourceType: 1,
  feedbackMsg: string
}

export interface QuestionLogPageBodyType extends QuestionClassAndTagType {
  sort: number,
  ptypeValueId: number,
  page: number,
  pageSize: number,
}

export interface QuestionLogLeaveBodyType {
  startTimeInt: string,
}

export interface QuestionLogSubmitBodyType {
  bookId: string,
  state: string,
  answer: string,
  questionId: string,
  ptypeValueId: number
}

export interface QuestionLogRemoveBodyType {
  id: string
}

export interface DoQuestionListBodyType extends QuestionClassAndTagType {
  page: number,
  pageSize: number,
  moduleManageId: string,
  [key: string]: any
}

export interface DoQuestionProtBodyType {
  id: string,
  moduleManageId: string,
}

export interface SaveUserCategoryModuleTagBodyType extends QuestionClassAndTagType {
  moduleManageId: string,
  textbookId?: string,
  questionType?: number,
  moduleType?: number
}

export interface GetModuleStudyLevelCategoryTagsListBodyType {
  categoryUsedId: string,
  fields?: string[]
}

export interface GetModuleManageTagsListBodyType {
  studyLevel1: number,
  moduleManageId: string,
  subjectType: number | null,
  region: number | null,
  directionType: number | null,
  academicSection: number | null,
  examFormat: number | null,
}
export interface QuestionListParams extends QuestionClassAndTagType, QuestionPageType {
  [key: string]: any,
  moduleManageId: string,
}

export interface FixedQuestionCardParams {
  chapterId: string,
  targetResultId: string,
}

export interface FixedStartProtParams extends QuestionClassAndTagType {
  moduleManageId: string,
  chapterId: string,
}
export interface FixedCatalogueParams extends QuestionClassAndTagType {
  moduleManageId: string,
  brushVersionId: string,
  withTag?: 1 | 0
  purchased?: 1 | 2
}

export interface TextBookParams extends QuestionClassAndTagType {
  moduleManageId: string,
  textbookId: string,
  withTag?: 1 | 0
  purchased?: 1 | 2
}

export interface GetModuleManagePrivilegeType extends GetModuleManageTagsListBodyType {
  moduleType: number
}

export interface GetBookPrivilegeType extends GetModuleManageTagsListBodyType {
  textbookId: string
}

// 第一次添加商品计算价格
export interface FeeFirstAddBodyType {
  moduleManageId: string; // 模块id
  studyLevel1: number; // 专业分类id
  expiry: number; // 有效期：数字几就是几个月
  subjectType?: number; // 学科（非必须）
  region?: number; // 地区（非必须）
  directionType?: number; // 方向类型（非必须）
  academicSection?: number; // 学术部门（非必须）
  examFormat?: number; // 考试形式（非必须）
  targetType: number; // 目标类型：99模块 2试卷 3教材 4必刷题
  targetId: string; // 目标id：模块传模块id，其他传对应资源id
}

// 非第一次添加商品计算价格
export interface FeeOtherAddBodyType extends FeeFirstAddBodyType {
  id: string
  goodsTitle: string // 所有标签值的名称按照项顺序拼接好的字符串

}

// 删除商品计算价格
export interface FeeDelBodyType extends QuestionLogRemoveBodyType {
  goodsId:string // 商品id
  expiry: number
}

// 编辑商品计算价格
export interface FeeEditBodyType extends FeeFirstAddBodyType {
  oldGoodsId: string // 老的商品id
  id: string
  goodsTitle: string // 所有标签值的名称按照项顺序拼接好的字符串
}

// 有效期类型
export interface FeeExpiryListType {
  expiry: number // 有效期
  expiryDesc: string // 有效期描述
  singlePrice: string // 原价
  discountPrice: string // 现价
  priceDesc: string // 价格描述
  isChecked: number // 是否选中
}

// 提交订单
export interface FeeSubmitBodyType {
  preOrderKey: string, // 预下单key
  shouldPay: string, // 应付金额
}

// 获取答题卡
export interface QuestionAnswerCardParamsType {
  cardType: 1 | 2, // 1.练习 2.报告
  targetResultId: string,
  questionNum: number, // 展示当前试题数量
}

// 获取试题详情
export interface QuestionDetailParamsType {
  cardType: 1 | 2, // 1.练习 2.报告
  questionId: string, // 试题ID
  targetResultId: string,
  sort: number, // 排序
}

// 答题卡
export interface AnswerCardQuestionType {
  awarding?: number;
  bigSort?: number;
  parentId?: string;
  questionId: string;
  sort: number;
  sonSort?: number;
  state?: number;
}

// 批量获取试题详情
export interface QuestionDetailListParamsType {
  cardType: 1 | 2, // 1.练习 2.报告
  questionList: AnswerCardQuestionType[], // 试题ID
  targetResultId: string,
}

// 首页接口
export interface ModuleManageTagsType {
  lastModule: GetModuleManageTagsListBodyType | null;
  nowModule: GetModuleManageTagsListBodyType;
}
// 判断商品中心试卷包权益
export interface CoreGoodsPermissionType {
  goodsMasterId: string;
  testpaperId: string;
}
