import { NuxtModule, RuntimeConfig } from 'nuxt/schema'
declare module 'nuxt/schema' {
  interface NuxtConfig {
    ["ssr-env-module"]?: typeof import("./../../../build/ssr-env-module").default extends NuxtModule<infer O> ? Partial<O> : Record<string, any>
    ["eslint"]?: typeof import("@nuxtjs/eslint-module").default extends NuxtModule<infer O> ? Partial<O> : Record<string, any>
    ["pinia"]?: typeof import("@pinia/nuxt").default extends NuxtModule<infer O> ? Partial<O> : Record<string, any>
    ["nuxtIcons"]?: typeof import("nuxt-icons").default extends NuxtModule<infer O> ? Partial<O> : Record<string, any>
    ["vant"]?: typeof import("@vant/nuxt").default extends NuxtModule<infer O> ? Partial<O> : Record<string, any>
    ["legacy"]?: typeof import("nuxt-vite-legacy").default extends NuxtModule<infer O> ? Partial<O> : Record<string, any>
    ["piniaPersistedstate"]?: typeof import("@pinia-plugin-persistedstate/nuxt").default extends NuxtModule<infer O> ? Partial<O> : Record<string, any>
    modules?: (undefined | null | false | NuxtModule | string | [NuxtModule | string, Record<string, any>] | ["./../../../build/ssr-env-module", Exclude<NuxtConfig["ssr-env-module"], boolean>] | ["@nuxtjs/eslint-module", Exclude<NuxtConfig["eslint"], boolean>] | ["@pinia/nuxt", Exclude<NuxtConfig["pinia"], boolean>] | ["nuxt-icons", Exclude<NuxtConfig["nuxtIcons"], boolean>] | ["@vant/nuxt", Exclude<NuxtConfig["vant"], boolean>] | ["nuxt-vite-legacy", Exclude<NuxtConfig["legacy"], boolean>] | ["@pinia-plugin-persistedstate/nuxt", Exclude<NuxtConfig["piniaPersistedstate"], boolean>])[],
  }
  interface RuntimeConfig {
   app: {
      baseURL: string,

      buildAssetsDir: string,

      cdnURL: string,
   },

   baseUrlServer: string,
  }
  interface PublicRuntimeConfig {
   TOKEN_KEY: string,

   BUILD_ENV: string,

   lastBuildTime: number,

   baseUrlClient: string,

   version: string,

   PLC_WX_APP_ID: string,

   USER_KEY: string,

   CALLBACK_URL: string,

   PAY_LOCALHOST: string,

   persistedState: {
      storage: string,

      debug: boolean,

      cookieOptions: any,
   },
  }
}
declare module 'vue' {
        interface ComponentCustomProperties {
          $config: RuntimeConfig
        }
      }