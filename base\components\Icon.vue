<template>
  <svg
    class="kkc-icon"
    aria-hidden="true"
    :style="{
      color: color,
      fontSize: fontSize,
      overflow: overflow,
    }"
  >
    <use :xlink:href="`#${prefix}${name}`" />
  </svg>
</template>

<script setup lang="ts">
import { px2rem } from '../utils/px2rem'
// TODO state
const { isPc } = useAppConfig()

const fontSize = computed(() => {
  if (!props.size) {
    return undefined
  }
  if (isPc) {
    return props.size + 'px'
  } else {
    return px2rem(String(props.size))
  }
})

const props = defineProps({
  size: {
    type: Number,
    default: undefined, // 16
  },
  prefix: {
    type: String,
    default: '',
  },
  color: {
    type: String,
    default: undefined,
  },
  name: {
    type: String,
    required: true,
  },
  overflow: {
    type: String,
    default: 'hidden',
  },
})
</script>

<style lang='scss'>
.kkc-icon {
  display: inline;
  width: 1em;
  height: 1em;
  vertical-align: -0.2em;
  fill: currentColor;
  overflow: hidden;
  user-select: none;
  outline: none;
}
</style>
