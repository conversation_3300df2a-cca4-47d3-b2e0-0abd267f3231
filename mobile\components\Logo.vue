<template>
  <a
    class="kkc-logo"
    href="/"
  >
    <img
      v-if="logo2"
      :src="logo2"
      alt=""
      class="kkc-logo__img"
    >
    <!-- <span class="kkc-logo__name truncate">{{ appConfig.logo.name }}</span> -->
  </a>
</template>

<script setup lang="ts">
const { logo2 } = useAppConfig()
</script>

<style lang="scss">
.kkc-logo {
  display: flex;
  align-items: center;
  height: 80px;
  background-color: #fff;
  &__img {
    height: 40px;
  }
  &__name {
    height: 48px;
    font-size: 32px;
    font-weight: 500;
    color: #111;
    line-height: 48px;
    margin-left: 18px;
  }
}
</style>
