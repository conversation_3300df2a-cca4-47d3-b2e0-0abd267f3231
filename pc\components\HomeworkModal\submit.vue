<template>
  <el-dialog
    :model-value="visible"
    width="640px"
    append-to-body
    destroy-on-close
    class="study-task-dialog"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <!-- [el-dialog] [API] the title slot is about to be deprecated in version 3.0.0, please use the header slot instead. -->
    <template #header>
      <span class="modal-title">提交作业<!-- {{ homeworkId }} --></span>
    </template>
    <div class="study-task-modal">
      <div class="modal-desc">
        提交作业后，老师未批改时可再次提交作业；老师批改中可联系老师等待批改；取消批改后再次提交作业
      </div>
      <div class="image-upload-grid">
        <div
          v-for="(item, idx) in images"
          :key="item.url || item.name"
          class="file-item image-item"
        >
          <template v-if="item.type && item.type.startsWith('image/')">
            <!-- 点击已选图片可放大预览，点击已选图片的删除按钮可以删除本图片 -->
            <!-- <img :src="item.url" class="preview-img"> -->
            <!-- style="width: 100px; height: 100px" -->
            <el-image
              class="preview-img"
              :src="item.url"
              :zoom-rate="1.2"
              :max-scale="7"
              :min-scale="0.2"
              :preview-src-list="[item.url]"
              show-progress
              :initial-index="4"
              fit="cover"
            />
          </template>
          <!-- <template v-else-if="item.type && item.type.startsWith('audio/')">
            <audio :src="item.url" class="preview-audio" />
          </template>
          <template v-else-if="item.type && item.type.startsWith('video/')">
            <video :src="item.url" class="preview-video" />
          </template> -->
          <!-- <template v-else-if="item.type === 'application/pdf'">
            <span class="file-icon pdf" />
            <span class="file-name">{{ item.name }}</span>
          </template>
          <template v-else-if="item.type === 'application/msword'">
            <span class="file-icon word" />
            <span class="file-name">{{ item.name }}</span>
          </template>
          <template v-else-if="item.type === 'application/vnd.ms-excel'">
            <span class="file-icon excel" />
            <span class="file-name">{{ item.name }}</span>
          </template> -->
          <template v-else>
            <span class="file-icon other" />
            <span class="file-name">{{ item.name }}</span>
          </template>
          <span class="delete-btn" @click="removeImage(idx)">×</span>
        </div>
        <div
          v-if="images.length < 9"
          class="image-item add-item"
          @click="handleAddImage"
        >
          <span class="add-icon">
            <!-- + -->
            <KKCIcon name="icon-jiahao" :size="36" />
          </span>
          <span class="add-text">添加图片<br>(最多9张)</span>
        </div>
      </div>
      <div class="modal-footer">
        <el-button
          class="submit-btn"
          type="danger"
          :disabled="isSubmitting"
          @click="handleSubmit"
        >
          确认提交
        </el-button>
      </div>
      <!-- 隐藏的文件选择器 -->
      <!-- ,.pdf,.doc,.docx,.xls,.xlsx,.mp3,.mp4,audio/*,video/*,application/pdf
       ,image/*,.wav,.aac -->
      <input
        ref="fileInput"
        type="file"
        accept=".png,.jpg,.jpeg,.gif"
        multiple
        hidden
        style="display:none"
        @change="onFileChange"
      >
    </div>
  </el-dialog>
</template>
<script setup>
import { ref, defineProps, defineEmits } from 'vue'

const props = defineProps({
  visible: {
    type: Boolean,
    required: true
  },
  homeworkId: {
    type: String,
    required: true
  }
})
const emit = defineEmits(['close', 'uploaded'])

const images = ref([])
const fileInput = ref(null)
const isSubmitting = ref(false)

function handleClose () {
  emit('close')
}

function handleAddImage () {
  if (images.value.length >= 9) { return }
  fileInput.value && fileInput.value.click()
}
/**
 * 增加功能：只能上传图片类型的文件
 */
function onFileChange (e) {
  const files = Array.from(e.target.files)
  const remain = 9 - images.value.length
  const addFiles = files.slice(0, remain)
  // 过滤出图片类型的文件
  const imageFiles = addFiles.filter(file => file.type.startsWith('image/'))
  if (imageFiles.length !== addFiles.length) {
    Message('请上传图片类型的文件')
    return
  }
  imageFiles.forEach((file) => {
    let url = ''
    let type = ''
    if (file.type.startsWith('image/') || file.type.startsWith('audio/') || file.type.startsWith('video/') || file.type === 'application/pdf') {
      url = URL.createObjectURL(file)
      type = file.type
    } else {
      type = file.type || getFileTypeByExt(file.name)
    }
    images.value.push({
      file,
      url,
      type,
      name: file.name
    })
  })
  e.target.value = ''
}

function getFileTypeByExt (name) {
  const ext = name.split('.').pop().toLowerCase()
  if (['doc', 'docx'].includes(ext)) { return 'application/msword' }
  if (['xls', 'xlsx'].includes(ext)) { return 'application/vnd.ms-excel' }
  if (['pdf'].includes(ext)) { return 'application/pdf' }
  return ''
}

function removeImage (idx) {
  const img = images.value[idx]
  URL.revokeObjectURL(img.url)
  images.value.splice(idx, 1)
}

const ossInstance = ref(null)
async function handleSubmit () {
  if (isSubmitting.value) { return }
  isSubmitting.value = true
  try {
    Loading(true)
    // 实例化oss
    const { data } = await fetchOSSConfig()
    ossInstance.value = initOSS(data || {})
    const uploadPromises = images.value.map((imgObj, _idx) => {
      const file = imgObj.file
      const objectKey = generateFilename(file, 'img/kukecloud/pc/study-plan/')
      return unref(ossInstance).put(objectKey, file).then(res => res.url)
    })
    const urls = await Promise.all(uploadPromises)
    console.log(urls, 'urls')
    await homeworkSubmitProt(urls)
    images.value.forEach((img) => {
      URL.revokeObjectURL(img.url)
    })
    images.value = []
  } catch (err) {
    console.error(err)
  } finally {
    Loading(false)
    isSubmitting.value = false
  }
}

// 文件类作业提交
async function homeworkSubmitProt (submitHomeworkList = []) {
  const { error } = await useHttp('/kukestudentservice/pc/kssHomeworkScore/homeworkSubmitProt', {
    body: {

      homeworkId: props.homeworkId,
      submitHomeworkList
    }
  })
  if (!unref(error)) {
    emit('uploaded', submitHomeworkList)
  } else if (unref(error).data?.code === '10071') {
    Message(unref(error).data.msg)
  }
}
onBeforeUnmount(() => {
  console.log('onBeforeUnmount ')
})
onUnmounted(() => {
  console.log('onUnmounted ')
  //
  images.value.forEach((img) => {
    URL.revokeObjectURL(img.url)
  })
})
</script>
<style lang="scss">
.study-task-dialog {
  border-radius: 12px;
  .el-dialog__header{
    padding-left: 24px;
  }
  .el-dialog__body {
    padding: 0;
  }
  .study-task-modal {
    background: #fff;
    border-radius: 12px;
    padding: 24px;
    // width: 540px;
    box-sizing: border-box;
    position: relative;
    padding-top: 0;
  }
  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .modal-title {
    font-size: 18px;
    font-weight: 600;
  }
  .modal-close {
    font-size: 24px;
    cursor: pointer;
  }
  .modal-desc {
    color: #999;
    font-size: 12px;
    margin: 0 0 14px 0;
    line-height: 20px;
  }
  .image-upload-grid {
    display: flex;
    flex-wrap: wrap;
    // grid-template-columns: repeat(4, 1fr);
    gap: 13px;
    margin-bottom: 24px;
  }
  .image-item {
    width: 108px;
    height: 108px;
    background: #f4f4f4;
    border-radius: 14px;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .preview-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  .delete-btn {
    position: absolute;
    top: 0px;
    right: 0px;
    // background: rgba(0,0,0,0.5);
    color: #fff;
    // border-radius: 50%;
    // border-bottom-left-radius: 12px;
    width: 24px;
    height: 24px;
    // line-height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 2;
    font-size: 0;
    background: url('@/assets/images/learn/icon_guanbi.png') no-repeat center center;
  }
  .add-item {
    flex-direction: column;
    cursor: pointer;
    background: #F5F6F9;
    // border: 1px dashed #ccc;
  }
  .add-icon {
    font-size: 36px;
    color: #999;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .add-text {
    font-size: 12px;
    color: #999;
    text-align: center;
    line-height: 20px;
    margin-top: 8px;
  }
  .modal-footer {
    text-align: right;
    .submit-btn {
      // width: 100px;
      @apply bg-brand text-white;
    }
  }
}
</style>
