<template>
  <div
    :style="{ width, height }"
    class="relative"
    @touchstart="handleTouchStart"
    @touchmove="handleTouchMove"
    @touchend="handleTouchEnd"
  >
    <div v-if="cantPlay" class="player-error">
      <div class="player-error__icon" />
      <div>暂不支持当前浏览器，建议更换谷歌浏览器</div>
    </div>
    <template v-else>
      <template v-if="isLoadVideo">
        <div class="video-loading flex items-center justify-center absolute top-0 left-0" :style="{ width, height }">
          <img :src="VideoLoading" class="w-[64px] h-[64px]" height="64" width="64">
        </div>
      </template>
      <PolyvPlayer
        v-if="videoDetail.videoType === 1"
        ref="polyvPlayer"
        :video-id="videoDetail.videoId"
        :ts="videoDetail.ts"
        :sign="videoDetail.sign"
        :playsafe="videoDetail.playSafe"
        :live-org-id="videoDetail.orgId"
        :width="width"
        :height="height"
        @on-ready="handleReady"
        @on-play="player => emits('play', player)"
        @on-ended="emits('ended')"
        @on-pause="emits('pause')"
        @on-seek="emits('seek', $event)"
      />
      <CCPlayer
        v-else-if="videoDetail.videoType === 2"
        ref="ccPlayer"
        :vid="videoDetail.videoId"
        :siteid="videoDetail.siteId"
        :width="width"
        :height="height"
        @on-ready="handleReady"
        @on-play="player => emits('play', player)"
        @on-ended="emits('ended')"
        @on-pause="emits('pause')"
        @on-seek="emits('seek', $event)"
      />
    </template>
  </div>
</template>

<script setup lang="ts">
import VideoLoading from '../../assets/video/loading.png'
import { pauseAudio } from '../../utils/audio'
import CCPlayer from './ccPlayer/index.vue'
import PolyvPlayer from './polyvPlayer/index.vue'

import type { VideoDetailType } from './types.ts'
defineProps<{
  videoDetail: VideoDetailType,
  width?: string,
  height?: string
}>()

const emits = defineEmits<{
  (e: 'play', data: any): void
  (e: 'pause'): void
  (e: 'ended'): void
  (e: 'ready'): void
  (e: 'seek', data: any): void
  (e: 'touchStart', data: TouchEvent): void
  (e: 'touchMove', data: TouchEvent): void
  (e: 'touchEnd', data: TouchEvent): void
}>()

const { isXcx } = useAppConfig()
// 视频播放浏览器黑名单
const blackBrowserList = ref(['vivo'])
if (isXcx) {
  blackBrowserList.value = []
}
const cantPlay = typeof window !== 'undefined' && blackBrowserList.value.some(item => navigator.userAgent.toLowerCase().includes(item))

// 视频加载中
const isLoadVideo = ref(false)
const handleReady = () => {
  isLoadVideo.value = false
  // 关闭其他音频播放
  pauseAudio()
  emits('ready')
}

const handleTouchStart = (e: TouchEvent) => {
  emits('touchStart', e)
}
const handleTouchMove = (e: TouchEvent) => {
  emits('touchMove', e)
}
const handleTouchEnd = (e: TouchEvent) => {
  emits('touchEnd', e)
}

const polyvPlayer = ref()
const ccPlayer = ref()
// 视频播放器暂停
const handlePauseVideo = () => {
  polyvPlayer.value && polyvPlayer.value.onPauseVideo()
  ccPlayer.value && ccPlayer.value.onCcPauseVideo()
}

// 销毁播放器
const handleDestroyPlayer = () => {
  // 目前保利威广告期间无法暂停实例，只能销毁
  polyvPlayer.value && polyvPlayer.value.destroyPlayer()
  ccPlayer.value && ccPlayer.value.destroyPlayer()
}

onMounted(() => {
  isLoadVideo.value = true
})

onBeforeUnmount(() => {
  console.log('onBeforeUnmount')
})

defineExpose({
  handlePauseVideo,
  handleDestroyPlayer
})

</script>

<style lang="scss" scoped>
.player-error {
  @apply flex h-full flex-col items-center justify-center bg-[#000] text-[#fff] text-[14px];

  .player-error__icon {
    @apply w-[80px] h-[80px];
    margin: 0 auto 10px auto;
    background: url(data:image/png;base64,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) center / 100% no-repeat;
  }
}
</style>
