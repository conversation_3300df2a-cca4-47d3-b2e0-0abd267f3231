<template>
  <div class="giftpacks-main" :style="{ '--clientHeight': clientHeight }">
    <NavBar v-if="!isXcx" :title="activityName" custom-back @back="onBack" />
    <img v-if="isShowBanner" class="banner" :src="detail?.wapMarketingImageUrl" alt="">
    <div
      class="content"
      :style="{ '--bgToColor': detail?.backgroundColorTop, '--bgBtColor': detail?.backgroundColorBottom }"
    >
      <div class="content-main">
        <div class="content-main-tab" :style="{ '--activeColor': detail?.fontColor }">
          <a v-if="detail?.goods?.length" :class="{ active: active === 1 }" @click="tabChange(1)">课程免费送</a>
          <a v-if="detail?.coupons?.length || detail?.couponNoLongerAvailable === 1" :class="{ active: active === 2 }" @click="tabChange(2)">优惠券免费领</a>
        </div>
        <div ref="mainItem" class="content-main-item">
          <div v-if="detail?.goods?.length" ref="courseContent" class="mx-[24px]">
            <h3 id="course">
              课程免费送
            </h3>
            <good-cell
              v-for="(item, index) in detail?.goods"
              :key="index"
              :is-show-mask="false"
              class="!mb-[0px] w-[100%] block"
              :goods="item"
              :is-skip="true"
              :is-confirm-abnormal="false"
            />
          </div>
          <div class="content-main-item__coupon">
            <h3 v-if="detail?.coupons?.length || detail?.couponNoLongerAvailable === 1" id="coupon" class="!pb-[0.24rem] !pl-[0px] !pt-[32px]">
              优惠券免费领
            </h3>
            <div v-if="detail?.couponNoLongerAvailable === 0">
              <div v-for="(value, index) in detail?.coupons" :key="index">
                <KCouponCell
                  :coupon-info="value"
                  :type="1"
                  :coupon-status="detail?.status"
                  class="mb-[24px]"
                />
              </div>
            </div>
            <div v-else>
              <KKCEmpty :text="'活动太火爆了，优惠券已领完～'" svg />
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-if="butStatus" class="giftpacks-main-btn safe-area">
      <div
        :class="{ animation: giftpackStatus === 2 }"
        :style="{ 'background-image': `url(${butStatus.bgc})` }"
        @click="handleReceive"
      >
        {{ butStatus.lable }}
      </div>
    </div>
    <DialogMask v-if="detail?.activityRule" v-model="isShowMask" :content="detail?.activityRule" />
    <div class="activity-rule" @click="showMask()">
      活动规则
    </div>
  </div>
</template>

<script setup lang="ts">

import DialogMask from './components/MaskDialog.vue'
import useGiftPacksHooks from './hooks/useGiftPacksHooks'
import KCouponCell from './components/CouponCell.vue'
import type { GiftPacks } from '~/apis/giftpacks/types'
import { useUserStore } from '~/stores/user.store'

definePageMeta({
  pageid: '9',
  ignoreLearnTarget: true, // [M端] 忽略验证学习目标的逻辑
})
useHead({
  title: '大礼包'
})
const { isXcx } = useAppConfig()

// TODO state
const { giftpacksApi } = useApi()
const { btnType } = useGiftPacksHooks()
const userStore = useUserStore()
const isShowMask = ref(false)
const active = ref<number>(1)
const route = useRoute()
const id = route.params.id as string
const detail = ref<GiftPacks>()
const giftpackStatus = ref<number>(2)
const mainItem = ref<HTMLElement>()
const clientHeight = ref<string>('100vh')
const courseContent = ref<HTMLElement>()
const shareData = ref()
const confirmCkickTab = ref(false)

const onBack = () => {
  window.location.href = window.location.origin
}

const activityName = computed(() => {
  if (detail?.value && detail?.value.activityName) {
    const { activityName } = detail?.value
    return activityName.length > 10 ? `${activityName.slice(0, 10)}...` : activityName
  } else {
    return '大礼包'
  }
})

// 获取详情数据
const getDetail = async () => {
  const { data, error } = await giftpacksApi.getGiftPacksDetail({ id }) as any
  if (error.value) {
    console.error(error)
  } else {
    if (data.value && data.value?.activityRule) {
      data.value.activityRule = data.value.activityRule.replace(/\n/g, '<br/>')
    }
    detail.value = data.value as GiftPacks || {}
    console.log('detail.value.coupons', detail.value)
    giftpackStatus.value = data?.value?.status as number
  }
}

// // 获取分享数据
const getShareDetail = async () => {
  const { data } = await giftpacksApi.getShareData({ id }) as any
  console.log(data)
  shareData.value = data.value
  useHead({
    title: (shareData.value && shareData?.value?.shareTitle) || '您有一个大礼包待领取，速戳领取啦~', // 网站标题
    meta: [
      { name: 'keywords', content: (shareData.value && shareData?.value?.shareDescribe) || '免费羊毛，不薅白不薅~' }
    ],
    titleTemplate: () => {
      return '大礼包'
    },
  })
  const { shareTitle, shareDescribe, shareImgUrl } = shareData.value
  setWxShareInfo({ title: shareTitle, desc: shareDescribe, shareImg: shareImgUrl })
}

// 分享
const { setShareInfo } = useWxShare()

const setWxShareInfo = ({ title, desc, shareImg } :any) => {
  setShareInfo({
    title,
    desc,
    shareImg
  }, false)
  userStore.wxMiniProgramPostShareData('', title, shareImg)
}

const showMask = () => {
  isShowMask.value = true
}

// 初始化激活菜单
const initActiveTab = () => {
  if (detail.value?.goods && detail.value?.goods?.length > 0) {
    active.value = 1
  } else {
    active.value = 2
  }
}
// tab切换
const tabChange = (val: number) => {
  active.value = val
  console.log('1111111111')
  confirmCkickTab.value = true
  const courseContentHeight = courseContent.value?.clientHeight || 0
  if (process.client) {
    if (val === 2) {
      mainItem?.value.scrollTo(0, courseContentHeight + 2)
    } else {
      mainItem?.value.scrollTo(0, 0)
    }
  }
}

// 按钮状态和样式
const butStatus = computed(() => {
  if (!detail.value) { return }
  const { status, oneClickColor } = detail.value as GiftPacks
  const targetBtnType = {
    lable: '',
    bgc: ''
  }
  targetBtnType.lable = btnType[status]?.lable
  if (oneClickColor === 1) {
    targetBtnType.bgc = btnType[status]?.bgcRed
  } else {
    targetBtnType.bgc = btnType[status]?.bgcBlue
  }
  return targetBtnType
})

// 一键领取
const handleReceiveApi = async () => {
  Loading(true)
  const { data, error } = await giftpacksApi.oneClickCollection({ id, pickupMethod: 2 })
  if (error.value) {
    Loading(false)
    Message(error?.value as any)
  } else {
    Loading(false)
    Message(data?.value?.msg)
    getDetail()

    const { addBehaviorRecords } = useBehaviorRecord()
    addBehaviorRecords(ActionEnum.GiftPacks, SourceEnum.Wap, {
      goodsTitle: detail.value?.activityName, // 显示大礼包活动名称
      clientType: isXcx ? ClientType.WX_XCX : ClientType.WAP,
      extension8: detail.value?.activityName // 显示大礼包活动名称
    })
  }
}

// 领取
const handleReceive = () => {
  if (detail?.value?.status !== 2) { return }
  if (userStore.isLogin) {
    handleReceiveApi()
  } else if (os?.isApp && os.isAndroid) {
    window.android.login()
  } else if (os?.isApp && os.isHarmonyOS) {
    window.harmony?.login()
  } else if (os?.isApp && os.isPhone) {
    window.webkit.messageHandlers.login.postMessage(null)
  } else {
    userStore.isLoginFn()
  }
}

// 监控内容滚动
const listenMainScroll = () => {
  const courseContentHeight = courseContent.value?.clientHeight || 0
  mainItem?.value?.addEventListener('scroll', function () {
    if (confirmCkickTab.value) {
      confirmCkickTab.value = false
      return
    }
    const mainItemScrollTop = mainItem?.value?.scrollTop || 0
    if (mainItemScrollTop > courseContentHeight - 2) {
      active.value = 2
    } else {
      active.value = 1
    }
  })
}
const isShowBanner = ref(true)
// 获取主内容的最大高度适配不通浏览器
const getMainContentMaxHeight = () => {
  nextTick(() => {
    const _html = document.documentElement
    clientHeight.value = _html.clientHeight + 'px'
    console.log('clientHeight.value', _html.clientHeight)

    const _el = document.querySelector('.content-main-item') as HTMLDivElement
    const _elContent = document.querySelector('.giftpacks-main .content') as HTMLDivElement
    if (_el && _el.style) {
      if (_html.clientHeight > 675) {
        isShowBanner.value = true
        if (isXcx) {
          _el.style.height = `calc(${clientHeight.value} - 6.7rem)`
          _elContent.style.top = '4rem'
          _elContent.style.minHeight = 'calc(100vh - 5.3rem)'
        } else {
          _el.style.height = `calc(${clientHeight.value} - 7.18rem)`
          _elContent.style.top = '4.56rem'
          _elContent.style.minHeight = 'calc(100vh - 6.62rem)'
        }
      } else {
        // 兼容折叠屏
        isShowBanner.value = false
        _el.style.height = `calc(${clientHeight.value} - 3rem)`
        _elContent.style.top = '0.8rem'
        _elContent.style.minHeight = 'calc(100vh - 0rem)'
      }
    }
  })
}

await getDetail()
initActiveTab()
getShareDetail()
onMounted(() => {
  if (process.client) {
    getMainContentMaxHeight()
    if (detail.value && detail.value.goods && detail.value.goods.length > 0) {
      listenMainScroll()
    }
    window.addEventListener('resize', getMainContentMaxHeight)
  }
})
</script>
<style lang="scss" scoped>
@keyframes scale {
  0% {
    transform: scale(0.95)
  }

  100% {
    transform: scale(1.05);
  }
}

.giftpacks-main {
  overflow: hidden;
  height: var(--clientHeight);

  .banner {
    // width: 750px;
    height: 450px;
    position: fixed;
    left: 50%;
    transform: translateX(-50%);
  }

  .activity-rule {
    position: fixed;
    right: 0px;
    top: 120px;
    width: 48px;
    height: 137px;
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.4) 100%);
    border-radius: 26px 0px 0px 26px;
    backdrop-filter: blur(8px);
    z-index: 11;
    font-weight: 600;
    font-size: 24px;
    text-align: center;
    writing-mode: vertical-lr;
    line-height: 48px;
    letter-spacing: 2px;
  }

  &-btn {
    position: fixed;
    width: 750px;
    bottom: 0;
    padding: 10px 0;
    background-color: #fff;
    min-height: 108px;
    box-sizing: border-box;

    div {
      text-align: center;
      font-size: 32px;
      color: #fff;
      font-weight: 600;
      width: 4.4rem;
      height: 1.01rem;
      margin: 0 auto;
      line-height: 92px;
      background-repeat: no-repeat;
      background-size: contain;

      &.animation {
        animation: scale 0.8s 0s infinite alternate;
      }
    }
  }

  .content {
    background: linear-gradient(to bottom, var(--bgToColor), var(--bgBtColor));
    box-shadow: inset 0px 2px 0px 0px var(--bgColor);
    display: flex;
    border-radius: 20px 20px 0 0;
    padding: 24px 0 0px;
    box-sizing: border-box;
    position: fixed;
    width: 750px;
    &-main {
      margin: 0px auto;
      background: #fff;
      box-sizing: border-box;
      border-radius: 20px 20px 0 0;
      width: 708px;

      &-tab {
        display: flex;
        justify-content: space-around;
        align-items: center;
        background-color: #fff;
        // border-bottom: 1px solid #EEEEEE;
        padding: 32px 0 38px;
        margin: 0 auto;
        font-size: 36px;
        color: #111;
        width: 654px;
        line-height: 1;

        a {
          position: relative;

          &.active {
            color: var(--activeColor);
            font-weight: 600;

            &::after {
              content: "";
              width: 32px;
              height: 6px;
              background: linear-gradient(135deg, #FF7D86 0%, #EB2330 100%);
              border-radius: 3px 0px 3px 0px;
              position: absolute;
              bottom: -16px;
              transform: translateX(-50%);
              left: 50%;
            }
          }
        }
      }

      h3 {
        color: #111;
        font-weight: 600;
        font-size: 32px;
        // padding: 0;
        padding: 0 0 24px 0;
        // padding-left: 24px;
      }

      &-item {
        overflow-x: auto;
        // background-color: #EEEEEE;

        &__coupon {
          padding: 0 24px 24px;

          &__item:nth-last-child(1) {
            margin-bottom: 0px;
          }
        }

      }

    }

  }
  :deep(.goods-cell-info-title){
    width: 370px !important;
  }
}
</style>
