export const audioRegex = /<p[^>]*class=('|")?audio\1[^>]*>(.*?)<\/p>/g
// export const audioRegex = /<audio[^>]*>.*?<\/audio>/g
// 处理音频资源
export const extractedAudio = (obj: any[]) => {
  if (Array.isArray(obj) && obj.every(item => typeof item === 'string')) {
    return obj
  }
  return obj.map((item: { [s: string]: unknown } | ArrayLike<unknown>) => {
    const extractedAudioTags: any = {}
    Object.entries(item).forEach(([key, value]) => {
      if (Array.isArray(value)) {
        // 如果value是数组，则递归调用handleData函数
        extractedAudioTags[key] = extractedAudio(value)
      } else if (typeof value === 'string' && value.startsWith('<') && value.includes('audio')) {
        // 如果value是字符串且以<开头，则解析HTML标签
        const parser = new DOMParser()
        const doc = parser.parseFromString(value, 'text/html')
        const element = doc.querySelector('p')
        if (element) {
          const audioTags = doc.querySelector('audio')
          // 如果解析后的文档中有<audio>标签，则提取audio标签中的src属性
          if (audioTags) {
            element.style.display = 'none'
            if (typeof extractedAudioTags[key + 'Src'] === 'undefined') {
              extractedAudioTags[key + 'Src'] = audioTags.getAttribute('src')
            }
          }
          // 否则，直接将value赋值给extractedAudioTags
          extractedAudioTags[key] = value.replace(/\n/g, '').replace(audioRegex, '')
        } else {
          extractedAudioTags[key] = value
        }
      } else if (typeof value === 'string' && value.startsWith('[') && value.includes('audio')) {
        try {
          // 如果value是JSON字符串，parse后进行解析
          const jsonValue = JSON.parse(value)
          if (jsonValue.every((jV: unknown) => typeof jV === 'string')) {
            const arr: string[] = []
            const arrSrc: (string | null)[] = []
            // 如果value是字符串数组，则直接赋值
            jsonValue.forEach((jV: string) => {
              const parser = new DOMParser()
              const doc = parser.parseFromString(jV, 'text/html')
              const element = doc.querySelector('p')
              if (element) {
                const audioTags = doc.querySelector('audio')
                // 如果解析后的文档中有<audio>标签，则提取audio标签中的src属性
                if (audioTags) {
                  arrSrc.push(audioTags.getAttribute('src'))
                }
                arr.push(jV.replace(/\n/g, '').replace(audioRegex, ''))
              } else {
                arr.push(jV)
              }
            })
            if (arrSrc.length > 0) {
              extractedAudioTags[key + 'Src'] = JSON.stringify(arrSrc)
            }
            extractedAudioTags[key] = JSON.stringify(arr)
          } else {
            extractedAudioTags[key] = JSON.stringify(extractedAudio(jsonValue))
          }
        } catch (error) {
          console.warn(error)
          extractedAudioTags[key] = value
        }
      } else {
        extractedAudioTags[key] = value
      }
    })

    return extractedAudioTags
  })
}

// TODO 内存泄漏可疑点: 全局变量
// 音频实例
const audioList: HTMLAudioElement[] = []
// 管理音频
export const addAudio = (audio: HTMLAudioElement) => audioList.push(audio)
// 暂停所有音频
export const pauseAudio = () => {
  audioList.forEach(audio => audio.pause())
}
// 删除当前音频
export const removeAudio = (audio: HTMLAudioElement) => {
  const index = audioList.indexOf(audio)
  if (index > -1) {
    audioList.splice(index, 1)
  }
}
// 添加音频 去重
export const addAudioUniquely = (audio: HTMLAudioElement) => {
  if (!audio) { return }
  // 删除之前的音频，添加最新的音频
  removeAudio(audio)
  // 如果音频不存在，添加音频，否则不做任何操作
  if (!audioList.includes(audio)) {
    audioList.push(audio)
  }
}
