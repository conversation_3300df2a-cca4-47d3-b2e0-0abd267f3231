---
description: api接口数据获取指南
globs: 
alwaysApply: true
---

api接口数据获取指南：
- 使用二次封装的 `useHttp`进行数据获取
- `base/composables/useHttp.ts` 中定义了 `useHttp` 函数

`useHttp`使用指南：
- `useHttp` 是一个组合函数，用于简化 API 请求。
- `useHttp`已经全局引用
- 使用时，传入请求配置对象，如下所示：
```typescript
const { data, error, pending } = useHttp<IResponseData>('/api/example', {
    method: 'POST',
    body: { id: 123 },
    default: () => ({} as IResponseData),
});
```
- `data` 是ref响应数据，`error` 是ref错误信息，`pending` 是加载状态。
- 确保在组件中处理这些状态，以提供良好的用户体验。
- 确保在使用 `useHttp` 时，遵循以下最佳实践：
- 使用 `try/catch` 块处理可能的错误。
- 在组件中使用 `pending` 状态来显示加载指示器。
- 确保在请求完成后清理状态，避免内存泄漏。
- 在使用 `useHttp` 时，确保遵循以下命名规范：
    - 请求函数命名应以 `fetch` 开头，如 `fetchUserData`。
    - 响应数据命名应以 `data` 结尾，如 `userData`。
- 确保在使用 `useHttp` 时，遵循以下代码风格：
    - 使用箭头函数定义请求函数。
    - 使用解构赋值获取响应数据。
    - 使用模板字符串构建 URL。