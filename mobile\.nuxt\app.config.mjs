
import { updateAppConfig } from '#app'
import { defuFn } from 'D:/workspace/0.nuxt3/migration/kuke-cloud---next/node_modules/defu/dist/defu.mjs'

const inlineConfig = {
  "requestAppid": "c4881275944",
  "requestAppkey": "awo6ureum8bn",
  "clientType": "7",
  "WX_APP_ID": "wx32638bab45b67226",
  "QQ_APP_ID": "102079805",
  "PRODUCT_TYPE_DOMAIN": "m-kukeyun-devc.tyyk99.com",
  "AUTH_CENTER": "https://auth-center-dev.kukewang.com",
  "USER_AGREEMENT_ID": "815463590374293504",
  "PRIVACY_POLICY_AGREEMENT_ID": "815463680002375680",
  "PRIVACY_POLICY_THIRD_ID": "897273773977350144",
  "FILE_VIEW_DOMAIN": "https://kkfileviewdev.kukewang.com",
  "KUKE_CLOUD_ORG_ID_LIST": "['1','2']",
  "requestEncryptKey": "vmHbx4bnI91ytL389b52nGBSScAQ50oIbYsNV4AfVgw=",
  "NUXT_KK_STUDENT_LOCALHOST": "*",
  "NUXT_KK_STUDENT_LOCALHOST_TY": "*",
  "nuxt": {}
}

// Vite - webpack is handled directly in #app/config
if (import.meta.hot) {
  import.meta.hot.accept((newModule) => {
    updateAppConfig(newModule.default)
  })
}

import cfg0 from "D:/workspace/0.nuxt3/migration/kuke-cloud---next/mobile/app.config.ts"

export default /* #__PURE__ */ defuFn(cfg0, inlineConfig)
