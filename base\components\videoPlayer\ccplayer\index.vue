<template>
  <div :style="{ width, height }">
    <div id="playerContainer" :style="{ width, height }" :class=" !isPc ? 'xcx-player' : '' " />
  </div>
</template>

<script setup lang="ts">
import md5 from 'md5'
import { useUserStore } from '~/stores/user.store'
const isPc = os?.isPc
const userStore = useUserStore()
const props = withDefaults(defineProps<{
    width?: string
    height?: string
    siteid: string
    vid: string,
    isBuy:boolean,
    lastTimePoint:number
    isPromote?:number
    isShowVideo: boolean
}>(), {
  width: '100%',
  height: '100%',
  isPromote: 0,
  isShowVideo: true
})

const emits = defineEmits<{
    (e: 'ccPlayOver', value: string): void // 播放结束
    (e: 'ccPlayPause', value: string, type?:string): void // 暂停播放
    (e: 'ccPlayStart', value: string): void // 开始播放
    (e: 'ccPlayTime', playTime: number, duration:number): void // 播放时长
}>()
const { width, height, } = toRefs(props)

// cc视频实例
let ccPlayer: any
const createPlayer = () => {
  const playerContainer = document.getElementById('playerContainer')
  if (props.isBuy) {
    ccPlayer = window.createCCH5Player({
      vid: props.vid,
      vc: md5(props.vid),
      siteid: props.siteid,
      autoStart: true,
      // realAutoPlay: true,
      width: width.value,
      height: height.value,
      parentNode: playerContainer,
      watchStartTime: props.lastTimePoint,
      closeHistoryTime: 1
    })
  }
}
// 暂停
const onCcPauseVideo = (): void => {
  if (ccPlayer) {
    ccPlayer.pause()
  }
}
// 播放
const onCcResumeVideo = (): void => {
  if (ccPlayer) {
    console.log('CC开始播放')
    ccPlayer.play()
  }
}
defineExpose({
  onCcPauseVideo,
  onCcResumeVideo
})
function setPlayerCallBack () {
  window.on_CCH5player_play = onPlayVideo
  window.on_CCH5player_pause = onPlayPause
  window.onCCH5PlayerLoaded = onCCH5PlayerLoaded
  window.on_CCH5player_ready = onPlayerReady
  window.on_player_timeupdate = onPlayerTimeupdate
  window.on_CCH5player_ended = onPlayerEnded
  window.on_player_seek = onPlayerSeek
  window.on_h5player_error = onPlayError
  window.on_player_buffering = onPlayerBuffer
}
const bufferStatus = ref(0)
function onPlayerBuffer (obj) {
  console.log('缓冲中', obj)
  bufferStatus.value = obj.flag
}
// 视频播放异常
function onPlayError () {
  if (!props.isShowVideo) { return false }
  Message('请使用其他浏览器观看')
}
// 播放
function onPlayVideo () {
  emits('ccPlayStart', props.vid)
}

function onPlayerReady () {
  console.log('加载完成', ccPlayer)
  // TODO 内存泄漏可疑点: 确保客户端执行并及时清除
  setTimeout(() => {
    ccPlayer.play()
  }, 50)
}
function onCCH5PlayerLoaded () {
  window.ccH5PlayerJsLoaded = true
  // createPlayer()
}
// 真实播放时间 正常播放
const realPlayTime = ref(0)
// 暂停
function onPlayPause () {
  emits('ccPlayPause', props.vid, '')
}
// 拖动时间
const seekTime = ref(0)
// 拖动前的时间节点
const seekTimePrev = ref(0)
// 拖动后的时间节点
const seekTimeNext = ref(0)
function onPlayerSeek (prevTime:number, currentTime:number) {
  if (currentTime > prevTime) {
    seekTime.value += (currentTime - prevTime)
  }
  seekTimePrev.value = prevTime
  seekTimeNext.value = currentTime
}

function onPlayerTimeupdate (currentTime:number) {
  const { playedTimes } = ccPlayer.getPlayedPausedTimes()
  realPlayTime.value = playedTimes
  emits('ccPlayTime', currentTime, realPlayTime.value)
  if (currentTime > 600 && props.isPromote === 1 && !userStore.isLogin) {
    ccPlayer.pause()
    emits('ccPlayPause', props.vid, 'isPromote')
  }
}
function onPlayerEnded () {
  emits('ccPlayOver', props.vid)
}

// 页面关闭
const handlePageClose = () => {
  console.log('页面关闭')
}
// 页面切换
const handlePageChange = () => {
  if (document.hidden) {
    // 执行相应的操作，如暂停视频、音频等
    console.log('页面切出')
  } else {
    // 切回 执行相应的操作，如恢复视频、音频等
    console.log('页面切回')
  }
}
watch(() => props.vid, () => {
  ccPlayer && ccPlayer.pause()
  ccPlayer && ccPlayer.destroy()
  createPlayer()
  setPlayerCallBack()
})
watch(() => props.isBuy, (newV) => {
  if (!newV) {
    ccPlayer && ccPlayer.destroy()
  }
})
onMounted(() => {
  useHead({
    script: [
      {
        src: '//p.bokecc.com/player?newversion=true',
      },
    ]
  })
  // createPlayer()
  // setPlayerCallBack()
  window.addEventListener('beforeunload', handlePageClose)
  window.addEventListener('pagehide', handlePageClose)
  window.addEventListener('unload', handlePageClose)
  document.addEventListener('visibilitychange', handlePageChange)
})

onUnmounted(() => {
  if (ccPlayer) {
    localStorage.setItem('polyvPlayerOption', JSON.stringify(ccPlayer))
    ccPlayer.destroy()
  }
})

</script>
<style lang="scss">
.xcx-player{
  .ccH5ProgressBar{
    left: 128PX!important;
  }
}

</style>
