// Generated by nuxi
/// <reference types="@pinia/nuxt" />
/// <reference types="nuxt-icons" />
/// <reference types="@vant/nuxt" />
/// <reference types="nuxt-vite-legacy" />
/// <reference types="@pinia-plugin-persistedstate/nuxt" />
/// <reference types="nuxt" />
/// <reference path="types/plugins.d.ts" />
/// <reference path="types/schema.d.ts" />
/// <reference path="types/app.config.d.ts" />
/// <reference path="../index.d.ts" />
/// <reference path="../../base/index.d.ts" />
/// <reference types="vite/client" />
/// <reference types="@pinia/nuxt" />
/// <reference types="vue-router" />
/// <reference path="types/middleware.d.ts" />
/// <reference path="types/layouts.d.ts" />
/// <reference path="vue-router.d.ts" />
/// <reference path="components.d.ts" />
/// <reference path="types/imports.d.ts" />
/// <reference path="imports.d.ts" />
/// <reference path="nuxt-config-schema" />
/// <reference path="schema/nuxt.schema.d.ts" />
/// <reference path="types/nitro.d.ts" />

export {}
