<template>
  <!-- 小程序不显示标题栏和顶部导航 -->
  <div v-if="!isXcx" id="common-header-warp" class="kkc-common-header-warp">
    <div class="kkc-common-header">
      <Logo />

      <div class="kkc-common-header__user">
        <button
          v-if="!userInfo"
          class="kkc-common-header__login"
          @click="handleLoginClick"
        >
          登录
        </button>
        <template v-else>
          <img
            :src="userInfo.photo ? userInfo.photo : 'https://oss.kuke99.com/kukecloud/static/common/default-avatar.png'"
            class="kkc-common-header__user-avatar"
            alt=""
            @click="handleGoUserCenter"
          >
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
//
import type { userInfoResultModel } from '@/apis/user/types'
import { useUserStore } from '~/stores/user.store'
const userStore = useUserStore()
//
const { isXcx } = useAppConfig()
// const userInfo = userStore.getUserInfo
const userInfo = computed((): userInfoResultModel => userStore.getUserInfo!)
const handleLoginClick = () => {
  if (os?.isApp && os.isAndroid) {
    window.android.login()
  } else if (os?.isApp && os.isPhone) {
    window.webkit.messageHandlers.login.postMessage(null)
  } else if (os?.isApp && os?.isHarmonyOS) {
    window.harmony?.login()
  } else {
    userStore.isLoginFn()
  }
}

// 获取header的高度和里顶部的距离
const getHeaderHeight = () => {
  const header = document.getElementById('common-header-warp')
  const headerHeight = header?.offsetHeight || 0
  const headerTop = header?.offsetTop || 0
  return {
    headerHeight,
    headerTop,
  }
}

const handleGoUserCenter = () => {
  //
  navigateTo('/me')
}

// 将高度和实例抛出去
defineExpose({
  getHeaderHeight,
})
</script>

<style lang="scss" scoped>
.kkc-common-header-warp {
  height: 80px;
}

.kkc-common-header {
  width: 7.5rem;
  position: fixed;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  padding: 0 24px;
  display: flex;
  justify-content: space-between;
  background: #fff;
  z-index: 101;
  height: 80px;

  &__user {
    display: flex;
    align-items: center;

    &-avatar {
      width: 56px;
      height: 56px;
      font-size: 48px;
      background: linear-gradient(180deg, #ffffff 0%, #dadada 100%);
      border: 4px solid #ffffff;
      border-radius: 50%;
    }
  }

  &__login {
    width: 96px;
    height: 56px;
    background-color: var(--kkc-brand-light5);
    box-shadow: inset 0px 1px 0px 0px rgba(255, 255, 255, 0.5);
    border-radius: 28px;
    text-align: center;
    line-height: 56px;
    font-size: 24px;
    font-weight: bold;
    @apply text-brand;
  }
}
</style>
