<script setup lang="ts">
interface Props {
  modelValue: boolean
  minheightContent?:string
  maxheightContent?:string // 内容区的最大高度
  titlePadding?: string
  title?: string
  customClass?: string
  isShowClose?:boolean
  minheight?: string
  maxheight?:string
}
const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  titlePadding: '7vw',
  title: '',
  maxheightContent: 'calc(84vw - 192px)',
  minheightContent: '0',
  isShowClose: true,
  maxheight: '735px'
})

const forbidScroll = (flag: boolean) => {
  const _body = document.body
  if (flag) {
    _body.classList.add('.no-scroll')
  } else {
    _body.classList.remove('.no-scroll')
  }
}

watch(
  () => props.modelValue,
  (nv) => {
    forbidScroll(nv)
  }
)

const emits = defineEmits<{
  (e: 'update:modelValue', isShowMask: boolean): void;
  (e: 'closeDia'): void;
}>()

const closeMask = () => {
  emits('closeDia')
  emits('update:modelValue', false)
}
</script>

<template>
  <div v-if="modelValue" class="mask-dialog" :class="customClass" @click.prevent>
    <div class="mask">
      <h3 v-if="title" class="mask-title">
        {{ title }}
      </h3>
      <div class="mask-content">
        <slot />
      </div>
      <img v-if="isShowClose" class="mask-delete" src="@/assets/images/giftPacks/delete-icon.png" alt="" @click="closeMask">
    </div>
  </div>
</template>

<style scoped lang="scss">
.no-scroll {
  overflow: hidden;
}

.mask-dialog {
  width: 100vw;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  background: rgba($color: #000000, $alpha: 0.75);
  z-index: 9997;

  .mask {
    width: 600px;
    min-height: v-bind(minheight);
    max-height: v-bind(maxheight);
    position: absolute;
    left: 50%;
    top: calc(50% - 90px);
    transform: translate(-50%, -50%);
    box-sizing: border-box;
    border-radius: 36px;
    padding-bottom: 42px;
    height:auto!important;
    &-content {
      position: relative;
      z-index: 999;
      width: 100%;
      padding: 0px 54px 0 54px;
      line-height: 36px;
      font-size: 24px;
      box-sizing: border-box;
      overflow-y: auto;
      color: #333;
      max-height: v-bind(maxheightContent);
      min-height: v-bind(minheightContent);

      &::-webkit-scrollbar-thumb {
        background-color: #f1f1f1;
        border-radius: 10px;
      }

      &::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }
    }

    &-title {
      @apply text-center text-[36px] text-[#333] font-bold mb-[35px];
      padding-top: v-bind(titlePadding);
      // padding-top:350px;
    }

    &-dialog-content {
      position: relative;
      z-index: 999;
      width: 100%;
      padding: 0px 54px 0 54px;
      line-height: 36px;
      font-size: 24px;
      box-sizing: border-box;
      overflow-y: auto;
      color: #333;

      &::-webkit-scrollbar-thumb {
        background-color: #f1f1f1;
        border-radius: 10px;
      }

      &::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }
    }

    &-delete {
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      width: 64px;
      height: 64px;
      bottom: -126px;
    }
  }
}
</style>
