<template>
  <div v-show="handleIsShow()" class="w-[100%]">
    <div class="flex p-[24px] items-center" @click="startAnsweringBtnClick">
      <img
        :class="['w-[34px]', 'h-[34px]', 'mr-[16px]', 'self-start', item.level != 1 && unFold ? '' : 'rotate-180']"
        :src="getIconUrl(item)"
        alt=""
        @click.self.stop="toggleClick()"
      >
      <div>
        <div
          :class="['w-[600px]', 'h-[38px]', 'text-[28px]', 'text-[#111]', 'leading-[38px]', 'title', 'one-ellipsis', item.level === 1 ? 'font-[500]' : 'font-[400]']"
        >
          {{ item.title }}
        </div>
        <div v-if="item.status" class="flex  mt-[10px] justify-between">
          <div>
            <span class="pr-[24px] text-[24px]" :class="getStatusClass(item.status)">{{ getStatusText(item.status) }}</span>
            <span class="pr-[48px] text-[24px] " :class="getStatusClass(item.status)">{{ item.progress }}</span>
          </div>
          <div>
            <span
              v-if="item.status===2"
              class="text-[24px] pr-[24px]"
              @click.self.stop="viewParsingBtnClick"
            >查看报告</span>
            <span
              class="text-[24px]"
              :class="getBtnClass(item.status)"
              @click.self.stop="viewParsingBtnClick"
            >{{ getBtnText(item.status) }}</span>
          </div>
        </div>
      </div>
    </div>
    <!-- 递归组件实现级联 (先判断是否存在子级)-->
    <template v-if="item.children && item.children.length">
      <SectionPracticeItem
        v-for="child in item.children"
        :key="child.id"
        :item="child"
      />
    </template>
  </div>
</template>

<script setup lang="ts">
import { useUserStore } from '~/stores/user.store'
// import treeFoldIcon from '@/assets/images/question-bank/tree-fold.png'
// import treeUnfoldIcon from '@/assets/images/question-bank/tree-unfold.png'
// import unfold2Icon from '@/assets/images/question-bank/unfold-2.png'
// import unfold3Icon from '@/assets/images/question-bank/unfold-3.png'
// import unfold4Icon from '@/assets/images/question-bank/unfold-4.png'
// import unfold5Icon from '@/assets/images/question-bank/unfold-5.png'
// import treeFold2Icon from '@/assets/images/question-bank/tree-fold-2.png'
// import treeFold3Icon from '@/assets/images/question-bank/tree-fold-3.png'
// import treeFold4Icon from '@/assets/images/question-bank/tree-fold-4.png'

const userStore = useUserStore()
const { questionBankApi } = useApi()

defineOptions({
  customOptions: {
    name: 'SectionPracticeItem'
  }
})

const props = defineProps<{
  item: any,

}>()

const emits = defineEmits<{
  (e: 'update'): void,
  (e: 'toggle', params: { key: string, id: string }): void
}>()

type IconItemType = {
  level: number;
  children: any[];
  [propsName: string]: any;
}

// 便于键值对数字key取值
type MyIconLevelKey = {
  1: string;
  2: string;
  3: string;
  4: string;
  5: string;
} & { [key: number]: string }

const unFold = ref<boolean>(false)
const getIconUrl = (item: IconItemType) => {
  const { level, children, id } = item
  const isUnfoled = children && children.length > 0
  unFold.value = props.openParentIds.has(id)
  // 只要最小级别展示图标 unfold5Icon，第一级无数据不显示图标
  const iconsByLevel: MyIconLevelKey = {
    1: isUnfoled ? (unFold.value ? treeFoldIcon : treeUnfoldIcon) : treeFoldIcon,
    2: isUnfoled ? unfold2Icon : treeFold2Icon,
    5: unfold5Icon
  }
  // 返回对应级别的图标
  return iconsByLevel[level] || ''
}

/**
 * 获取查看解析按钮是否显示
 */
// const getViewParsingBtnVisible = () => {
//   const { haveReport, children } = props.item
//   // 只有最小子级的做题状态会随着用户的实时做题情况来展示对应的做题状态（去做题/继续做题/查看解析)
//   // haveReport是否有解析 1是0否
//   // children判断是否为最小级
//   return haveReport === 1 && children.length === 0
// }

// 展开收起状态判断
const handleIsShow = () => {
  const { openParentIds, item } = props
  //  parentId被记录为展开状态
  //  第一级别默认显示，通过level判别
  return openParentIds.has(item.parentId) || item.level === 1
}

// 展开收起点击
const toggleClick = () => {
  const { item, parentIdsKey } = props
  // 最小级（!item.children.length）无展开收起
  if (item.children.length) {
    emits('toggle', {
      key: parentIdsKey,
      id: item.id
    })
  }
}

// const handleToggle = ({ key, id }: { key: string, id: string }) => {
//   emits('toggle', { key, id })
// }

// 查看解析
const viewParsingBtnClick = () => {
  navigateTo({
    path: '/question-bank/analysis',
    query: {
      ...module,
      ...props.params,
      targetResultId: props.item.targetResultId,
    },
  })
}

// 获取最小级别信息
const getMinChapterInfo = (item: any) => {
  if (item.targetResultId && item.sonInfo) {
    return { ...item.sonInfo, targetResultId: item.targetResultId }
  } else if (item.children.length > 0) {
    return findMiniChapterId(item.children)
  } else {
    return item
  }
}

interface StatusMap {
  [key: number]: {
    text: string;
    textClass: string;
    btnClass: string;
    textValue:string;
  };
}

// 定义状态对象
const statusMap:StatusMap = {
  0: { text: '无', textClass: 'text-[#999999]', btnClass: '', textValue: '' },
  1: { text: '刷题中', textClass: 'text-[#EB2330]', btnClass: 'primary-btn', textValue: '继续答题' },
  2: { text: '已做完', textClass: 'text-[#15CCC0]', btnClass: 'secondary-btn', textValue: '再次答题' },
  3: { text: '未开始', textClass: 'text-[#999999]', btnClass: 'primary-btn', textValue: '开始答题' },
}
// 完成状态文字
const getStatusText = (status: number) => {
  return statusMap[status]?.text || ''
}
// 完成状态颜色
const getStatusClass = (status: number) => {
  return statusMap[status]?.textClass || ''
}
// 操作按钮颜色
const getBtnClass = (status: number) => {
  return statusMap[status]?.btnClass || ''
}
// 操作按钮颜色
const getBtnText = (status: number) => {
  return statusMap[status]?.textValue || ''
}

// 构建查询参数对象
const buildQuery = (chapter: any) => {
  const { params, ...rest } = props
  return {
    ...module,
    moduleId: props.params.moduleManageId,
    chapterId: chapter.id,
    title: chapter.title,
    examStatus: 'start',
    ...params,
    ...rest,
  }
}

// 检查模块管理权限
const checkModuleManageProt = async (query: any) => {
  return questionBankApi.checkModuleManageProt({
    ...query,
    moduleManageId: props.params.moduleManageId,
  })
}

// 处理当前章节无目标结果的情况
const handleNoTargetResult = async (chapter: any) => {
  if (chapter.allCount > 0) {
    const query = buildQuery(chapter)
    const { error } = await checkModuleManageProt(query)

    if (error.value) {
      displayErrorMessage(error.value)
      if (error.value.data.code === '10103') {
        redirectToCurrentPathAfterDelay()
      }
      return false
    }

    navigateToToDoPageAfterDelay(query)
  } else {
    displayNoQuestionsMessage()
  }
}

// 显示错误消息
const displayErrorMessage = (errorValue: any) => {
  Message(errorValue.message)
}

// 重定向到当前路径（延迟3秒）
const redirectToCurrentPathAfterDelay = () => {
  setTimeout(() => {
    location.href = location.origin + location.pathname
  }, 3000)
}

// 跳转到做题页面（延迟1秒）
const navigateToToDoPageAfterDelay = (query: any) => {
  setTimeout(() => {
    toDoPage(query)
  }, 1000)
}

// 显示“当前小节试题正在加急上传中”消息
const displayNoQuestionsMessage = () => {
  Message('当前小节试题正在加急上传中，请您耐心等待~')
}

// 开始做题
const startAnsweringBtnClick = debounceFunc(async () => {
  if (!userStore.isLoginFn(history.state.current)) {
    return false
  }
  // 跳转答题页，只需关注章节ID，其余参数上级传入
  const { item } = props
  // 获取最小级别信息
  const chapter = getMinChapterInfo(item)
  if (!item.targetResultId) {
    // 开始做题
    handleNoTargetResult(chapter)
  } else {
    // 存在记录，存在最小级且需继续做题
    continueDoQuestionBtnClick(chapter)
  }
}, 1000, true)

const findMiniChapterId = (arr: any[]): string => {
  const firstChild = arr[0]
  if (firstChild.children.length === 0) {
    return firstChild
  } else {
    return findMiniChapterId(firstChild.children)
  }
}

const isShowContinuePopup = ref<boolean>(false)
const targetResultId = ref<string>('')
// 继续做题
const continueDoQuestionBtnClick = (item: any) => {
  if (item.doCount === item.allCount) {
    // 当前小节的总试题无余留，弹窗提示"本试题已全部练习完毕，是否开启下一轮重新练习~"
    // confirm询问框
    // 暂不开启 => 返回章节练习列表页，保持原有页面状态
    // 现在开启 => 返回章节练习列表页，且客观题正确率“————”，做题精度：0/小题总量
    targetResultId.value = item.targetResultId
    isShowContinuePopup.value = true
  } else {
    // 当前小节的总试题有余留 以及 抽取试题未做完 => 前往答题页继续做题
    const query = {
      ...module,
      ...props.params,
      moduleId: props.params.moduleManageId,
      title: item.title,
      targetResultId: item.targetResultId,
      questionType: props.questionType,
      examStatus: 'continue', // start 开始练习   continue 做题
    }
    toDoPage(query)
  }
}

// const handleHide = () => {
//   isShowContinuePopup.value = false
// }

// 开启下一轮成功，更新对应数据
// const handleConfirm = () => {
//   // 更新数据
//   emits('update')
// }

const toDoPage = (query: any) => {
  navigateTo({
    path: '/question-bank/chapter',
    query,
  })
}

</script>

<style scoped lang="scss">
@import "../../pages/question-bank/components/list.scss";
.box {
  .title {
    font-family: PingFang SC-Medium, PingFang SC;
  }
}

.one-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
