<!-- 验证码输入框 -->
<template>
  <div>
    <div v-if="mode === 'code'" class="code-warp">
      <input
        v-model="code"
        class="custom-input-style !mb-[0px]"
        placeholder="请输入验证码"
        maxlength="6"
        type="tel"
        autocomplete="off"
        @input="handleCodeInput"
      >
      <!-- @keyup="code = code.replace(/[^0-9\s]+/g, '')" -->
      <button
        class="code-warp--btn"
        :class="{ 'btn-active': active, 'down-time': !canClick }"
        :disabled="!canClick"
      >
        <span @click="getCode"> {{ contentText }}</span>
      </button>
      <button id="captcha-button" hidden />
    </div>
    <div v-else class="pwd-rapper">
      <input
        v-model="password"
        type="password"
        class="custom-input-style"
        :placeholder="pwdPlaceholder"
        maxlength="20"
        autocomplete="off"
        @input="handlePwdInput"
        @blur="handleCheckPwd"
        @focus="handleFocus"
      >
    </div>
  </div>
</template>

<script setup lang="ts">
import sha1 from 'crypto-js/sha1'
import type { CodeInputType } from './types'
// 获取验证码类型

const props = defineProps({
  mobile: {
    type: String,
    default: '',
  },
  mode: {
    type: String,
    validator: (val: string) => {
      return ['password', 'code'].includes(val)
    },
    default: 'code',
  },
  codeType: {
    type: String,
    default: GetCodeType.login,
  },
  checkPwd: {
    type: Boolean,
    default: false,
  },
  pwdPlaceholder: {
    type: String,
    default: '请输入密码',
  },
  downTime: {
    type: Number,
    default: 60,
  },
})

const encrypt = (content: string) => sha1(content).toString()

const emit = defineEmits<{
  (e: 'input', value: CodeInputType): void;
  (e: 'blur', value: CodeInputType): void;
}>()
const active = ref(false)
const code = ref('')
const contentText = ref('获取验证码')
const downTime = ref(props.downTime)
const canClick = ref(true)

watch(
  () => props.mobile,
  (newVal) => {
    active.value = newVal.length === 11
    if (newVal.length === 11 && canClick.value) {
      contentText.value = '获取验证码'
    }
  },
  {
    immediate: true,
  }
)
// 手机号输入框
watch(
  () => code.value,
  (newVal) => {
    emit('input', { plaintext: newVal })
  }
)

// 验证码倒计时
const { commonApi } = useApi()
// 验证码验证的回调函数
const captchaVerifyCallback = async (captchaVerifyParam: string) => {
  // 获取验证码接口参数
  const params = {
    mobile: props.mobile,
    scene: props.codeType,
    captchaVerifyParam
  }

  return getVerificationCodeStatus(commonApi.sendCode, params)
}
// 执行成功的回调地址
const onBizResultCallback = (bizResult: boolean) => {
  // 开始执行倒计时
  if (bizResult) { countDown() }
}
// 初始化验证码配置文件
const captchaConfig: globalThis.Captcha = { captchaVerifyCallback, onBizResultCallback }
// 注册验证码验证函数
const { initAliyunCaptcha, destroyAliyunCaptcha, getVerificationCodeStatus, captchaButton } = useCaptchaVerify(captchaConfig)
// 初始化验证码
onMounted(() => {
  if (props.mode === 'code') { initAliyunCaptcha() }
})
onBeforeUnmount(destroyAliyunCaptcha)

const countDown = () => {
  if (!canClick.value) {
    return
  }
  canClick.value = false
  contentText.value = `${downTime.value}s后重新获取`
  const timer = setInterval(() => { // TODO 内存泄漏可疑点: 确保客户端执行并及时清除
    downTime.value--
    contentText.value = `${downTime.value}s后重新获取`
    if (downTime.value === 0) {
      clearInterval(timer)
      downTime.value = props.downTime
      contentText.value = '重新发送'
      canClick.value = true
    }
  }, 1000)
}
// 获取验证码
const getCode = (): void => {
  if (props.mobile.length !== 11 || !canClick.value) {
    return
  }

  if (regPhone.test(props.mobile)) {
    captchaButton.value?.click()
  } else {
    Message('请输入正确的手机号')
  }
}
// code
const handleCodeInput = (e: Event): void => {
  code.value = (e.target as HTMLInputElement).value.replace(/\s/g, '').replace(/[^0-9\s]+/g, '')
}

// 密码
const password = ref('')
const handlePwdInput = (e: Event): void => {
  password.value = (e.target as HTMLInputElement).value.replace(/\s/g, '')

  emit('input', { plaintext: password.value, encrypt: encrypt(password.value) })
}
// 校验密码  密码支持8-20位，支持字母、数字、特殊符号2种以上组合的密码，支持字母大小写
const handleCheckPwd = (): void => {
  if (!props.checkPwd) {
    emit('blur', { plaintext: password.value, encrypt: encrypt(password.value) })
    return
  }
  if (passwordReg.test(password.value)) {
    emit('blur', { plaintext: password.value, encrypt: encrypt(password.value) })
  } else if (password.value.length >= 8) { Message('密码不符合规范，请重新输入~') }
}

const handleFocus = (): void => {
  window.scrollTo(0, 0)
}
</script>

<style lang="scss">
.code-warp {
  display: flex;
  width: 100%;
  // height: 112px;
  background: #f2f2f2;
  border-radius: 56px;
  margin-bottom: 24px;

  &--btn {
    display: flex;
    flex: 0 0 45%;
    align-items: center;
    justify-content: flex-end;
    padding-right: 40px;
    font-size: 32px;
    font-weight: 500;
    @apply text-brand;
    opacity: 0.3;
    background: none;
    border-radius: 56px;
  }

  .btn-active {
    opacity: 1;
  }

  .down-time {
    font-size: 32px;
    font-weight: 400;
    color: #999999;
    opacity: 1;
  }
}
</style>
