import { events } from '@kukefe/kkutils'

export const useComponentEventsHome = (switchPage: (id: string) => void) => {
  let pageHandle: (ctx: IComponentData) => void = () => { }
  let customPageHandle: (ctx: IComponentData) => void = () => { }

  onMounted(async () => {
    // 功能页面跳转
    pageHandle = async (ctx) => {
      const { componentName } = ctx || {}
      if (componentName === 'KKCategoryNavigateWap') {
        console.log('~~page~', ctx)
        switchPage('/')
      }
    }

    // 自定义页面跳转
    customPageHandle = async (ctx) => {
      const { item, componentName } = ctx || {}
      if (componentName === 'KKCategoryNavigateWap') {
        console.log(ctx, 'customPage')
        const { customPageId } = item
        switchPage(customPageId)
      }
    }

    events.subscribe('page', pageHandle)
    events.subscribe('customPage', customPageHandle)
  })

  onUnmounted(() => {
    events.unsubscribe(pageHandle)
    events.unsubscribe(customPageHandle)
  })
  return {

  }
}
