// import memberWxShareImg from '@/assets/images/member/member-wx-share.png'
// import type { PointsCenterStatusType } from '../types'
import {
  openMemberListProt,
  type OpenMemberListProtResponse,
  type Item,
  getItNowProt,
  type getItNowProtRequestData,
  // memberDetails
} from '~/apis/memberUser'
import { getShareInfoV2 } from '~/apis/common'
import { useUserStore } from '~/stores/user.store'
export const useUserMember = () => {
  /**
     * 用户已开通会员
     */
  const userMemberList = ref<OpenMemberListProtResponse['list']>([])
  /**
   * 用户是否是会员
  */
  const isUserMember = computed(() => {
    return !!userMemberList.value?.length
  })
  // const isUserMembe r = ref(false)

  /**
   * 获取用户已开通会员
  */
  const _openMemberListProt = async (params = {}) => {
  //
    const res = await openMemberListProt(params)
    const { data } = res || {}

    userMemberList.value = data.value.list || []
    return res
  }
  /* // 基础信息
  const member = ref<Record<string, any>>({
    id: '',
    isMember: 0,
    name: '会员',
    isError: false
  }) */
  /**
   * 用户是否是会员
  */
  // const enableMember = computed(() => {
  //   return member.value.isMember === 1
  // })
  /* const getMemberDetails = async () => {
    // 调用接口方法
    const { data } = await memberDetails()

    if (data.value) {
      member.value = data.value
    } else {
      member.value.isError = true
    }
  } */
  return {
    /**
 * @description 会员中心详情
 */
    // member,
    // enableMember,
    // getMemberDetails,
    //
    isUserMember,
    userMemberList,
    /**
     * 会员入口-用户已开通会员
     */
    _openMemberListProt,
  }
}
export const useMemberUtils = () => {
  /**
   * 跳转转会员中心
  */
  const toMemberCenterPage = async (item:Item) => {
    // if (!item.id) {
    //   Message('会员 ID 不能为空')
    //   return
    // }
    //
    console.log('[ toMemberCenterPage ] >', item)
    navigateTo({
      path: '/member-center',
      query: {
        memberGoodsId: item.memberGoodsId || item.id,
        // id: item.id,
        cateId: item.cateId,
        // memberGoodsId: item.memberGoodsId || '',
        firstLabelType: item.firstLabelType,
        firstLabelValue: item.firstLabelValue,
        secondLabelType: item.secondLabelType,
        secondLabelValue: item.secondLabelValue,
        logoSetting: item.logoSetting,
        // [item.firstLabelType]: item.firstLabelValue,
        // [item.secondLabelType]: item.secondLabelValue,
        // name: item.name,
      }
    })
  }
  /**
   * 创建会员中心
  */
  const toCreateMemberOrderPage = async (item:MaybeRef<{id:string, memberSpecId:string}>) => {
    // const _item = toValue(item)
    const { id, memberSpecId } = unref(item) || {}
    console.log('[ toCreateMemberOrderPage TODO ] >', item)
    if (!id) {
      Message('会员 id 不存在')
      return
    }
    if (!memberSpecId) {
      Message('会员规格 id 不存在')
      return
    }
    navigateTo({
      path: '/member-center/order',
      query: {
        memberGoodsId: id,
        memberSpecId,
      }
    })
  }
  /**
 * 不需要发货商品
 * 点击立即领取按钮
若未填写收货地址，toast提示“请先填写收货地址”
若已填写收货地址，toast提示“领取成功”，关闭此弹窗，领取成功不生成订单，订单列表不显示
 *
*/
  const handleMemberReceiveGoods = async (body:getItNowProtRequestData) => {
  //
    // const { resourceTypes, cateIds, id } = courseStore.courseInfo || {}

    // const body = {
    //   cateId: cateIds,
    //   // TODO
    //   goodsId: id,
    //   // goodsId: '735875016151552000',
    //   goodsType: 1,
    //   // TODO
    //   resourceType: resourceTypes,
    // // deliveryInfos: null
    // }
    const { error, data } = await getItNowProt(body)
    if (!error.value && data.value?.code === '10000') {
      Message('领取成功')
      setTimeout(() => {
        location.reload()
      }, 500)
    }
  }
  //
  return {
    /**
     * @description 跳转转会员中心
     */
    toMemberCenterPage,
    /**
     * @description 创建会员订单
     */
    toCreateMemberOrderPage,
    handleMemberReceiveGoods,
  }
}

export const useMemberWxShareInfo = (/* _memberInfo?:Ref<PointsCenterStatusType> */) => {
  const { isXcx, productName, logoImg } = useAppConfig()
  const userStore = useUserStore()
  const { setShareInfo } = useWxShare()
  // 标题，在根据运营中心的配置动态展示
  // const { member, getMemberDetails } = useUserMember()
  // console.log('[ member ] >', member)
  const setWxShareInfoMemberPage = async () => {
    // console.log('_memberInfo', _memberInfo)
    // 对接接口获取运营中心名称
    const { shareTitle, shareDescription, shareImg: shareImg2 } = await getWxShareInfoMember()
    const memberName = '会员'
    //
    const title = shareTitle || (productName + `${memberName}中心`)
    const desc = shareDescription || (productName + `${memberName}中心`)
    const shareImg = shareImg2 || logoImg
    // 设置微信分享信息
    setShareInfo({
      title,
      desc,
      shareImg
    }, false)
    let shareBigImg = ''
    if (!shareImg2) {
      // if (memberWxShareImg.startsWith('http')) {
      //   shareBigImg = memberWxShareImg
      // } else {
      //   shareBigImg = location.origin + memberWxShareImg
      // }
      /**
       * 兜底默认图片
       * 使用 oss.kuke99.com 的图片地址会造成图片分享到微信展示被裁切
       * 使用 kuke-production.oss-cn-beijing.aliyuncs.com 的图片地址正常
       * */
      // shareBigImg = 'https://oss.kuke99.com/kukecloud/static/share-wx-member.png'
      shareBigImg = 'https://kuke-production.oss-cn-beijing.aliyuncs.com/img/operatecenter/receiptPath/pc/20250822/1755846048210_9888.png'
      // shareBigImg = 'https://oss.kuke99.com/img/operatecenter/receiptPath/pc/20250822/1755846048210_9888.png'
    } else {
      shareBigImg = shareImg2
    }
    console.log('setWxShareInfoMemberPage', {
      title,
      desc,
      shareImg,
      // memberWxShareImg,
      shareImgXcx: shareBigImg
    })
    userStore.wxMiniProgramPostShareData('', title, shareBigImg)
  }
  // console.log('[ memberWxShareImg ] >', location.origin + memberWxShareImg)
  const { CookieKeyPoints } = usePointsClueSource()
  const getWxShareInfoMember = async () => {
    const body = {
      productSide: 7,
      // pageId: 1000,
      pageId: 1001,
    }
    if (isXcx) {
      body.productSide = 5
    }

    const list = [
      getShareInfoV2(body),
    //   getMemberDetail s(),
    ]
    // if (!unref(_memberInfo)) {
    //   list.push(getMemberDetails())
    // }
    const [res] = await Promise.all(list)

    //
    const { shareTitle, shareDescription, shareImg, clueSource, clientPoolId } = unref(res.data) || {}
    const _clueSource = clueSource || SourceEnum.Wap
    // 后端接口从cookie中获取
    // 兜底规则 线索来源 Wap = '846548671976882176'  WAP注册
    // 设置积分中心来源
    useCookie(CookieKeyPoints).value = _clueSource
    //
    // 设置客户池id
    if (clientPoolId) {
      useCookie('poolId').value = clientPoolId
    }
    if (_clueSource || clientPoolId) {
      // 设置内部系统id
      useCookie('internalSystemId').value = '21'
    }
    return { shareTitle, shareDescription, shareImg }
  }

  return {
    getWxShareInfoMember,
    setWxShareInfoMemberPage
  }
}
