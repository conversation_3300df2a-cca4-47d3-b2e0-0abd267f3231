<template>
  <div class="kkc-empty">
    <nuxt-icon
      v-if="svg"
      :name="img"
      filled
      class="kkc-empty__img rem-ignore"
      :style="_iconStyle"
    />
    <img v-else :src="img === 'no-data' ? emptyImage : img" class="kkc-empty__img" alt="" :style="_iconStyle">
    <div class="kkc-empty__text mt-[24px]">
      <slot v-if="$slots.default" />
      <template v-else>
        {{ text }}
      </template>
    </div>
  </div>
</template>
<script setup lang="ts">
import emptyImage from '../../assets/icon__data-empty.webp'
interface Props {
  text?: string;
  img?: string;
  topHeight?: number;
  svg?: boolean;
  iconStyle?: object;
}

const props = withDefaults(defineProps<Props>(), {
  text: '暂无数据',
  // img: '/images/icon__data-empty.png',
  img: 'no-data',
  topHeight: 0,
  svg: false,
  iconStyle: () => ({}),
})

const _iconStyle = computed(() => {
  return {
    marginTop: props.topHeight + 'px',
    ...props.iconStyle
  }
})

</script>
<style lang="scss">
.kkc-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 0;

  &__img {
    // font-size: 160px;
    height: 140px;
    text-align: center;
    svg{
      width: auto !important;
      height: 100% !important;
    }
  }

  &__text {
    // font-size: 28px;
    font-size: 14px;
    color: #999999;
    // line-height: 18px;
    // margin: 24px 0;
    // margin-bottom: 40px;
    text-align: center;
  }
}
</style>
